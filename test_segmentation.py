#!/usr/bin/env python3
import numpy as np
from typing import List, <PERSON><PERSON>

def _identify_distance_segments(distances: List[float], similarity_threshold: float) -> List[Tuple[int, int]]:
    """
    识别距离序列中的连续相似距离串
    
    Args:
        distances: 距离序列
        similarity_threshold: 相似性阈值
    
    Returns:
        距离串的列表，每个元素为 (start_index, end_index)
    """
    if len(distances) < 2:
        return [(0, len(distances)-1)] if distances else []
    
    segments = []
    current_start = 0
    
    for i in range(1, len(distances)):
        # 检查当前点与前一点的距离差异
        distance_diff = abs(distances[i] - distances[i-1])
        
        # 如果差异超过阈值，结束当前串，开始新串
        if distance_diff > similarity_threshold:
            segments.append((current_start, i-1))
            current_start = i
    
    # 添加最后一个串
    segments.append((current_start, len(distances)-1))
    
    # 合并过短的串（长度为1的串与相邻串合并）
    merged_segments = []
    i = 0
    while i < len(segments):
        start, end = segments[i]
        
        # 如果当前串只有1个点，尝试与相邻串合并
        if end - start == 0 and len(merged_segments) > 0:
            # 与前一个串合并
            prev_start, _ = merged_segments[-1]
            merged_segments[-1] = (prev_start, end)
        else:
            merged_segments.append((start, end))
        i += 1
    
    return merged_segments

def analyze_distance_sequence(distances):
    """分析距离序列的特征"""
    print(f"距离序列长度: {len(distances)}")
    print(f"距离范围: {min(distances):.1f} - {max(distances):.1f} cm")
    print(f"平均距离: {np.mean(distances):.1f} cm")
    print(f"中位数: {np.median(distances):.1f} cm")
    print(f"标准差: {np.std(distances):.1f} cm")
    
    # 计算相邻点的距离差异
    diffs = [abs(distances[i] - distances[i-1]) for i in range(1, len(distances))]
    print(f"相邻点最大差异: {max(diffs):.1f} cm")
    print(f"相邻点平均差异: {np.mean(diffs):.1f} cm")
    
    # 显示距离变化趋势
    print("\n距离变化趋势:")
    for i, dist in enumerate(distances):
        if i > 0:
            diff = distances[i] - distances[i-1]
            trend = "↑" if diff > 2 else "↓" if diff < -2 else "→"
            print(f"索引{i:2d}: {dist:5.1f}cm {trend} (变化: {diff:+5.1f})")
        else:
            print(f"索引{i:2d}: {dist:5.1f}cm")

def test_segmentation_with_different_thresholds(distances):
    """测试不同阈值下的分串结果"""
    thresholds = [1.0, 1.5, 2.0, 3.0, 5.0, 8.0, 10.0]
    
    print("\n=== 不同阈值下的分串结果 ===")
    for threshold in thresholds:
        segments = _identify_distance_segments(distances, threshold)
        print(f"\n阈值 {threshold:.1f}cm: 分为 {len(segments)} 个串")
        
        for i, (start, end) in enumerate(segments):
            segment_distances = distances[start:end+1]
            avg_dist = np.mean(segment_distances)
            length = end - start + 1
            print(f"  串{i+1}: 索引[{start}:{end}] 长度={length} 平均距离={avg_dist:.1f}cm 范围=[{min(segment_distances):.1f}, {max(segment_distances):.1f}]")

def manual_analysis(distances):
    """手动分析合理的分串方案"""
    print("\n=== 手动分析建议 ===")
    
    # 寻找明显的距离跳跃点
    jump_points = []
    for i in range(1, len(distances)):
        diff = abs(distances[i] - distances[i-1])
        if diff > 5.0:  # 大于5cm的跳跃
            jump_points.append((i, diff))
    
    print(f"发现 {len(jump_points)} 个明显跳跃点 (>5cm):")
    for idx, diff in jump_points:
        print(f"  索引 {idx}: {distances[idx-1]:.1f} -> {distances[idx]:.1f} (差异: {diff:.1f}cm)")
    
    # 基于趋势分析
    print("\n基于趋势的分串建议:")
    
    # 寻找连续上升/下降的区间
    trends = []
    current_trend = None
    trend_start = 0
    
    for i in range(1, len(distances)):
        diff = distances[i] - distances[i-1]
        if abs(diff) < 1.0:  # 平稳
            trend = "stable"
        elif diff > 0:  # 上升
            trend = "up"
        else:  # 下降
            trend = "down"
        
        if current_trend != trend:
            if current_trend is not None:
                trends.append((trend_start, i-1, current_trend))
            trend_start = i-1
            current_trend = trend
    
    # 添加最后一个趋势
    if current_trend is not None:
        trends.append((trend_start, len(distances)-1, current_trend))
    
    print("趋势分析:")
    for start, end, trend_type in trends:
        if end > start:
            segment_distances = distances[start:end+1]
            avg_dist = np.mean(segment_distances)
            trend_name = {"up": "上升", "down": "下降", "stable": "平稳"}[trend_type]
            print(f"  索引[{start}:{end}] {trend_name} 平均={avg_dist:.1f}cm")

def _detect_local_outliers(distances: List[float], threshold: float) -> List[int]:
    """
    检测局部异常点（在序列中突然跳高的孤立点或小段）
    """
    if len(distances) < 3:
        return []

    outliers = []

    # 方法1: 检测孤立的高峰点
    for i in range(1, len(distances) - 1):
        curr_dist = distances[i]
        prev_dist = distances[i-1]
        next_dist = distances[i+1]

        # 如果当前点比前后点都高很多
        if (curr_dist - prev_dist > threshold and
            curr_dist - next_dist > threshold):
            outliers.append(i)
            print(f"检测到孤立高峰点 {i}: {curr_dist:.1f}cm (前:{prev_dist:.1f}, 后:{next_dist:.1f})")

    # 方法2: 检测连续的高距离段（相对于周围环境）
    window_size = 3
    for i in range(window_size, len(distances) - window_size):
        # 计算当前点周围的局部环境
        before_window = distances[i-window_size:i]
        after_window = distances[i+1:i+1+window_size]
        local_env = before_window + after_window

        if local_env:
            local_median = np.median(local_env)
            curr_dist = distances[i]

            # 如果当前点比局部环境高很多
            if curr_dist - local_median > threshold:
                if i not in outliers:  # 避免重复添加
                    outliers.append(i)
                    print(f"检测到局部异常点 {i}: {curr_dist:.1f}cm (局部环境中位数:{local_median:.1f})")

    # 方法3: 检测相对于全局基线的异常点
    # 计算移动平均作为基线
    baseline = []
    window = 5
    for i in range(len(distances)):
        start = max(0, i - window//2)
        end = min(len(distances), i + window//2 + 1)
        window_data = distances[start:end]
        baseline.append(np.median(window_data))

    for i, (dist, base) in enumerate(zip(distances, baseline)):
        if dist - base > threshold * 1.5:  # 更严格的阈值
            if i not in outliers:
                outliers.append(i)
                print(f"检测到基线异常点 {i}: {dist:.1f}cm (基线:{base:.1f})")

    return sorted(outliers)

def _detect_gradient_outliers(distances: List[float], threshold: float) -> List[int]:
    """
    基于距离梯度变化检测异常点
    """
    if len(distances) < 5:
        return []

    outliers = []

    # 计算一阶梯度（相邻点的距离差）
    gradients = []
    for i in range(1, len(distances)):
        grad = distances[i] - distances[i-1]
        gradients.append(grad)

    # 计算二阶梯度（梯度的变化率）
    gradient_changes = []
    for i in range(1, len(gradients)):
        grad_change = abs(gradients[i] - gradients[i-1])
        gradient_changes.append(grad_change)

    print(f"梯度变化分析:")
    print(f"最大梯度变化: {max(gradient_changes):.1f}cm")
    print(f"平均梯度变化: {np.mean(gradient_changes):.1f}cm")
    print(f"梯度变化阈值: {threshold:.1f}cm")

    # 检测梯度变化异常的点
    for i, grad_change in enumerate(gradient_changes):
        if grad_change > threshold:
            point_idx = i + 1

            if point_idx < len(distances) - 1:
                prev_dist = distances[point_idx - 1]
                curr_dist = distances[point_idx]
                next_dist = distances[point_idx + 1]

                diff_prev = abs(curr_dist - prev_dist)
                diff_next = abs(curr_dist - next_dist)

                if diff_prev > threshold * 0.8 or diff_next > threshold * 0.8:
                    outliers.append(point_idx)
                    print(f"梯度异常点 {point_idx}: 梯度变化={grad_change:.1f}, "
                          f"距离={curr_dist:.1f} (前:{prev_dist:.1f}, 后:{next_dist:.1f})")

    return sorted(list(set(outliers)))

def test_new_algorithm(distances):
    """测试新的分串和异常检测算法"""
    print("\n=== 测试改进的分串算法 ===")

    # 使用自适应阈值进行分串
    max_distance_jump = 5.0
    segmentation_threshold = min(3.0, max_distance_jump * 0.6)
    segments = _identify_distance_segments(distances, segmentation_threshold)
    print(f"使用{segmentation_threshold}cm阈值分为 {len(segments)} 个串:")

    # 分析每个串的特征
    segment_stats = []
    for i, segment in enumerate(segments):
        start_idx, end_idx = segment
        segment_distances = distances[start_idx:end_idx+1]
        avg_distance = np.mean(segment_distances)
        max_distance = np.max(segment_distances)
        min_distance = np.min(segment_distances)
        segment_length = end_idx - start_idx + 1

        segment_stats.append({
            'start': start_idx,
            'end': end_idx,
            'avg_distance': avg_distance,
            'max_distance': max_distance,
            'min_distance': min_distance,
            'length': segment_length,
            'distances': segment_distances
        })
        print(f"串 {i}: 索引[{start_idx}:{end_idx}], 长度={segment_length}, "
              f"平均={avg_distance:.1f}cm, 范围=[{min_distance:.1f}, {max_distance:.1f}]cm")

    # 异常检测
    print("\n异常检测分析:")
    max_distance_jump = 5.0
    all_distances = np.array(distances)
    global_median = np.median(all_distances)
    global_std = np.std(all_distances)

    print(f"全局统计: 中位数={global_median:.1f}cm, 标准差={global_std:.1f}cm")

    outlier_segments = []
    for i, segment in enumerate(segment_stats):
        is_outlier = False
        reasons = []

        # 条件1: 距离值明显偏大
        distance_threshold = global_median + 1.5 * global_std
        if segment['avg_distance'] > distance_threshold:
            is_outlier = True
            reasons.append(f"距离偏大({segment['avg_distance']:.1f} > {distance_threshold:.1f})")

        # 条件2: 相对于其他串明显偏高
        if len(segment_stats) >= 3:
            other_segments_distances = [seg['avg_distance'] for j, seg in enumerate(segment_stats) if j != i]
            if other_segments_distances:
                other_median = np.median(other_segments_distances)
                if segment['avg_distance'] - other_median > max_distance_jump:
                    is_outlier = True
                    reasons.append(f"相对其他串偏高(差异{segment['avg_distance'] - other_median:.1f})")

        # 条件3: 孤立高峰
        prev_avg = segment_stats[i-1]['avg_distance'] if i > 0 else None
        next_avg = segment_stats[i+1]['avg_distance'] if i < len(segment_stats) - 1 else None

        if prev_avg is not None and next_avg is not None:
            if (segment['avg_distance'] - prev_avg > max_distance_jump and
                segment['avg_distance'] - next_avg > max_distance_jump):
                is_outlier = True
                reasons.append(f"孤立高峰(比前后高{segment['avg_distance'] - prev_avg:.1f}, {segment['avg_distance'] - next_avg:.1f})")

        if is_outlier:
            outlier_segments.append(i)
            print(f"  串 {i} 为异常串: {', '.join(reasons)}")
        else:
            print(f"  串 {i} 正常")

    # 如果没有异常串，检查距离分层
    if not outlier_segments and len(segment_stats) >= 2:
        max_avg = max(seg['avg_distance'] for seg in segment_stats)
        min_avg = min(seg['avg_distance'] for seg in segment_stats)

        if max_avg - min_avg > max_distance_jump * 2:
            for i, segment in enumerate(segment_stats):
                if segment['avg_distance'] == max_avg:
                    outlier_segments.append(i)
                    print(f"  基于距离分层标记串 {i} 为异常串: 最高={max_avg:.1f}, 最低={min_avg:.1f}")
                    break

    # 检测局部异常点
    print("\n局部异常点检测:")
    local_outliers = _detect_local_outliers(distances, max_distance_jump)
    if local_outliers:
        print(f"检测到 {len(local_outliers)} 个局部异常点: {local_outliers}")
    else:
        print("未检测到局部异常点")

    # 检测梯度异常点
    print("\n梯度异常点检测:")
    gradient_outliers = _detect_gradient_outliers(distances, max_distance_jump)
    if gradient_outliers:
        print(f"检测到 {len(gradient_outliers)} 个梯度异常点: {gradient_outliers}")
    else:
        print("未检测到梯度异常点")

    # 综合验证异常点
    print("\n综合验证:")
    all_outliers = []
    for seg_idx in outlier_segments:
        start, end = segments[seg_idx]
        all_outliers.extend(range(start, end + 1))
    all_outliers.extend(local_outliers)

    # 梯度验证：只保留同时被多种方法检测到的异常点
    if gradient_outliers:
        verified_outliers = []
        for idx in all_outliers:
            if idx in gradient_outliers or idx in local_outliers:
                verified_outliers.append(idx)
        print(f"经梯度验证后的异常点: {sorted(set(verified_outliers))}")
        return segments, outlier_segments, local_outliers, gradient_outliers, sorted(set(verified_outliers))

    return segments, outlier_segments, local_outliers, gradient_outliers, sorted(set(all_outliers))

if __name__ == "__main__":
    # 您提供的新距离序列
    distances = [43.8, 45.9, 50.3, 52.6, 59.7, 67.9, 55.3, 72.4, 59.7, 55.9, 55.9, 56.8, 55.9, 57.9, 59.7, 61.5, 61.5, 63.5, 65.3, 65.3, 63.5, 63.5, 61.5, 61.5, 59.7, 59.7, 53.5, 59.7, 52.4, 57.9, 53.5]

    print("=== 新距离序列分析 ===")
    print("您指出的异常点:")
    print("索引5: 67.9cm, 索引7: 72.4cm")
    print("索引15-23: 61.5, 61.5, 63.5, 65.3, 65.3, 63.5, 63.5, 61.5, 61.5")
    print()

    analyze_distance_sequence(distances)

    test_segmentation_with_different_thresholds(distances)

    manual_analysis(distances)

    # 测试新算法
    segments, outliers, local_outliers, gradient_outliers, verified_outliers = test_new_algorithm(distances)

    # 验证异常检测结果
    print("\n=== 异常检测验证 ===")
    user_indicated_outliers = [5, 7] + list(range(15, 24))  # 您指出的异常点索引

    print(f"您指出的异常点索引: {user_indicated_outliers}")
    print(f"串级异常检测结果: {[list(range(segments[i][0], segments[i][1]+1)) for i in outliers]}")
    print(f"局部异常检测结果: {local_outliers}")
    print(f"梯度异常检测结果: {gradient_outliers}")
    print(f"经验证的最终异常点: {verified_outliers}")

    # 计算不同方法的检测准确性
    methods = {
        "串级检测": [idx for seg_idx in outliers for idx in range(segments[seg_idx][0], segments[seg_idx][1]+1)],
        "局部检测": local_outliers,
        "梯度检测": gradient_outliers,
        "综合验证": verified_outliers
    }

    print(f"\n=== 各方法检测效果对比 ===")
    for method_name, detected_indices in methods.items():
        correct = set(user_indicated_outliers) & set(detected_indices)
        missed = set(user_indicated_outliers) - set(detected_indices)
        false_positive = set(detected_indices) - set(user_indicated_outliers)

        accuracy = len(correct) / len(user_indicated_outliers) * 100 if user_indicated_outliers else 0
        precision = len(correct) / len(detected_indices) * 100 if detected_indices else 0

        print(f"{method_name}:")
        print(f"  正确检测: {len(correct)}个, 漏检: {len(missed)}个, 误检: {len(false_positive)}个")
        print(f"  准确率: {accuracy:.1f}%, 精确率: {precision:.1f}%")
