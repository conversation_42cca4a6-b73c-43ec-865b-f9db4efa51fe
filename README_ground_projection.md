# Wire线地面投影距离修正功能

## 问题背景

在wire线距离测量中，当wire线是非刚体并发生变形时，会导致线上的点隆起（在图像中表现为y值变小），这会造成距离测量的误差。原因是距离表是基于目标贴地时建立的，当目标隆起时，直接查表得到的距离值就不准确了。

## 解决方案

通过地面投影技术，将隆起的wire线点投影到地面位置，然后重新计算距离值，从而获得更准确的测量结果。

### 核心原理

1. **坐标转换**: 将像素坐标和距离值转换为相机坐标系(x,y,z)
2. **地面投影**: 将3D点投影到地面平面（假设地面为y=相机高度）
3. **反向投影**: 将投影后的3D点转换回像素坐标
4. **距离重算**: 使用距离表重新计算投影点的距离值

### 数学模型

```
投影比例 = 相机高度 / 原始点的y坐标
投影点x = 原始点x × 投影比例
投影点y = 相机高度
投影点z = 原始点z × 投影比例
```

## 使用方法

### 1. 基本使用

```bash
# 启用地面投影功能
python object_detection4_by_camera.py --use-ground-projection

# 设置相机高度（默认60cm）
python object_detection4_by_camera.py --use-ground-projection --camera-height 65.0
```

### 2. 参数说明

- `--use-ground-projection`: 启用地面投影修正功能
- `--camera-height`: 设置相机离地面的高度（单位：cm，默认60.0）

### 3. 视觉标识

- **原始模式**: 检测点用白色边框标识
- **投影模式**: 检测点用青色边框标识，表示已经过投影修正

## 测试和演示

### 1. 功能测试

```bash
# 测试地面投影功能
python test_ground_projection.py
```

### 2. 原理演示

```bash
# 查看地面投影的几何原理演示
python demo_ground_projection_principle.py
```

## 代码结构

### 核心函数

1. **`project_to_ground()`**: 将相机坐标投影到地面
2. **`process_wire_points_with_ground_projection()`**: 处理wire线点的投影修正
3. **`CameraCoordinateConverter`**: 相机坐标转换器

### 主要文件

- `object_detection4_by_camera.py`: 主程序，集成了地面投影功能
- `test_ground_projection.py`: 功能测试脚本
- `demo_ground_projection_principle.py`: 原理演示脚本
- `pixel_to_physical_py/src/camera_coordinate_converter.py`: 相机坐标转换器

## 效果评估

### 适用场景

- ✅ Wire线发生变形隆起
- ✅ 目标点偏离地面较多（>5cm）
- ✅ 需要高精度距离测量

### 预期改善

- 减少由于wire线变形造成的距离测量误差
- 提高距离测量的一致性和准确性
- 特别适用于隆起高度较大的情况

### 限制条件

- 需要准确的相机内参标定
- 需要已知的相机安装高度
- 假设地面为平面
- 相机坐标y值不能过小（避免除零错误）

## 配置要求

### 必需文件

1. 相机内参配置文件: `pixel_to_physical_py/config/calib_intrix_new.yaml`
2. 距离表文件: `pixel_to_physical_py/config/distance_table`
3. 相机标定映射表: `pixel_to_physical_py/config/mapx`, `pixel_to_physical_py/config/mapy`

### 参数调整

根据实际安装情况调整以下参数：
- 相机高度 (`--camera-height`)
- 是否启用投影 (`--use-ground-projection`)

## 故障排除

### 常见问题

1. **投影失败**: 检查相机坐标y值是否过小
2. **距离异常**: 确认相机高度设置是否正确
3. **配置错误**: 检查相机内参和距离表文件是否存在

### 调试建议

1. 先运行测试脚本验证功能
2. 查看控制台输出的详细信息
3. 对比投影前后的距离值变化
4. 根据实际场景调整相机高度参数

## 技术细节

### 坐标系定义

- **像素坐标系**: (u, v) 图像像素坐标
- **相机坐标系**: (x, y, z) 以相机为原点，z轴向前，y轴向下
- **地面坐标系**: 假设地面为y=相机高度的平面

### 投影几何

投影过程基于相似三角形原理，通过比例缩放将隆起点投影到地面平面上，从而获得该点在地面时的等效距离。

## 更新日志

- v1.0: 初始版本，实现基本的地面投影功能
- v1.1: 添加参数配置和视觉标识
- v1.2: 增加测试脚本和原理演示
