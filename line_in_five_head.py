from copy import copy
from copy import deepcopy
default_scope = 'mmyolo'
work_dir = '.'
resume = False
find_unused_parameters = True
max_epochs = 300
switch_epochs = 10
main_version = "1.0.0"
runner_type = "EcoAi.EcoAiMultiTaskRunner"
train_batch_size = 32
# custom
custom_imports = dict(
    imports=[
        'EcoAi.datasets',
        'EcoAi.engine',
        'EcoAi.models._mutitask_models',
        'EcoAi.models._econn_models',
        'EcoAi.visualization.detection_visualizer',
        'EcoAi.evaluation.metrics',
        'EcoAi.transforms',
        'EcoAi.visualization'
        ],
    allow_failed_imports=False)

# region 模型文件
################################################## model ##################################################
#                                                   backbone
#                    +----------------------------------|-----------------------+----------------------+----------------------+
#                    |                                  |                       |                      |                      |
#                sbackbone_1-                      sbackbone_2              sbackbone_3            sbackbone_4            sbackbone_4
#                    |                                  |                       |                      |                      |
#             head_ground_old7                  head_ground_new2           head_pershome           head_petsshit           line
## sub-backbone
neck = dict(
    act_cfg=dict(inplace=True, negative_slope=0.1, type='LeakyReLU'),
    deepen_factor=0.33,
    in_channels=[
        256,
        512,
        1024,
    ],
    norm_cfg=dict(eps=0.001, momentum=0.03, type='BN'),
    num_csp_blocks=3,
    out_channels=[
        256,
        512,
        1024,
    ],
    type='YOLOv8PAFPN',
    widen_factor=0.5
)
## sub-backbone-1


## sub-backbone-5
neck5= deepcopy(neck)
neck5["name"] = "sbackbone_5"
neck5["parent"] = "backbone"
neck5["version"] = "1.0"




model_test_cfg = dict(
    # The config of multi-label for multi-class prediction.
    multi_label=True,
    # The number of boxes before NMS
    nms_pre=1000,
    score_thr=0.05,  # Threshold to filter out boxes.
    nms=dict(type='nms', iou_threshold=0.6),  # NMS type and threshold
    max_per_img=100)
## head-5 line classs
head5 =dict(
        anchor_strides=[8, 16, 32, 64],
        feat_channels=64,
        input_channel=128,
        loss=dict(type='BCELoss'),
        norm_cfg=dict(type='BN'),
        num_classes=1,
        reg_max=16,
        stacked_convs=4,
        loss_ratio=1.,
        test_cfg=dict(
            max_per_img=100,
            min_bbox_size=0,
            nms=dict(iou_threshold=0.6, type='nms'),
            nms_pre=1000,
            score_thr=0.05),
        type='EcoAi.LineV3Head')
# head5['head_module']['num_classes'] = head5_num_classes
# head5['train_cfg']['assigner']['num_classes'] = head5_num_classes
head5['name'] = "head_ground_line"
head5['parent'] = "sbackbone_5"
# head5['parent'] = "sbackbone_1"
# head2["init_cfg"] = dict(type='Pretrained',
#                          prefix='bbox_head.',
#                          checkpoint="/hzj/code/muti_task/exps/econn_new3cls/20240115_yolov8s_new3cls_base_fzbck"
#                                     "/20240115_151009/best/model_best.pth")
head5["version"] = "1.0"

model = dict(
    # 此处需要加不同库名
    type="EcoAi.MutiTaskStructure",
    data_preprocessor=dict(
        bgr_to_rgb=True,
        mean=[
            0.0,
            0.0,
            0.0,
        ],
        pad_size_divisor=32,
        std=[
            255.0,
            255.0,
            255.0,
        ],
        type='EcoAi.MutitaskDetDataPreprocessor'),
    backbone=dict(
        name="backbone",
        version="1.1.1",
        act_cfg=dict(inplace=True, negative_slope=0.1, type='LeakyReLU'),
        arch='P5',
        deepen_factor=0.33,
        last_stage_out_channels=1024,
        norm_cfg=dict(eps=0.001, momentum=0.03, type='BN'),
        type='YOLOv8CSPDarknet',
        widen_factor=0.5
    ),
    subbackbones=[neck5],#, neck3],
    heads=[head5],
)
############################################## model end ###############################################
# endregion

# region 数据集
############################################## dataset #################################################
# train dataset
train_pipeline1 = [
        dict(backend_args=None, type='LoadImageFromFile'),
        dict(type='LoadAnnotations', with_bbox=True),
        dict(type='Resize', scale=(640, 640), keep_ratio=True),
        dict(type='Pad',size=(640, 640),pad_val=dict(img=114.0)), # resize之后要根据Pad填充，这样才能把图片保持一致
        dict(
            img_scale=(640, 640),
            pad_val=114.0,
            pre_transform=[
                dict(backend_args=None, type='LoadImageFromFile'),
                dict(type='LoadAnnotations', with_bbox=True),
            ],
            type='Mosaic'),
        dict(
            border=(-320, -320),
            border_val=(114, 114, 114),
            max_aspect_ratio=100,
            max_rotate_degree=0.0,
            max_shear_degree=0.0,
            scaling_ratio_range=(0.5, 1.5),
            type='YOLOv5RandomAffine'),
        # dict(type='Lambda', func=lambda results: (print("Current keys in results:", results.keys()), results)[1]),
        # dict(
        #     bbox_params=dict(
        #         format='pascal_voc',
        #         label_fields=['gt_bboxes_labels', 'gt_ignore_flags'],
        #         type='BboxParams'),

        #     keymap=dict(gt_bboxes='bboxes', img='image',img_path='img_path'),
        #     transforms=[
        #         dict(p=0.01, type='Blur'),
        #         dict(p=0.01, type='MedianBlur'),
        #         dict(p=0.01, type='ToGray'),
        #         dict(p=0.01, type='CLAHE'),
        #     ],
        #     type='mmdet.Albu'),
        dict(type='YOLOv5HSVRandomAug'),
        dict(prob=0.5, type='mmdet.RandomFlip'),
        dict(
            meta_keys=('img_id', 'img_path', 'ori_shape', 'img_shape', 'flip', 'flip_direction'),
            type='mmdet.PackDetInputs')]
train_pipeline_line = [
            # dict(backend_args=None, type='LoadImageFromFile'),
            dict(_scope_='mmdet', type='mmdet.LoadPointAnnotations'),
            dict(prob=0.5, type='mmdet.EconnRandomFlip'),
            dict(
                crop_size=(640, 640),
                ioa_thres=0.7,
                keep_ratio=True,
                random_stretch=True,
                type='mmdet.IoaRandomCrop'),
            dict(
                brightness=(0.8, 1.2),
                color_shift=(0.8, 1.2),
                saturation=(0.8, 1.4),
                type='mmdet.IAAColorAug'),
            dict(type='mmdet.PackPointInputs'),
        ]


ground_new2cls_train_dataset = dict(ann_file='/李燃青/4data/扫地机/ground_new3cls_of/train.json',
                                    data_prefix=dict(sub_data_root='/李燃青/4data/扫地机/ground_new3cls_of/'),
                                    pipeline=train_pipeline1,
                                    type='EcoAi.SZgroundNew2Dataset')

ground_old7cls_train_dataset = dict(ann_file='/李燃青/4data/扫地机/ground_old8cls_of/train.json',
                                    data_prefix=dict(sub_data_root='/李燃青/4data/扫地机/ground_old8cls_of/ground_train_0526'),
                                    filter_cfg=None,
                                    pipeline=train_pipeline1,
                                    type='EcoAi.SZgroundOld7Dataset')
petshome_train_dataset = dict(ann_file='/李燃青/4data/扫地机/petshome_of/train.json',
                              data_prefix=dict(sub_data_root='/李燃青/4data/扫地机/petshome_of/train_20230329_mix_sp'),
                              filter_cfg=None,
                              pipeline=train_pipeline1,
                              type='EcoAi.SZpetshomeDataset')

petsshit_train_dataset = dict(ann_file='/李燃青/4data/扫地机/petsshit_of/train.json',
                              data_prefix=dict(sub_data_root='/李燃青/4data/扫地机/petsshit_of/train_231222_add_neg_less'),
                              filter_cfg=None,
                              pipeline=train_pipeline1,
                              type='EcoAi.SZPetsshitDataset')
#                                 type='EcoAi.LinePointCopy')
ground_line_train_dataset = dict(
                                ann_path=
                                ["/李燃青/4data/扫地机/ground_wire/wire/wire_chatou_undistort/train_chatou_v5_undistort.json",
                                 "/李燃青/4data/扫地机/ground_wire/wire/x2优化二期/labelme_json_x2_nobg.json",
                                 "/李燃青/4data/扫地机/ground_wire/wire/su_bihuan_20230516/su_bihuan_20230516_nobg.json",
                                 "/李燃青/4data/扫地机/obstcale/train_line.json",
                                 # "/secret/mutitask/datasets/ground_obstacle/24_ground_obstacle_waicai/train_line.json"
                                 ],
                                #backend_args=None,
                                img_path=
                                ["/李燃青/4data/扫地机/ground_wire/wire/wire_chatou_undistort/train_chatou_undistort",
                                 "/李燃青/4data/扫地机/ground_wire/wire/x2优化二期/all_images",
                                 "/李燃青/4data/扫地机/ground_wire/wire/su_bihuan_20230516/images",
                                 "/李燃青/4data/扫地机/obstcale/whole_data_package",
                                 # "/secret/mutitask/datasets/ground_obstacle/24_ground_obstacle_waicai"
                                 ],
                                bk_path=["/李燃青/4data/扫地机/ground_wire/wire/bg_img/eco_didian",
                                         "/李燃青/4data/扫地机/ground_wire/wire/bg_img/eco_indoor",
                                         "/李燃青/4data/扫地机/ground_wire/wire/bg_img/ground",
                                         "/李燃青/4data/扫地机/ground_wire/wire/wujian/test_kexing_imgs",
                                         "/李燃青/4data/扫地机/ground_wire/wire/wujian/lab",
                                         "/李燃青/4data/扫地机/ground_wire/wire/bg_img/pets",
                                         "/李燃青/4data/扫地机/ground_wire/wire/wujian/suzhoudiban_wujian",
                                         "/李燃青/4data/扫地机/ground_wire/wire/bg_img/su_test_lab",
                                         "/李燃青/4data/扫地机/ground_wire/wire/bg_img/song_xiang_yuhuawen",
                                         "/李燃青/4data/扫地机/ground_wire/wire/bg_img/njLab02",
                                         "/李燃青/4data/扫地机/ground_wire/wire/bg_img/dibancaizhi2/",
                                         "/李燃青/4data/扫地机/ground_wire/wire/bg_img/bg_keliwu/",
                                         "/李燃青/4data/扫地机/ground_wire/wire/bg_img/error_bg_2022Data",
                                         "/李燃青/4data/扫地机/ground_wire/wire/bg_img/bihuan_ground_5_wire_10w_20220222_bg",
                                        #  "/李燃青/4data/扫地机/ground_wire/wire_error/241203_wire_error"
                                         # "/李燃青/4data/扫地机/ground_wire/wire/bg_img/suzhou_background/images"
                                         ],
                                pipeline=train_pipeline_line,
                                paste_rate=0.65,
                                type='EcoAi.LinePointV3')


train_dataloader = dict(
    batch_size=train_batch_size,
    collate_fn=dict(type='EcoAi.mutitask_collate'),
    num_workers=16,
    persistent_workers=False,
    pin_memory=False,
    sampler=dict(_scope_='mmyolo', shuffle=True, type='DefaultSampler'),
    dataset=dict(datasets=[ground_new2cls_train_dataset, ground_old7cls_train_dataset, petshome_train_dataset, petsshit_train_dataset,ground_line_train_dataset],
                 corr_heads=["head_ground_new2", "head_ground_old7", "head_petshome", "head_petsshit","head_ground_line"],
                 head_type=["yolo", "yolo", "yolo", "yolo","line"],
                 type='EcoAi.MutitaskDataset'),
)

# val dataset
val_pipeline = [
    dict(backend_args=None, type='LoadImageFromFile'),
    dict(type='LoadAnnotations', with_bbox=True),
    dict(type='Resize', scale=(640, 640), keep_ratio=True),
    dict(type='Pad',size=(640, 640),pad_val=dict(img=0.0)), # resize之后要根据Pad填充，这样才能把图片保持一致
    # dict(
    #     allow_scale_up=False,
    #     pad_val=dict(img=114),
    #     scale=(640, 384),
    #     type='mmyolo.LetterResize'),
    dict(
        meta_keys=('img_id', 'img_path', 'ori_shape', 'img_shape', 'scale_factor', 'pad_param'),
        type='mmdet.PackDetInputs'),
]
val_line_pipeline = [
            dict(backend_args=None, type='LoadImageFromFile'),
            dict(_scope_='mmdet', type='mmdet.LoadPointAnnotations'),
            dict(input_wh=(640, 384), keep_ratio=False, type='mmdet.EconnResize'),
            dict(
                meta_keys=(
                    'img_id',
                    'img_path',
                    'ori_shape',
                    'img_shape',
   'scale_factor',
                ),
                type='mmdet.PackPointInputs'),
        ]
ground_new2cls_val_dataset = dict(ann_file='/李燃青/4data/扫地机/ground_new3cls_of/val.json',
                                  data_prefix=dict(sub_data_root=''),
                                  data_root='/李燃青/4data/扫地机/ground_new3cls_of',
                                  filter_cfg=None,
                                  pipeline=val_pipeline,
                                  type='EcoAi.SZgroundNew2Dataset')

ground_old7cls_val_dataset = dict(ann_file='/李燃青/4data/扫地机/ground_old8cls_of/val.json',
                                  data_prefix=dict(sub_data_root=''),
                                  data_root='/李燃青/4data/扫地机/ground_old8cls_of/ground_val_0526',
                                  filter_cfg=None,
                                  pipeline=val_pipeline,
                                  type='EcoAi.SZgroundOld7Dataset')

petshome_val_dataset = dict(ann_file='/李燃青/4data/扫地机/petshome_of/val.json',
                            data_prefix=dict(sub_data_root=''),
                            data_root='/李燃青/4data/扫地机/petshome_of/val_20230329_sp',
                            filter_cfg=None,
                            pipeline=val_pipeline,
                            type='EcoAi.SZpetshomeDataset')

petsshit_val_dataset = dict(ann_file='/李燃青/4data/扫地机/petsshit_of/val.json',
                            data_prefix=dict(sub_data_root=''),
                            data_root='/李燃青/4data/扫地机/petsshit_of/val_231129_mb_cb_1',
                            filter_cfg=None,
                            pipeline=val_pipeline,
                            type='EcoAi.SZPetsshitDataset')

ground_line_val_dataset = dict(
                                ann_path=[
                                          "/李燃青/4data/扫地机/ground_wire/wire/白盒二期/val_linestrip_v1_nobg.json",
                                         ],
                                img_path=[
                                          "/李燃青/4data/扫地机/ground_wire/wire/白盒二期/images/val",
                                         ],
                                # backend_args=None,
                                pipeline=val_line_pipeline,
                                data_root='/李燃青/4data/扫地机/ground_wire/wire/白盒二期/images',
                                test_mode=True,
                                type='EcoAi.LinePointV3')

val_dataloader = dict(
    batch_size=train_batch_size,
    # collate_fn=dict(type='EcoAi.mutitask_collate'),
    num_workers=16,
    persistent_workers=False,
    pin_memory=False,
    sampler=dict(_scope_='mmyolo', shuffle=False, type='DefaultSampler'),
    dataset=dict(datasets=[ground_old7cls_val_dataset, ground_new2cls_val_dataset, petshome_val_dataset, petsshit_val_dataset,ground_line_val_dataset],
                 corr_heads=["head_ground_old7", "head_ground_new2", "head_petshome", "head_petsshit","head_ground_line"],
                 head_type=["yolo", "yolo", "yolo", "yolo","line"],
                 type='EcoAi.MutitaskDataset'),
)
############################################ dataset end ###############################################
# endregion

# region 训练验证设置
####################################### training/val cfgs #############################################
train_cfg = dict(
    dynamic_intervals=[(490, 1)],
    max_epochs=max_epochs,
    type='EcoAi.MutitaskEpochBasedTrainLoop',
    val_interval=1)

optim_wrapper = dict(
    clip_grad=dict(max_norm=10.0),
    constructor='YOLOv5OptimizerConstructor',
    optimizer=dict(
        batch_size_per_gpu=80,
        lr=0.01,
        momentum=0.937,
        nesterov=True,
        type='SGD',
        weight_decay=0.0005),
    type='OptimWrapper')

val_cfg = dict(type='EcoAi.MutitaskValLoop')

head_ground_line_val_evaluator = dict(
    ann_file='/李燃青/4data/扫地机/ground_wire/wire/wire_v2/val.json',
    outfile_prefix=work_dir + '/result',
    type='EcoAi.PointMetric',
    corr_head="head_ground_line")
val_evaluator = dict(
    metrics=[head_ground_line_val_evaluator],
    mAP_name=["mAP"],
    ratio=[1.],
    type='EcoAi.evaluation.MutitaskMetric'
)
# endregion

# region 可视化和hook
head_ground_line_visualizer = dict(
    name='visualizer_ground_line',
    type='mmdet.PointCustomVisualizer',
    corr_head="head_ground_line",
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
visualizer = dict(
    visulizers=[head_ground_line_visualizer],
    name='visualizer',
    type='EcoAi.MutitaskCustomVisualizer',
    vis_backends=[dict(type='LocalVisBackend'),])

default_hooks = dict(
    checkpoint=dict(interval=10, max_keep_ckpts=2, type='CheckpointHook'),
    logger=dict(interval=10, type='LoggerHook'),
    param_scheduler=dict(
        lr_factor=0.01,
        max_epochs=max_epochs,
        scheduler_type='linear',
        type='YOLOv5ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    timer=dict(type='IterTimerHook'),
    visualization=dict(draw=True, interval=20, type='EcoAi.MutitaskDetVisualizationHook'))
test_dataloader = val_dataloader
test_evaluator = val_dataloader
test_pipeline = val_pipeline
custom_hooks = [
    dict(
        ema_type='ExpMomentumEMA',
        momentum=0.0001,
        priority=49,
        strict_load=False,
        type='EMAHook',
        update_buffers=True),
    dict(type='EcoAi.SaveBestHook'),
]
# endregion