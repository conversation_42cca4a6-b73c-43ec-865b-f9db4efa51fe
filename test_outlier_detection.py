#!/usr/bin/env python3
"""
测试隆起检测异常点方法
"""

import numpy as np
from scipy.signal import find_peaks

def detect_bulges_sliding_window(data, window_size=5, threshold_ratio=1.3):
    """
    滑动窗口局部阈值检测隆起部分
    
    参数:
        data (list): 距离数据
        window_size (int): 滑动窗口大小
        threshold_ratio (float): 阈值比例
    
    返回:
        bulges (list): 隆起点的索引列表
    """
    bulges = []
    for i in range(len(data)):
        left = max(0, i - window_size)
        right = min(len(data), i + window_size)
        window = data[left:right]
        local_mean = np.mean(window)
        if data[i] > local_mean * threshold_ratio:
            bulges.append(i)
    return bulges

def detect_bulges_peaks(data, prominence=10):
    """
    峰值检测隆起部分
    
    参数:
        data (list): 距离数据
        prominence (float): 峰值突出度
    
    返回:
        peaks (list): 峰值点的索引列表
    """
    peaks, _ = find_peaks(data, prominence=prominence)
    return peaks.tolist()

def find_outlier_indices(distance_list, window_size=10, threshold_ratio=1.3, prominence=10):
    """
    基于滑动窗口和峰值检测方法找出异常点的索引

    参数:
        distance_list (list of str or float): 原始距离序列
        window_size (int): 滑动窗口大小（默认 10）
        threshold_ratio (float): 阈值比例（默认 1.3）
        prominence (float): 峰值突出度（默认 10）

    返回:
        outlier_indices (list of int): 异常值的索引位置
    """
    data = [float(x) for x in distance_list]
    
    # 方法1：滑动窗口局部阈值检测
    bulges_window = detect_bulges_sliding_window(data, window_size, threshold_ratio)
    
    # 方法2：峰值检测
    bulges_peaks = detect_bulges_peaks(data, prominence)
    
    # 合并结果（取并集）
    all_bulges = list(set(bulges_window + bulges_peaks))
    all_bulges.sort()  # 按索引排序
    
    return all_bulges

def correct_outliers_by_neighbors(distance_list, outlier_indices, window=2):
    """
    用周围非异常值的平均值校正给定的异常点

    参数:
        distance_list (list of str or float): 原始距离序列
        outlier_indices (list of int): 异常值的索引位置
        window (int): 向前向后各取多少个非异常值（默认 2）

    返回:
        corrected (list of float): 校正后的距离序列
    """
    data = np.array([float(x) for x in distance_list])
    corrected = data.copy()

    # 全部异常点用于跳过异常邻居
    outlier_set = set(outlier_indices)

    for idx in outlier_indices:
        neighbors = []

        # 向前查找
        i = idx - 1
        while i >= 0 and len(neighbors) < window:
            if i not in outlier_set:
                neighbors.append(data[i])
            i -= 1

        # 向后查找
        i = idx + 1
        while i < len(data) and len(neighbors) < 2 * window:
            if i not in outlier_set:
                neighbors.append(data[i])
            i += 1

        # 平均值替代
        if neighbors:
            corrected[idx] = np.mean(neighbors)

    return corrected.tolist()

# 测试数据
test_data = ['43.8', '39.7', '46.8', '38.2', '49.1', '51.2', '37.6', '53.5', '36.8', '55.9', 
             '36.8', '56.8', '35.9', '57.9', '59.7', '35.9', '61.5', '36.8', '63.5', '36.5', 
             '63.5', '36.5', '63.5', '37.4', '65.3', '37.1', '65.3', '37.9', '67.1', '37.9', 
             '72.4', '67.1', '37.9', '72.4', '72.4', '67.1', '38.5', '72.4', '67.1', '38.5']

if __name__ == "__main__":
    print("测试隆起检测异常点方法")
    print("=" * 50)
    
    # 转换为浮点数
    distances = [float(x) for x in test_data]
    print(f"原始距离序列: {[f'{d:.1f}' for d in distances]}")
    
    # 检测异常点
    outlier_indices = find_outlier_indices(test_data, window_size=10, threshold_ratio=1.3, prominence=10)
    print(f"\n检测到的异常点索引: {outlier_indices}")
    print(f"异常点距离值: {[f'{distances[i]:.1f}' for i in outlier_indices]}")
    
    # 校正异常点
    corrected_distances = correct_outliers_by_neighbors(test_data, outlier_indices, window=2)
    print(f"\n校正后的距离序列: {[f'{d:.1f}' for d in corrected_distances]}")
    
    # 显示修正详情
    print(f"\n修正详情:")
    for i in outlier_indices:
        print(f"索引 {i}: {distances[i]:.1f}cm -> {corrected_distances[i]:.1f}cm")
