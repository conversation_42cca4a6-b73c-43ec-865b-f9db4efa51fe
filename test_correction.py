#!/usr/bin/env python3
import numpy as np
from typing import List

def test_correction_scenarios():
    """测试分层修正策略"""

    print("=== 测试分层修正策略 ===\n")

    # 场景1: 多个不同误差的异常串
    print("场景1: 多个不同误差的异常串")
    distances1 = [45.0, 46.0, 67.9, 72.4, 45.5, 61.5, 63.5, 46.5, 47.0]
    outliers1 = [2, 3, 5, 6]  # 两个异常串：[2,3]和[5,6]
    test_layered_scenario(distances1, outliers1, "两个不同误差的异常串")

    # 场景2: 三个异常串，误差递减
    print("\n场景2: 三个异常串，误差递减")
    distances2 = [75.0, 45.0, 65.0, 46.0, 55.0, 47.0, 45.5]
    outliers2 = [0, 2, 4]  # 三个单点异常：75.0(最大), 65.0(中等), 55.0(最小)
    test_layered_scenario(distances2, outliers2, "三个不同误差的单点异常")

    # 场景3: 复杂场景 - 您提供的实际数据
    print("\n场景3: 实际数据场景")
    distances3 = [43.8, 45.9, 50.3, 52.6, 59.7, 67.9, 55.3, 72.4, 59.7, 55.9, 55.9, 56.8, 55.9, 57.9, 59.7, 61.5, 61.5, 63.5, 65.3, 65.3, 63.5, 63.5, 61.5, 61.5, 59.7, 59.7, 53.5, 59.7, 52.4, 57.9, 53.5]
    outliers3 = [5, 7] + list(range(15, 24))  # 您指出的异常点
    test_layered_scenario(distances3, outliers3, "实际wire检测数据")

def test_layered_scenario(distances: List[float], outlier_indices: List[int], description: str):
    """测试分层修正场景"""
    print(f"  {description}")
    print(f"  原始距离: {[f'{d:.1f}' for d in distances]}")
    print(f"  异常索引: {outlier_indices}")

    # 模拟分层修正过程
    corrected = simulate_layered_correction(distances, outlier_indices)
    print(f"  修正后距离: {[f'{d:.1f}' for d in corrected]}")

    # 显示修正详情
    for i, (orig, corr) in enumerate(zip(distances, corrected)):
        if i in outlier_indices:
            change = corr - orig
            print(f"    索引{i}: {orig:.1f} -> {corr:.1f} (变化: {change:+.1f})")

def simulate_layered_correction(distances: List[float], outlier_indices: List[int]) -> List[float]:
    """模拟分层修正过程"""
    corrected = distances.copy()

    # 识别连续异常串
    segments = group_consecutive_indices(outlier_indices)

    # 按误差大小排序
    ranked_segments = rank_segments_by_error(segments, distances)
    print(f"    异常串按误差排序: {[(seg['segment'], f'{seg['error']:.1f}cm') for seg in ranked_segments]}")

    # 分层修正
    remaining_outliers = set(outlier_indices)

    for layer_idx, segment_info in enumerate(ranked_segments):
        start_idx, end_idx = segment_info['segment']
        error = segment_info['error']

        print(f"    第{layer_idx+1}层修正: 异常串 [{start_idx}:{end_idx}], 误差={error:.1f}cm")

        # 寻找参考点（排除剩余异常点）
        before_distances = []
        after_distances = []

        # 前面参考点
        for i in range(max(0, start_idx - 4), start_idx):
            if i not in remaining_outliers:
                before_distances.append(corrected[i])

        # 后面参考点
        for i in range(end_idx + 1, min(len(distances), end_idx + 5)):
            if i not in remaining_outliers:
                after_distances.append(corrected[i])

        # 选择修正策略
        strategy = select_layered_correction_strategy(before_distances, after_distances, corrected, remaining_outliers)
        ref_str = f"{strategy['reference']:.1f}" if strategy['reference'] is not None else "None"
        print(f"      策略: {strategy['method']}, 参考值: {ref_str}")

        # 应用修正
        if strategy['method'] == 'interpolation':
            before_ref = np.median(before_distances)
            after_ref = np.median(after_distances)
            segment_length = end_idx - start_idx + 1
            for i, idx in enumerate(range(start_idx, end_idx + 1)):
                ratio = i / (segment_length - 1) if segment_length > 1 else 0
                corrected[idx] = before_ref + (after_ref - before_ref) * ratio
        elif strategy['method'] in ['single_reference', 'global_baseline']:
            ref_value = strategy['reference']
            for idx in range(start_idx, end_idx + 1):
                corrected[idx] = ref_value

        # 从剩余异常点中移除已修正的点
        for idx in range(start_idx, end_idx + 1):
            remaining_outliers.discard(idx)

    return corrected

def rank_segments_by_error(segments: List[tuple], distances: List[float]) -> List[dict]:
    """按误差大小排序异常串"""
    segment_info = []
    global_baseline = np.median(distances)

    for start_idx, end_idx in segments:
        segment_distances = distances[start_idx:end_idx+1]
        segment_avg = np.mean(segment_distances)
        error = abs(segment_avg - global_baseline)

        segment_info.append({
            'segment': (start_idx, end_idx),
            'avg_distance': segment_avg,
            'error': error,
            'length': end_idx - start_idx + 1
        })

    # 按误差从大到小排序
    segment_info.sort(key=lambda x: x['error'], reverse=True)
    return segment_info

def select_layered_correction_strategy(before_distances: List[float], after_distances: List[float],
                                     corrected_distances: List[float], remaining_outliers: set) -> dict:
    """选择分层修正策略"""

    before_ref = np.median(before_distances) if len(before_distances) >= 2 else None
    after_ref = np.median(after_distances) if len(after_distances) >= 2 else None

    # 前后都有参考
    if before_ref is not None and after_ref is not None:
        ref_diff = abs(before_ref - after_ref)
        if ref_diff <= 3.0:
            return {
                'method': 'interpolation',
                'reference': (before_ref + after_ref) / 2
            }
        else:
            if len(before_distances) >= len(after_distances):
                return {
                    'method': 'single_reference',
                    'reference': before_ref
                }
            else:
                return {
                    'method': 'single_reference',
                    'reference': after_ref
                }

    # 只有一侧参考
    elif before_ref is not None:
        return {
            'method': 'single_reference',
            'reference': before_ref
        }
    elif after_ref is not None:
        return {
            'method': 'single_reference',
            'reference': after_ref
        }

    # 没有局部参考，使用全局基线
    else:
        valid_distances = [d for i, d in enumerate(corrected_distances) if i not in remaining_outliers]
        if valid_distances:
            return {
                'method': 'global_baseline',
                'reference': np.median(valid_distances)
            }
        else:
            return {
                'method': 'keep_original',
                'reference': None
            }

def test_single_scenario(distances: List[float], outlier_indices: List[int], description: str):
    """测试单个修正场景"""
    print(f"  {description}")
    print(f"  原始距离: {[f'{d:.1f}' for d in distances]}")
    print(f"  异常索引: {outlier_indices}")
    
    # 模拟修正过程
    corrected = simulate_correction(distances, outlier_indices)
    print(f"  修正后距离: {[f'{d:.1f}' for d in corrected]}")
    
    # 显示修正详情
    for i, (orig, corr) in enumerate(zip(distances, corrected)):
        if i in outlier_indices:
            change = corr - orig
            print(f"    索引{i}: {orig:.1f} -> {corr:.1f} (变化: {change:+.1f})")

def simulate_correction(distances: List[float], outlier_indices: List[int]) -> List[float]:
    """模拟修正过程"""
    corrected = distances.copy()
    
    # 识别连续异常段
    segments = group_consecutive_indices(outlier_indices)
    
    for start_idx, end_idx in segments:
        print(f"    修正异常段 [{start_idx}:{end_idx}]")
        
        # 寻找前后正常点
        before_distances = []
        after_distances = []
        
        # 前面正常点
        for i in range(max(0, start_idx - 4), start_idx):
            if i not in outlier_indices:
                before_distances.append(distances[i])
        
        # 后面正常点
        for i in range(end_idx + 1, min(len(distances), end_idx + 5)):
            if i not in outlier_indices:
                after_distances.append(distances[i])
        
        # 选择修正策略
        strategy = select_correction_strategy(before_distances, after_distances)
        ref_str = f"{strategy['reference']:.1f}" if strategy['reference'] is not None else "None"
        print(f"      策略: {strategy['method']}, 参考值: {ref_str}")
        
        # 应用修正
        if strategy['method'] == 'interpolation':
            before_ref = np.median(before_distances)
            after_ref = np.median(after_distances)
            segment_length = end_idx - start_idx + 1
            for i, idx in enumerate(range(start_idx, end_idx + 1)):
                ratio = i / (segment_length - 1) if segment_length > 1 else 0
                corrected[idx] = before_ref + (after_ref - before_ref) * ratio
        elif strategy['method'] == 'single_reference':
            ref_value = strategy['reference']
            for idx in range(start_idx, end_idx + 1):
                corrected[idx] = ref_value
        elif strategy['method'] == 'global_baseline':
            ref_value = strategy['reference']
            for idx in range(start_idx, end_idx + 1):
                corrected[idx] = ref_value
    
    return corrected

def group_consecutive_indices(indices: List[int]) -> List[tuple]:
    """将连续索引分组"""
    if not indices:
        return []
    
    segments = []
    start = indices[0]
    end = indices[0]
    
    for i in range(1, len(indices)):
        if indices[i] == end + 1:
            end = indices[i]
        else:
            segments.append((start, end))
            start = indices[i]
            end = indices[i]
    
    segments.append((start, end))
    return segments

def select_correction_strategy(before_distances: List[float], after_distances: List[float]) -> dict:
    """选择修正策略"""
    
    before_ref = np.median(before_distances) if len(before_distances) >= 2 else None
    after_ref = np.median(after_distances) if len(after_distances) >= 2 else None
    
    # 前后都有参考
    if before_ref is not None and after_ref is not None:
        ref_diff = abs(before_ref - after_ref)
        if ref_diff <= 3.0:
            return {
                'method': 'interpolation',
                'reference': (before_ref + after_ref) / 2
            }
        else:
            # 选择样本更多的参考
            if len(before_distances) >= len(after_distances):
                return {
                    'method': 'single_reference',
                    'reference': before_ref
                }
            else:
                return {
                    'method': 'single_reference',
                    'reference': after_ref
                }
    
    # 只有前面参考
    elif before_ref is not None:
        return {
            'method': 'single_reference',
            'reference': before_ref
        }
    
    # 只有后面参考
    elif after_ref is not None:
        return {
            'method': 'single_reference',
            'reference': after_ref
        }
    
    # 没有参考，使用全局基线
    else:
        # 这里简化处理，实际应该计算全局正常点的统计量
        return {
            'method': 'global_baseline',
            'reference': 45.0  # 假设的全局基线
        }

if __name__ == "__main__":
    test_correction_scenarios()
