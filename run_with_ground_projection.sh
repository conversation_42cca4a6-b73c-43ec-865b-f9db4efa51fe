#!/bin/bash

# Wire线地面投影距离修正功能使用示例

echo "=== Wire线地面投影距离修正功能 ==="
echo ""

# 检查必要文件是否存在
echo "检查必要文件..."
files=(
    "pixel_to_physical_py/config/calib_intrix_new.yaml"
    "pixel_to_physical_py/config/distance_table"
    "pixel_to_physical_py/config/mapx"
    "pixel_to_physical_py/config/mapy"
    "yolov8v6_only.onnx"
    "yolov8v13_only.onnx"
)

missing_files=()
for file in "${files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo "❌ 缺少以下必要文件:"
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    echo ""
    echo "请确保所有配置文件和模型文件都存在后再运行。"
    exit 1
fi

echo "✅ 所有必要文件检查通过"
echo ""

# 显示使用选项
echo "使用选项:"
echo "1. 运行原理演示（推荐先运行）"
echo "2. 运行功能测试"
echo "3. 启动相机检测（不使用地面投影）"
echo "4. 启动相机检测（使用地面投影，相机高度60cm）"
echo "5. 启动相机检测（使用地面投影，自定义相机高度）"
echo ""

read -p "请选择选项 (1-5): " choice

case $choice in
    1)
        echo "运行原理演示..."
        python demo_ground_projection_principle.py
        ;;
    2)
        echo "运行功能测试..."
        python test_ground_projection.py
        ;;
    3)
        echo "启动相机检测（原始模式）..."
        python object_detection4_by_camera.py --fps-display
        ;;
    4)
        echo "启动相机检测（地面投影模式，相机高度60cm）..."
        python object_detection4_by_camera.py --use-ground-projection --camera-height 60.0 --fps-display
        ;;
    5)
        read -p "请输入相机离地面高度（cm）: " height
        echo "启动相机检测（地面投影模式，相机高度${height}cm）..."
        python object_detection4_by_camera.py --use-ground-projection --camera-height $height --fps-display
        ;;
    *)
        echo "无效选择，退出。"
        exit 1
        ;;
esac

echo ""
echo "使用说明:"
echo "- 在地面投影模式下，检测点会用青色边框标识"
echo "- 控制台会显示投影前后的距离对比信息"
echo "- 按ESC键退出相机检测程序"
echo ""
echo "参数说明:"
echo "  --use-ground-projection  : 启用地面投影修正"
echo "  --camera-height <值>     : 设置相机离地面高度（cm）"
echo "  --fps-display           : 显示FPS信息"
