# 导出onnx文件的工具类
import argparse
import os
import sys
import warnings
from io import BytesIO
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parents[3]))
sys.path.append('/李燃青/1code/eco_code/ecoaitoolkits')
import onnx
import torch
from mmdet.apis import init_detector
from mmengine.config import ConfigDict
from mmengine.logging import print_log
from mmengine.utils.path import mkdir_or_exist
from mmengine.structures import InstanceData
from projects.easydeploy.nms.ort_nms import  onnx_nms

import cv2
# Add MMYOLO ROOT to sys.path

from projects.easydeploy.model import DeployModel, MMYOLOBackend  # noqa E402
import numpy as np
from mmengine.config import Config, ConfigDict
from mmengine.utils import ProgressBar, path
from typing import List, Tuple, Union
from torch.nn.modules.utils import _pair
device =  torch.device('cuda:0')
DeviceType = Union[str, torch.device]
from mmyolo.utils import register_all_modules
from mmyolo.utils.misc import get_file_list
from torch import Tensor
warnings.filterwarnings(action='ignore', category=torch.jit.TracerWarning)
warnings.filterwarnings(action='ignore', category=torch.jit.ScriptWarning)
warnings.filterwarnings(action='ignore', category=UserWarning)
warnings.filterwarnings(action='ignore', category=FutureWarning)
warnings.filterwarnings(action='ignore', category=ResourceWarning)
import matplotlib.pyplot as plt
from typing import Optional, Sequence, Union

def show_result_pyplot(img, result, score_thr=0.3):
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    plt.imshow(img)
    ax = plt.gca()
    for i, (bbox, score) in enumerate(zip(result.bboxes, result.scores)):
        if score < score_thr:
            continue
        ax.add_patch(plt.Rectangle(
            (bbox[0], bbox[1]), bbox[2]-bbox[0], bbox[3]-bbox[1],
            fill=False, color='red', linewidth=2))
        ax.text(bbox[0], bbox[1], f'{score:.2f}', fontsize=8)
    plt.show()
def distance2bbox(points, distance, max_shape=None):
    x1 = points[..., 0] - distance[..., 0]
    y1 = points[..., 1] - distance[..., 1]
    x2 = points[..., 0] + distance[..., 2]
    y2 = points[..., 1] + distance[..., 3]

    bboxes = torch.stack([x1, y1, x2, y2], -1)
    return bboxes
def decode(
    points: torch.Tensor,
    pred_bboxes: torch.Tensor,
    stride: torch.Tensor,
    max_shape: Optional[Union[Sequence[int], torch.Tensor,
                                Sequence[Sequence[int]]]] = None
) -> torch.Tensor:
    """Decode distance prediction to bounding box.

    Args:
        points (Tensor): Shape (B, N, 2) or (N, 2).
        pred_bboxes (Tensor): Distance from the given point to 4
            boundaries (left, top, right, bottom). Shape (B, N, 4)
            or (N, 4)
        stride (Tensor): Featmap stride.
        max_shape (Sequence[int] or torch.Tensor or Sequence[
            Sequence[int]],optional): Maximum bounds for boxes, specifies
            (H, W, C) or (H, W). If priors shape is (B, N, 4), then
            the max_shape should be a Sequence[Sequence[int]],
            and the length of max_shape should also be B.
            Default None.
    Returns:
        Tensor: Boxes with shape (N, 4) or (B, N, 4)
    """
    assert points.size(-2) == pred_bboxes.size(-2)
    assert points.size(-1) == 2
    assert pred_bboxes.size(-1) == 4
    # if clip_border is False:
    #     max_shape = None
    pred_bboxes = pred_bboxes * stride[None, :, None]

    return distance2bbox(points, pred_bboxes, max_shape)
def preprocess(config):
    data_preprocess = config.get('model', {}).get('data_preprocessor', {})
    mean = data_preprocess.get('mean', [0., 0., 0.])
    std = data_preprocess.get('std', [1., 1., 1.])

    mean = torch.tensor(mean, dtype=torch.float32).reshape(1, 3, 1, 1)
    std = torch.tensor(std, dtype=torch.float32).reshape(1, 3, 1, 1)

    class PreProcess(torch.nn.Module):

        def __init__(self):
            super().__init__()

        def forward(self, x):
            x = x[None].float()
            x -= mean.to(x.device)
            x /= std.to(x.device)
            return x

    return PreProcess().eval()
def _meshgrid(x: Tensor,
                y: Tensor,
                row_major: bool = True) -> Tuple[Tensor, Tensor]:
    yy, xx = torch.meshgrid(y, x)
    if row_major:
        # warning .flatten() would cause error in ONNX exporting
        # have to use reshape here
        return xx.reshape(-1), yy.reshape(-1)

    else:
        return yy.reshape(-1), xx.reshape(-1)
def single_level_grid_priors(featmap_size: Tuple[int],
                                level_idx: int,
                                strides,
                                offset=0.5,
                                dtype: torch.dtype = torch.float32,
                                device: DeviceType = 'cuda',
                                with_stride: bool = False) -> Tensor:

    feat_h, feat_w = featmap_size
    strides = [_pair(stride) for stride in strides]
    stride_w, stride_h = strides[level_idx]
    shift_x = (torch.arange(0, feat_w, device=device) +
                offset) * stride_w
    # keep featmap_size as Tensor instead of int, so that we
    # can convert to ONNX correctly
    # print("dtype",dtype)
    shift_x = shift_x.to(dtype)
    shift_y = (torch.arange(0, feat_h, device=device) +
                offset) * stride_h
    # keep featmap_size as Tensor instead of int, so that we
    # can convert to ONNX correctly
    shift_y = shift_y.to(dtype)
    shift_xx, shift_yy = _meshgrid(shift_x, shift_y)
    if not with_stride:
        shifts = torch.stack([shift_xx, shift_yy], dim=-1)
    else:
        # use `shape[0]` instead of `len(shift_xx)` for ONNX export
        stride_w = shift_xx.new_full((shift_xx.shape[0], ),
                                        stride_w).to(dtype)
        stride_h = shift_xx.new_full((shift_yy.shape[0], ),
                                        stride_h).to(dtype)
        shifts = torch.stack([shift_xx, shift_yy, stride_w, stride_h],
                                dim=-1)
    all_points = shifts.to(device)
    return all_points

def grid_priors(featmap_sizes: List[Tuple],
                    dtype: torch.dtype = torch.float32,
                    device: DeviceType = 'cuda',
                    with_stride: bool = False,strides=[
                8,
                16,
                32,
            ]) -> List[Tensor]:
        num_levels = len(strides)
        # print(featmap_sizes)
        assert num_levels == len(featmap_sizes)
        multi_level_priors = []
        for i in range(num_levels):
            priors = single_level_grid_priors(
                featmap_sizes[i],
                level_idx=i,
                dtype=dtype,
                device=device,
                with_stride=with_stride,strides=strides)
            multi_level_priors.append(priors)
        return multi_level_priors


def bbox_postprocess(outputs,num_classes,featmap_strides):
    cls_scores, bbox_preds = outputs[0], outputs[1]
    # bbox_preds = [single_feature(bbox_pred) for bbox_pred in bbox_preds]
    dtype = cls_scores[0].dtype
    featmap_strides = [8, 16, 32]
    reg_max = 16
    num_classes = 7
    num_base_priors = 1

    num_imgs = cls_scores[0].shape[0]
    featmap_sizes = [cls_score.shape[2:] for cls_score in cls_scores]
    mlvl_priors = grid_priors(featmap_sizes, dtype=dtype, device=device)
    flatten_priors = torch.cat(mlvl_priors)
    mlvl_strides = [
        flatten_priors.new_full(
            (featmap_size[0] * featmap_size[1] * num_base_priors,),
            stride
        ) for featmap_size, stride in zip(featmap_sizes, featmap_strides)
    ]
    flatten_stride = torch.cat(mlvl_strides)
    flatten_cls_scores = [
        cls_score.permute(0, 2, 3, 1).reshape(num_imgs, -1, num_classes)
        for cls_score in cls_scores
    ]
    cls_scores = torch.cat(flatten_cls_scores, dim=1).sigmoid()
    flatten_bbox_preds = [
        bbox_pred.permute(0, 2, 3, 1).reshape(num_imgs, -1, 4)
        for bbox_pred in bbox_preds
    ]
    flatten_bbox_preds = torch.cat(flatten_bbox_preds, dim=1).to(device=device)

    scores = cls_scores.to(device=device)
    bboxes = decode(flatten_priors[None], flatten_bbox_preds,None).to(device=device)

    # Perform NMS
    num_dets, batched_dets, batched_scores, batched_labels = onnx_nms(bboxes, scores)
    return num_dets, batched_dets, batched_scores, batched_labels


def postprocess(outputs,num_classes,featmap_strides):
    cls_scores, bbox_preds = outputs[0], outputs[1]
    # bbox_preds = [single_feature(bbox_pred) for bbox_pred in bbox_preds]
    dtype = cls_scores[0].dtype
    featmap_strides = [8, 16, 32]
    reg_max = 16
    num_classes = 7
    num_base_priors = 1

    num_imgs = cls_scores[0].shape[0]
    featmap_sizes = [cls_score.shape[2:] for cls_score in cls_scores]
    mlvl_priors = grid_priors(featmap_sizes, dtype=dtype, device=device)
    flatten_priors = torch.cat(mlvl_priors)
    mlvl_strides = [
        flatten_priors.new_full(
            (featmap_size[0] * featmap_size[1] * num_base_priors,),
            stride
        ) for featmap_size, stride in zip(featmap_sizes, featmap_strides)
    ]
    flatten_stride = torch.cat(mlvl_strides)
    flatten_cls_scores = [
        cls_score.permute(0, 2, 3, 1).reshape(num_imgs, -1, num_classes)
        for cls_score in cls_scores
    ]
    cls_scores = torch.cat(flatten_cls_scores, dim=1).sigmoid()
    flatten_bbox_preds = [
        bbox_pred.permute(0, 2, 3, 1).reshape(num_imgs, -1, 4)
        for bbox_pred in bbox_preds
    ]
    flatten_bbox_preds = torch.cat(flatten_bbox_preds, dim=1).to(device=device)

    scores = cls_scores.to(device=device)
    bboxes = decode(flatten_priors[None], flatten_bbox_preds,None).to(device=device)

    # Perform NMS
    num_dets, batched_dets, batched_scores, batched_labels = onnx_nms(bboxes, scores)
    return num_dets, batched_dets, batched_scores, batched_labels

def visualize_detections(image, 
                        result: InstanceData, 
                        class_names: list, 
                        score_thr: float = 0.3,
                        thickness: int = 2,
                        font_scale: float = 0.5):
    """
    可视化检测结果   
    Args:
        image (np.ndarray): 原始图像 (H, W, 3) BGR格式
        result (InstanceData): 包含检测结果的实例数据
        class_names (list): 类别名称列表
        score_thr (float): 分数阈值，过滤低置信度检测框
        thickness (int): 框线粗细
        font_scale (float): 字体大小
    Returns:
        np.ndarray: 绘制了检测框的图像
    """
    # 转换为 CPU numpy 数组
    bboxes = result.bboxes.cpu().detach().numpy()  # (N, 4) 格式应为 xyxy
    scores = result.scores.cpu().detach().numpy()  # (N,)
    labels = result.labels.cpu().detach().numpy()  # (N,)

    # 过滤低分数检测框
    keep = scores >= score_thr
    bboxes = bboxes[keep]
    scores = scores[keep]
    labels = labels[keep]

    # 遍历每个检测框
    for bbox, score, label in zip(bboxes, scores, labels):
        x1, y1, x2, y2 = bbox.astype(int)

        # 绘制边界框
        color = (0, 255, 0)  # BGR 格式
        cv2.rectangle(image, (x1, y1), (x2, y2), color, thickness)
        # print(label)
        # 构造标签文本
        label_text = f"{class_names[label]}: {score:.2f}"

        # 计算文本位置
        (text_width, text_height), _ = cv2.getTextSize(
            label_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)
        text_y = y1 - 10 if y1 - 10 > 10 else y1 + 10

        # 绘制文本背景
        cv2.rectangle(image,
                     (x1, text_y - text_height),
                     (x1 + text_width, text_y),
                     color, -1)  # -1 表示填充

        # 绘制文本
        cv2.putText(image, label_text,
                   (x1, text_y),
                   cv2.FONT_HERSHEY_SIMPLEX,
                   font_scale,
                   (0, 0, 0),  # 黑色文字
                   1,
                   cv2.LINE_AA)

    return image
def build_model_from_cfg(config_path, checkpoint_path, device):
    model = init_detector(config_path, checkpoint_path, device=device)
    model.eval()
    return model
def export_onnx(config,checkpoint,device,model_only,backend="onnxruntime",pre_topk=1000,keep_topk=100,iou_threshold=0.65,score_threshold=0.25):
    backend = MMYOLOBackend(backend.lower())
    if backend in (MMYOLOBackend.ONNXRUNTIME, MMYOLOBackend.OPENVINO,
                MMYOLOBackend.TENSORRT8, MMYOLOBackend.TENSORRT7):
        if not model_only:
            print_log('Export ONNX with bbox decoder and NMS ...')
        else:
            model_only = True
            print_log(f'Can not export postprocess for {backend.lower()}.\n'
                        f'Set "args.model_only=True" default.')
        if model_only:
            postprocess_cfg = None
            output_names = None
        else:
            postprocess_cfg = ConfigDict(
                pre_top_k=pre_topk,
                keep_top_k=keep_topk,
                iou_threshold=iou_threshold,
                score_threshold=score_threshold)
            output_names = ['num_dets', 'boxes', 'scores', 'labels']

    baseModel = build_model_from_cfg(config, checkpoint, device)
    baseModel2 = build_model_from_cfg(config, checkpoint, device)
    fake_input = torch.randn(1, 3,640,640).to(device)
    # dry run
    x = baseModel(fake_input)
    # for y in x:
        # print(y.shape)
    deploy_model = DeployModel(
        baseModel=baseModel, backend=backend, postprocess_cfg=postprocess_cfg)
    deploy_model.eval()
    # deploy_model = baseModel

    return deploy_model,baseModel2
