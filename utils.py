# 导出onnx文件的工具类
import argparse
import os
import sys
import warnings
from io import BytesIO
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parents[3]))
sys.path.append('/李燃青/1code/eco_code/ecoaitoolkits')
import onnx
import torch
# from mmdet.apis import init_detector
from mmengine.config import ConfigDict
from mmengine.logging import print_log
from mmengine.utils.path import mkdir_or_exist
from mmengine.structures import InstanceData
from projects.easydeploy.nms.ort_nms import  onnx_nms

import cv2
# Add MMYOLO ROOT to sys.path

from projects.easydeploy.model import DeployModel, MMYOLOBackend  # noqa E402
import numpy as np
from mmengine.config import Config, ConfigDict
from mmengine.utils import ProgressBar, path
from typing import List, Tuple, Union
from torch.nn.modules.utils import _pair
device =  torch.device('cuda:0')
DeviceType = Union[str, torch.device]
from mmyolo.utils import register_all_modules
from mmyolo.utils.misc import get_file_list
from torch import Tensor
warnings.filterwarnings(action='ignore', category=torch.jit.TracerWarning)
warnings.filterwarnings(action='ignore', category=torch.jit.ScriptWarning)
warnings.filterwarnings(action='ignore', category=UserWarning)
warnings.filterwarnings(action='ignore', category=FutureWarning)
warnings.filterwarnings(action='ignore', category=ResourceWarning)
import matplotlib.pyplot as plt
from typing import Optional, Sequence, Union
import cv2
import numpy as np
import torch

def resize_and_pad(img, target_size=640, fill_value=114):
    """
    等比例缩放图像并在空白区域填充指定值
    
    参数:
        img: 输入图像 (OpenCV格式, HxWxC)
        target_size: 目标正方形尺寸 (默认640)
        fill_value: 填充像素值 (默认114)
    
    返回:
        处理后的图像 (target_size x target_size)
    """
    # 获取原始尺寸
    h, w = img.shape[:2]
    # print(h,w)
    # 1. 计算缩放比例和新尺寸
    scale = max(h, w) / float(target_size)
    new_h, new_w = int(round(h / scale)), int(round(w / scale))
    
    # 2. 等比例缩放
    resized_img = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
    
    # 3. 计算填充量（自动处理奇偶问题）
    pad_h = target_size - new_h
    pad_w = target_size - new_w
    pad_top = pad_h // 2
    pad_bottom = pad_h - pad_top
    pad_left = pad_w // 2
    pad_right = pad_w - pad_left
    
    # 4. 填充图像
    padded_img = cv2.copyMakeBorder(
        resized_img, 
        pad_top, pad_bottom, 
        pad_left, pad_right, 
        cv2.BORDER_CONSTANT, 
        value=(fill_value, fill_value, fill_value)
    )
    # 转化为Tensor
    tensor_img = torch.from_numpy(padded_img).permute(2, 0, 1)
    return tensor_img,scale,(pad_top, pad_bottom, pad_left, pad_right)

def transform_point_back(x_dst, y_dst, scale, padding):
    """
    将检测点从640x640图像坐标映射回原始图像坐标
    
    参数:
        x_dst, y_dst: 640x640图像上的坐标
        scale: 缩放比例 (max(h,w)/640)
        padding: (pad_top, pad_bottom, pad_left, pad_right)
    
    返回:
        (x_orig, y_orig): 原始图像坐标
    """
    pad_top, pad_bottom, pad_left, pad_right = padding
    
    # 1. 去除填充偏移
    x_scaled = x_dst - pad_left
    y_scaled = y_dst - pad_top
    
    # 2. 反缩放
    x_orig = x_scaled * scale
    y_orig = y_scaled * scale
    
    return int(x_orig), int(y_orig)

def show_result_pyplot(img, result, score_thr=0.3):
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    plt.imshow(img)
    ax = plt.gca()
    for i, (bbox, score) in enumerate(zip(result.bboxes, result.scores)):
        if score < score_thr:
            continue
        ax.add_patch(plt.Rectangle(
            (bbox[0], bbox[1]), bbox[2]-bbox[0], bbox[3]-bbox[1],
            fill=False, color='red', linewidth=2))
        ax.text(bbox[0], bbox[1], f'{score:.2f}', fontsize=8)
    plt.show()
def distance2bbox(points, distance, max_shape=None):
    x1 = points[..., 0] - distance[..., 0]
    y1 = points[..., 1] - distance[..., 1]
    x2 = points[..., 0] + distance[..., 2]
    y2 = points[..., 1] + distance[..., 3]

    bboxes = torch.stack([x1, y1, x2, y2], -1)
    return bboxes
def decode(
    points: torch.Tensor,
    pred_bboxes: torch.Tensor,
    stride: torch.Tensor,
    max_shape: Optional[Union[Sequence[int], torch.Tensor,
                                Sequence[Sequence[int]]]] = None
) -> torch.Tensor:
    """Decode distance prediction to bounding box.

    Args:
        points (Tensor): Shape (B, N, 2) or (N, 2).
        pred_bboxes (Tensor): Distance from the given point to 4
            boundaries (left, top, right, bottom). Shape (B, N, 4)
            or (N, 4)
        stride (Tensor): Featmap stride.
        max_shape (Sequence[int] or torch.Tensor or Sequence[
            Sequence[int]],optional): Maximum bounds for boxes, specifies
            (H, W, C) or (H, W). If priors shape is (B, N, 4), then
            the max_shape should be a Sequence[Sequence[int]],
            and the length of max_shape should also be B.
            Default None.
    Returns:
        Tensor: Boxes with shape (N, 4) or (B, N, 4)
    """
    assert points.size(-2) == pred_bboxes.size(-2)
    assert points.size(-1) == 2
    assert pred_bboxes.size(-1) == 4
    # if clip_border is False:
    #     max_shape = None
    pred_bboxes = pred_bboxes * stride[None, :, None]

    return distance2bbox(points, pred_bboxes, max_shape)
def preprocess():
    # data_preprocess = config.get('model', {}).get('data_preprocessor', {})
    # mean = data_preprocess.get('mean', [0., 0., 0.])
    # std = data_preprocess.get('std', [1., 1., 1.])
    mean = [0,0,0]
    std = [255,255,255]
    mean = torch.tensor(mean, dtype=torch.float32).reshape(1, 3, 1, 1)
    std = torch.tensor(std, dtype=torch.float32).reshape(1, 3, 1, 1)
    class PreProcess(torch.nn.Module):

        def __init__(self):
            super().__init__()

        def forward(self, x):
            x = x[None].float()
            x -= mean.to(x.device)
            x /= std.to(x.device)
            return x

    return PreProcess().eval()
def _meshgrid(x: Tensor,
                y: Tensor,
                row_major: bool = True) -> Tuple[Tensor, Tensor]:
    yy, xx = torch.meshgrid(y, x)
    if row_major:
        # warning .flatten() would cause error in ONNX exporting
        # have to use reshape here
        return xx.reshape(-1), yy.reshape(-1)

    else:
        return yy.reshape(-1), xx.reshape(-1)
def single_level_grid_priors(featmap_size: Tuple[int],
                                level_idx: int,
                                strides,
                                offset=0.5,
                                dtype: torch.dtype = torch.float32,
                                device: DeviceType = 'cuda',
                                with_stride: bool = False) -> Tensor:

    feat_h, feat_w = featmap_size
    strides = [_pair(stride) for stride in strides]
    stride_w, stride_h = strides[level_idx]
    shift_x = (torch.arange(0, feat_w, device=device) +
                offset) * stride_w
    # keep featmap_size as Tensor instead of int, so that we
    # can convert to ONNX correctly
    # print("dtype",dtype)
    shift_x = shift_x.to(dtype)
    shift_y = (torch.arange(0, feat_h, device=device) +
                offset) * stride_h
    # keep featmap_size as Tensor instead of int, so that we
    # can convert to ONNX correctly
    shift_y = shift_y.to(dtype)
    shift_xx, shift_yy = _meshgrid(shift_x, shift_y)
    if not with_stride:
        shifts = torch.stack([shift_xx, shift_yy], dim=-1)
    else:
        # use `shape[0]` instead of `len(shift_xx)` for ONNX export
        stride_w = shift_xx.new_full((shift_xx.shape[0], ),
                                        stride_w).to(dtype)
        stride_h = shift_xx.new_full((shift_yy.shape[0], ),
                                        stride_h).to(dtype)
        shifts = torch.stack([shift_xx, shift_yy, stride_w, stride_h],
                                dim=-1)
    all_points = shifts.to(device)
    return all_points
def reg_max2bbox(bbox_dist_preds,proj):
    b,c,h,w = bbox_dist_preds.shape
    bbox_dist_preds = bbox_dist_preds.reshape(
    [-1, 4, 16, h * w]).permute(0, 3, 1, 2)
    # TODO: The get_flops script cannot handle the situation of
    #  matmul, and needs to be fixed later
    # bbox_preds = bbox_dist_preds.softmax(3).matmul(self.proj)
    bbox_preds = bbox_dist_preds.softmax(3).matmul(
        proj.view([-1, 1])).squeeze(-1)
    bbox_preds = bbox_preds.transpose(1, 2).reshape(b, -1, h, w)
    return bbox_preds
def grid_priors(featmap_sizes: List[Tuple],
                    dtype: torch.dtype = torch.float32,
                    device: DeviceType = 'cuda',
                    with_stride: bool = False,strides=[
                8,
                16,
                32,
            ]) -> List[Tensor]:
        num_levels = len(strides)
        # print(featmap_sizes)
        assert num_levels == len(featmap_sizes)
        multi_level_priors = []
        for i in range(num_levels):
            priors = single_level_grid_priors(
                featmap_sizes[i],
                level_idx=i,
                dtype=dtype,
                device=device,
                with_stride=with_stride,strides=strides)
            multi_level_priors.append(priors)
        return multi_level_priors


def bbox_postprocess(outputs,num_classes,featmap_strides):
    reg_max = 16
    feat_channel = reg_max+num_classes
    num_base_priors = 1
    featmap_strides = [8, 16, 32]
    num_imgs = outputs[0].shape[0]
    dtype = outputs[0].dtype
    bbox_preds = []
    cls_scores = []
    for feat_now in outputs:
        bbox_pred , cls_score = torch.split(feat_now, [64, num_classes], dim=1)
        bbox_preds.append(bbox_pred)
        cls_scores.append(cls_score)
    if bbox_preds[0].shape[1] ==64:
        proj = torch.arange(16, dtype=torch.float).to(device)
        bbox_preds = [reg_max2bbox(bbox,proj)  for bbox in bbox_preds]    
    featmap_sizes = [cls_score.shape[2:] for cls_score in cls_scores]
    mlvl_priors = grid_priors(featmap_sizes, dtype=dtype, device=device)
    flatten_priors = torch.cat(mlvl_priors)

    mlvl_strides = [
        flatten_priors.new_full(
            (featmap_size[0] * featmap_size[1] * num_base_priors,),
            stride
        ) for featmap_size, stride in zip(featmap_sizes, featmap_strides)
    ]
    flatten_stride = torch.cat(mlvl_strides)
    flatten_cls_scores = [
        cls_score.permute(0, 2, 3, 1).reshape(num_imgs, -1, num_classes)
        for cls_score in cls_scores
    ]
    flatten_cls_scores = torch.cat(flatten_cls_scores, dim=1).sigmoid()
    flatten_bbox_preds = [
        bbox_pred.permute(0, 2, 3, 1).reshape(num_imgs, -1, 4)
        for bbox_pred in bbox_preds
    ]
    flatten_bbox_preds = torch.cat(flatten_bbox_preds, dim=1).to(device=device)
    scores = flatten_cls_scores.to(device=device)
    bboxes = decode(flatten_priors[None], flatten_bbox_preds,flatten_stride).to(device=device)

    num_dets, batched_dets, batched_scores, batched_labels = onnx_nms(bboxes, scores)
    return num_dets, batched_dets, batched_scores, batched_labels



def visualize_detections(image, 
                        result: InstanceData, 
                        class_names: list, 
                        score_thr: float = 0.3,
                        thickness: int = 2,
                        font_scale: float = 0.5):

    # 转换为 CPU numpy 数组
    bboxes = result.bboxes.cpu().detach().numpy()  # (N, 4) 格式应为 xyxy
    scores = result.scores.cpu().detach().numpy()  # (N,)
    labels = result.labels.cpu().detach().numpy()  # (N,)

    # 过滤低分数检测框
    keep = scores >= score_thr
    bboxes = bboxes[keep]
    scores = scores[keep]
    labels = labels[keep]

    # 遍历每个检测框
    for bbox, score, label in zip(bboxes, scores, labels):
        x1, y1, x2, y2 = bbox.astype(int)

        # 绘制边界框
        color = (0, 255, 0)  # BGR 格式
        cv2.rectangle(image, (x1, y1), (x2, y2), color, thickness)
        # print(label)
        # 构造标签文本
        label_text = f"{class_names[label]}: {score:.2f}"

        # 计算文本位置
        (text_width, text_height), _ = cv2.getTextSize(
            label_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)
        text_y = y1 - 10 if y1 - 10 > 10 else y1 + 10

        # 绘制文本背景
        cv2.rectangle(image,
                     (x1, text_y - text_height),
                     (x1 + text_width, text_y),
                     color, -1)  # -1 表示填充

        # 绘制文本
        cv2.putText(image, label_text,
                   (x1, text_y),
                   cv2.FONT_HERSHEY_SIMPLEX,
                   font_scale,
                   (0, 0, 0),  # 黑色文字
                   1,
                   cv2.LINE_AA)

    return image
# def build_model_from_cfg(config_path, checkpoint_path, device):
#     model = init_detector(config_path, checkpoint_path, device=device)
#     model.eval()
#     return model
