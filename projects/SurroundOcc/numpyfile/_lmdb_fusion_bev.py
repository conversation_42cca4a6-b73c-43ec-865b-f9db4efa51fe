auto_scale_lr = dict(base_batch_size=4, enable=False)
bn_kwargs = dict(eps=2e-05, momentum=0.1)
class_names = [
    'drivable_area',
    'stop_line',
    'unknow',
]
custom_imports = dict(
    allow_failed_imports=False,
    imports=[
        'projects.SurroundOcc.surroundocc.models',
        'projects.SurroundOcc.surroundocc.datasets',
    ])
data_root = '/yangping/Dataset/KuJiaLe'
data_shape = (
    3,
    384,
    512,
)
dataset_type = 'EcoAi.KujialeFusionDataset_LMDB'
default_hooks = dict(
    checkpoint=dict(interval=1, type='CheckpointHook'),
    logger=dict(interval=500, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    timer=dict(type='IterTimerHook'),
    visualization=dict(type='mmdet3d.Det3DVisualizationHook'))
default_scope = 'mmdet'
depoly_model = dict(
    bev_decoder=dict(
        Dz=1, in_dim=256, num_classes=3, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=128, num_channels=[
                256,
                512,
                1024,
            ]),
        fpn_lss_params=dict(in_channels=1280, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(
        activation='relu',
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        include_top=False,
        model_type='b0',
        num_classes=1000,
        type='efficientnet',
        use_se_block=False),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    lidar_config=dict(
        max_num_points=10,
        max_voxels=[
            90000,
            120000,
        ],
        point_cloud_range=[
            -2.0,
            0.0,
            -0.02,
            2.0,
            4.0,
            0.055,
        ],
        voxel_size=[
            0.05,
            0.05,
            0.075,
        ]),
    reader=dict(
        bn_kwargs=None,
        hw_reverse=True,
        num_filters=(64, ),
        num_input_features=3,
        pc_range=[
            -2.0,
            0.0,
            -0.02,
            2.0,
            4.0,
            0.055,
        ],
        pool_size=(
            10,
            1,
        ),
        quantize=False,
        type='PillarFeatureNet_ld',
        use_4dim=True,
        use_conv=True,
        voxel_size=[
            0.05,
            0.05,
            0.075,
        ],
        with_distance=False),
    scatter=dict(
        num_input_features=64,
        quantize=True,
        type='PointPillarScatter_dpx',
        use_horizon_pillar_scatter=True),
    type='EcoBEVFusionDepoly',
    use_grid_mask=False,
    view_transformer=dict(
        bev_size=[
            -2.0,
            2.0,
            0.0,
            4.0,
            0.05,
        ],
        depoly=True,
        depth=120,
        depth_grid_quant_scale=0.03125,
        feat_channels=64,
        grid_quant_scale=0.0078125,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=2,
        num_views=1,
        type='LSSTransformer_Fusion',
        z_range=(
            -0.02,
            0.055,
        )))
depth_quant_scale = 0.03125
depthview_shape = [
    720,
    768.0,
]
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=1))
featview_shape = [
    144.0,
    32.0,
]
find_unused_parameters = True
grid_quant_scale = 0.0078125
img_norm_cfg = dict(
    mean=[
        103.53,
        116.28,
        123.675,
    ], std=[
        1.0,
        1.0,
        1.0,
    ], to_rgb=False)
keys = (
    'img',
    'points',
)
launcher = 'pytorch'
load_from = '/yangping/ecoaitoolkit/work_dirs/fusion_bev/0514/__epoch_24.pth'
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=50)
lss_bev_size = [
    -2.0,
    2.0,
    0.0,
    4.0,
    0.05,
]
lss_depth = 120
lss_grid_size = (
    80,
    80,
)
lss_num_points = 2
lss_z_range = (
    -0.02,
    0.055,
)
meta_keys = (
    'lidar2img',
    'img_shape',
    'pc_range',
    'occ_size',
    'gt_occ',
    'filename',
)
model = dict(
    bev_decoder=dict(
        Dz=1, in_dim=256, num_classes=3, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=128, num_channels=[
                256,
                512,
                1024,
            ]),
        fpn_lss_params=dict(in_channels=1280, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(
        activation='relu',
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        include_top=False,
        model_type='b0',
        num_classes=1000,
        type='efficientnet',
        use_se_block=False),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    is_vis=True,
    lambda_dice=1,
    lidar_config=dict(
        max_num_points=10,
        max_voxels=[
            90000,
            120000,
        ],
        point_cloud_range=[
            -2.0,
            0.0,
            -0.02,
            2.0,
            4.0,
            0.055,
        ],
        voxel_size=[
            0.05,
            0.05,
            0.075,
        ]),
    norm_range=[
        -2.0,
        0.0,
        -0.02,
        2.0,
        4.0,
        0.055,
    ],
    reader=dict(
        bn_kwargs=None,
        hw_reverse=True,
        num_filters=(64, ),
        num_input_features=3,
        pc_range=[
            -2.0,
            0.0,
            -0.02,
            2.0,
            4.0,
            0.055,
        ],
        pool_size=(
            10,
            1,
        ),
        quantize=False,
        type='PillarFeatureNet_ld',
        use_4dim=True,
        use_conv=True,
        voxel_size=[
            0.05,
            0.05,
            0.075,
        ],
        with_distance=False),
    scatter=dict(
        num_input_features=64,
        quantize=True,
        type='PointPillarScatter_dpx',
        use_horizon_pillar_scatter=True),
    type='EcoBEVFusion',
    use_grid_mask=True,
    view_transformer=dict(
        bev_size=[
            -2.0,
            2.0,
            0.0,
            4.0,
            0.05,
        ],
        depth=120,
        depth_grid_quant_scale=0.03125,
        feat_channels=64,
        grid_quant_scale=0.0078125,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=2,
        num_views=1,
        type='LSSTransformer_Fusion',
        z_range=(
            -0.02,
            0.055,
        )))
numC_Trans = 128
occ_size = [
    80,
    80,
    1,
]
optim_wrapper = dict(
    clip_grad=dict(max_norm=5, norm_type=2),
    optimizer=dict(lr=0.0001, type='AdamW', weight_decay=0.01),
    type='AmpOptimWrapper')
param_scheduler = [
    dict(
        begin=0,
        by_epoch=False,
        end=500,
        start_factor=0.3333333333333333,
        type='LinearLR'),
    dict(by_epoch=True, type='CosineAnnealingLR'),
]
point_cloud_range = [
    -2.0,
    0.0,
    -0.02,
    2.0,
    4.0,
    0.055,
]
resume = False
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=1,
    dataset=dict(
        ann_file='lmdb/kujiale_fusion_0509_val',
        classes=[
            'drivable_area',
            'stop_line',
            'unknow',
        ],
        data_root='/yangping/Dataset/KuJiaLe',
        occ_size=[
            80,
            80,
            1,
        ],
        pc_range=[
            -2.0,
            0.0,
            -0.02,
            2.0,
            4.0,
            0.055,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_LMDB'),
            dict(type='LoadKJLBev_LMDB'),
            dict(
                coord_type='LIDAR',
                load_dim=3,
                reduce_beams=32,
                type='LoadPointsFromFileSingle_LMDB',
                use_dim=3),
            dict(
                point_cloud_range=[
                    -2.0,
                    0.0,
                    -0.02,
                    2.0,
                    4.0,
                    0.055,
                ],
                type='PointsRangeFilter'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=(
                    'img',
                    'points',
                ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeFusionDataset_LMDB',
        use_semantic=True),
    drop_last=False,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    classes=[
        'drivable_area',
        'stop_line',
        'unknow',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
test_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_LMDB'),
    dict(type='LoadKJLBev_LMDB'),
    dict(
        coord_type='LIDAR',
        load_dim=3,
        reduce_beams=32,
        type='LoadPointsFromFileSingle_LMDB',
        use_dim=3),
    dict(
        point_cloud_range=[
            -2.0,
            0.0,
            -0.02,
            2.0,
            4.0,
            0.055,
        ],
        type='PointsRangeFilter'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=(
            'img',
            'points',
        ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
train_cfg = dict(max_epochs=24, type='EpochBasedTrainLoop', val_interval=1)
train_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='lmdb/kujiale_fusion_0509_train',
        classes=[
            'drivable_area',
            'stop_line',
            'unknow',
        ],
        data_root='/yangping/Dataset/KuJiaLe',
        occ_size=[
            80,
            80,
            1,
        ],
        pc_range=[
            -2.0,
            0.0,
            -0.02,
            2.0,
            4.0,
            0.055,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_LMDB'),
            dict(type='LoadKJLBev_LMDB'),
            dict(
                coord_type='LIDAR',
                load_dim=3,
                reduce_beams=32,
                type='LoadPointsFromFileSingle_LMDB',
                use_dim=3),
            dict(
                point_cloud_range=[
                    -2.0,
                    0.0,
                    -0.02,
                    2.0,
                    4.0,
                    0.055,
                ],
                type='PointsRangeFilter'),
            dict(type='PhotoMetricDistortionMultiViewImage'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=(
                    'img',
                    'points',
                ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        test_mode=False,
        type='EcoAi.KujialeFusionDataset_LMDB',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    prefetch_factor=8,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_LMDB'),
    dict(type='LoadKJLBev_LMDB'),
    dict(
        coord_type='LIDAR',
        load_dim=3,
        reduce_beams=32,
        type='LoadPointsFromFileSingle_LMDB',
        use_dim=3),
    dict(
        point_cloud_range=[
            -2.0,
            0.0,
            -0.02,
            2.0,
            4.0,
            0.055,
        ],
        type='PointsRangeFilter'),
    dict(type='PhotoMetricDistortionMultiViewImage'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=(
            'img',
            'points',
        ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
use_semantic = True
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='lmdb/kujiale_fusion_0509_val',
        classes=[
            'drivable_area',
            'stop_line',
            'unknow',
        ],
        data_root='/yangping/Dataset/KuJiaLe',
        occ_size=[
            80,
            80,
            1,
        ],
        pc_range=[
            -2.0,
            0.0,
            -0.02,
            2.0,
            4.0,
            0.055,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_LMDB'),
            dict(type='LoadKJLBev_LMDB'),
            dict(
                coord_type='LIDAR',
                load_dim=3,
                reduce_beams=32,
                type='LoadPointsFromFileSingle_LMDB',
                use_dim=3),
            dict(
                point_cloud_range=[
                    -2.0,
                    0.0,
                    -0.02,
                    2.0,
                    4.0,
                    0.055,
                ],
                type='PointsRangeFilter'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=(
                    'img',
                    'points',
                ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeFusionDataset_LMDB',
        use_semantic=True),
    drop_last=False,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    classes=[
        'drivable_area',
        'stop_line',
        'unknow',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
val_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_LMDB'),
    dict(type='LoadKJLBev_LMDB'),
    dict(
        coord_type='LIDAR',
        load_dim=3,
        reduce_beams=32,
        type='LoadPointsFromFileSingle_LMDB',
        use_dim=3),
    dict(
        point_cloud_range=[
            -2.0,
            0.0,
            -0.02,
            2.0,
            4.0,
            0.055,
        ],
        type='PointsRangeFilter'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=(
            'img',
            'points',
        ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
view_shape = [
    24.0,
    32.0,
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='mmdet3d.Det3DLocalVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
voxel_size = [
    0.05,
    0.05,
    0.075,
]
work_dir = 'work_dirs/fusion_bev/0514'
