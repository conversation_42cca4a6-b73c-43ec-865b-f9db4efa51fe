# --root-path 数据的路径
# --out-dir 输出的pkl路径
# --extra-tag 与第一个参数保持一致
# --pkl-name 存下的pkl文件前缀，代码中会自动加上后缀 '_train.pkl' 和 '_val.pkl'


python tools/create_data.py \
    kujiale_stereo \
    --root-path /home/<USER>/yp/Datasets/kws_deliver_0509 \
    --out-dir /home/<USER>/yp/Datasets/kws_deliver_0509/pkl \
    --extra-tag kujiale_stereo \
    --pkl-name kujiale_stereo_0509

# python tools/create_data.py \
#     kujiale_fusion \
#     --root-path /home/<USER>/yp/Datasets/kws_deliver_0509 \
#     --out-dir /home/<USER>/yp/Datasets/kws_deliver_0509/pkl \
#     --extra-tag kujiale_fusion \
#     --pkl-name kujiale_fusion_0509

# python tools/create_data.py \
#     zed \
#     --root-path /home/<USER>/zed \
#     --out-dir /home/<USER>/zed/pkl \
#     --extra-tag zed \
#     --pkl-name zed

