# created by zhen.liang
# 2013/01/29

import mmcv
import numpy as np
import os
from concurrent import futures as futures
from os import path as osp
import glob


class StereoData(object):
    """Stereo data.
    Generate stereo infos for stereo_converter.
    Args:
        root_path (str): Root path of the raw data.
        split (str): Set split type of the data. Default: 'train'.
    """

    def __init__(self, root_path, split='train'):
        self.root_dir = root_path
        self.split = split
        self.split_dir = osp.join(root_path)
        self.num_perspective = 1
        self.classes = [
            'occupancy', 'non_occupancy'
        ]
        self.cat2label = {cat: self.classes.index(cat) for cat in self.classes}
        self.label2cat = {self.cat2label[t]: t for t in self.cat2label}
        self.cat_ids = np.array(
            [0, 1])
        self.cat_ids2class = {
            nyu40id: i
            for i, nyu40id in enumerate(list(self.cat_ids))
        }
        assert split in ['train', 'val', 'test']
        split_file = osp.join(self.root_dir, 'meta_data',
                              f'{split}.txt')
        mmcv.check_file_exist(split_file)
        self.sample_id_list = mmcv.list_from_file(split_file)
        self.test_mode = (split == 'test')

    def __len__(self):
        return len(self.sample_id_list)

    def get_aligned_box_label(self, idx):
        box_file = osp.join(self.root_dir, 'stereo_instance_data',
                            f'{idx}_aligned_bbox.npy')
        mmcv.check_file_exist(box_file)
        return np.load(box_file)

    def get_unaligned_box_label(self, idx):
        box_file = osp.join(self.root_dir, 'stereo_instance_data',
                            f'{idx}_unaligned_bbox.npy')
        mmcv.check_file_exist(box_file)
        return np.load(box_file)

    def get_axis_align_matrix(self, idx):
        matrix_file = osp.join(self.root_dir, 'stereo_instance_data',
                               f'{idx}_axis_align_matrix.npy')
        mmcv.check_file_exist(matrix_file)
        return np.load(matrix_file)

    def get_images(self, idx):
        paths = []
        path = osp.join(self.root_dir, idx, 'image_02/data', idx)
        for file in sorted(os.listdir(path)):
            if file.endswith('.jpg'):
                paths.append(osp.join('posed_images', idx, file))
        return paths

    def get_extrinsics(self, idx):
        extrinsics = []
        path = osp.join(self.root_dir, idx, 'pose/data')
        for file in sorted(os.listdir(path)):
            if file.endswith('.npy'):
                extrinsics.append(np.loadtxt(osp.join(path, file)))
        return extrinsics

    def get_intrinsics(self, idx):
        matrix_file = osp.join(self.root_dir, idx, 'meta_data',
                               'intrinsic.txt')
        mmcv.check_file_exist(matrix_file)
        return np.load(matrix_file)
    
    def get_occupancy_label(self,idx):
        occupancy_label_file=osp.join(self.root_dir,"stereo_instance_data",
                                f'{idx}_occ_label.npy')
        mmcv.check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)
    
    def get_pcts_transform_matrix(self,idx):
        transform_file=osp.join(self.root_dir,'stereo_instance_data',
                                f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
        """Get data infos.
        This method gets information from the raw data.
        Args:
            num_workers (int): Number of threads to be used. Default: 4.
            has_label (bool): Whether the data has label. Default: True.
            sample_id_list (list[int]): Index list of the sample.
                Default: None.
        Returns:
            infos (list[dict]): Information of the raw data.
        """

        def process_single_scene(sample_idx):
            print(f'{self.split} sample_idx: {sample_idx}')
            info = dict()
            pc_info = {'num_features': 6, 'lidar_idx': sample_idx}
            info['point_cloud'] = pc_info
            pts_filename = osp.join(self.root_dir, 'stereo_instance_data',
                                    f'{sample_idx}_vert.npy')
            points = np.load(pts_filename)
            mmcv.mkdir_or_exist(osp.join(self.root_dir, 'points'))
            points.tofile(
                osp.join(self.root_dir, 'points', f'{sample_idx}.bin'))
            info['pts_path'] = osp.join('points', f'{sample_idx}.bin')


            if os.path.exists(osp.join(self.root_dir, 'posed_images')):
                info['intrinsics'] = self.get_intrinsics(sample_idx)
                all_extrinsics = self.get_extrinsics(sample_idx)
                all_img_paths = self.get_images(sample_idx)
                # some poses in Stereo are invalid
                extrinsics, img_paths = [], []
                for extrinsic, img_path in zip(all_extrinsics, all_img_paths):
                    if np.all(np.isfinite(extrinsic)):
                        img_paths.append(img_path)
                        extrinsics.append(extrinsic)
                info['extrinsics'] = extrinsics
                info['img_paths'] = img_paths

            if not self.test_mode:
                pts_instance_mask_path = osp.join(
                    self.root_dir, 'stereo_instance_data',
                    f'{sample_idx}_ins_label.npy')
                pts_semantic_mask_path = osp.join(
                    self.root_dir, 'stereo_instance_data',
                    f'{sample_idx}_sem_label.npy')

                pts_instance_mask = np.load(pts_instance_mask_path).astype(
                    np.long)
                pts_semantic_mask = np.load(pts_semantic_mask_path).astype(
                    np.long)

                mmcv.mkdir_or_exist(osp.join(self.root_dir, 'instance_mask'))
                mmcv.mkdir_or_exist(osp.join(self.root_dir, 'semantic_mask'))

                pts_instance_mask.tofile(
                    osp.join(self.root_dir, 'instance_mask',
                             f'{sample_idx}.bin'))
                pts_semantic_mask.tofile(
                    osp.join(self.root_dir, 'semantic_mask',
                             f'{sample_idx}.bin'))

                info['pts_instance_mask_path'] = osp.join(
                    'instance_mask', f'{sample_idx}.bin')
                info['pts_semantic_mask_path'] = osp.join(
                    'semantic_mask', f'{sample_idx}.bin')

            if has_label:
                annotations = {}
                # box is of shape [k, 6 + class]
                aligned_box_label = self.get_aligned_box_label(sample_idx)
                unaligned_box_label = self.get_unaligned_box_label(sample_idx)
                annotations['gt_num'] = aligned_box_label.shape[0]
                if annotations['gt_num'] != 0:
                    aligned_box = aligned_box_label[:, :-1]  # k, 6
                    unaligned_box = unaligned_box_label[:, :-1]
                    classes = aligned_box_label[:, -1]  # k
                    annotations['name'] = np.array([
                        self.label2cat[self.cat_ids2class[classes[i]]]
                        for i in range(annotations['gt_num'])
                    ])
                    # default names are given to aligned bbox for compatibility
                    # we also save unaligned bbox info with marked names
                    annotations['location'] = aligned_box[:, :3]
                    annotations['dimensions'] = aligned_box[:, 3:6]
                    annotations['gt_boxes_upright_depth'] = aligned_box
                    annotations['unaligned_location'] = unaligned_box[:, :3]
                    annotations['unaligned_dimensions'] = unaligned_box[:, 3:6]
                    annotations[
                        'unaligned_gt_boxes_upright_depth'] = unaligned_box
                    annotations['index'] = np.arange(
                        annotations['gt_num'], dtype=np.int32)
                    annotations['class'] = np.array([
                        self.cat_ids2class[classes[i]]
                        for i in range(annotations['gt_num'])
                    ])
                axis_align_matrix = self.get_axis_align_matrix(sample_idx)
                annotations['axis_align_matrix'] = axis_align_matrix  # 4x4
                info['annos'] = annotations
            return info

        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene, sample_id_list)
        return list(infos)


class ColmapStereoOccupancy(StereoData):

    def get_occupancy_label(self,idx): 
        occupancy_label_file=osp.join(self.root_dir,"stereo_instance_data",
                                f'{idx}_occ_label.npy')
        mmcv.check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)
    
    def get_pcts_transform_matrix(self,idx):
        transform_file=osp.join(self.root_dir,'stereo_instance_data',
                                f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
        """Get data infos.
        This method gets information from the raw data.
        Args:
            num_workers (int): Number of threads to be used. Default: 4.
            has_label (bool): Whether the data has label. Default: True.
            sample_id_list (list[int]): Index list of the sample.
                Default: None.
        Returns:
            infos (list[dict]): Information of the raw data.
        """

        def process_single_scene(sample_idx):
            print(f'{self.split} sample_idx: {sample_idx}')
            info = dict()
            
            # update with RGB image paths if exist
            objs = os.listdir(osp.join(self.root_dir, sample_idx, 'occupancy/data'))
            objs.sort()
            
            occupancy_labels, total_img_info, total_img_prefix, total_lidar2imgs, total_occupancy_label_info = [], [], [], [], []
            annotations = {}
            for i in range(self.num_perspective, len(objs)):
                start_index = i-self.num_perspective
                filepaths = objs[start_index:i]
                img_info, img_prefix, lidar2imgs, occupancy_label_info = [], [], [], []
                for filepath in filepaths:
                    filename = filepath.split('.')[0]
                    stereo_img_filename, stereo_img_prefix = {}, {}
                    left_img_prefix = osp.join(self.root_dir, sample_idx, 'image_02/data')
                    stereo_img_filename['left_img'] = {'filename': filename+'.jpg'}
                    stereo_img_prefix['left_img'] = left_img_prefix
                    
                    right_img_prefix = osp.join(self.root_dir, sample_idx, 'image_03/data')
                    stereo_img_filename['right_img'] = {'filename': filename + '.jpg'}
                    stereo_img_prefix['right_img'] = right_img_prefix
                    
                    img_info.append(stereo_img_filename)
                    img_prefix.append(stereo_img_prefix)
                    
                    lidar2img_path = osp.join(self.root_dir, sample_idx, 'pose/data', filename + '.npy')
                    if not os.path.exists(lidar2img_path):
                        lidar2imgs.append(None)
                    else:
                        lidar2imgs.append(np.load(lidar2img_path))
                    
                    # lable
                    occupancy_label_path = osp.join(self.root_dir, sample_idx, 'occupancy/data', filename + '.npy')
                    assert os.path.exists(occupancy_label_path)
                    occupancy_label_info.append(occupancy_label_path)
                total_img_info.append(img_info)
                total_img_prefix.append(img_prefix)
                total_lidar2imgs.append({'extrinsic': lidar2imgs})
                total_occupancy_label_info.append(occupancy_label_info)
            
            right2lidar = osp.join(self.root_dir, 'intrinsic_extrinsic', 'left2right.npy')
            assert os.path.exists(right2lidar)
            right2lidar = np.load(right2lidar)
            
            info['right2lidar'] = right2lidar
            info['lidar2img'] = total_lidar2imgs
            info['img_info'] = total_img_info
            info['img_prefix'] = total_img_prefix

            annotations['occupancy_label_info'] = total_occupancy_label_info
            annotations['class'] = self.cat_ids2class
            info['annos'] = annotations
            return info

        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list
            
        for sample_idx in sample_id_list:
            process_single_scene(sample_idx)
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene, sample_id_list)
        # infos = list(infos)
        # for scene_info in infos:
        #     pass
        return list(infos)
    

class StereoOccupancy(StereoData):
    
    def get_occupancy_label(self,idx): 
        occupancy_label_file=osp.join(self.root_dir,"stereo_instance_data",
                                f'{idx}_occ_label.npy')
        mmcv.check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)
    
    def get_pcts_transform_matrix(self,idx):
        transform_file=osp.join(self.root_dir,'stereo_instance_data',
                                f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
        """Get data infos.
        This method gets information from the raw data.
        Args:
            num_workers (int): Number of threads to be used. Default: 4.
            has_label (bool): Whether the data has label. Default: True.
            sample_id_list (list[int]): Index list of the sample.
                Default: None.
        Returns:
            infos (list[dict]): Information of the raw data.
        """

        def process_single_scene(sample_idx):
            print(f'{self.split} sample_idx: {sample_idx}')
            infos = []
            
            # update with RGB image paths if exist
            objs = glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, '无人场景/12cm/**/occupancy/**/*.npy'), recursive=True)
            objs.sort()
            
            dataset_name = self.root_dir.split('/')[-1]     # *.npy
            
            left2right_path = osp.join(self.root_dir, 'intrinsic_extrinsic', 'left2right.npy')
            right2left = np.linalg.inv(np.load(left2right_path))
            left2left = np.array([
                [1., 0., 0., 0.],
                [0., 1., 0., 0.],
                [0., 0., 1., 0.],
                [0., 0., 0., 1.]
            ])
            
            left_intrinsic = np.load(osp.join(self.root_dir, 'intrinsic_extrinsic', 'left_intrinsic.npy'))
            right_intrinsic = np.load(osp.join(self.root_dir, 'intrinsic_extrinsic', 'right_intrinsic.npy'))

            for i in range(len(objs)):
                info = {}
                src_occ_path = objs[i]
                # filename = src_occ_path.split('/')[-1].split('.')[0]
                
                # occupancy label
                occ_path = src_occ_path.split(dataset_name)[-1][1:]
                info['occ_path'] = occ_path
                
                # imgs
                data_path = []
                left_imgpath = occ_path.replace('occupancy', 'image_02')
                left_imgpath = left_imgpath.replace('.npy', '.jpg')
                data_path.append(left_imgpath)
                
                right_imgpath = occ_path.replace('occupancy', 'image_03')
                right_imgpath = right_imgpath.replace('.npy', '.jpg')
                data_path.append(right_imgpath)

                info['imgs2lidar'] = [left2left, right2left]
                info['intrinsic'] = [left_intrinsic, right_intrinsic]
                info['img_path'] = data_path
                infos.append(info)
            return infos

        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list
            
        for sample_idx in sample_id_list:
            process_single_scene(sample_idx)
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene, sample_id_list)
        # infos = list(infos)
        totol_infos = []
        for scene_info in infos:
            totol_infos = totol_infos + scene_info
        return totol_infos
