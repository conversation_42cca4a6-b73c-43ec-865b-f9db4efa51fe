# Copyright (c) OpenMMLab. All rights reserved.
import mmcv
import mmengine
import numpy as np
import os
from mmengine.fileio import dump

from tools.data_converters.s3dis_data_utils import S3DISData, S3DISSegData
from tools.data_converters.scannet_data_utils import ScanNetData, ScanNetSegData
from tools.data_converters.sunrgbd_data_utils import SUNRGBDData
from tools.data_converters.stereo_data_utils import StereoOccupancy
from tools.data_converters.kujiale_data_utils import KujialeStereo, KujialeStereo3, KujialeFusion, ZedStereo
from tools.data_converters.gecaoji_data_utils import GecaojiStereo
from tools.data_converters.indoor_data_utils import IndoorData, IndoorOms_dk, IndoorOms_lidar, IndoorZed_lidar, IndoorMatterport_data



# root_path=args.root_path,   /home/<USER>/yp/Datasets/kws_deliver_0509
# info_prefix=args.extra_tag,   kujiale_stereo
# out_dir=args.out_dir,               /home/<USER>/yp/Datasets/kws_deliver_0509/pkl
# workers=args.workers,            4
# pkl_name=args.pkl_name     kujiale_stereo_0509
def create_indoor_info_file(data_path,    #/home/<USER>/yp/Datasets/kws_deliver_0509
                            pkl_name,                                  #kujiale_stereo_0509
                            pkl_prefix='sunrgbd',            #kujiale_stereo
                            save_path=None,                    #/home/<USER>/yp/Datasets/kws_deliver_0509/pkl
                            use_v1=False,
                            workers=4,
                            ):
    """Create indoor information file.

    Get information of the raw data and save it to the pkl file.

    Args:
        data_path (str): Path of the data.
        pkl_prefix (str): Prefix of the pkl to be saved. Default: 'sunrgbd'.
        save_path (str): Path of the pkl to be saved. Default: None.
        use_v1 (bool): Whether to use v1. Default: False.
        workers (int): Number of threads to be used. Default: 4.
    """
    assert os.path.exists(data_path)
    assert pkl_prefix in ['sunrgbd', 'scannet', 's3dis', 'stereo', 'kujiale_stereo', 'kujiale_stereo3', 'kujiale_fusion', 'gecaoji', 'zed', 'indoor_oms_dk', 'indoor_oms_lidar', 'indoor_zed_lidar', 'matterport_dataset'], \
        f'unsupported indoor dataset {pkl_prefix}'

    # save_path = data_path if save_path is None else save_path
    # assert os.path.exists(save_path)
    if not os.path.exists(save_path):
        os.makedirs(save_path)

    # generate infos for both detection and segmentation task
    if pkl_prefix in ['sunrgbd', 'scannet', 'stereo', 'kujiale_stereo', 'kujiale_stereo3', 'kujiale_fusion', 'gecaoji', 'zed', 'indoor_oms_dk', 'indoor_oms_lidar', 'indoor_zed_lidar', 'matterport_dataset']:

        train_filename = os.path.join(save_path, pkl_name + '_train.pkl')
        val_filename = os.path.join(save_path, pkl_name + '_val.pkl')

        if pkl_prefix == 'sunrgbd':
            # SUN RGB-D has a train-val split
            train_dataset = SUNRGBDData(
                root_path=data_path, split='train', use_v1=use_v1)
            val_dataset = SUNRGBDData(
                root_path=data_path, split='val', use_v1=use_v1)
        elif pkl_prefix in ('stereo'):
            dataset = StereoOccupancy
            train_dataset = dataset(root_path=data_path, split='train')
            val_dataset = dataset(root_path=data_path, split='val')    
        elif pkl_prefix in ('kujiale_stereo'):
            dataset = KujialeStereo
            train_dataset = dataset(root_path=data_path, split='train')
            val_dataset = dataset(root_path=data_path, split='val')
        elif pkl_prefix in ('kujiale_stereo3'):
            dataset = KujialeStereo3
            train_dataset = dataset(root_path=data_path, split='train')
            val_dataset = dataset(root_path=data_path, split='val')
        elif pkl_prefix in ('kujiale_fusion'):
            dataset = KujialeFusion
            train_dataset = dataset(root_path=data_path, split='train')
            val_dataset = dataset(root_path=data_path, split='val')
        elif pkl_prefix in ('gecaoji'):
            dataset = GecaojiStereo
            train_dataset = dataset(root_path=data_path, split='train')
            val_dataset = dataset(root_path=data_path, split='val')
        elif pkl_prefix in ('zed'):
            dataset = ZedStereo
            train_dataset = dataset(root_path=data_path, split='train')
            val_dataset = dataset(root_path=data_path, split='val')
        elif pkl_prefix in ('indoor_oms_dk'):
            dataset = IndoorOms_dk
            train_dataset = dataset(root_path=data_path, split='train')
            val_dataset = dataset(root_path=data_path, split='val')
        elif pkl_prefix in ('indoor_oms_lidar'):
            dataset = IndoorOms_lidar
            train_dataset = dataset(root_path=data_path, split='train')
            val_dataset = dataset(root_path=data_path, split='val')
        elif pkl_prefix in ('indoor_zed_lidar'):
            dataset = IndoorZed_lidar
            train_dataset = dataset(root_path=data_path, split='train')
            val_dataset = dataset(root_path=data_path, split='val')
        elif pkl_prefix in ('matterport_dataset'):
            dataset = IndoorMatterport_data
            train_dataset = dataset(root_path=data_path, split='train')
            val_dataset = dataset(root_path=data_path, split='val')
        else:
            # ScanNet has a train-val-test split
            train_dataset = ScanNetData(root_path=data_path, split='train')
            val_dataset = ScanNetData(root_path=data_path, split='val')
            test_dataset = ScanNetData(root_path=data_path, split='test')
            test_filename = os.path.join(save_path,
                                         f'{pkl_prefix}_infos_test.pkl')

        infos_train = train_dataset.get_infos(num_workers=workers, has_label=True)
        # infos_train = {'infos': infos_train}

        # mmcv.dump(infos_train, train_filename, 'pkl')
        dump(infos_train, train_filename, 'pkl')
        print(f'{pkl_prefix} info train file is saved to {train_filename}')

        infos_val = val_dataset.get_infos(num_workers=workers, has_label=True)
        # infos_val = {'infos': infos_val}
        # mmcv.dump(infos_val, val_filename, 'pkl')
        dump(infos_val, val_filename, 'pkl')
        print(f'{pkl_prefix} info val file is saved to {val_filename}')

    if pkl_prefix == 'scannet':
        infos_test = test_dataset.get_infos(
            num_workers=workers, has_label=False)
        # mmcv.dump(infos_test, test_filename, 'pkl')
        dump(infos_test, test_filename, 'pkl')
        print(f'{pkl_prefix} info test file is saved to {test_filename}')

    # generate infos for the semantic segmentation task
    # e.g. re-sampled scene indexes and label weights
    # scene indexes are used to re-sample rooms with different number of points
    # label weights are used to balance classes with different number of points
    if pkl_prefix == 'scannet':
        # label weight computation function is adopted from
        # https://github.com/charlesq34/pointnet2/blob/master/scannet/scannet_dataset.py#L24
        train_dataset = ScanNetSegData(
            data_root=data_path,
            ann_file=train_filename,
            split='train',
            num_points=8192,
            label_weight_func=lambda x: 1.0 / np.log(1.2 + x))
        # TODO: do we need to generate on val set?
        val_dataset = ScanNetSegData(
            data_root=data_path,
            ann_file=val_filename,
            split='val',
            num_points=8192,
            label_weight_func=lambda x: 1.0 / np.log(1.2 + x))
        # no need to generate for test set
        train_dataset.get_seg_infos()
        val_dataset.get_seg_infos()
    elif pkl_prefix == 's3dis':
        # S3DIS doesn't have a fixed train-val split
        # it has 6 areas instead, so we generate info file for each of them
        # in training, we will use dataset to wrap different areas
        splits = [f'Area_{i}' for i in [1, 2, 3, 4, 5, 6]]
        for split in splits:
            dataset = S3DISData(root_path=data_path, split=split)
            info = dataset.get_infos(num_workers=workers, has_label=True)
            filename = os.path.join(save_path,
                                    f'{pkl_prefix}_infos_{split}.pkl')
            # mmcv.dump(info, filename, 'pkl')
            dump(info, filename, 'pkl')
            print(f'{pkl_prefix} info {split} file is saved to {filename}')
            seg_dataset = S3DISSegData(
                data_root=data_path,
                ann_file=filename,
                split=split,
                num_points=4096,
                label_weight_func=lambda x: 1.0 / np.log(1.2 + x))
            seg_dataset.get_seg_infos()
