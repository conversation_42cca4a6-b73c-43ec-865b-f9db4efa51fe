import lmdb
import json
import base64
from PIL import Image
from tqdm import tqdm
from pycocotools.coco import COCO

data_root = '/defaultShare/mutitask_dataset/ground_old8cls'
# 定义COCO数据集的路径
coco_train_annotation_file = data_root + '/ground_val_0526.json'
coco_train_image_dir = data_root + "/ground_val_0526"

# 定义LMDB数据库的路径
lmdb_path = data_root + '/val_0526.lmdb'

# 打开COCO注释文件
coco = COCO(coco_train_annotation_file)

# 打开LMDB数据库
env = lmdb.open(lmdb_path, map_size=1099511627776)  # 设置数据库大小

with env.begin(write=True) as txn:
    # 迭代COCO数据集中的每个图像
    for img_id in tqdm(coco.getImgIds()):
        img_info = coco.loadImgs(img_id)[0]
        img_path = coco_train_image_dir + "/" + img_info['file_name']
        # 读取图像
        img = Image.open(img_path)

        # 将图像转换为字节流
        # img_bytes = img.tobytes()
        img_bytes = base64.b64encode(img.tobytes()).decode('utf-8')

        # 获取图像的标签信息
        ann_ids = coco.getAnnIds(imgIds=img_id)
        annotations = coco.loadAnns(ann_ids)

        # 构建包含图像和标签信息的数据结构
        data = {
            'image': img_bytes,
            'annotations': annotations,
        }

        # 将数据结构存储到LMDB数据库
        txn.put(str(img_id).encode('utf-8'), json.dumps(data).encode('utf-8'))

    # # 关闭LMDB数据库
    # env.close()