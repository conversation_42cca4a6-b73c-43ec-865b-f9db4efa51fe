# created by zhen.liang
# 2013/01/29

import mmcv
import mmengine
import numpy as np
import os
from concurrent import futures as futures
from os import path as osp
import glob
import json
from mmengine.utils import check_file_exist
from mmengine.fileio import list_from_file
from mmengine.utils import check_file_exist
from mmengine import mkdir_or_exist

class IndoorData(object):
    """Stereo data.
    Generate stereo infos for stereo_converter.
    Args:
        root_path (str): Root path of the raw data.
        split (str): Set split type of the data. Default: 'train'.
    """

    def __init__(self, root_path, split='train'):
        self.root_dir = root_path
        self.split = split
        self.split_dir = osp.join(root_path)
        self.num_perspective = 1
        self.classes = [
            'occupancy', 'non_occupancy'
        ]
        self.cat2label = {cat: self.classes.index(cat) for cat in self.classes}
        self.label2cat = {self.cat2label[t]: t for t in self.cat2label}
        self.cat_ids = np.array(
            [0, 1])
        self.cat_ids2class = {
            nyu40id: i
            for i, nyu40id in enumerate(list(self.cat_ids))
        }
        assert split in ['train', 'val', 'test']

        split_file = osp.join(self.root_dir, 'meta_data',
                              f'{split}.txt')
        # mmcv.check_file_exist(split_file)
        check_file_exist(split_file)
        self.sample_id_list = list_from_file(split_file)
        self.test_mode = (split == 'test')

    def __len__(self):
        return len(self.sample_id_list)

    def get_aligned_box_label(self, idx):
        box_file = osp.join(self.root_dir, 'stereo_instance_data',
                            f'{idx}_aligned_bbox.npy')
        check_file_exist(box_file)
        return np.load(box_file)

    def get_unaligned_box_label(self, idx):
        box_file = osp.join(self.root_dir, 'stereo_instance_data',
                            f'{idx}_unaligned_bbox.npy')
        check_file_exist(box_file)
        return np.load(box_file)

    def get_axis_align_matrix(self, idx):
        matrix_file = osp.join(self.root_dir, 'stereo_instance_data',
                               f'{idx}_axis_align_matrix.npy')
        check_file_exist(matrix_file)
        return np.load(matrix_file)

    def get_images(self, idx):
        paths = []
        path = osp.join(self.root_dir, idx, 'image_02/data', idx)
        for file in sorted(os.listdir(path)):
            if file.endswith('.jpg'):
                paths.append(osp.join('posed_images', idx, file))
        return paths

    def get_extrinsics(self, idx):
        extrinsics = []
        path = osp.join(self.root_dir, idx, 'pose/data')
        for file in sorted(os.listdir(path)):
            if file.endswith('.npy'):
                extrinsics.append(np.loadtxt(osp.join(path, file)))
        return extrinsics

    def get_intrinsics(self, idx):
        matrix_file = osp.join(self.root_dir, idx, 'meta_data',
                               'intrinsic.txt')
        check_file_exist(matrix_file)
        return np.load(matrix_file)

    def get_occupancy_label(self, idx):
        occupancy_label_file = osp.join(self.root_dir, "stereo_instance_data",
                                        f'{idx}_occ_label.npy')
        check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)

    def get_pcts_transform_matrix(self, idx):
        transform_file = osp.join(self.root_dir, 'stereo_instance_data',
                                  f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
        """Get data infos.
        This method gets information from the raw data.
        Args:
            num_workers (int): Number of threads to be used. Default: 4.
            has_label (bool): Whether the data has label. Default: True.
            sample_id_list (list[int]): Index list of the sample.
                Default: None.
        Returns:
            infos (list[dict]): Information of the raw data.
        """

        def process_single_scene(sample_idx):
            # print(f'{self.split} sample_idx: {sample_idx}')
            info = dict()
            pc_info = {'num_features': 6, 'lidar_idx': sample_idx}
            info['point_cloud'] = pc_info
            pts_filename = osp.join(self.root_dir, 'stereo_instance_data',
                                    f'{sample_idx}_vert.npy')
            points = np.load(pts_filename)
            mmcv.mkdir_or_exist(osp.join(self.root_dir, 'points'))
            points.tofile(
                osp.join(self.root_dir, 'points', f'{sample_idx}.bin'))
            info['pts_path'] = osp.join('points', f'{sample_idx}.bin')

            if os.path.exists(osp.join(self.root_dir, 'posed_images')):
                info['intrinsics'] = self.get_intrinsics(sample_idx)
                all_extrinsics = self.get_extrinsics(sample_idx)
                all_img_paths = self.get_images(sample_idx)
                # some poses in Stereo are invalid
                extrinsics, img_paths = [], []
                for extrinsic, img_path in zip(all_extrinsics, all_img_paths):
                    if np.all(np.isfinite(extrinsic)):
                        img_paths.append(img_path)
                        extrinsics.append(extrinsic)
                info['extrinsics'] = extrinsics
                info['img_paths'] = img_paths

            if not self.test_mode:
                pts_instance_mask_path = osp.join(
                    self.root_dir, 'stereo_instance_data',
                    f'{sample_idx}_ins_label.npy')
                pts_semantic_mask_path = osp.join(
                    self.root_dir, 'stereo_instance_data',
                    f'{sample_idx}_sem_label.npy')

                pts_instance_mask = np.load(pts_instance_mask_path).astype(
                    np.long)
                pts_semantic_mask = np.load(pts_semantic_mask_path).astype(
                    np.long)

                mmcv.mkdir_or_exist(osp.join(self.root_dir, 'instance_mask'))
                mmcv.mkdir_or_exist(osp.join(self.root_dir, 'semantic_mask'))

                pts_instance_mask.tofile(
                    osp.join(self.root_dir, 'instance_mask',
                             f'{sample_idx}.bin'))
                pts_semantic_mask.tofile(
                    osp.join(self.root_dir, 'semantic_mask',
                             f'{sample_idx}.bin'))

                info['pts_instance_mask_path'] = osp.join(
                    'instance_mask', f'{sample_idx}.bin')
                info['pts_semantic_mask_path'] = osp.join(
                    'semantic_mask', f'{sample_idx}.bin')

            if has_label:
                annotations = {}
                # box is of shape [k, 6 + class]
                aligned_box_label = self.get_aligned_box_label(sample_idx)
                unaligned_box_label = self.get_unaligned_box_label(sample_idx)
                annotations['gt_num'] = aligned_box_label.shape[0]
                if annotations['gt_num'] != 0:
                    aligned_box = aligned_box_label[:, :-1]  # k, 6
                    unaligned_box = unaligned_box_label[:, :-1]
                    classes = aligned_box_label[:, -1]  # k
                    annotations['name'] = np.array([
                        self.label2cat[self.cat_ids2class[classes[i]]]
                        for i in range(annotations['gt_num'])
                    ])
                    # default names are given to aligned bbox for compatibility
                    # we also save unaligned bbox info with marked names
                    annotations['location'] = aligned_box[:, :3]
                    annotations['dimensions'] = aligned_box[:, 3:6]
                    annotations['gt_boxes_upright_depth'] = aligned_box
                    annotations['unaligned_location'] = unaligned_box[:, :3]
                    annotations['unaligned_dimensions'] = unaligned_box[:, 3:6]
                    annotations[
                        'unaligned_gt_boxes_upright_depth'] = unaligned_box
                    annotations['index'] = np.arange(
                        annotations['gt_num'], dtype=np.int32)
                    annotations['class'] = np.array([
                        self.cat_ids2class[classes[i]]
                        for i in range(annotations['gt_num'])
                    ])
                axis_align_matrix = self.get_axis_align_matrix(sample_idx)
                annotations['axis_align_matrix'] = axis_align_matrix  # 4x4
                info['annos'] = annotations
            return info

        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene, sample_id_list)
        return list(infos)


class IndoorZed_lidar(IndoorData):

    def get_occupancy_label(self, idx):
        occupancy_label_file = osp.join(self.root_dir, "stereo_instance_data",
                                        f'{idx}_occ_label.npy')
        check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)

    def get_pcts_transform_matrix(self, idx):
        transform_file = osp.join(self.root_dir, 'stereo_instance_data',
                                  f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
        """Get data infos.
        This method gets information from the raw data.
        Args:
            num_workers (int): Number of threads to be used. Default: 4.
            has_label (bool): Whether the data has label. Default: True.
            sample_id_list (list[int]): Index list of the sample.
                Default: None.
        Returns:
            infos (list[dict]): Information of the raw data.
        """

        print(222, sample_id_list)

        def process_single_scene(sample_idx):
            print(11111, f'{self.split} sample_idx: {sample_idx}')
            infos = []

            objs = glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, 'lidar_zed/project_ouster_no_ground_post/*.jpg'), recursive=True)
            objs.sort()

            for i in range(len(objs)):
                info = {}
                project_ouster_no_ground_post = objs[i]
                # print(src_tof_path)
                # exit()
                scene_token = 'scenes_' + project_ouster_no_ground_post.split('/')[6] + '_' + project_ouster_no_ground_post.split('/')[-1].replace('.jpg', '')
                src_image_left = project_ouster_no_ground_post.replace('project_ouster_no_ground_post', 'zed_ubdistort_left')
                src_image_right = project_ouster_no_ground_post.replace('project_ouster_no_ground_post', 'zed_ubdistort_right')

                bev_path = project_ouster_no_ground_post.replace('project_ouster_no_ground_post', 'bev').replace('.jpg', '.png')
                occ_path = project_ouster_no_ground_post.replace('project_ouster_no_ground_post', 'occ').replace('.jpg', '_occ.npy')
                occ_fill_path = project_ouster_no_ground_post.replace('project_ouster_no_ground_post', 'occ_fill').replace('.jpg', '_occ.npy')

                info['token'] = scene_token
                # print(bev_path.replace(self.root_dir, '')[1:])
                # exit()
                info['left_image_path'] = src_image_left.replace(self.root_dir, '')[1:]
                info['right_image_path'] = src_image_right.replace(self.root_dir, '')[1:]
                info['occ_path'] = occ_path.replace(self.root_dir, '')[1:]
                info['occ_fill_path'] = occ_fill_path.replace(self.root_dir, '')[1:]
                info['bev_path'] = bev_path.replace(self.root_dir, '')[1:]

                infos.append(info)
            return infos

        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list

        # for sample_idx in sample_id_list:
        #     process_single_scene(sample_idx)
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene, sample_id_list)
        # infos = list(infos)
        totol_infos = []
        for scene_info in infos:
            totol_infos = totol_infos + scene_info

        right2left = np.linalg.inv(
            np.array([
                [0.9999996074191118, -0.0001663949801876781, -0.0008703300150569494, -0.06282940483827766],
                [0.00016526031905401343, 0.9999991366274906, -0.001303623144355979, 7.545870246627618e-05],
                [0.0008705461799852165, 0.0013034788015624753, 0.9999987715454277, -0.00011347695302428774],
                [0.0, 0.0, 0.0, 1.0]
            ])
        )

        left2left = np.array([
            [1., 0., 0., 0.],
            [0., 1., 0., 0.],
            [0., 0., 1., 0.],
            [0., 0., 0., 1.]
        ])

        left_intrinsic = np.array([
            [730.117, 0, 637.406],
            [0, 730.232, 367.128],
            [0, 0, 1]], dtype=np.float64)

        right_intrinsic = np.array([
            [729.5, 0, 640.74],
            [0, 729.44, 366.31],
            [0, 0, 1]], dtype=np.float64)

        totol_infos = {
            # stereo 4个参数
            'right2left': right2left,
            'left2left': left2left,
            'left_intrinsic': left_intrinsic,
            'right_intrinsic': right_intrinsic,

            'infos': totol_infos,
        }

        return totol_infos


class IndoorOms_dk(IndoorData):

    def get_occupancy_label(self, idx):
        occupancy_label_file = osp.join(self.root_dir, "stereo_instance_data",
                                        f'{idx}_occ_label.npy')
        mmcv.check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)

    def get_pcts_transform_matrix(self, idx):
        transform_file = osp.join(self.root_dir, 'stereo_instance_data',
                                  f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
        """Get data infos.
        This method gets information from the raw data.
        Args:
            num_workers (int): Number of threads to be used. Default: 4.
            has_label (bool): Whether the data has label. Default: True.
            sample_id_list (list[int]): Index list of the sample.
                Default: None.
        Returns:
            infos (list[dict]): Information of the raw data.
        """

        def process_single_scene(sample_idx):

            print(f'{self.split} sample_idx: {sample_idx}')
            infos = []

            objs = glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, 'dk_oms/tof_6/*.npy'), recursive=True)
            objs.sort()
            # print(objs)

            for i in range(len(objs)):
                info = {}
                src_tof_path = objs[i]
                # print(src_tof_path)
                # exit()
                scene_token = src_tof_path.split('/')[5] + '_' + src_tof_path.split('/')[6] + src_tof_path.split('/')[-1].replace('.npy', '')
                rgb_path = src_tof_path.replace('tof_6', 'oms_rgb').replace('.npy', '.jpg')
                occ_path = src_tof_path.replace('tof_6', 'occ').replace('.npy', '_occ.npy')
                bev_path = src_tof_path.replace('tof_6', 'bev').replace('.npy', '.png')
                project_dk_post = src_tof_path.replace('tof_6', 'project_dk_post').replace('.npy', '.jpg')

                oms_intrinsic = np.array([
                    [627.361, 0, 630.733],
                    [0, 627.189, 531.027],
                    [0, 0, 1]], dtype=np.float64)

                # [1:] 去掉 前面的'/'
                # if os.path.exists(project_dk_post):
                info['token'] = scene_token
                # print(bev_path.replace(self.root_dir, '')[1:])
                # exit()
                info['cams'] = rgb_path.replace(self.root_dir, '')[1:]
                info['lidar_path'] = src_tof_path.replace(self.root_dir, '')[1:]
                info['occ_path'] = occ_path.replace(self.root_dir, '')[1:]
                info['bev_path'] = bev_path.replace(self.root_dir, '')[1:]
                info['intrinsic'] = oms_intrinsic

                infos.append(info)

            return infos

        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list

        # for sample_idx in sample_id_list:
        #     process_single_scene(sample_idx)
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene, sample_id_list)
        # infos = list(infos)
        totol_infos = []
        for scene_info in infos:
            totol_infos = totol_infos + scene_info

        cam2lidar = np.array([
            [1, 0, 0, 0],
            [0, 0, 1, 0],
            [0, -1, 0, 0],
            [0, 0, 0, 1]], dtype=np.float64)

        totol_infos = {
            'cam2lidar': cam2lidar,
            'infos': totol_infos,
        }

        return totol_infos


class IndoorOms_lidar(IndoorData):

    def get_occupancy_label(self, idx):
        occupancy_label_file = osp.join(self.root_dir, "stereo_instance_data",
                                        f'{idx}_occ_label.npy')
        mmcv.check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)

    def get_pcts_transform_matrix(self, idx):
        transform_file = osp.join(self.root_dir, 'stereo_instance_data',
                                  f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
        """Get data infos.
        This method gets information from the raw data.
        Args:
            num_workers (int): Number of threads to be used. Default: 4.
            has_label (bool): Whether the data has label. Default: True.
            sample_id_list (list[int]): Index list of the sample.
                Default: None.
        Returns:
            infos (list[dict]): Information of the raw data.
        """

        def process_single_scene(sample_idx):

            print(f'{self.split} sample_idx: {sample_idx}')
            infos = []

            objs = glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, 'lidar_oms/tof_lidar_1/*.npy'), recursive=True)
            objs.sort()
            # print(objs)

            for i in range(len(objs)):
                src_tof_path = objs[i]
                # print(src_tof_path)
                # exit()

                scene_token = 'scnens_' + src_tof_path.split('/')[6] + '_' + src_tof_path.split('/')[-1].replace('.npy', '')

                rgb_path = src_tof_path.replace('tof_lidar_1', 'oms_rgb').replace('.npy', '.jpg')
                occ_path = src_tof_path.replace('tof_lidar_1', 'occ').replace('.npy', '_occ.npy')
                occ_fill_path = src_tof_path.replace('tof_lidar_1', 'occ_fill').replace('.npy', '_occ.npy')                
                bev_path = src_tof_path.replace('tof_lidar_1', 'bev').replace('.npy', '.png')
                tof6 = src_tof_path.replace('tof_lidar_1', 'tof_6')
                project_dk_post = src_tof_path.replace('tof_lidar_1', 'project_ouster_no_ground_post').replace('.npy', '.jpg')
                erci = src_tof_path.replace('tof_lidar_1', 'output_2').replace('.npy', '_out_put_2.png')
                # print(src_tof_path)
                # exit()

                oms_intrinsic = np.array([
                    [627.361, 0, 630.733],
                    [0, 627.189, 531.027],
                    [0, 0, 1]], dtype=np.float64)

                # [1:] 去掉 前面的'/'
                if os.path.exists(tof6) and os.path.exists(erci):
                    info = {}
                    info['token'] = scene_token
                    # print(bev_path.replace(self.root_dir, '')[1:])
                    # exit()
                    info['cams'] = rgb_path.replace(self.root_dir, '')[1:]
                    info['lidar_path'] = tof6.replace(self.root_dir, '')[1:]
                    info['lidar1_path'] = src_tof_path.replace(self.root_dir, '')[1:]
                    info['occ_path'] = occ_path.replace(self.root_dir, '')[1:]
                    info['occ_fill_path'] = occ_fill_path.replace(self.root_dir, '')[1:]
                    info['bev_path'] = bev_path.replace(self.root_dir, '')[1:]
                    info['intrinsic'] = oms_intrinsic

                    infos.append(info)

            return infos

        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list

        # for sample_idx in sample_id_list:
        #     process_single_scene(sample_idx)
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene, sample_id_list)
        # infos = list(infos)
        totol_infos = []
        for scene_info in infos:
            totol_infos = totol_infos + scene_info

        cam2lidar = np.array([
            [1, 0, 0, 0],
            [0, 0, 1, 0],
            [0, -1, 0, 0],
            [0, 0, 0, 1]], dtype=np.float64)

        totol_infos = {
            'cam2lidar': cam2lidar,
            'infos': totol_infos,
        }

        return totol_infos

class IndoorMatterport_data(IndoorData):

    def get_occupancy_label(self, idx):
        occupancy_label_file = osp.join(self.root_dir, "stereo_instance_data",
                                        f'{idx}_occ_label.npy')
        mmcv.check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)

    def get_pcts_transform_matrix(self, idx):
        transform_file = osp.join(self.root_dir, 'stereo_instance_data',
                                  f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
        """Get data infos.
        This method gets information from the raw data.
        Args:
            num_workers (int): Number of threads to be used. Default: 4.
            has_label (bool): Whether the data has label. Default: True.
            sample_id_list (list[int]): Index list of the sample.
                Default: None.
        Returns:
            infos (list[dict]): Information of the raw data.
        """
        
        def process_single_scene(sample_idx):
            
            print(f'{self.split} sample_idx: {sample_idx}')
            infos = []
            
            cam_intrinsics_path = os.path.join(self.root_dir, "scans",sample_idx, sample_idx, "cam_intrinsics", "cam1_intrinsics.txt")
            
            rgb_path_list = glob.glob(osp.join(self.root_dir, 'scans', sample_idx, sample_idx, 'cam1_rgb_front4/*.jpg'), recursive=True)
            rgb_path_list.sort()
            
            occ_path_list = glob.glob(osp.join(self.root_dir, 'scans', sample_idx, sample_idx, 'occ_label_front4/*.npy'), recursive=True)
            occ_path_list.sort()
            
            tof_points_path_list = glob.glob(osp.join(self.root_dir, 'scans', sample_idx, sample_idx, 'tof_points_front4/*.npy'), recursive=True)
            tof_points_path_list.sort()
            
            depth_path_list = glob.glob(osp.join(self.root_dir, 'scans', sample_idx, sample_idx, 'cam1_depth_front4/*.png'), recursive=True)
            depth_path_list.sort()
            
            cam1_intrinsic = np.loadtxt(cam_intrinsics_path)
            
            for i in range(len(rgb_path_list)):
                
                rgb_path = rgb_path_list[i]
                occ_path = occ_path_list[i]
                tof_path = tof_points_path_list[i]
                depth_path = depth_path_list[i]
                
                scene_token = sample_idx + '_' + rgb_path.split('/')[-1].replace('.jpg', '')
                
                info = {}
                info['token'] = scene_token
                info['rgb_path'] = rgb_path.replace(self.root_dir, '')[1:]
                info['depth_path'] = depth_path.replace(self.root_dir, '')[1:]
                info['occ_path'] = occ_path.replace(self.root_dir, '')[1:]
                info['tof_path'] = tof_path.replace(self.root_dir, '')[1:]
                info['intrinsic'] = cam1_intrinsic

                infos.append(info)
            return infos
        
        def process_single_scene2(sample_idx):
            
            print(f'{self.split} sample_idx: {sample_idx}')
            infos = []
            
            cam_intrinsics_path = os.path.join(self.root_dir, "scans",sample_idx, sample_idx, "cam_intrinsics", "cam1_intrinsics.txt")
            
            tof_points_path_list = glob.glob(osp.join(self.root_dir, 'scans', sample_idx, sample_idx, 'tof_points_front4_8_shang70/*.npy'), recursive=True)
            tof_points_path_list.sort()
            
            cam1_intrinsic = np.loadtxt(cam_intrinsics_path)
            
            for i in range(len(tof_points_path_list)):
                
                tof_path = tof_points_path_list[i]
                rgb_path = tof_path.replace('tof_points_front4_8_shang70', 'cam1_rgb').replace('_tof_points.npy', '.jpg').replace("d1_","i1_")
                if (not os.path.exists(rgb_path)): continue
                # rgb_path[-8:].replace("d","i")
                occ_path = tof_path.replace('tof_points_front4_8_shang70', 'occ_label').replace('_tof_points.npy', '_occ.npy')
                depth_path = tof_path.replace('tof_points_front4_8_shang70', 'cam1_depth').replace('_tof_points.npy', '.png')
                
                scene_token = sample_idx + '_' + rgb_path.split('/')[-1].replace('.jpg', '')
                
                info = {}
                info['token'] = scene_token
                info['rgb_path'] = rgb_path.replace(self.root_dir, '')[1:]
                info['depth_path'] = depth_path.replace(self.root_dir, '')[1:]
                info['occ_path'] = occ_path.replace(self.root_dir, '')[1:]
                info['tof_path'] = tof_path.replace(self.root_dir, '')[1:]
                info['intrinsic'] = cam1_intrinsic

                infos.append(info)
            return infos
        
        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list

        # for sample_idx in sample_id_list:
        #     process_single_scene(sample_idx)
        
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene2, sample_id_list)
            
        # infos = list(infos)
        totol_infos = []
        for scene_info in infos:
            totol_infos = totol_infos + scene_info

        cam2lidar = np.array([
            [1, 0, 0, 0],
            [0, 0, 1, 0],
            [0, -1, 0, 0],
            [0, 0, 0, 1]], dtype=np.float64)

        totol_infos = {
            'cam2lidar': cam2lidar,
            'infos': totol_infos,
        }

        return totol_infos
                


