# created by zhen.liang
# 2013/01/29

import mmcv
import mmengine
import numpy as np
import os
from concurrent import futures as futures
from os import path as osp
import glob
import json
from mmengine.utils import check_file_exist
from mmengine.fileio import list_from_file
from mmengine.utils import check_file_exist
from mmengine import mkdir_or_exist


class KujialeData(object):
    """Stereo data.
    Generate stereo infos for stereo_converter.
    Args:
        root_path (str): Root path of the raw data.
        split (str): Set split type of the data. Default: 'train'.
    """

    def __init__(self, root_path, split='train'):
        self.root_dir = root_path
        self.split = split
        self.split_dir = osp.join(root_path)
        self.num_perspective = 1
        self.classes = [
            'occupancy', 'non_occupancy'
        ]
        self.cat2label = {cat: self.classes.index(cat) for cat in self.classes}
        self.label2cat = {self.cat2label[t]: t for t in self.cat2label}
        self.cat_ids = np.array(
            [0, 1])
        self.cat_ids2class = {
            nyu40id: i
            for i, nyu40id in enumerate(list(self.cat_ids))
        }
        assert split in ['train', 'val', 'test']

        split_file = osp.join(self.root_dir, 'meta_data',
                              f'{split}.txt')
        # mmcv.check_file_exist(split_file)
        check_file_exist(split_file)
        self.sample_id_list = list_from_file(split_file)
        self.test_mode = (split == 'test')

    def __len__(self):
        return len(self.sample_id_list)

    def get_aligned_box_label(self, idx):
        box_file = osp.join(self.root_dir, 'stereo_instance_data',
                            f'{idx}_aligned_bbox.npy')
        check_file_exist(box_file)
        return np.load(box_file)

    def get_unaligned_box_label(self, idx):
        box_file = osp.join(self.root_dir, 'stereo_instance_data',
                            f'{idx}_unaligned_bbox.npy')
        check_file_exist(box_file)
        return np.load(box_file)

    def get_axis_align_matrix(self, idx):
        matrix_file = osp.join(self.root_dir, 'stereo_instance_data',
                               f'{idx}_axis_align_matrix.npy')
        check_file_exist(matrix_file)
        return np.load(matrix_file)

    def get_images(self, idx):
        paths = []
        path = osp.join(self.root_dir, idx, 'image_02/data', idx)
        for file in sorted(os.listdir(path)):
            if file.endswith('.jpg'):
                paths.append(osp.join('posed_images', idx, file))
        return paths

    def get_extrinsics(self, idx):
        extrinsics = []
        path = osp.join(self.root_dir, idx, 'pose/data')
        for file in sorted(os.listdir(path)):
            if file.endswith('.npy'):
                extrinsics.append(np.loadtxt(osp.join(path, file)))
        return extrinsics

    def get_intrinsics(self, idx):
        matrix_file = osp.join(self.root_dir, idx, 'meta_data',
                               'intrinsic.txt')
        check_file_exist(matrix_file)
        return np.load(matrix_file)

    def get_occupancy_label(self, idx):
        occupancy_label_file = osp.join(self.root_dir, "stereo_instance_data",
                                        f'{idx}_occ_label.npy')
        check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)

    def get_pcts_transform_matrix(self, idx):
        transform_file = osp.join(self.root_dir, 'stereo_instance_data',
                                  f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
        """Get data infos.
        This method gets information from the raw data.
        Args:
            num_workers (int): Number of threads to be used. Default: 4.
            has_label (bool): Whether the data has label. Default: True.
            sample_id_list (list[int]): Index list of the sample.
                Default: None.
        Returns:
            infos (list[dict]): Information of the raw data.
        """

        def process_single_scene(sample_idx):
            # print(f'{self.split} sample_idx: {sample_idx}')
            info = dict()
            pc_info = {'num_features': 6, 'lidar_idx': sample_idx}
            info['point_cloud'] = pc_info
            pts_filename = osp.join(self.root_dir, 'stereo_instance_data',
                                    f'{sample_idx}_vert.npy')
            points = np.load(pts_filename)
            mmcv.mkdir_or_exist(osp.join(self.root_dir, 'points'))
            points.tofile(
                osp.join(self.root_dir, 'points', f'{sample_idx}.bin'))
            info['pts_path'] = osp.join('points', f'{sample_idx}.bin')

            if os.path.exists(osp.join(self.root_dir, 'posed_images')):
                info['intrinsics'] = self.get_intrinsics(sample_idx)
                all_extrinsics = self.get_extrinsics(sample_idx)
                all_img_paths = self.get_images(sample_idx)
                # some poses in Stereo are invalid
                extrinsics, img_paths = [], []
                for extrinsic, img_path in zip(all_extrinsics, all_img_paths):
                    if np.all(np.isfinite(extrinsic)):
                        img_paths.append(img_path)
                        extrinsics.append(extrinsic)
                info['extrinsics'] = extrinsics
                info['img_paths'] = img_paths

            if not self.test_mode:
                pts_instance_mask_path = osp.join(
                    self.root_dir, 'stereo_instance_data',
                    f'{sample_idx}_ins_label.npy')
                pts_semantic_mask_path = osp.join(
                    self.root_dir, 'stereo_instance_data',
                    f'{sample_idx}_sem_label.npy')

                pts_instance_mask = np.load(pts_instance_mask_path).astype(
                    np.long)
                pts_semantic_mask = np.load(pts_semantic_mask_path).astype(
                    np.long)

                mmcv.mkdir_or_exist(osp.join(self.root_dir, 'instance_mask'))
                mmcv.mkdir_or_exist(osp.join(self.root_dir, 'semantic_mask'))

                pts_instance_mask.tofile(
                    osp.join(self.root_dir, 'instance_mask',
                             f'{sample_idx}.bin'))
                pts_semantic_mask.tofile(
                    osp.join(self.root_dir, 'semantic_mask',
                             f'{sample_idx}.bin'))

                info['pts_instance_mask_path'] = osp.join(
                    'instance_mask', f'{sample_idx}.bin')
                info['pts_semantic_mask_path'] = osp.join(
                    'semantic_mask', f'{sample_idx}.bin')

            if has_label:
                annotations = {}
                # box is of shape [k, 6 + class]
                aligned_box_label = self.get_aligned_box_label(sample_idx)
                unaligned_box_label = self.get_unaligned_box_label(sample_idx)
                annotations['gt_num'] = aligned_box_label.shape[0]
                if annotations['gt_num'] != 0:
                    aligned_box = aligned_box_label[:, :-1]  # k, 6
                    unaligned_box = unaligned_box_label[:, :-1]
                    classes = aligned_box_label[:, -1]  # k
                    annotations['name'] = np.array([
                        self.label2cat[self.cat_ids2class[classes[i]]]
                        for i in range(annotations['gt_num'])
                    ])
                    # default names are given to aligned bbox for compatibility
                    # we also save unaligned bbox info with marked names
                    annotations['location'] = aligned_box[:, :3]
                    annotations['dimensions'] = aligned_box[:, 3:6]
                    annotations['gt_boxes_upright_depth'] = aligned_box
                    annotations['unaligned_location'] = unaligned_box[:, :3]
                    annotations['unaligned_dimensions'] = unaligned_box[:, 3:6]
                    annotations[
                        'unaligned_gt_boxes_upright_depth'] = unaligned_box
                    annotations['index'] = np.arange(
                        annotations['gt_num'], dtype=np.int32)
                    annotations['class'] = np.array([
                        self.cat_ids2class[classes[i]]
                        for i in range(annotations['gt_num'])
                    ])
                axis_align_matrix = self.get_axis_align_matrix(sample_idx)
                annotations['axis_align_matrix'] = axis_align_matrix  # 4x4
                info['annos'] = annotations
            return info

        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene, sample_id_list)
        return list(infos)

class StereoData(object):
    """Stereo data.
    Generate stereo infos for stereo_converter.
    Args:
        root_path (str): Root path of the raw data.
        split (str): Set split type of the data. Default: 'train'.
    """

    def __init__(self, root_path, split='train'):
        self.root_dir = root_path
        self.split = split
        self.split_dir = osp.join(root_path)
        self.num_perspective = 1
        self.classes = [
            'occupancy', 'non_occupancy'
        ]
        self.cat2label = {cat: self.classes.index(cat) for cat in self.classes}
        self.label2cat = {self.cat2label[t]: t for t in self.cat2label}
        self.cat_ids = np.array(
            [0, 1])
        self.cat_ids2class = {
            nyu40id: i
            for i, nyu40id in enumerate(list(self.cat_ids))
        }
        assert split in ['train', 'val', 'test']
        split_file = osp.join(self.root_dir, 'meta_data',
                              f'{split}.txt')
        check_file_exist(split_file)
        self.sample_id_list = list_from_file(split_file)
        self.test_mode = (split == 'test')

    def __len__(self):
        return len(self.sample_id_list)

    def get_aligned_box_label(self, idx):
        box_file = osp.join(self.root_dir, 'stereo_instance_data',
                            f'{idx}_aligned_bbox.npy')
        check_file_exist(box_file)
        return np.load(box_file)

    def get_unaligned_box_label(self, idx):
        box_file = osp.join(self.root_dir, 'stereo_instance_data',
                            f'{idx}_unaligned_bbox.npy')
        check_file_exist(box_file)
        return np.load(box_file)

    def get_axis_align_matrix(self, idx):
        matrix_file = osp.join(self.root_dir, 'stereo_instance_data',
                               f'{idx}_axis_align_matrix.npy')
        check_file_exist(matrix_file)
        return np.load(matrix_file)

    def get_images(self, idx):
        paths = []
        path = osp.join(self.root_dir, idx, 'image_02/data', idx)
        for file in sorted(os.listdir(path)):
            if file.endswith('.jpg'):
                paths.append(osp.join('posed_images', idx, file))
        return paths

    def get_extrinsics(self, idx):
        extrinsics = []
        path = osp.join(self.root_dir, idx, 'pose/data')
        for file in sorted(os.listdir(path)):
            if file.endswith('.npy'):
                extrinsics.append(np.loadtxt(osp.join(path, file)))
        return extrinsics

    def get_intrinsics(self, idx):
        matrix_file = osp.join(self.root_dir, idx, 'meta_data',
                               'intrinsic.txt')
        check_file_exist(matrix_file)
        return np.load(matrix_file)

    def get_occupancy_label(self, idx):
        occupancy_label_file = osp.join(self.root_dir, "stereo_instance_data",
                                        f'{idx}_occ_label.npy')
        check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)

    def get_pcts_transform_matrix(self, idx):
        transform_file = osp.join(self.root_dir, 'stereo_instance_data',
                                  f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
        """Get data infos.
        This method gets information from the raw data.
        Args:
            num_workers (int): Number of threads to be used. Default: 4.
            has_label (bool): Whether the data has label. Default: True.
            sample_id_list (list[int]): Index list of the sample.
                Default: None.
        Returns:
            infos (list[dict]): Information of the raw data.
        """

        def process_single_scene(sample_idx):
            print(f'{self.split} sample_idx: {sample_idx}')
            info = dict()
            pc_info = {'num_features': 6, 'lidar_idx': sample_idx}
            info['point_cloud'] = pc_info
            pts_filename = osp.join(self.root_dir, 'stereo_instance_data',
                                    f'{sample_idx}_vert.npy')
            points = np.load(pts_filename)

            mkdir_or_exist(osp.join(self.root_dir, 'points'))
            points.tofile(
                osp.join(self.root_dir, 'points', f'{sample_idx}.bin'))
            info['pts_path'] = osp.join('points', f'{sample_idx}.bin')

            if os.path.exists(osp.join(self.root_dir, 'posed_images')):
                info['intrinsics'] = self.get_intrinsics(sample_idx)
                all_extrinsics = self.get_extrinsics(sample_idx)
                all_img_paths = self.get_images(sample_idx)
                # some poses in Stereo are invalid
                extrinsics, img_paths = [], []
                for extrinsic, img_path in zip(all_extrinsics, all_img_paths):
                    if np.all(np.isfinite(extrinsic)):
                        img_paths.append(img_path)
                        extrinsics.append(extrinsic)
                info['extrinsics'] = extrinsics
                info['img_paths'] = img_paths

            if not self.test_mode:
                pts_instance_mask_path = osp.join(
                    self.root_dir, 'stereo_instance_data',
                    f'{sample_idx}_ins_label.npy')
                pts_semantic_mask_path = osp.join(
                    self.root_dir, 'stereo_instance_data',
                    f'{sample_idx}_sem_label.npy')

                pts_instance_mask = np.load(pts_instance_mask_path).astype(
                    np.long)
                pts_semantic_mask = np.load(pts_semantic_mask_path).astype(
                    np.long)

                mkdir_or_exist(osp.join(self.root_dir, 'instance_mask'))
                mkdir_or_exist(osp.join(self.root_dir, 'semantic_mask'))

                pts_instance_mask.tofile(
                    osp.join(self.root_dir, 'instance_mask',
                             f'{sample_idx}.bin'))
                pts_semantic_mask.tofile(
                    osp.join(self.root_dir, 'semantic_mask',
                             f'{sample_idx}.bin'))

                info['pts_instance_mask_path'] = osp.join(
                    'instance_mask', f'{sample_idx}.bin')
                info['pts_semantic_mask_path'] = osp.join(
                    'semantic_mask', f'{sample_idx}.bin')

            if has_label:
                annotations = {}
                # box is of shape [k, 6 + class]
                aligned_box_label = self.get_aligned_box_label(sample_idx)
                unaligned_box_label = self.get_unaligned_box_label(sample_idx)
                annotations['gt_num'] = aligned_box_label.shape[0]
                if annotations['gt_num'] != 0:
                    aligned_box = aligned_box_label[:, :-1]  # k, 6
                    unaligned_box = unaligned_box_label[:, :-1]
                    classes = aligned_box_label[:, -1]  # k
                    annotations['name'] = np.array([
                        self.label2cat[self.cat_ids2class[classes[i]]]
                        for i in range(annotations['gt_num'])
                    ])
                    # default names are given to aligned bbox for compatibility
                    # we also save unaligned bbox info with marked names
                    annotations['location'] = aligned_box[:, :3]
                    annotations['dimensions'] = aligned_box[:, 3:6]
                    annotations['gt_boxes_upright_depth'] = aligned_box
                    annotations['unaligned_location'] = unaligned_box[:, :3]
                    annotations['unaligned_dimensions'] = unaligned_box[:, 3:6]
                    annotations[
                        'unaligned_gt_boxes_upright_depth'] = unaligned_box
                    annotations['index'] = np.arange(
                        annotations['gt_num'], dtype=np.int32)
                    annotations['class'] = np.array([
                        self.cat_ids2class[classes[i]]
                        for i in range(annotations['gt_num'])
                    ])
                axis_align_matrix = self.get_axis_align_matrix(sample_idx)
                annotations['axis_align_matrix'] = axis_align_matrix  # 4x4
                info['annos'] = annotations
            return info

        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene, sample_id_list)
        return list(infos)

class ColmapKujialeOccupancy(KujialeData):

    def get_occupancy_label(self, idx):
        occupancy_label_file = osp.join(self.root_dir, "stereo_instance_data",
                                        f'{idx}_occ_label.npy')
        check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)

    def get_pcts_transform_matrix(self, idx):
        transform_file = osp.join(self.root_dir, 'stereo_instance_data',
                                  f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
        """Get data infos.
        This method gets information from the raw data.
        Args:
            num_workers (int): Number of threads to be used. Default: 4.
            has_label (bool): Whether the data has label. Default: True.
            sample_id_list (list[int]): Index list of the sample.
                Default: None.
        Returns:
            infos (list[dict]): Information of the raw data.
        """

        def process_single_scene(sample_idx):
            print(f'{self.split} sample_idx: {sample_idx}')
            info = dict()

            # update with RGB image paths if exist
            objs = os.listdir(osp.join(self.root_dir, sample_idx, 'occupancy_label/data'))
            objs.sort()

            occupancy_labels, total_img_info, total_img_prefix, total_lidar2imgs, total_occupancy_label_info = [], [], [], [], []
            annotations = {}
            for i in range(self.num_perspective, len(objs)):
                start_index = i - self.num_perspective
                filepaths = objs[start_index:i]
                img_info, img_prefix, lidar2imgs, occupancy_label_info = [], [], [], []
                for filepath in filepaths:
                    filename = filepath.split('.')[0]
                    stereo_img_filename, stereo_img_prefix = {}, {}
                    left_img_prefix = osp.join(self.root_dir, sample_idx, 'camera2/data')
                    stereo_img_filename['left_img'] = {'filename': filename + '.jpg'}
                    stereo_img_filename['left_img'] = {'filename': filename + '.jpg'}
                    stereo_img_prefix['left_img'] = left_img_prefix

                    right_img_prefix = osp.join(self.root_dir, sample_idx, 'camera4/data')
                    stereo_img_filename['right_img'] = {'filename': filename + '.jpg'}
                    stereo_img_prefix['right_img'] = right_img_prefix

                    img_info.append(stereo_img_filename)
                    img_prefix.append(stereo_img_prefix)

                    lidar2img_path = osp.join(self.root_dir, sample_idx, 'pose/data', filename + '.npy')
                    if not os.path.exists(lidar2img_path):
                        lidar2imgs.append(None)
                    else:
                        lidar2imgs.append(np.load(lidar2img_path))

                    # lable
                    occupancy_label_path = osp.join(self.root_dir, sample_idx, 'occupancy/data', filename + '.npy')
                    assert os.path.exists(occupancy_label_path)
                    occupancy_label_info.append(occupancy_label_path)
                total_img_info.append(img_info)
                total_img_prefix.append(img_prefix)
                total_lidar2imgs.append({'extrinsic': lidar2imgs})
                total_occupancy_label_info.append(occupancy_label_info)

            right2lidar = osp.join(self.root_dir, 'intrinsic_extrinsic', 'left2right.npy')
            assert os.path.exists(right2lidar)
            right2lidar = np.load(right2lidar)

            info['right2lidar'] = right2lidar
            info['lidar2img'] = total_lidar2imgs
            info['img_info'] = total_img_info
            info['img_prefix'] = total_img_prefix

            annotations['occupancy_label_info'] = total_occupancy_label_info
            annotations['class'] = self.cat_ids2class
            info['annos'] = annotations
            return info

        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list

        for sample_idx in sample_id_list:
            process_single_scene(sample_idx)
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene, sample_id_list)
        # infos = list(infos)
        # for scene_info in infos:
        #     pass
        return list(infos)

class KujialeStereo(KujialeData):

    def get_occupancy_label(self, idx):
        occupancy_label_file = osp.join(self.root_dir, "stereo_instance_data",
                                        f'{idx}_occ_label.npy')
        check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)

    def get_pcts_transform_matrix(self, idx):
        transform_file = osp.join(self.root_dir, 'stereo_instance_data',
                                  f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
        """Get data infos.
        This method gets information from the raw data.
        Args:
            num_workers (int): Number of threads to be used. Default: 4.
            has_label (bool): Whether the data has label. Default: True.
            sample_id_list (list[int]): Index list of the sample.
                Default: None.
        Returns:
            infos (list[dict]): Information of the raw data.
        """

        def process_single_scene(sample_idx):
            print(f'{self.split} sample_idx: {sample_idx}')
            infos = []

            # update with RGB image paths if exist
            # camera_left = ['camera2']
            # camera_right = ['camera4']
            camera_left = ['camera0', 'camera2', 'camera5']
            camera_right = ['camera1', 'camera4', 'camera6']

            # 初始化存放路径的列表
            objs_left = []
            objs_right = []

            # 通过循环遍历camera_left列表，添加对应的文件夹路径到objs_left
            for camera in camera_left:
                objs_left += glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, f'{camera}/*_rgb.jpg'),
                                       recursive=True)

            # 通过循环遍历camera_right列表，添加对应的文件夹路径到objs_right
            for camera in camera_right:
                objs_right += glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, f'{camera}/*_rgb.jpg'),
                                        recursive=True)

            # 对获取的路径进行排序
            objs_left.sort()
            objs_right.sort()

            dataset_name = self.root_dir.split('/')[-1]  # *.npy

            for i in range(len(objs_left)):
                info = {}

                # /home/<USER>/kws_deliver_0319/scenes/3FO3OFAUA1GR/camera0/3FO3OFAUA1GR_camera0_000001_rgb.jpg
                src_image_left = objs_left[i]
                src_image_right = objs_right[i]
                scene_token = '_'.join(src_image_left.split('/')[-1].split('_')[i] for i in [0, 2, 1])
                scene_token = scene_token + '_' + src_image_right.split('/')[-1].split('_')[1]

                # occupancy label
                camera = src_image_left.split('/')[-2]

                # stereo 的 bev 标签是直接使用 rgbd 生成的 bev，所以 replace 到 bev_rgbd 文件夹
                bev_path = src_image_left.replace(camera, '_bev_rgbd/' + camera, 1).replace('rgb.jpg', 'bev.png')

                occ_path = bev_path.replace('_bev_rgbd/', 'occ_stereo_new/', 1).replace('bev.png', 'occ.npy')

                left_depth_path = src_image_left.replace('_rgb.jpg', '_depth.png')
                right_depth_path = src_image_right.replace('_rgb.jpg', '_depth.png')

                info['token'] = scene_token
                info['left_image_path'] = src_image_left.replace(self.root_dir, '')[1:]
                info['right_image_path'] = src_image_right.replace(self.root_dir, '')[1:]
                info['left_depth_path'] = left_depth_path.replace(self.root_dir, '')[1:]
                info['right_depth_path'] = right_depth_path.replace(self.root_dir, '')[1:]
                info['occ_path'] = occ_path.replace(self.root_dir, '')[1:]
                info['bev_path'] = bev_path.replace(self.root_dir, '')[1:]

                infos.append(info)
            return infos

        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list

        # for sample_idx in sample_id_list:
        #     process_single_scene(sample_idx)
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene, sample_id_list)
        # infos = list(infos)
        totol_infos = []
        for scene_info in infos:
            totol_infos = totol_infos + scene_info

        # left2right_path = osp.join(self.root_dir, 'intrinsic_extrinsic', 'left2middle.npy')  # cam3, cam3-B
        left2right_path = osp.join(self.root_dir, 'intrinsic_extrinsic', 'left2right.npy')  # extrinsic_RL
        right2left = np.linalg.inv(np.load(left2right_path))   # 若训练不对则去掉
        left2left = np.array([
            [1., 0., 0., 0.],
            [0., 1., 0., 0.],
            [0., 0., 1., 0.],
            [0., 0., 0., 1.]
        ])

        left_intrinsic = np.load(osp.join(self.root_dir, 'intrinsic_extrinsic', 'left_intrinsic.npy'))
        right_intrinsic = np.load(osp.join(self.root_dir, 'intrinsic_extrinsic', 'right_intrinsic.npy'))

        # right_intrinsic = np.array([
        #     [637.4346235544159, 0., 560., 0.],
        #     [0., 637.4346235544159, 477, 0.],
        #     [0., 0., 1., 0.],
        #     [0., 0., 0., 1.]
        # ])

        totol_infos = {
            # stereo 4个参数
            'right2left': right2left,
            'left2left': left2left,
            'left_intrinsic': left_intrinsic,
            'right_intrinsic': right_intrinsic,

            'infos': totol_infos,
        }

        return totol_infos


class KujialeStereo3(KujialeData):

    def get_occupancy_label(self, idx):
        occupancy_label_file = osp.join(self.root_dir, "stereo_instance_data",
                                        f'{idx}_occ_label.npy')
        check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)

    def get_pcts_transform_matrix(self, idx):
        transform_file = osp.join(self.root_dir, 'stereo_instance_data',
                                  f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
        """Get data infos.
        This method gets information from the raw data.
        Args:
            num_workers (int): Number of threads to be used. Default: 4.
            has_label (bool): Whether the data has label. Default: True.
            sample_id_list (list[int]): Index list of the sample.
                Default: None.
        Returns:
            infos (list[dict]): Information of the raw data.
        """

        def process_single_scene(sample_idx):
            print(f'{self.split} sample_idx: {sample_idx}')
            infos = []

            # update with RGB image paths if exist
            camera_left = ['camera2']
            camera_right = ['camera4']
            camera_middle = ['camera3']
            # camera_left = ['camera0', 'camera2', 'camera5']
            # camera_right = ['camera1', 'camera4', 'camera6']

            # 初始化存放路径的列表
            objs_left = []
            objs_right = []
            objs_middle = []

            # 通过循环遍历camera_left列表，添加对应的文件夹路径到objs_left
            for camera in camera_left:
                objs_left += glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, f'{camera}/*_rgb.jpg'),
                                       recursive=True)

            # 通过循环遍历camera_right列表，添加对应的文件夹路径到objs_right
            for camera in camera_right:
                objs_right += glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, f'{camera}/*_rgb.jpg'),
                                        recursive=True)

            # 通过循环遍历camera_middle列表，添加对应的文件夹路径到objs_right
            for camera in camera_middle:
                objs_middle += glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, f'{camera}/*_rgb.jpg'),
                                         recursive=True)

            # 对获取的路径进行排序
            objs_left.sort()
            objs_right.sort()
            objs_middle.sort()

            dataset_name = self.root_dir.split('/')[-1]  # *.npy

            for i in range(len(objs_left)):
                info = {}

                # /home/<USER>/kws_deliver_0319/scenes/3FO3OFAUA1GR/camera0/3FO3OFAUA1GR_camera0_000001_rgb.jpg
                src_image_left = objs_left[i]
                src_image_right = objs_right[i]
                src_image_middle = objs_middle[i]
                scene_token = '_'.join(src_image_left.split('/')[-1].split('_')[i] for i in [0, 2, 1])
                scene_token = scene_token + '_' + src_image_right.split('/')[-1].split('_')[1]

                # occupancy label
                camera = src_image_left.split('/')[-2]

                # stereo 的 bev 标签是直接使用 rgbd 生成的 bev，所以 replace 到 bev_rgbd 文件夹
                bev_path = src_image_left.replace(camera, '_bev_rgbd/' + camera, 1).replace('rgb.jpg', 'bev.png')

                occ_path = bev_path.replace('_bev_rgbd/', 'occ_stereo/', 1).replace('bev.png', 'occ.npy')

                # right_depth_path = right_imgpath.replace('_rgb.jpg', '_depth.png')

                info['token'] = scene_token
                info['left_image_path'] = src_image_left.replace(self.root_dir, '')[1:]
                info['right_image_path'] = src_image_right.replace(self.root_dir, '')[1:]
                info['middle_image_path'] = src_image_middle.replace(self.root_dir, '')[1:]
                info['occ_path'] = occ_path.replace(self.root_dir, '')[1:]
                info['bev_path'] = bev_path.replace(self.root_dir, '')[1:]

                infos.append(info)
            return infos

        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list

        # for sample_idx in sample_id_list:
        #     process_single_scene(sample_idx)
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene, sample_id_list)
        # infos = list(infos)
        totol_infos = []
        for scene_info in infos:
            totol_infos = totol_infos + scene_info

        left2right_path = osp.join(self.root_dir, 'intrinsic_extrinsic', 'left2right.npy')  # extrinsic_RL
        right2left = np.linalg.inv(np.load(left2right_path))  # 若训练不对则去掉

        left2middle_path = osp.join(self.root_dir, 'intrinsic_extrinsic', 'left2middle.npy')
        middle2left = np.linalg.inv(np.load(left2middle_path))

        left2left = np.array([
            [1., 0., 0., 0.],
            [0., 1., 0., 0.],
            [0., 0., 1., 0.],
            [0., 0., 0., 1.]
        ])

        left_intrinsic = np.load(osp.join(self.root_dir, 'intrinsic_extrinsic', 'left_intrinsic.npy'))
        right_intrinsic = np.load(osp.join(self.root_dir, 'intrinsic_extrinsic', 'right_intrinsic.npy'))
        middle_intrinsic = np.load(osp.join(self.root_dir, 'intrinsic_extrinsic', 'middle_intrinsic.npy'))

        totol_infos = {
            # stereo 4个参数
            'right2left': right2left,
            'middle2left': middle2left,
            'left2left': left2left,
            'left_intrinsic': left_intrinsic,
            'right_intrinsic': right_intrinsic,
            'middle_intrinsic': middle_intrinsic,

            'infos': totol_infos,
        }

        return totol_infos


class KujialeFusion(KujialeData):

    def get_occupancy_label(self, idx):
        occupancy_label_file = osp.join(self.root_dir, "stereo_instance_data",
                                        f'{idx}_occ_label.npy')
        mmcv.check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)

    def get_pcts_transform_matrix(self, idx):
        transform_file = osp.join(self.root_dir, 'stereo_instance_data',
                                  f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):

        def process_single_scene(sample_idx):

            print(f'{self.split} sample_idx: {sample_idx}')
            infos = []

            objs = glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, 'camera3/*.jpg'), recursive=True)
            objs += glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, 'camera0/*.jpg'), recursive=True)
            objs += glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, 'camera5/*.jpg'), recursive=True)
            objs.sort()

            for i in range(len(objs)):
                info = {}
                src_occ_path = objs[i]
                # filename = src_occ_path.split('/')[-1].split('.')[0]
                scene_token = '_'.join(src_occ_path.split('/')[-1].split('_')[i] for i in [0, 2, 1])
                camera = src_occ_path.split('/')[-2]

                # occupancy label
                bev_path = src_occ_path.replace(camera, '_bev_rgbd/' + camera, 1).replace('rgb.jpg', 'bev.png')  # bev 路径
                if not os.path.exists(bev_path):
                    continue
                occ_path = bev_path.replace('_bev_rgbd/', 'occ_rgbd_1.5m_new_new/', 1).replace('bev.png', 'occ.npy')
                if not os.path.exists(occ_path):
                    continue
                lidar_path = bev_path.replace('_bev_rgbd/', 'points_tof_56/', 1).replace('bev.png', 'pcd.npy')
                if not os.path.exists(lidar_path):
                    continue
                lidar_path_150cm = bev_path.replace('_bev_rgbd/', 'points_tof_150cm/', 1).replace('bev.png', 'pcd.npy')
                lidar1_path = bev_path.replace('_bev_rgbd/', 'points_tof1/', 1).replace('bev.png', 'pcd.npy')

                # img_path = bev_path.replace('bev/', '').replace('bev.png', 'rgb.jpg')

                pose_path = src_occ_path.replace('rgb.jpg', 'pose.json')
                with open(pose_path) as f:
                    # 加载JSON数据
                    pose = json.load(f)

                left_intrinsic = np.array(pose['intrinsic'])
                left_extrinsic = np.array(pose['extrinsic'])  # 这个是Tcw

                # [1:] 去掉 前面的'/'

                if os.path.exists(src_occ_path) and os.path.exists(lidar_path) and os.path.exists(occ_path) and os.path.exists(bev_path):
                    info['token'] = scene_token
                    info['cams'] = src_occ_path.replace(self.root_dir, '')[1:]
                    info['lidar_path'] = lidar_path.replace(self.root_dir, '')[1:]
                    info['lidar1_path'] = lidar1_path.replace(self.root_dir, '')[1:]
                    info['lidar_path_150cm'] = lidar_path_150cm.replace(self.root_dir, '')[1:]
                    info['occ_path'] = occ_path.replace(self.root_dir, '')[1:]
                    info['bev_path'] = bev_path.replace(self.root_dir, '')[1:]
                    info['intrinsic'] = left_intrinsic  # / 2.

                    infos.append(info)
                else:
                    print(src_occ_path)
                    print(occ_path)
            return infos

        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list

        # for sample_idx in sample_id_list:
        #     process_single_scene(sample_idx)
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene, sample_id_list)
        # infos = list(infos)
        totol_infos = []
        for scene_info in infos:
            totol_infos = totol_infos + scene_info

        cam2lidar = np.array([
            [1, 0, 0, 0],
            [0, 0, 1, 0],
            [0, -1, 0, 0],
            [0, 0, 0, 1]], dtype=np.float64)

        totol_infos = {
            # fusion 1个参数
            'cam2lidar': cam2lidar,
            'infos': totol_infos,
        }

        return totol_infos

class KujialeFusion_(KujialeData):

    def get_occupancy_label(self, idx):
        occupancy_label_file = osp.join(self.root_dir, "stereo_instance_data",
                                        f'{idx}_occ_label.npy')
        mmcv.check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)

    def get_pcts_transform_matrix(self, idx):
        transform_file = osp.join(self.root_dir, 'stereo_instance_data',
                                  f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
        """Get data infos.
        This method gets information from the raw data.
        Args:
            num_workers (int): Number of threads to be used. Default: 4.
            has_label (bool): Whether the data has label. Default: True.
            sample_id_list (list[int]): Index list of the sample.
                Default: None.
        Returns:
            infos (list[dict]): Information of the raw data.
        """

        def process_single_scene(sample_idx):

            print(f'{self.split} sample_idx: {sample_idx}')
            infos = []

            objs = glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, 'camera3-B/*.jpg'), recursive=True)
            # print(objs)
            # objs += glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, 'camera0/*.jpg'), recursive=True)
            # objs += glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, 'camera5/*.jpg'), recursive=True)
            objs.sort()

            for i in range(len(objs)):
                info = {}
                src_occ_path = objs[i]
                # filename = src_occ_path.split('/')[-1].split('.')[0]
                scene_token = '_'.join(src_occ_path.split('/')[-1].split('_')[i] for i in [0, 2, 1])
                camera = src_occ_path.split('/')[-2]

                # occupancy label
                # bev_path = src_occ_path.replace(camera, '_bev_rgbd/' + camera, 1).replace('rgb.jpg', 'bev.png')  # bev 路径
                bev_path = src_occ_path.replace(camera, '_bev_rgbd/' + camera, 1).replace(camera, 'camera3').replace('rgb.jpg', 'bev.png')  # bev 路径
                if not os.path.exists(bev_path):
                    continue
                occ_path = bev_path.replace('_bev_rgbd/', 'occ_rgbd/', 1).replace('bev.png', 'occ.npy')
                if not os.path.exists(occ_path):
                    continue
                lidar_path = bev_path.replace('_bev_rgbd/', 'points_tof_56/', 1).replace('bev.png', 'pcd.npy')
                if not os.path.exists(lidar_path):
                    continue
                # img_path = bev_path.replace('bev/', '').replace('bev.png', 'rgb.jpg')

                pose_path = src_occ_path.replace('rgb.jpg', 'pose.json')
                with open(pose_path) as f:
                    # 加载JSON数据
                    pose = json.load(f)

                left_intrinsic = np.array(pose['intrinsic'])
                left_extrinsic = np.array(pose['extrinsic'])  # 这个是Tcw

                # [1:] 去掉 前面的'/'

                if os.path.exists(src_occ_path) and os.path.exists(lidar_path) and os.path.exists(occ_path) and os.path.exists(bev_path):
                    info['token'] = scene_token
                    info['cams'] = src_occ_path.replace(self.root_dir, '')[1:]
                    info['lidar_path'] = lidar_path.replace(self.root_dir, '')[1:]
                    info['occ_path'] = occ_path.replace(self.root_dir, '')[1:]
                    info['bev_path'] = bev_path.replace(self.root_dir, '')[1:]
                    info['intrinsic'] = left_intrinsic  # / 2.

                    # ego2global_tr = np.linalg.inv(left_extrinsic)  # 得到Twc 把相机当作车体坐标系
                    # ego2global_translation = ego2global_tr[:3, 3]
                    # ego2global_rotation = ego2global_tr[:3, :3]
                    # lidar2ego_translation = np.array([0, 0, 0], dtype=np.float64)
                    #
                    # lidar2ego_rotation = np.array([
                    #     [1., 0., 0.0],
                    #     [0, 1., 0.],
                    #     [0., 0., 1.]], dtype=np.float64)

                    # cam2lidar = np.array([
                    #     [1, 0, 0, 0],
                    #     [0, 0, 1, 0],
                    #     [0, -1, 0, 0],
                    #     [0, 0, 0, 1]], dtype=np.float64)

                    # info['ego2global_translation'] = ego2global_translation
                    # info['ego2global_rotation'] = ego2global_rotation
                    # info['lidar2ego_translation'] = lidar2ego_translation
                    # info['lidar2ego_rotation'] = lidar2ego_rotation

                    # info['cam2lidar'] = cam2lidar

                    infos.append(info)
                else:
                    print(src_occ_path)
                    print(occ_path)
            return infos

        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list

        # for sample_idx in sample_id_list:
        #     process_single_scene(sample_idx)
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene, sample_id_list)
        # infos = list(infos)
        totol_infos = []
        for scene_info in infos:
            totol_infos = totol_infos + scene_info

        cam2lidar = np.array([
            [1, 0, 0, 0],
            [0, 0, 1, 0],
            [0, -1, 0, 0],
            [0, 0, 0, 1]], dtype=np.float64)

        totol_infos = {
            # fusion 1个参数
            'cam2lidar': cam2lidar,
            'infos': totol_infos,
        }

        return totol_infos


class ZedStereo(KujialeData):

    def get_occupancy_label(self, idx):
        occupancy_label_file = osp.join(self.root_dir, "stereo_instance_data",
                                        f'{idx}_occ_label.npy')
        check_file_exist(occupancy_label_file)
        return np.load(occupancy_label_file)

    def get_pcts_transform_matrix(self, idx):
        transform_file = osp.join(self.root_dir, 'stereo_instance_data',
                                  f"{idx}_scale_mat.npy")
        return np.load(transform_file)

    def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
        """Get data infos.
        This method gets information from the raw data.
        Args:
            num_workers (int): Number of threads to be used. Default: 4.
            has_label (bool): Whether the data has label. Default: True.
            sample_id_list (list[int]): Index list of the sample.
                Default: None.
        Returns:
            infos (list[dict]): Information of the raw data.
        """

        def process_single_scene(sample_idx):
            print(f'{self.split} sample_idx: {sample_idx}')
            infos = []

            # update with RGB image paths if exist
            camera_left = ['left']
            camera_right = ['right']

            # 初始化存放路径的列表
            objs_left = []
            objs_right = []

            # 通过循环遍历camera_left列表，添加对应的文件夹路径到objs_left
            for camera in camera_left:
                objs_left += glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, f'{camera}/*.jpg'),
                                       recursive=True)

            # 通过循环遍历camera_right列表，添加对应的文件夹路径到objs_right
            for camera in camera_right:
                objs_right += glob.glob(osp.join(self.root_dir, 'scenes', sample_idx, f'{camera}/*.jpg'),
                                        recursive=True)

            # 对获取的路径进行排序
            objs_left.sort()
            objs_right.sort()

            dataset_name = self.root_dir.split('/')[-1]  # *.npy

            for i in range(len(objs_left)):
                info = {}

                # /home/<USER>/kws_deliver_0319/scenes/3FO3OFAUA1GR/camera0/3FO3OFAUA1GR_camera0_000001_rgb.jpg
                src_image_left = objs_left[i]
                src_image_right = objs_right[i]

                scene_token = sample_idx + '-' + src_image_left.split('/')[-1]

                # # occupancy label
                # camera = src_image_left.split('/')[-2]
                #
                #
                # # stereo 的 bev 标签是直接使用 rgbd 生成的 bev，所以 replace 到 bev_rgbd 文件夹
                # bev_path = src_image_left.replace(camera, '_bev_rgbd/' + camera, 1).replace('rgb.jpg', 'bev.png')
                #
                # occ_path = bev_path.replace('_bev_rgbd/', 'occ_stereo/', 1).replace('bev.png', 'occ.npy')

                # right_depth_path = right_imgpath.replace('_rgb.jpg', '_depth.png')

                info['token'] = scene_token
                info['left_image_path'] = src_image_left.replace(self.root_dir, '')[1:]  # [1:] 去掉 '/'
                info['right_image_path'] = src_image_right.replace(self.root_dir, '')[1:]
                info['occ_path'] = None
                info['bev_path'] = None

                infos.append(info)
            return infos

        sample_id_list = sample_id_list if sample_id_list is not None \
            else self.sample_id_list

        # for sample_idx in sample_id_list:
        #     process_single_scene(sample_idx)
        with futures.ThreadPoolExecutor(num_workers) as executor:
            infos = executor.map(process_single_scene, sample_id_list)
        # infos = list(infos)
        totol_infos = []
        for scene_info in infos:
            totol_infos = totol_infos + scene_info

        # left2right_path = osp.join(self.root_dir, 'intrinsic_extrinsic', 'left2right.npy')  # extrinsic_RL
        # right2left = np.linalg.inv(np.load(left2right_path))    # 若训练不对则去掉
        right2left = np.array([
            [0.9999996074191118, -0.0001663949801876781, -0.0008703300150569494, -0.06282940483827766],
            [0.00016526031905401343, 0.9999991366274906, -0.001303623144355979, 7.545870246627618e-05],
            [0.0008705461799852165, 0.0013034788015624753, 0.9999987715454277, -0.00011347695302428774],
            [0.0, 0.0, 0.0, 1.0]
        ])

        left2left = np.array([
            [1., 0., 0., 0.],
            [0., 1., 0., 0.],
            [0., 0., 1., 0.],
            [0., 0., 0., 1.]
        ])

        left_intrinsic = np.load(osp.join(self.root_dir, 'intrinsic_extrinsic', 'left_intrinsic.npy'))
        right_intrinsic = np.load(osp.join(self.root_dir, 'intrinsic_extrinsic', 'right_intrinsic.npy'))

        totol_infos = {
            # stereo 4个参数
            'right2left': right2left,
            'left2left': left2left,
            'left_intrinsic': left_intrinsic,
            'right_intrinsic': right_intrinsic,

            'infos': totol_infos,
        }

        return totol_infos


# root_dir_ = "/home/<USER>/kws_deliver_0325/scenes"


# class KujialeFusion(StereoData):
#
#     def get_occupancy_label(self, idx):
#         occupancy_label_file = osp.join(self.root_dir, "stereo_instance_data",
#                                         f'{idx}_occ_label.npy')
#         check_file_exist(occupancy_label_file)
#         return np.load(occupancy_label_file)
#
#     def get_pcts_transform_matrix(self, idx):
#         transform_file = osp.join(self.root_dir, 'stereo_instance_data',
#                                   f"{idx}_scale_mat.npy")
#         return np.load(transform_file)
#
#     def get_infos(self, num_workers=4, has_label=True, sample_id_list=None):
#         """Get data infos.
#         This method gets information from the raw data.
#         Args:
#             num_workers (int): Number of threads to be used. Default: 4.
#             has_label (bool): Whether the data has label. Default: True.
#             sample_id_list (list[int]): Index list of the sample.
#                 Default: None.
#         Returns:
#             infos (list[dict]): Information of the raw data.
#         """
#
#         def process_single_scene(sample_idx):
#
#             # 这里对每一个sample_idx也就是对应的家庭户分别处理
#             print(f'{self.split} sample_idx: {sample_idx}')
#             infos = []
#
#             # update with RGB image paths if exist
#             # objs = glob.glob(osp.join(self.root_dir, sample_idx, 'occupancy_label_for_map_lidar/*.npy'), recursive=True)
#
#             # objs = glob.glob(osp.join(self.root_dir,'bev', sample_idx, '*.png'), recursive=True)
#
#             # objs = glob.glob(osp.join(self.root_dir,sample_idx,'bev/camera3','*.png'), recursive=True)
#
#             objs = glob.glob(osp.join(self.root_dir, sample_idx, '*.png'), recursive=True)
#
#             objs.sort()
#
#             # 这里应该是bev map的路径
#
#             dataset_name = self.root_dir.split('/')[-1]
#
#             # left2right_path = osp.join(self.root_dir, 'intrinsic_extrinsic', 'left2right.npy')
#             # right2left = np.linalg.inv(np.load(left2right_path))
#             # left2left = np.array([
#             #     [1., 0., 0., 0.],
#             #     [0., 1., 0., 0.],
#             #     [0., 0., 1., 0.],
#             #     [0., 0., 0., 1.]
#             # ])
#
#             # 这里我们认为left就是当前相机
#
#             # left_intrinsic = np.load(osp.join(self.root_dir, 'intrinsic_extrinsic', 'left_intrinsic.npy'))#这里对于每一个数据单独存内参
#
#             # 内参
#
#             # right_intrinsic = np.load(osp.join(self.root_dir, 'intrinsic_extrinsic', 'right_intrinsic.npy'))
#
#             for i in range(len(objs)):
#                 info = {}
#                 src_occ_path = objs[i]
#                 filename = src_occ_path.split('/')[-1].split('.')[0]  # 这个主要用于后面的时间戳
#
#                 # occupancy label
#                 bev_path = src_occ_path.split(dataset_name)[-1][1:]  # bev 路径
#
#                 # lidar_path = 'points_56'+ bev_path[3:-7] +'pcd.npy'#点云路径
#
#                 lidar_path = bev_path.replace('bev_rgbd', 'points_tof_56')[:-7] + 'pcd.npy'
#
#                 # img_path = bev_path[3:-7]+"rgb.jpg"
#
#                 img_path = bev_path.replace('bev_rgbd/', '').replace('bev.png', 'rgb.jpg')
#
#                 info['lidar_path'] = os.path.join(root_dir_, lidar_path)
#
#                 info['bev_path'] = os.path.join(root_dir_, bev_path)
#
#                 # info['img_path'] = os.path.join(root_dir_, img_path)
#
#                 # imgs
#                 # data_path = []
#                 # left_imgpath = lidar_path.replace('occupancy_label_for_map_lidar', 'image_02')
#                 # left_imgpath = left_imgpath.replace('.npy', '.jpg')
#                 # data_path.append(os.path.join(root_dir_, left_imgpath))
#
#                 # right_imgpath = lidar_path.replace('occupancy', 'image_03')
#                 # right_imgpath = right_imgpath.replace('.npy', '.jpg')
#                 # data_path.append(right_imgpath)
#
#                 # info['imgs2lidar'] = np.array([
#                 #                                 [1., 0., 0.0],
#                 #                                 [0.0, 1., 0.],
#                 #                                 [0., 0., 1.]], dtype=np.float64)  #这个后面代码里面算。这里不存
#
#                 pose_path = (self.root_dir + '/' + img_path)[:-7] + 'pose.json'
#
#                 with open(pose_path) as f:
#                     # 加载JSON数据
#                     pose = json.load(f)
#
#                 left_intrinsic = np.array(pose['intrinsic'])
#
#                 left_extrinsic = np.array(pose['extrinsic'])  # 这个是Tcw
#
#                 # left_intrinsic =np.load((self.root_dir+img_path)[:-7]+"pose.json")
#
#                 # left_intrinsic = np.load(osp.join(self.root_dir, 'intrinsic_extrinsic', 'right_intrinsic.npy'))
#
#                 info['intrinsic'] = left_intrinsic  # / 2.
#
#                 # img_path = img_path[1:]
#
#                 info['cams'] = {"CAM_FRONT": os.path.join(root_dir_, img_path)}  # 这里包含所有相机（目前只用了一个相机）的路径
#
#                 info['timestamp'] = filename[-10:-4]
#
#                 cam2lidar = np.array([
#                     [1, 0, 0, 0],
#                     [0, 0, 1, 0],
#                     [0, -1, 0, 0],
#                     [0, 0, 0, 1]], dtype=np.float64)
#
#                 # ego2global_tr = np.linalg.inv(left_extrinsic)#得到Twc
#
#                 ego2global_tr = np.linalg.inv(cam2lidar @ left_extrinsic)  # 得到Twc
#
#                 # ego2global_translation = np.array([0,0,0], dtype=np.float64)
#
#                 # ego2global_rotation = np.array([
#                 #                         [1., 0., 0.0],
#                 #                         [0.0, 1., 0.],
#                 #                         [0., 0., 1.]], dtype=np.float64)
#
#                 ego2global_translation = ego2global_tr[:3, 3]
#
#                 ego2global_rotation = ego2global_tr[:3, :3]
#
#                 lidar2ego_translation = np.array([0, 0, 0], dtype=np.float64)
#
#                 lidar2ego_rotation = np.array([
#                     [1., 0., 0.0],
#                     [0.0, 1., 0.],
#                     [0., 0., 1.]], dtype=np.float64)
#
#                 info['ego2global_translation'] = ego2global_translation
#                 info['ego2global_rotation'] = ego2global_rotation
#                 info['lidar2ego_translation'] = lidar2ego_translation
#                 info['lidar2ego_rotation'] = lidar2ego_rotation
#
#                 info['cam2lidar'] = cam2lidar
#
#                 infos.append(info)
#             return infos
#
#         sample_id_list = sample_id_list if sample_id_list is not None \
#             else self.sample_id_list
#
#         for sample_idx in sample_id_list:
#             process_single_scene(sample_idx)
#         with futures.ThreadPoolExecutor(num_workers) as executor:
#             infos = executor.map(process_single_scene, sample_id_list)
#         # infos = list(infos)
#         totol_infos = []
#         for scene_info in infos:
#             totol_infos = totol_infos + scene_info
#         return totol_infos
