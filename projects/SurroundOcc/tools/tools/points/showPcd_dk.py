import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
import os
import time


class PointCloudVisualizer:
    def __init__(self, folder_path):
        self.folder_path = folder_path
        self.files = sorted([file for file in os.listdir(folder_path) if file.endswith('.pcd')])
        self.index = 0  # 当前显示的文件索引
        self.vis = o3d.visualization.VisualizerWithKeyCallback()
        self.vis.create_window()

        self.origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.4, origin=[0, 0, 0])
        self.vis.register_key_callback(262, self.next_callback)  # Right key
        self.vis.register_key_callback(263, self.previous_callback)  # Left key

    def set_custom_viewpoint(self, vis):
        ctr = vis.get_view_control()
        ctr.set_lookat([0.082356285770728621, 0.12500918059420799, 1.2065578989324817])
        ctr.set_front([-0.25967809953642768, -0.37328082918973515, -0.89063387942553196])
        ctr.set_up([0.031435381830351189, -0.92505090009996083, 0.37854015506077077])
        ctr.set_zoom(0.55999999999999983)

    def load_pcd(self, index):
        # 载入点云
        pcd_path = os.path.join(self.folder_path, self.files[index])
        pcd = o3d.io.read_point_cloud(pcd_path)
        return pcd

    def process_pcd(self, pcd):
        # 变换、裁剪和 FOV 过滤
        T_oms_dk = np.array([
            [1.00008513, 0.01026008, 0.00913286, 0.01722355],
            [-0.01236991, 0.96158858, 0.27421972, -0.1017069],
            [-0.00596806, -0.27430616, 0.96163204, -0.05610114],
            [0., 0., 0., 1.]], dtype=np.float64)
        T_oms_lidar = np.linalg.inv(np.array([
            [0.0038578, 0.24938, 0.96839, 0.207344],
            [-0.9998, 0.00601088, 0.0024357, -0.0144162],
            [-0.005213, -0.968386, 0.24939867, -0.17134],
            [0, 0, 0, 1]
        ], dtype=np.float64))
        occupancy_range = np.array([[-2, -0.15, 0], [2, 0.02, 4]])
        bounding_box = o3d.geometry.AxisAlignedBoundingBox(occupancy_range[0], occupancy_range[1])
        pcd.transform(T_oms_dk)
        # pcd.transform(T_oms_lidar)
        pcd = pcd.crop(bounding_box)

        # hFOV = 82.47  # zed
        hFOV = 91.14274835922176  # oms
        points = np.asarray(pcd.points)
        angles = np.arctan2(points[:, 0], points[:, 2]) * 180 / np.pi
        half_hFOV = hFOV / 2
        fov_mask = np.abs(angles) <= half_hFOV

        # 首先为所有点应用颜色映射
        y_coords = points[:, 1]
        y_normalized = 1 - ((y_coords - y_coords.min()) / (y_coords.max() - y_coords.min()))
        cmap = plt.get_cmap('jet')
        colors = cmap(y_normalized)[:, :3]  # 取 RGB 分量

        # 然后将 FOV 之外的点置为灰色
        gray_color = np.array([0.9, 0.9, 0.9])
        gray_color_fov = np.array([0.97, 0.97, 0.97])
        # colors[~fov_mask] = gray_color_fov  # FOV之外的点设置为灰色
        # colors[points[:, 2] > 0.5] = gray_color

        pcd.colors = o3d.utility.Vector3dVector(colors)
        # pcd = pcd.select_by_index(np.where(fov_mask)[0])

        return pcd

    def visualize(self):
        pcd = self.load_pcd(self.index)
        pcd = self.process_pcd(pcd)

        self.vis.add_geometry(pcd)
        self.vis.add_geometry(self.origin)
        self.set_custom_viewpoint(self.vis)  # 设置自定义视角

        # 键盘回调函数
    def next_callback(self, vis):
        self.index = (self.index + 1) % len(self.files)
        print(f"Loading next point cloud: {os.path.join(self.folder_path, self.files[self.index])}")
        vis.clear_geometries()
        new_pcd = self.load_pcd(self.index)
        new_pcd = self.process_pcd(new_pcd)
        self.vis.add_geometry(new_pcd)
        self.vis.add_geometry(self.origin)
        self.set_custom_viewpoint(vis)  # 重新设置视角

    def previous_callback(self, vis):
        self.index = (self.index - 1 + len(self.files)) % len(self.files)
        print(f"Loading previous point cloud: {os.path.join(self.folder_path, self.files[self.index])}")
        vis.clear_geometries()
        new_pcd = self.load_pcd(self.index)
        new_pcd = self.process_pcd(new_pcd)
        self.vis.add_geometry(new_pcd)
        self.vis.add_geometry(self.origin)
        self.set_custom_viewpoint(vis)  # 重新设置视角

    def run(self):
        self.visualize()
        self.vis.run()
        self.vis.destroy_window()


if __name__ == "__main__":
    # folder_path = '/home/<USER>/Datasets/indoor/scenes1_3_withoutSlam/dk_oms/dk_points'
    folder_path = '/home/<USER>/Datasets/indoor/scenes4_3/dk_oms/dk_points'
    visualizer = PointCloudVisualizer(folder_path)
    visualizer.run()
