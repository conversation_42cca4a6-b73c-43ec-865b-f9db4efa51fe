2024/07/08 15:30:11 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: linux
    Python: 3.8.10 (default, May 26 2023, 14:05:08) [GCC 9.4.0]
    CUDA available: True
    numpy_random_seed: 185275486
    GPU 0,1,2,3: NVIDIA TITAN Xp
    CUDA_HOME: /usr/local/cuda
    NVCC: Cuda compilation tools, release 11.6, V11.6.124
    GCC: x86_64-linux-gnu-gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0
    PyTorch: 1.13.0+cu116
    PyTorch compiling details: PyTorch built with:
  - GCC 9.3
  - C++ Version: 201402
  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v2.6.0 (Git Hash 52b5f107dd9cf10910aaa19cb47f3abf9b349815)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX2
  - CUDA Runtime 11.6
  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86
  - CuDNN 8.4
    - Built with CuDNN 8.3.2
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.6, CUDNN_VERSION=8.3.2, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -fabi-version=11 -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -DEDGE_PROFILER_USE_KINETO -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wunused-local-typedefs -Wno-unused-parameter -Wno-unused-function -Wno-unused-result -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Werror=cast-function-type -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.13.0, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, 

    TorchVision: 0.14.0+cu116
    OpenCV: 4.8.1
    MMEngine: 0.10.1

Runtime environment:
    cudnn_benchmark: False
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 1}
    dist_cfg: {'backend': 'nccl'}
    seed: 185275486
    Distributed launcher: pytorch
    Distributed training: True
    GPU number: 4
------------------------------------------------------------

2024/07/08 15:30:11 - mmengine - INFO - Config:
auto_scale_lr = dict(base_batch_size=4, enable=False)
bn_kwargs = dict(eps=2e-05, momentum=0.1)
class_names = [
    'no_occupancy',
    'occupancy',
]
custom_imports = dict(
    allow_failed_imports=False,
    imports=[
        'projects.SurroundOcc.surroundocc.models',
        'projects.SurroundOcc.surroundocc.datasets',
    ])
data_root = '/home/<USER>/indoor/oms_dk'
data_shape = (
    3,
    384,
    512,
)
dataset_type = 'EcoAi.KujialeFusionDataset'
default_hooks = dict(
    checkpoint=dict(interval=1, type='CheckpointHook'),
    logger=dict(interval=100, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    timer=dict(type='IterTimerHook'),
    visualization=dict(type='mmdet3d.Det3DVisualizationHook'))
default_scope = 'mmdet'
depoly_model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=128, num_channels=[
                256,
                512,
                1024,
            ]),
        fpn_lss_params=dict(in_channels=1280, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(
        activation='relu',
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        include_top=False,
        model_type='b0',
        num_classes=1000,
        type='efficientnet',
        use_se_block=False),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    lidar_config=dict(
        max_num_points=10,
        max_voxels=[
            90000,
            120000,
        ],
        point_cloud_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        voxel_size=[
            0.05,
            0.05,
            0.2,
        ]),
    norm_range=[
        -2,
        0,
        -0.05,
        2,
        4,
        0.15,
    ],
    reader=dict(
        bn_kwargs=None,
        hw_reverse=True,
        num_filters=(64, ),
        num_input_features=3,
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pool_size=(
            10,
            1,
        ),
        quantize=False,
        type='PillarFeatureNet_ld',
        use_4dim=True,
        use_conv=True,
        voxel_size=[
            0.05,
            0.05,
            0.2,
        ],
        with_distance=False),
    scatter=dict(
        num_input_features=64,
        quantize=True,
        type='PointPillarScatter_dpx',
        use_horizon_pillar_scatter=True),
    type='EcoOccFusionDepoly',
    use_grid_mask=False,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depoly=True,
        depth=120,
        depth_grid_quant_scale=0.03125,
        feat_channels=64,
        grid_quant_scale=0.0078125,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=4,
        num_views=1,
        type='LSSTransformer_Fusion',
        z_range=(
            -0.05,
            0.15,
        )))
depth_quant_scale = 0.03125
depthview_shape = [
    720,
    768.0,
]
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=1))
featview_shape = [
    144.0,
    32.0,
]
grid_quant_scale = 0.0078125
img_norm_cfg = dict(
    mean=[
        103.53,
        116.28,
        123.675,
    ], std=[
        1.0,
        1.0,
        1.0,
    ], to_rgb=False)
keys = (
    'img',
    'points',
)
launcher = 'pytorch'
load_from = '/home/<USER>/ecoaitoolkit/projects/SurroundOcc/depoly/0509_fusion_occ_epc24.pth'
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=50)
lss_bev_size = [
    -2,
    2,
    0,
    4,
    0.05,
]
lss_depth = 120
lss_grid_size = (
    80,
    80,
)
lss_num_points = 4
lss_z_range = (
    -0.05,
    0.15,
)
meta_keys = (
    'lidar2img',
    'img_shape',
    'pc_range',
    'occ_size',
    'gt_occ',
    'occ_path',
    'filename',
)
model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=128, num_channels=[
                256,
                512,
                1024,
            ]),
        fpn_lss_params=dict(in_channels=1280, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(
        activation='relu',
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        include_top=False,
        model_type='b0',
        num_classes=1000,
        type='efficientnet',
        use_se_block=False),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    is_vis=False,
    lambda_dice=1,
    lidar_config=dict(
        max_num_points=10,
        max_voxels=[
            90000,
            120000,
        ],
        point_cloud_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        voxel_size=[
            0.05,
            0.05,
            0.2,
        ]),
    norm_range=[
        -2,
        0,
        -0.05,
        2,
        4,
        0.15,
    ],
    reader=dict(
        bn_kwargs=None,
        hw_reverse=True,
        num_filters=(64, ),
        num_input_features=3,
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pool_size=(
            10,
            1,
        ),
        quantize=False,
        type='PillarFeatureNet_ld',
        use_4dim=True,
        use_conv=True,
        voxel_size=[
            0.05,
            0.05,
            0.2,
        ],
        with_distance=False),
    scatter=dict(
        num_input_features=64,
        quantize=True,
        type='PointPillarScatter_dpx',
        use_horizon_pillar_scatter=True),
    type='EcoOccFusion',
    use_grid_mask=True,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depth=120,
        depth_grid_quant_scale=0.03125,
        feat_channels=64,
        grid_quant_scale=0.0078125,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=4,
        num_views=1,
        type='LSSTransformer_Fusion',
        z_range=(
            -0.05,
            0.15,
        )))
numC_Trans = 128
occ_size = [
    80,
    80,
    4,
]
optim_wrapper = dict(
    clip_grad=dict(max_norm=5, norm_type=2),
    optimizer=dict(lr=0.0001, type='AdamW', weight_decay=0.01),
    type='AmpOptimWrapper')
param_scheduler = [
    dict(
        begin=0,
        by_epoch=False,
        end=500,
        start_factor=0.3333333333333333,
        type='LinearLR'),
    dict(by_epoch=True, type='CosineAnnealingLR'),
]
point_cloud_range = [
    -2,
    0,
    -0.05,
    2,
    4,
    0.15,
]
point_cloud_range2 = [
    -2,
    0,
    -0.03,
    2,
    4,
    0.15,
]
resume = False
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=4,
    dataset=dict(
        ann_file='pkl/indoor_oms_dk_0704_val.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor/oms_dk',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(
                coord_type='LIDAR',
                load_dim=3,
                reduce_beams=32,
                type='LoadPointsFromFileSingle',
                use_dim=3),
            dict(
                point_cloud_range=[
                    -2,
                    0,
                    -0.03,
                    2,
                    4,
                    0.15,
                ],
                type='PointsRangeFilter'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=(
                    'img',
                    'points',
                ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeFusionDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
test_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(
        coord_type='LIDAR',
        load_dim=3,
        reduce_beams=32,
        type='LoadPointsFromFileSingle',
        use_dim=3),
    dict(
        point_cloud_range=[
            -2,
            0,
            -0.03,
            2,
            4,
            0.15,
        ],
        type='PointsRangeFilter'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=(
            'img',
            'points',
        ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
train_cfg = dict(max_epochs=120, type='EpochBasedTrainLoop', val_interval=1)
train_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_oms_dk_0704_train.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor/oms_dk',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(
                coord_type='LIDAR',
                load_dim=3,
                reduce_beams=32,
                type='LoadPointsFromFileSingle',
                use_dim=3),
            dict(
                point_cloud_range=[
                    -2,
                    0,
                    -0.03,
                    2,
                    4,
                    0.15,
                ],
                type='PointsRangeFilter'),
            dict(type='PhotoMetricDistortionMultiViewImage'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=(
                    'img',
                    'points',
                ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        test_mode=False,
        type='EcoAi.KujialeFusionDataset',
        use_semantic=True),
    drop_last=True,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    prefetch_factor=8,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(
        coord_type='LIDAR',
        load_dim=3,
        reduce_beams=32,
        type='LoadPointsFromFileSingle',
        use_dim=3),
    dict(
        point_cloud_range=[
            -2,
            0,
            -0.03,
            2,
            4,
            0.15,
        ],
        type='PointsRangeFilter'),
    dict(type='PhotoMetricDistortionMultiViewImage'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=(
            'img',
            'points',
        ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
use_semantic = True
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=4,
    dataset=dict(
        ann_file='pkl/indoor_oms_dk_0704_val.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor/oms_dk',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(
                coord_type='LIDAR',
                load_dim=3,
                reduce_beams=32,
                type='LoadPointsFromFileSingle',
                use_dim=3),
            dict(
                point_cloud_range=[
                    -2,
                    0,
                    -0.03,
                    2,
                    4,
                    0.15,
                ],
                type='PointsRangeFilter'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=(
                    'img',
                    'points',
                ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeFusionDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
val_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(
        coord_type='LIDAR',
        load_dim=3,
        reduce_beams=32,
        type='LoadPointsFromFileSingle',
        use_dim=3),
    dict(
        point_cloud_range=[
            -2,
            0,
            -0.03,
            2,
            4,
            0.15,
        ],
        type='PointsRangeFilter'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=(
            'img',
            'points',
        ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
view_shape = [
    24.0,
    32.0,
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='mmdet3d.Det3DLocalVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
voxel_size = [
    0.05,
    0.05,
    0.2,
]
work_dir = 'work_dirs/indoor_oms_dk_occ/kjlPretrain'

2024/07/08 15:30:17 - mmengine - INFO - Autoplay mode, press [SPACE] to pause.
2024/07/08 15:30:17 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2024/07/08 15:30:19 - mmengine - WARNING - The prefix is not set in metric class BEVMetric.
Name of parameter - Initialization information

img_backbone.mod1.0.0.weight - torch.Size([32, 3, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone.mod1.0.1.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone.mod1.0.1.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._depthwise_conv.0.weight - torch.Size([32, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._depthwise_conv.1.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._depthwise_conv.1.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._project_conv.0.weight - torch.Size([16, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._project_conv.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._project_conv.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._expand_conv.0.weight - torch.Size([96, 16, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._expand_conv.1.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._expand_conv.1.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._depthwise_conv.0.weight - torch.Size([96, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._depthwise_conv.1.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._depthwise_conv.1.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._project_conv.0.weight - torch.Size([24, 96, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._project_conv.1.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._project_conv.1.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._expand_conv.0.weight - torch.Size([144, 24, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._expand_conv.1.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._expand_conv.1.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._depthwise_conv.0.weight - torch.Size([144, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._depthwise_conv.1.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._depthwise_conv.1.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._project_conv.0.weight - torch.Size([24, 144, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._project_conv.1.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._project_conv.1.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._expand_conv.0.weight - torch.Size([144, 24, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._expand_conv.1.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._expand_conv.1.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._depthwise_conv.0.weight - torch.Size([144, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._depthwise_conv.1.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._depthwise_conv.1.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._project_conv.0.weight - torch.Size([40, 144, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._project_conv.1.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._project_conv.1.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._expand_conv.0.weight - torch.Size([240, 40, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._expand_conv.1.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._expand_conv.1.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._depthwise_conv.0.weight - torch.Size([240, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._depthwise_conv.1.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._depthwise_conv.1.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._project_conv.0.weight - torch.Size([40, 240, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._project_conv.1.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._project_conv.1.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._expand_conv.0.weight - torch.Size([240, 40, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._expand_conv.1.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._expand_conv.1.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._depthwise_conv.0.weight - torch.Size([240, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._depthwise_conv.1.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._depthwise_conv.1.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._project_conv.0.weight - torch.Size([80, 240, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._project_conv.1.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._project_conv.1.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._expand_conv.0.weight - torch.Size([480, 80, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._expand_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._expand_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._depthwise_conv.0.weight - torch.Size([480, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._depthwise_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._depthwise_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._project_conv.0.weight - torch.Size([80, 480, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._project_conv.1.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._project_conv.1.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._expand_conv.0.weight - torch.Size([480, 80, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._expand_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._expand_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._depthwise_conv.0.weight - torch.Size([480, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._depthwise_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._depthwise_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._project_conv.0.weight - torch.Size([80, 480, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._project_conv.1.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._project_conv.1.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._expand_conv.0.weight - torch.Size([480, 80, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._expand_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._expand_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._depthwise_conv.0.weight - torch.Size([480, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._depthwise_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._depthwise_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._project_conv.0.weight - torch.Size([112, 480, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._project_conv.1.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._project_conv.1.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._expand_conv.0.weight - torch.Size([672, 112, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._expand_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._expand_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._depthwise_conv.0.weight - torch.Size([672, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._depthwise_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._depthwise_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._project_conv.0.weight - torch.Size([112, 672, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._project_conv.1.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._project_conv.1.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._expand_conv.0.weight - torch.Size([672, 112, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._expand_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._expand_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._depthwise_conv.0.weight - torch.Size([672, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._depthwise_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._depthwise_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._project_conv.0.weight - torch.Size([112, 672, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._project_conv.1.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._project_conv.1.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._expand_conv.0.weight - torch.Size([672, 112, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._expand_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._expand_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._depthwise_conv.0.weight - torch.Size([672, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._depthwise_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._depthwise_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._project_conv.0.weight - torch.Size([192, 672, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._project_conv.1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._project_conv.1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._expand_conv.0.weight - torch.Size([1152, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._expand_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._expand_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._depthwise_conv.0.weight - torch.Size([1152, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._depthwise_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._depthwise_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._project_conv.0.weight - torch.Size([192, 1152, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._project_conv.1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._project_conv.1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._expand_conv.0.weight - torch.Size([1152, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._expand_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._expand_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._depthwise_conv.0.weight - torch.Size([1152, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._depthwise_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._depthwise_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._project_conv.0.weight - torch.Size([192, 1152, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._project_conv.1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._project_conv.1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._expand_conv.0.weight - torch.Size([1152, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._expand_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._expand_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._depthwise_conv.0.weight - torch.Size([1152, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._depthwise_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._depthwise_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._project_conv.0.weight - torch.Size([192, 1152, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._project_conv.1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._project_conv.1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._expand_conv.0.weight - torch.Size([1152, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._expand_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._expand_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._depthwise_conv.0.weight - torch.Size([1152, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._depthwise_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._depthwise_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._project_conv.0.weight - torch.Size([320, 1152, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._project_conv.1.weight - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._project_conv.1.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.0.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.0.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.0.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.0.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.1.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.1.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.1.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.1.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.2.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.2.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.2.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.2.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.3.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.3.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.3.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.3.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.project.0.weight - torch.Size([64, 384, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.project.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.project.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.project.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.project.0.weight - torch.Size([64, 112, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.project.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.project.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.project.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.0.0.weight - torch.Size([64, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.0.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.0.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.0.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.1.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.1.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.1.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.1.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.0.0.weight - torch.Size([64, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.0.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.0.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.0.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.1.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.1.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.1.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.1.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

view_transformer.depth_net.0.weight - torch.Size([120, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

view_transformer.feat_net.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.conv1.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.downsample.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.downsample.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.conv1.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.conv1.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.downsample.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.downsample.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.conv1.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.conv1.weight - torch.Size([1024, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.bn1.weight - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.bn1.bias - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.conv2.weight - torch.Size([1024, 1024, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.bn2.weight - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.bn2.bias - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.downsample.weight - torch.Size([1024, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.downsample.bias - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.conv1.weight - torch.Size([1024, 1024, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.bn1.weight - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.bn1.bias - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.conv2.weight - torch.Size([1024, 1024, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.bn2.weight - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.bn2.bias - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.0.weight - torch.Size([512, 1280, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.3.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.4.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.4.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.up2.1.weight - torch.Size([256, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.up2.2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.up2.2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.up2.4.weight - torch.Size([256, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.up2.4.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_decoder.final_conv.conv.weight - torch.Size([256, 256, 3, 3]): 
Initialized by user-defined `init_weights` in ConvModule  

bev_decoder.final_conv.conv.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_decoder.predicter.0.weight - torch.Size([512, 256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_decoder.predicter.0.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_decoder.predicter.2.weight - torch.Size([4, 512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_decoder.predicter.2.bias - torch.Size([4]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

lidarmodel.reader.pfn_layers.0.linear.weight - torch.Size([64, 3, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

lidarmodel.reader.pfn_layers.0.norm.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

lidarmodel.reader.pfn_layers.0.norm.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

fuser.0.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

fuser.1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

fuser.1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  
2024/07/08 15:30:20 - mmengine - INFO - Load checkpoint from /home/<USER>/ecoaitoolkit/projects/SurroundOcc/depoly/0509_fusion_occ_epc24.pth
2024/07/08 15:30:20 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2024/07/08 15:30:20 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2024/07/08 15:30:20 - mmengine - INFO - Checkpoints will be saved to /home/<USER>/ecoaitoolkit/work_dirs/indoor_oms_dk_occ/kjlPretrain.
2024/07/08 15:31:30 - mmengine - INFO - Epoch(train)   [1][100/111]  lr: 4.6560e-05  eta: 2:32:50  time: 0.5656  data_time: 0.0006  memory: 2942  grad_norm: 0.9425  loss: 0.6434  loss_bce: 0.0247  loss_dice: 0.6186
2024/07/08 15:31:36 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:31:36 - mmengine - INFO - Saving checkpoint at 1 epochs
2024/07/08 15:31:46 - mmengine - INFO - Epoch(val) [1][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9894250631049423, 'occupancy': 0.21970191546400342, 'mIoU': 0.6045634892844729}, '1~2m': {'no_occupancy': 0.9938432626237161, 'occupancy': 0.12602666666666668, 'mIoU': 0.5599349646451914}, '2~3m': {'no_occupancy': 0.9992214536443965, 'occupancy': 0.02857142857142857, 'mIoU': 0.5138964411079125}, '3~4m': {'no_occupancy': 0.9998547297297299, 'occupancy': 0.0, 'mIoU': 0.49992736486486494}, 'patch_mean': {'no_occupancy': 0.9955861272756963, 'occupancy': 0.09357500267552467, 'mIoU': 0.5445805649756105}}  total_overall: {'no_occupancy': 0.9955910702640303, 'occupancy': 0.18042204702410577, 'mIoU': 0.588006558644068}  data_time: 0.0215  time: 0.0772
2024/07/08 15:32:51 - mmengine - INFO - Epoch(train)   [2][100/111]  lr: 6.1379e-05  eta: 2:26:05  time: 0.5690  data_time: 0.0007  memory: 2942  grad_norm: 1.1564  loss: 0.6004  loss_bce: 0.0234  loss_dice: 0.5770
2024/07/08 15:32:57 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:32:57 - mmengine - INFO - Saving checkpoint at 2 epochs
2024/07/08 15:33:06 - mmengine - INFO - Epoch(val) [2][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9880097932477936, 'occupancy': 0.26310332644065637, 'mIoU': 0.625556559844225}, '1~2m': {'no_occupancy': 0.9934887349252265, 'occupancy': 0.13765303075544938, 'mIoU': 0.565570882840338}, '2~3m': {'no_occupancy': 0.9991729837866532, 'occupancy': 0.08589211618257263, 'mIoU': 0.5425325499846129}, '3~4m': {'no_occupancy': 0.9998547297297299, 'occupancy': 0.0, 'mIoU': 0.49992736486486494}, 'patch_mean': {'no_occupancy': 0.9951315604223508, 'occupancy': 0.1216621183446696, 'mIoU': 0.5583968393835103}}  total_overall: {'no_occupancy': 0.9951395086354388, 'occupancy': 0.21693267880595432, 'mIoU': 0.6060360937206966}  data_time: 0.0098  time: 0.0636
2024/07/08 15:34:12 - mmengine - INFO - Epoch(train)   [3][100/111]  lr: 7.6167e-05  eta: 2:23:25  time: 0.5745  data_time: 0.0007  memory: 2942  grad_norm: 1.2021  loss: 0.5468  loss_bce: 0.0223  loss_dice: 0.5245
2024/07/08 15:34:18 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:34:18 - mmengine - INFO - Saving checkpoint at 3 epochs
2024/07/08 15:34:23 - mmengine - INFO - Epoch(val) [3][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9884897231893139, 'occupancy': 0.27737208999562096, 'mIoU': 0.6329309065924674}, '1~2m': {'no_occupancy': 0.9933934685530232, 'occupancy': 0.14564805365213587, 'mIoU': 0.5695207611025795}, '2~3m': {'no_occupancy': 0.9991478281631151, 'occupancy': 0.08762057877813503, 'mIoU': 0.5433842034706251}, '3~4m': {'no_occupancy': 0.9998547297297299, 'occupancy': 0.0, 'mIoU': 0.49992736486486494}, 'patch_mean': {'no_occupancy': 0.9952214374087955, 'occupancy': 0.12766018060647297, 'mIoU': 0.5614408090076343}}  total_overall: {'no_occupancy': 0.9952292848076232, 'occupancy': 0.22729590477712583, 'mIoU': 0.6112625947923745}  data_time: 0.0100  time: 0.0634
2024/07/08 15:35:28 - mmengine - INFO - Epoch(train)   [4][100/111]  lr: 9.0908e-05  eta: 2:21:18  time: 0.5734  data_time: 0.0007  memory: 2942  grad_norm: 1.2278  loss: 0.5199  loss_bce: 0.0215  loss_dice: 0.4984
2024/07/08 15:35:34 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:35:34 - mmengine - INFO - Saving checkpoint at 4 epochs
2024/07/08 15:35:38 - mmengine - INFO - Epoch(val) [4][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9889560736410076, 'occupancy': 0.2917966388586628, 'mIoU': 0.6403763562498352}, '1~2m': {'no_occupancy': 0.9937861684378241, 'occupancy': 0.14657308009909165, 'mIoU': 0.5701796242684578}, '2~3m': {'no_occupancy': 0.9992251853792781, 'occupancy': 0.062244434348023625, 'mIoU': 0.5307348098636508}, '3~4m': {'no_occupancy': 0.9998524774774775, 'occupancy': 0.0, 'mIoU': 0.49992623873873876}, 'patch_mean': {'no_occupancy': 0.9954549762338969, 'occupancy': 0.12515353832644452, 'mIoU': 0.5603042572801707}}  total_overall: {'no_occupancy': 0.9954627430905346, 'occupancy': 0.23757204895380968, 'mIoU': 0.6165173960221721}  data_time: 0.0097  time: 0.0623
2024/07/08 15:36:44 - mmengine - INFO - Epoch(train)   [5][100/111]  lr: 9.9726e-05  eta: 2:19:31  time: 0.5767  data_time: 0.0007  memory: 2942  grad_norm: 1.1899  loss: 0.4852  loss_bce: 0.0200  loss_dice: 0.4652
2024/07/08 15:36:50 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:36:50 - mmengine - INFO - Saving checkpoint at 5 epochs
2024/07/08 15:36:54 - mmengine - INFO - Epoch(val) [5][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9887468383653494, 'occupancy': 0.2866081406536667, 'mIoU': 0.6376774895095081}, '1~2m': {'no_occupancy': 0.993632332390295, 'occupancy': 0.13931636954644727, 'mIoU': 0.5664743509683712}, '2~3m': {'no_occupancy': 0.9991069162746208, 'occupancy': 0.07611650485436892, 'mIoU': 0.5376117105644949}, '3~4m': {'no_occupancy': 0.9997935421485581, 'occupancy': 0.03169014084507042, 'mIoU': 0.5157418414968142}, 'patch_mean': {'no_occupancy': 0.9953199072947058, 'occupancy': 0.1334327889748883, 'mIoU': 0.564376348134797}}  total_overall: {'no_occupancy': 0.9953276711588676, 'occupancy': 0.2311403729205408, 'mIoU': 0.6132340220397041}  data_time: 0.0105  time: 0.0655
2024/07/08 15:38:01 - mmengine - INFO - Epoch(train)   [6][100/111]  lr: 9.9572e-05  eta: 2:18:14  time: 0.5766  data_time: 0.0008  memory: 2942  grad_norm: 1.1694  loss: 0.4689  loss_bce: 0.0195  loss_dice: 0.4494
2024/07/08 15:38:07 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:38:07 - mmengine - INFO - Saving checkpoint at 6 epochs
2024/07/08 15:38:11 - mmengine - INFO - Epoch(val) [6][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9892381608834806, 'occupancy': 0.2950228479683833, 'mIoU': 0.642130504425932}, '1~2m': {'no_occupancy': 0.9938120100219433, 'occupancy': 0.15580620353755445, 'mIoU': 0.5748091067797488}, '2~3m': {'no_occupancy': 0.9990971581692545, 'occupancy': 0.07286044718581342, 'mIoU': 0.535978802677534}, '3~4m': {'no_occupancy': 0.9998025521819673, 'occupancy': 0.009416195856873822, 'mIoU': 0.5046093740194206}, 'patch_mean': {'no_occupancy': 0.9954874703141614, 'occupancy': 0.13327642363715625, 'mIoU': 0.5643819469756588}}  total_overall: {'no_occupancy': 0.9954948973401775, 'occupancy': 0.24046581636694922, 'mIoU': 0.6179803568535633}  data_time: 0.0094  time: 0.0618
2024/07/08 15:39:16 - mmengine - INFO - Epoch(train)   [7][100/111]  lr: 9.9384e-05  eta: 2:16:41  time: 0.5699  data_time: 0.0007  memory: 2942  grad_norm: inf  loss: 0.4823  loss_bce: 0.0205  loss_dice: 0.4618
2024/07/08 15:39:23 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:39:23 - mmengine - INFO - Saving checkpoint at 7 epochs
2024/07/08 15:39:27 - mmengine - INFO - Epoch(val) [7][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9891678791202256, 'occupancy': 0.27953316075491563, 'mIoU': 0.6343505199375706}, '1~2m': {'no_occupancy': 0.993552711971785, 'occupancy': 0.13709515188090926, 'mIoU': 0.5653239319263471}, '2~3m': {'no_occupancy': 0.9990543832844695, 'occupancy': 0.04907512268780671, 'mIoU': 0.5240647529861381}, '3~4m': {'no_occupancy': 0.9998404652858094, 'occupancy': 0.007009345794392523, 'mIoU': 0.503424905540101}, 'patch_mean': {'no_occupancy': 0.9954038599155723, 'occupancy': 0.11817819527950603, 'mIoU': 0.5567910275975392}}  total_overall: {'no_occupancy': 0.9954108214752189, 'occupancy': 0.22289714979016254, 'mIoU': 0.6091539856326907}  data_time: 0.0097  time: 0.0632
2024/07/08 15:40:33 - mmengine - INFO - Epoch(train)   [8][100/111]  lr: 9.9163e-05  eta: 2:15:32  time: 0.5707  data_time: 0.0007  memory: 2942  grad_norm: 1.1253  loss: 0.4459  loss_bce: 0.0196  loss_dice: 0.4263
2024/07/08 15:40:40 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:40:40 - mmengine - INFO - Saving checkpoint at 8 epochs
2024/07/08 15:40:44 - mmengine - INFO - Epoch(val) [8][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9882094033933233, 'occupancy': 0.26601230881153676, 'mIoU': 0.62711085610243}, '1~2m': {'no_occupancy': 0.9934165266147492, 'occupancy': 0.12429406766954869, 'mIoU': 0.558855297142149}, '2~3m': {'no_occupancy': 0.9990930365781051, 'occupancy': 0.06429124709527499, 'mIoU': 0.53169214183669}, '3~4m': {'no_occupancy': 0.999821320918891, 'occupancy': 0.012448132780082987, 'mIoU': 0.506134726849487}, 'patch_mean': {'no_occupancy': 0.9951350718762672, 'occupancy': 0.11676143908911087, 'mIoU': 0.555948255482689}}  total_overall: {'no_occupancy': 0.9951427859358182, 'occupancy': 0.21305173575297162, 'mIoU': 0.6040972608443949}  data_time: 0.0099  time: 0.0624
2024/07/08 15:41:50 - mmengine - INFO - Epoch(train)   [9][100/111]  lr: 9.8907e-05  eta: 2:14:26  time: 0.5726  data_time: 0.0007  memory: 2942  grad_norm: 1.1786  loss: 0.4140  loss_bce: 0.0173  loss_dice: 0.3968
2024/07/08 15:41:57 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:41:57 - mmengine - INFO - Saving checkpoint at 9 epochs
2024/07/08 15:42:01 - mmengine - INFO - Epoch(val) [9][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9891344439981752, 'occupancy': 0.27882684813950726, 'mIoU': 0.6339806460688412}, '1~2m': {'no_occupancy': 0.9935469470592634, 'occupancy': 0.13928428227746592, 'mIoU': 0.5664156146683647}, '2~3m': {'no_occupancy': 0.9991437205548358, 'occupancy': 0.061703002879473466, 'mIoU': 0.5304233617171547}, '3~4m': {'no_occupancy': 0.9998333330830826, 'occupancy': 0.008928571428571428, 'mIoU': 0.504380952255827}, 'patch_mean': {'no_occupancy': 0.9954146111738392, 'occupancy': 0.12218567618125452, 'mIoU': 0.5588001436775468}}  total_overall: {'no_occupancy': 0.9954216214222574, 'occupancy': 0.22416312036974376, 'mIoU': 0.6097923708960006}  data_time: 0.0107  time: 0.0635
2024/07/08 15:42:04 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:43:06 - mmengine - INFO - Epoch(train)  [10][100/111]  lr: 9.8618e-05  eta: 2:13:07  time: 0.5758  data_time: 0.0007  memory: 2942  grad_norm: 1.0997  loss: 0.3903  loss_bce: 0.0171  loss_dice: 0.3733
2024/07/08 15:43:13 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:43:13 - mmengine - INFO - Saving checkpoint at 10 epochs
2024/07/08 15:43:17 - mmengine - INFO - Epoch(val) [10][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9896300542438368, 'occupancy': 0.2783103615595452, 'mIoU': 0.633970207901691}, '1~2m': {'no_occupancy': 0.9940452283113346, 'occupancy': 0.13327135513507601, 'mIoU': 0.5636582917232053}, '2~3m': {'no_occupancy': 0.9990716367573282, 'occupancy': 0.06502835538752363, 'mIoU': 0.532049996072426}, '3~4m': {'no_occupancy': 0.9998081826071548, 'occupancy': 0.015414258188824661, 'mIoU': 0.5076112203979897}, 'patch_mean': {'no_occupancy': 0.9956387754799136, 'occupancy': 0.12300608256774236, 'mIoU': 0.559322429023828}}  total_overall: {'no_occupancy': 0.9956450725431311, 'occupancy': 0.2220338556309954, 'mIoU': 0.6088394640870632}  data_time: 0.0107  time: 0.0643
2024/07/08 15:44:23 - mmengine - INFO - Epoch(train)  [11][100/111]  lr: 9.8296e-05  eta: 2:11:52  time: 0.5645  data_time: 0.0007  memory: 2942  grad_norm: 1.0947  loss: 0.3886  loss_bce: 0.0171  loss_dice: 0.3715
2024/07/08 15:44:29 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:44:29 - mmengine - INFO - Saving checkpoint at 11 epochs
2024/07/08 15:44:33 - mmengine - INFO - Epoch(val) [11][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9897080474182763, 'occupancy': 0.288622122637208, 'mIoU': 0.6391650850277422}, '1~2m': {'no_occupancy': 0.9939923725788575, 'occupancy': 0.1375478720535088, 'mIoU': 0.5657701223161832}, '2~3m': {'no_occupancy': 0.9990682690037272, 'occupancy': 0.05375524208921083, 'mIoU': 0.526411755546469}, '3~4m': {'no_occupancy': 0.9997638864072147, 'occupancy': 0.0426179604261796, 'mIoU': 0.5211909234166971}, 'patch_mean': {'no_occupancy': 0.995633143852019, 'occupancy': 0.1306357993015268, 'mIoU': 0.5631344715767729}}  total_overall: {'no_occupancy': 0.9956396482091243, 'occupancy': 0.22917999019941696, 'mIoU': 0.6124098192042706}  data_time: 0.0096  time: 0.0620
2024/07/08 15:45:39 - mmengine - INFO - Epoch(train)  [12][100/111]  lr: 9.7941e-05  eta: 2:10:31  time: 0.5731  data_time: 0.0007  memory: 2942  grad_norm: 1.0817  loss: 0.3930  loss_bce: 0.0166  loss_dice: 0.3763
2024/07/08 15:45:45 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:45:45 - mmengine - INFO - Saving checkpoint at 12 epochs
2024/07/08 15:45:49 - mmengine - INFO - Epoch(val) [12][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9897526988551772, 'occupancy': 0.2850173895320934, 'mIoU': 0.6373850441936353}, '1~2m': {'no_occupancy': 0.9939779520979096, 'occupancy': 0.1401824034334764, 'mIoU': 0.567080177765693}, '2~3m': {'no_occupancy': 0.9991256887219098, 'occupancy': 0.07505957108816522, 'mIoU': 0.5370926299050375}, '3~4m': {'no_occupancy': 0.9998085576962059, 'occupancy': 0.022988505747126436, 'mIoU': 0.5113985317216662}, 'patch_mean': {'no_occupancy': 0.9956662243428006, 'occupancy': 0.13081196745021534, 'mIoU': 0.563239095896508}}  total_overall: {'no_occupancy': 0.995672594995014, 'occupancy': 0.22865469406212974, 'mIoU': 0.6121636445285719}  data_time: 0.0101  time: 0.0634
2024/07/08 15:46:55 - mmengine - INFO - Epoch(train)  [13][100/111]  lr: 9.7553e-05  eta: 2:09:13  time: 0.5790  data_time: 0.0007  memory: 2942  grad_norm: 1.1174  loss: 0.3615  loss_bce: 0.0154  loss_dice: 0.3461
2024/07/08 15:47:01 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:47:01 - mmengine - INFO - Saving checkpoint at 13 epochs
2024/07/08 15:47:05 - mmengine - INFO - Epoch(val) [13][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9899319863774891, 'occupancy': 0.2854420222014177, 'mIoU': 0.6376870042894534}, '1~2m': {'no_occupancy': 0.9938136389254182, 'occupancy': 0.13182177695755337, 'mIoU': 0.5628177079414858}, '2~3m': {'no_occupancy': 0.9990982965613263, 'occupancy': 0.059882583170254404, 'mIoU': 0.5294904398657904}, '3~4m': {'no_occupancy': 0.9997999238734739, 'occupancy': 0.02559414990859232, 'mIoU': 0.5126970368910331}, 'patch_mean': {'no_occupancy': 0.995660961434427, 'occupancy': 0.12568513305945445, 'mIoU': 0.5606730472469407}}  total_overall: {'no_occupancy': 0.9956670848812795, 'occupancy': 0.22435325977258969, 'mIoU': 0.6100101723269347}  data_time: 0.0102  time: 0.0636
2024/07/08 15:48:12 - mmengine - INFO - Epoch(train)  [14][100/111]  lr: 9.7132e-05  eta: 2:08:02  time: 0.5747  data_time: 0.0007  memory: 2942  grad_norm: 1.1222  loss: 0.3873  loss_bce: 0.0170  loss_dice: 0.3703
2024/07/08 15:48:18 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:48:18 - mmengine - INFO - Saving checkpoint at 14 epochs
2024/07/08 15:48:22 - mmengine - INFO - Epoch(val) [14][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9900723533325055, 'occupancy': 0.27807918892999045, 'mIoU': 0.634075771131248}, '1~2m': {'no_occupancy': 0.9940410156345388, 'occupancy': 0.11872881826768153, 'mIoU': 0.5563849169511101}, '2~3m': {'no_occupancy': 0.999141091167091, 'occupancy': 0.0634465820712239, 'mIoU': 0.5312938366191575}, '3~4m': {'no_occupancy': 0.999809308307176, 'occupancy': 0.02681992337164751, 'mIoU': 0.5133146158394117}, 'patch_mean': {'no_occupancy': 0.9957659421103278, 'occupancy': 0.12176862816013584, 'mIoU': 0.5587672851352318}}  total_overall: {'no_occupancy': 0.9957716627415175, 'occupancy': 0.21675455542212707, 'mIoU': 0.6062631090818222}  data_time: 0.0099  time: 0.0641
2024/07/08 15:49:28 - mmengine - INFO - Epoch(train)  [15][100/111]  lr: 9.6679e-05  eta: 2:06:45  time: 0.5658  data_time: 0.0007  memory: 2942  grad_norm: 1.0582  loss: 0.3625  loss_bce: 0.0154  loss_dice: 0.3471
2024/07/08 15:49:34 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:49:34 - mmengine - INFO - Saving checkpoint at 15 epochs
2024/07/08 15:49:38 - mmengine - INFO - Epoch(val) [15][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9896914064307074, 'occupancy': 0.278965092294208, 'mIoU': 0.6343282493624577}, '1~2m': {'no_occupancy': 0.9939971385245668, 'occupancy': 0.1322978330527345, 'mIoU': 0.5631474857886507}, '2~3m': {'no_occupancy': 0.9990986770335701, 'occupancy': 0.05435210712879086, 'mIoU': 0.5267253920811805}, '3~4m': {'no_occupancy': 0.9997867855862551, 'occupancy': 0.025728987993138937, 'mIoU': 0.512757886789697}, 'patch_mean': {'no_occupancy': 0.9956435018937748, 'occupancy': 0.12283600511721808, 'mIoU': 0.5592397535054965}}  total_overall: {'no_occupancy': 0.9956497465793455, 'occupancy': 0.2214828297944788, 'mIoU': 0.6085662881869122}  data_time: 0.0094  time: 0.0616
2024/07/08 15:50:44 - mmengine - INFO - Epoch(train)  [16][100/111]  lr: 9.6194e-05  eta: 2:05:30  time: 0.5698  data_time: 0.0007  memory: 2942  grad_norm: 1.0478  loss: 0.3379  loss_bce: 0.0151  loss_dice: 0.3228
2024/07/08 15:50:50 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:50:50 - mmengine - INFO - Saving checkpoint at 16 epochs
2024/07/08 15:50:55 - mmengine - INFO - Epoch(val) [16][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9901404667254526, 'occupancy': 0.2712659681854572, 'mIoU': 0.6307032174554549}, '1~2m': {'no_occupancy': 0.9940763003174485, 'occupancy': 0.11999107043196784, 'mIoU': 0.5570336853747082}, '2~3m': {'no_occupancy': 0.999154608323129, 'occupancy': 0.06088407005838198, 'mIoU': 0.5300193391907555}, '3~4m': {'no_occupancy': 0.9998198195492789, 'occupancy': 0.008264462809917354, 'mIoU': 0.5040421411795981}, 'patch_mean': {'no_occupancy': 0.9957977987288273, 'occupancy': 0.11510139287143108, 'mIoU': 0.5554495958001292}}  total_overall: {'no_occupancy': 0.9958032765723392, 'occupancy': 0.21233000506954092, 'mIoU': 0.6040666408209401}  data_time: 0.0098  time: 0.0628
2024/07/08 15:52:00 - mmengine - INFO - Epoch(train)  [17][100/111]  lr: 9.5677e-05  eta: 2:04:16  time: 0.5732  data_time: 0.0007  memory: 2942  grad_norm: 1.0445  loss: 0.3321  loss_bce: 0.0148  loss_dice: 0.3173
2024/07/08 15:52:06 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:52:06 - mmengine - INFO - Saving checkpoint at 17 epochs
2024/07/08 15:52:11 - mmengine - INFO - Epoch(val) [17][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9896905662155319, 'occupancy': 0.286240866388309, 'mIoU': 0.6379657163019204}, '1~2m': {'no_occupancy': 0.9936833142704269, 'occupancy': 0.1355478994189335, 'mIoU': 0.5646156068446802}, '2~3m': {'no_occupancy': 0.9991392135347313, 'occupancy': 0.06408163265306123, 'mIoU': 0.5316104230938963}, '3~4m': {'no_occupancy': 0.9998025521819673, 'occupancy': 0.009416195856873822, 'mIoU': 0.5046093740194206}, 'patch_mean': {'no_occupancy': 0.9955789115506644, 'occupancy': 0.12382164857929437, 'mIoU': 0.5597002800649794}}  total_overall: {'no_occupancy': 0.9955853955747493, 'occupancy': 0.22662062948574443, 'mIoU': 0.6111030125302469}  data_time: 0.0099  time: 0.0636
2024/07/08 15:53:16 - mmengine - INFO - Epoch(train)  [18][100/111]  lr: 9.5129e-05  eta: 2:02:59  time: 0.5720  data_time: 0.0007  memory: 2942  grad_norm: 1.0548  loss: 0.3558  loss_bce: 0.0154  loss_dice: 0.3404
2024/07/08 15:53:22 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:53:22 - mmengine - INFO - Saving checkpoint at 18 epochs
2024/07/08 15:53:27 - mmengine - INFO - Epoch(val) [18][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9900395759967681, 'occupancy': 0.28672635634332133, 'mIoU': 0.6383829661700446}, '1~2m': {'no_occupancy': 0.9939920725865535, 'occupancy': 0.1281282372825909, 'mIoU': 0.5610601549345722}, '2~3m': {'no_occupancy': 0.9990908009097996, 'occupancy': 0.04608113430484443, 'mIoU': 0.522585967607322}, '3~4m': {'no_occupancy': 0.9997991732695494, 'occupancy': 0.021937842778793418, 'mIoU': 0.5108685080241714}, 'patch_mean': {'no_occupancy': 0.9957304056906676, 'occupancy': 0.12071839267738753, 'mIoU': 0.5582243991840276}}  total_overall: {'no_occupancy': 0.9957364287186093, 'occupancy': 0.2240604653747665, 'mIoU': 0.6098984470466879}  data_time: 0.0101  time: 0.0630
2024/07/08 15:53:31 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:54:32 - mmengine - INFO - Epoch(train)  [19][100/111]  lr: 9.4550e-05  eta: 2:01:47  time: 0.5818  data_time: 0.0007  memory: 2942  grad_norm: 1.0123  loss: 0.3260  loss_bce: 0.0142  loss_dice: 0.3118
2024/07/08 15:54:39 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:54:39 - mmengine - INFO - Saving checkpoint at 19 epochs
2024/07/08 15:54:43 - mmengine - INFO - Epoch(val) [19][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9897321007545445, 'occupancy': 0.2784016420578693, 'mIoU': 0.634066871406207}, '1~2m': {'no_occupancy': 0.9938111632743841, 'occupancy': 0.12120565484129102, 'mIoU': 0.5575084090578376}, '2~3m': {'no_occupancy': 0.9990930355566848, 'occupancy': 0.06537717601547388, 'mIoU': 0.5322351057860794}, '3~4m': {'no_occupancy': 0.9998246995022889, 'occupancy': 0.006382978723404255, 'mIoU': 0.5031038391128466}, 'patch_mean': {'no_occupancy': 0.9956152497719756, 'occupancy': 0.11784186290950963, 'mIoU': 0.5567285563407426}}  total_overall: {'no_occupancy': 0.9956213890588682, 'occupancy': 0.21753347605255427, 'mIoU': 0.6065774325557113}  data_time: 0.0104  time: 0.0643
2024/07/08 15:55:49 - mmengine - INFO - Epoch(train)  [20][100/111]  lr: 9.3941e-05  eta: 2:00:34  time: 0.5720  data_time: 0.0007  memory: 2942  grad_norm: 0.9768  loss: 0.3236  loss_bce: 0.0147  loss_dice: 0.3089
2024/07/08 15:55:55 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:55:55 - mmengine - INFO - Saving checkpoint at 20 epochs
2024/07/08 15:55:59 - mmengine - INFO - Epoch(val) [20][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9899521666311856, 'occupancy': 0.2726256068954231, 'mIoU': 0.6312888867633043}, '1~2m': {'no_occupancy': 0.9938849237090039, 'occupancy': 0.1199243038659097, 'mIoU': 0.5569046137874568}, '2~3m': {'no_occupancy': 0.9990675266143447, 'occupancy': 0.04498269896193771, 'mIoU': 0.5220251127881412}, '3~4m': {'no_occupancy': 0.9998078068699285, 'occupancy': 0.024761904761904763, 'mIoU': 0.5122848558159167}, 'patch_mean': {'no_occupancy': 0.9956781059561157, 'occupancy': 0.1155736286212938, 'mIoU': 0.5556258672887048}}  total_overall: {'no_occupancy': 0.9956838143130692, 'occupancy': 0.2117770190278469, 'mIoU': 0.603730416670458}  data_time: 0.0100  time: 0.0623
2024/07/08 15:57:05 - mmengine - INFO - Epoch(train)  [21][100/111]  lr: 9.3301e-05  eta: 1:59:22  time: 0.5759  data_time: 0.0007  memory: 2942  grad_norm: 1.0018  loss: 0.3084  loss_bce: 0.0134  loss_dice: 0.2950
2024/07/08 15:57:12 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:57:12 - mmengine - INFO - Saving checkpoint at 21 epochs
2024/07/08 15:57:16 - mmengine - INFO - Epoch(val) [21][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9899248750500068, 'occupancy': 0.27195376792752074, 'mIoU': 0.6309393214887637}, '1~2m': {'no_occupancy': 0.9938852143988248, 'occupancy': 0.12168798229992986, 'mIoU': 0.5577865983493774}, '2~3m': {'no_occupancy': 0.9991118265216259, 'occupancy': 0.04288025889967637, 'mIoU': 0.5209960427106511}, '3~4m': {'no_occupancy': 0.999800299400748, 'occupancy': 0.022058823529411766, 'mIoU': 0.5109295614650798}, 'patch_mean': {'no_occupancy': 0.9956805538428013, 'occupancy': 0.11464520816413469, 'mIoU': 0.555162881003468}}  total_overall: {'no_occupancy': 0.9956862966624078, 'occupancy': 0.21212017211571044, 'mIoU': 0.6039032343890591}  data_time: 0.0094  time: 0.0624
2024/07/08 15:58:22 - mmengine - INFO - Epoch(train)  [22][100/111]  lr: 9.2632e-05  eta: 1:58:11  time: 0.5735  data_time: 0.0007  memory: 2942  grad_norm: 1.0462  loss: 0.3211  loss_bce: 0.0136  loss_dice: 0.3075
2024/07/08 15:58:29 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:58:29 - mmengine - INFO - Saving checkpoint at 22 epochs
2024/07/08 15:58:33 - mmengine - INFO - Epoch(val) [22][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9897399093002556, 'occupancy': 0.26138074122289573, 'mIoU': 0.6255603252615757}, '1~2m': {'no_occupancy': 0.9939181866504385, 'occupancy': 0.10823464610300192, 'mIoU': 0.5510764163767202}, '2~3m': {'no_occupancy': 0.9991309596832862, 'occupancy': 0.05779405779405779, 'mIoU': 0.5284625087386721}, '3~4m': {'no_occupancy': 0.9998096837559645, 'occupancy': 0.025, 'mIoU': 0.5124048418779822}, 'patch_mean': {'no_occupancy': 0.9956496848474863, 'occupancy': 0.11310236127998886, 'mIoU': 0.5543760230637376}}  total_overall: {'no_occupancy': 0.9956553037945163, 'occupancy': 0.20270328514658595, 'mIoU': 0.5991792944705511}  data_time: 0.0107  time: 0.0647
2024/07/08 15:59:40 - mmengine - INFO - Epoch(train)  [23][100/111]  lr: 9.1934e-05  eta: 1:57:02  time: 0.5768  data_time: 0.0007  memory: 2942  grad_norm: 1.0211  loss: 0.3046  loss_bce: 0.0140  loss_dice: 0.2907
2024/07/08 15:59:46 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 15:59:46 - mmengine - INFO - Saving checkpoint at 23 epochs
2024/07/08 15:59:50 - mmengine - INFO - Epoch(val) [23][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9900323586433256, 'occupancy': 0.2679891819357695, 'mIoU': 0.6290107702895475}, '1~2m': {'no_occupancy': 0.9941553948207739, 'occupancy': 0.11682561307901908, 'mIoU': 0.5554905039498965}, '2~3m': {'no_occupancy': 0.9990844202792463, 'occupancy': 0.04465334900117509, 'mIoU': 0.5218688846402106}, '3~4m': {'no_occupancy': 0.9998070556809527, 'occupancy': 0.03564727954971857, 'mIoU': 0.5177271676153357}, 'patch_mean': {'no_occupancy': 0.9957698073560747, 'occupancy': 0.11627885589142055, 'mIoU': 0.5560243316237476}}  total_overall: {'no_occupancy': 0.995775297786394, 'occupancy': 0.2089356630501229, 'mIoU': 0.6023554804182585}  data_time: 0.0107  time: 0.0640
2024/07/08 16:00:56 - mmengine - INFO - Epoch(train)  [24][100/111]  lr: 9.1206e-05  eta: 1:55:48  time: 0.5742  data_time: 0.0007  memory: 2942  grad_norm: 0.9724  loss: 0.2957  loss_bce: 0.0128  loss_dice: 0.2829
2024/07/08 16:01:02 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 16:01:02 - mmengine - INFO - Saving checkpoint at 24 epochs
2024/07/08 16:01:07 - mmengine - INFO - Epoch(val) [24][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9898433856586828, 'occupancy': 0.2689415768883947, 'mIoU': 0.6293924812735388}, '1~2m': {'no_occupancy': 0.9939772545852389, 'occupancy': 0.10719456509633589, 'mIoU': 0.5505859098407874}, '2~3m': {'no_occupancy': 0.9991561143420018, 'occupancy': 0.05546218487394958, 'mIoU': 0.5273091496079757}, '3~4m': {'no_occupancy': 0.9998111857609289, 'occupancy': 0.011787819253438116, 'mIoU': 0.5057995025071835}, 'patch_mean': {'no_occupancy': 0.995696985086713, 'occupancy': 0.11084653652802957, 'mIoU': 0.5532717608073713}}  total_overall: {'no_occupancy': 0.9957027045302737, 'occupancy': 0.2075484773541348, 'mIoU': 0.6016255909422042}  data_time: 0.0099  time: 0.0639
2024/07/08 16:02:12 - mmengine - INFO - Epoch(train)  [25][100/111]  lr: 9.0451e-05  eta: 1:54:35  time: 0.5729  data_time: 0.0007  memory: 2942  grad_norm: 0.9732  loss: 0.3037  loss_bce: 0.0140  loss_dice: 0.2897
2024/07/08 16:02:19 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 16:02:19 - mmengine - INFO - Saving checkpoint at 25 epochs
2024/07/08 16:02:22 - mmengine - INFO - Epoch(val) [25][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9903009456062384, 'occupancy': 0.27143645470753036, 'mIoU': 0.6308687001568843}, '1~2m': {'no_occupancy': 0.994144889381761, 'occupancy': 0.11633972105680915, 'mIoU': 0.5552423052192851}, '2~3m': {'no_occupancy': 0.9991211970939816, 'occupancy': 0.059839357429718874, 'mIoU': 0.5294802772618502}, '3~4m': {'no_occupancy': 0.9997826566786336, 'occupancy': 0.020304568527918784, 'mIoU': 0.5100436126032762}, 'patch_mean': {'no_occupancy': 0.9958374221901536, 'occupancy': 0.1169800254304943, 'mIoU': 0.5564087238103239}}  total_overall: {'no_occupancy': 0.9958426871209453, 'occupancy': 0.21058851862423292, 'mIoU': 0.6032156028725891}  data_time: 0.0097  time: 0.0622
2024/07/08 16:03:28 - mmengine - INFO - Epoch(train)  [26][100/111]  lr: 8.9668e-05  eta: 1:53:21  time: 0.5777  data_time: 0.0007  memory: 2942  grad_norm: 0.9727  loss: 0.2934  loss_bce: 0.0135  loss_dice: 0.2798
2024/07/08 16:03:35 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 16:03:35 - mmengine - INFO - Saving checkpoint at 26 epochs
2024/07/08 16:03:39 - mmengine - INFO - Epoch(val) [26][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9900461851433302, 'occupancy': 0.27040125928585235, 'mIoU': 0.6302237222145912}, '1~2m': {'no_occupancy': 0.9940454163794256, 'occupancy': 0.12115331300249516, 'mIoU': 0.5575993646909604}, '2~3m': {'no_occupancy': 0.9991425921122228, 'occupancy': 0.06431790249897583, 'mIoU': 0.5317302473055993}, '3~4m': {'no_occupancy': 0.9997935426910544, 'occupancy': 0.019607843137254905, 'mIoU': 0.5097006929141547}, 'patch_mean': {'no_occupancy': 0.9957569340815082, 'occupancy': 0.11887007948114456, 'mIoU': 0.5573135067813264}}  total_overall: {'no_occupancy': 0.9957624847500042, 'occupancy': 0.21213841531581892, 'mIoU': 0.6039504500329116}  data_time: 0.0104  time: 0.0635
2024/07/08 16:04:45 - mmengine - INFO - Epoch(train)  [27][100/111]  lr: 8.8857e-05  eta: 1:52:09  time: 0.5760  data_time: 0.0007  memory: 2942  grad_norm: 0.9671  loss: 0.2886  loss_bce: 0.0131  loss_dice: 0.2756
2024/07/08 16:04:51 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 16:04:51 - mmengine - INFO - Saving checkpoint at 27 epochs
2024/07/08 16:04:55 - mmengine - INFO - Epoch(val) [27][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9899958816988803, 'occupancy': 0.27345118213660247, 'mIoU': 0.6317235319177414}, '1~2m': {'no_occupancy': 0.993959242921833, 'occupancy': 0.1146349520977866, 'mIoU': 0.5542970975098098}, '2~3m': {'no_occupancy': 0.999139219028027, 'occupancy': 0.05754212905877518, 'mIoU': 0.5283406740434011}, '3~4m': {'no_occupancy': 0.9998081828951696, 'occupancy': 0.007766990291262135, 'mIoU': 0.5037875865932159}, 'patch_mean': {'no_occupancy': 0.9957256316359774, 'occupancy': 0.11334881339610661, 'mIoU': 0.554537222516042}}  total_overall: {'no_occupancy': 0.9957313101425143, 'occupancy': 0.21193672598605476, 'mIoU': 0.6038340180642845}  data_time: 0.0097  time: 0.0620
2024/07/08 16:05:01 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 16:06:01 - mmengine - INFO - Epoch(train)  [28][100/111]  lr: 8.8020e-05  eta: 1:50:57  time: 0.5785  data_time: 0.0007  memory: 2942  grad_norm: 0.8835  loss: 0.2790  loss_bce: 0.0132  loss_dice: 0.2658
2024/07/08 16:06:08 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 16:06:08 - mmengine - INFO - Saving checkpoint at 28 epochs
2024/07/08 16:06:12 - mmengine - INFO - Epoch(val) [28][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9902249850042651, 'occupancy': 0.27644101713114355, 'mIoU': 0.6333330010677043}, '1~2m': {'no_occupancy': 0.9940419370992347, 'occupancy': 0.11500474303889292, 'mIoU': 0.5545233400690638}, '2~3m': {'no_occupancy': 0.999182010077906, 'occupancy': 0.0656089193825043, 'mIoU': 0.5323954647302052}, '3~4m': {'no_occupancy': 0.9998089333601601, 'occupancy': 0.015473887814313346, 'mIoU': 0.5076414105872367}, 'patch_mean': {'no_occupancy': 0.9958144663853915, 'occupancy': 0.11813214184171353, 'mIoU': 0.5569733041135525}}  total_overall: {'no_occupancy': 0.9958199633479041, 'occupancy': 0.2142841375169424, 'mIoU': 0.6050520504324233}  data_time: 0.0097  time: 0.0637
2024/07/08 16:07:17 - mmengine - INFO - Epoch(train)  [29][100/111]  lr: 8.7157e-05  eta: 1:49:43  time: 0.5759  data_time: 0.0007  memory: 2942  grad_norm: 0.9333  loss: 0.2636  loss_bce: 0.0122  loss_dice: 0.2514
2024/07/08 16:07:24 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 16:07:24 - mmengine - INFO - Saving checkpoint at 29 epochs
2024/07/08 16:07:28 - mmengine - INFO - Epoch(val) [29][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9898241118448944, 'occupancy': 0.2765983096025825, 'mIoU': 0.6332112107237384}, '1~2m': {'no_occupancy': 0.9939286807842372, 'occupancy': 0.12483076089899811, 'mIoU': 0.5593797208416177}, '2~3m': {'no_occupancy': 0.9991358459868831, 'occupancy': 0.05072164948453608, 'mIoU': 0.5249287477357096}, '3~4m': {'no_occupancy': 0.9998081824631475, 'occupancy': 0.019193857965451054, 'mIoU': 0.5095010202142993}, 'patch_mean': {'no_occupancy': 0.9956742052697906, 'occupancy': 0.11783614448789193, 'mIoU': 0.5567551748788413}}  total_overall: {'no_occupancy': 0.9956802145813346, 'occupancy': 0.21728138646055892, 'mIoU': 0.6064808005209468}  data_time: 0.0097  time: 0.0637
2024/07/08 16:08:33 - mmengine - INFO - Epoch(train)  [30][100/111]  lr: 8.6269e-05  eta: 1:48:28  time: 0.5773  data_time: 0.0007  memory: 2942  grad_norm: 0.9040  loss: 0.2730  loss_bce: 0.0125  loss_dice: 0.2605
2024/07/08 16:08:40 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 16:08:40 - mmengine - INFO - Saving checkpoint at 30 epochs
2024/07/08 16:08:44 - mmengine - INFO - Epoch(val) [30][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9898030681029023, 'occupancy': 0.2652418539306778, 'mIoU': 0.62752246101679}, '1~2m': {'no_occupancy': 0.9939420487934625, 'occupancy': 0.12048652776262682, 'mIoU': 0.5572142882780446}, '2~3m': {'no_occupancy': 0.9991215711713173, 'occupancy': 0.06137184115523466, 'mIoU': 0.530246706163276}, '3~4m': {'no_occupancy': 0.9998081825351514, 'occupancy': 0.01730769230769231, 'mIoU': 0.5085579374214219}, 'patch_mean': {'no_occupancy': 0.9956687176507084, 'occupancy': 0.11610197878905788, 'mIoU': 0.5558853482198831}}  total_overall: {'no_occupancy': 0.9956744070847426, 'occupancy': 0.20867618560643492, 'mIoU': 0.6021752963455888}  data_time: 0.0096  time: 0.0619
2024/07/08 16:09:49 - mmengine - INFO - Epoch(train)  [31][100/111]  lr: 8.5355e-05  eta: 1:47:14  time: 0.5719  data_time: 0.0007  memory: 2942  grad_norm: 0.8480  loss: 0.2852  loss_bce: 0.0130  loss_dice: 0.2721
2024/07/08 16:09:55 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 16:09:55 - mmengine - INFO - Saving checkpoint at 31 epochs
2024/07/08 16:10:00 - mmengine - INFO - Epoch(val) [31][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9900868879547368, 'occupancy': 0.2685772659990688, 'mIoU': 0.6293320769769027}, '1~2m': {'no_occupancy': 0.9940194218524014, 'occupancy': 0.114079020589872, 'mIoU': 0.5540492212211368}, '2~3m': {'no_occupancy': 0.9991718843085591, 'occupancy': 0.05199828104856037, 'mIoU': 0.5255850826785597}, '3~4m': {'no_occupancy': 0.9998220718717027, 'occupancy': 0.006289308176100629, 'mIoU': 0.5030556900239017}, 'patch_mean': {'no_occupancy': 0.99577506649685, 'occupancy': 0.11023596895340045, 'mIoU': 0.5530055177251252}}  total_overall: {'no_occupancy': 0.995780528184271, 'occupancy': 0.20856847067116013, 'mIoU': 0.6021744994277156}  data_time: 0.0100  time: 0.0633
2024/07/08 16:11:06 - mmengine - INFO - Epoch(train)  [32][100/111]  lr: 8.4418e-05  eta: 1:46:03  time: 0.5758  data_time: 0.0008  memory: 2942  grad_norm: 0.8708  loss: 0.2673  loss_bce: 0.0124  loss_dice: 0.2549
2024/07/08 16:11:12 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 16:11:12 - mmengine - INFO - Saving checkpoint at 32 epochs
2024/07/08 16:11:16 - mmengine - INFO - Epoch(val) [32][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9899635486725229, 'occupancy': 0.25722370126702254, 'mIoU': 0.6235936249697727}, '1~2m': {'no_occupancy': 0.9940066920388633, 'occupancy': 0.11292743953294411, 'mIoU': 0.5534670657859037}, '2~3m': {'no_occupancy': 0.9991583679696322, 'occupancy': 0.054008438818565395, 'mIoU': 0.5265834033940988}, '3~4m': {'no_occupancy': 0.9998130627122049, 'occupancy': 0.009940357852882704, 'mIoU': 0.5048767102825438}, 'patch_mean': {'no_occupancy': 0.9957354178483058, 'occupancy': 0.10852498436785368, 'mIoU': 0.5521302011080798}}  total_overall: {'no_occupancy': 0.9957407078080557, 'occupancy': 0.20079500083730684, 'mIoU': 0.5982678543226813}  data_time: 0.0103  time: 0.0631
2024/07/08 16:12:22 - mmengine - INFO - Epoch(train)  [33][100/111]  lr: 8.3457e-05  eta: 1:44:49  time: 0.5749  data_time: 0.0007  memory: 2942  grad_norm: 0.9032  loss: 0.2693  loss_bce: 0.0125  loss_dice: 0.2567
2024/07/08 16:12:28 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240708_153008
2024/07/08 16:12:28 - mmengine - INFO - Saving checkpoint at 33 epochs
2024/07/08 16:12:32 - mmengine - INFO - Epoch(val) [33][27/27]    by_distance: {'0-1m': {'no_occupancy': 0.9899755456776281, 'occupancy': 0.26848898908531055, 'mIoU': 0.6292322673814693}, '1~2m': {'no_occupancy': 0.9940475832699711, 'occupancy': 0.1148539187754874, 'mIoU': 0.5544507510227292}, '2~3m': {'no_occupancy': 0.9991722463394891, 'occupancy': 0.06922752216124947, 'mIoU': 0.5341998842503692}, '3~4m': {'no_occupancy': 0.9998010506029668, 'occupancy': 0.01119402985074627, 'mIoU': 0.5054975402268566}, 'patch_mean': {'no_occupancy': 0.9957491064725138, 'occupancy': 0.11594111496819842, 'mIoU': 0.5558451107203561}}  total_overall: {'no_occupancy': 0.9957546772852227, 'occupancy': 0.20972297521817448, 'mIoU': 0.6027388262516986}  data_time: 0.0088  time: 0.0619
