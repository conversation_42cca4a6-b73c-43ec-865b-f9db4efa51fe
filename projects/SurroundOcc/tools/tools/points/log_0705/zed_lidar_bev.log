2024/07/09 14:20:47 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: linux
    Python: 3.8.10 (default, May 26 2023, 14:05:08) [GCC 9.4.0]
    CUDA available: True
    numpy_random_seed: 1546261788
    GPU 0,1,2,3: NVIDIA TITAN Xp
    CUDA_HOME: /usr/local/cuda
    NVCC: Cuda compilation tools, release 11.6, V11.6.124
    GCC: x86_64-linux-gnu-gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0
    PyTorch: 1.13.0+cu116
    PyTorch compiling details: PyTorch built with:
  - GCC 9.3
  - C++ Version: 201402
  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v2.6.0 (Git Hash 52b5f107dd9cf10910aaa19cb47f3abf9b349815)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX2
  - CUDA Runtime 11.6
  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86
  - CuDNN 8.4
    - Built with CuDNN 8.3.2
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.6, CUDNN_VERSION=8.3.2, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -fabi-version=11 -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -DEDGE_PROFILER_USE_KINETO -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wunused-local-typedefs -Wno-unused-parameter -Wno-unused-function -Wno-unused-result -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Werror=cast-function-type -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.13.0, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, 

    TorchVision: 0.14.0+cu116
    OpenCV: 4.8.1
    MMEngine: 0.10.1

Runtime environment:
    cudnn_benchmark: False
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 1}
    dist_cfg: {'backend': 'nccl'}
    seed: 1546261788
    Distributed launcher: pytorch
    Distributed training: True
    GPU number: 4
------------------------------------------------------------

2024/07/09 14:20:47 - mmengine - INFO - Config:
auto_scale_lr = dict(base_batch_size=4, enable=False)
bn_kwargs = dict(eps=2e-05, momentum=0.1)
class_names = [
    'drivable_area',
    'stop_line',
    'unknow',
]
custom_imports = dict(
    allow_failed_imports=False,
    imports=[
        'projects.SurroundOcc.surroundocc.models',
        'projects.SurroundOcc.surroundocc.datasets',
    ])
data_root = '/home/<USER>/indoor/zed_lidar'
data_shape = (
    3,
    384,
    512,
)
dataset_type = 'EcoAi.KujialeStereoDataset'
default_hooks = dict(
    checkpoint=dict(interval=1, type='CheckpointHook'),
    logger=dict(interval=100, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    timer=dict(type='IterTimerHook'),
    visualization=dict(type='mmdet3d.Det3DVisualizationHook'))
default_scope = 'mmdet'
depoly_model = dict(
    bev_decoder=dict(
        Dz=1, in_dim=256, num_classes=3, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=64, num_channels=[
                128,
                256,
                512,
            ]),
        fpn_lss_params=dict(in_channels=640, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(
        depth=18,
        frozen_stages=-1,
        init_cfg=dict(
            checkpoint='projects/SurroundOcc/depoly/resnet18-f37072fd.pth',
            type='Pretrained'),
        norm_cfg=dict(requires_grad=True, type='BN'),
        norm_eval=False,
        num_stages=4,
        out_indices=(
            2,
            3,
        ),
        style='pytorch',
        type='ResNet',
        with_cp=False),
    img_neck=dict(
        add_extra_convs='on_output',
        in_channels=[
            256,
            512,
        ],
        num_outs=1,
        out_channels=64,
        out_ids=[
            0,
        ],
        relu_before_extra_convs=True,
        start_level=0,
        type='CustomFPN'),
    type='EcoOccVisionDepoly',
    use_grid_mask=False,
    view_transformer=dict(
        bev_size=[
            -2.0,
            2.0,
            0.0,
            4.0,
            0.05,
        ],
        depth=120,
        depth_grid_quant_scale=0.03125,
        feat_channels=64,
        grid_quant_scale=0.0078125,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=4,
        num_views=2,
        type='LSSTransformer',
        z_range=(
            -0.02,
            0.055,
        )))
depth_quant_scale = 0.03125
depthview_shape = [
    720,
    768.0,
]
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=1))
featview_shape = [
    144.0,
    32.0,
]
find_unused_parameters = True
grid_quant_scale = 0.0078125
img_norm_cfg = dict(
    mean=[
        103.53,
        116.28,
        123.675,
    ], std=[
        1.0,
        1.0,
        1.0,
    ], to_rgb=False)
keys = ('img', )
launcher = 'pytorch'
load_from = None
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=50)
lss_bev_size = [
    -2.0,
    2.0,
    0.0,
    4.0,
    0.05,
]
lss_depth = 120
lss_grid_size = (
    80,
    80,
)
lss_num_points = 4
lss_z_range = (
    -0.02,
    0.055,
)
meta_keys = (
    'lidar2img',
    'img_shape',
    'pc_range',
    'occ_size',
    'gt_occ',
    'occ_path',
    'filename',
)
model = dict(
    bev_decoder=dict(
        Dz=1, in_dim=256, num_classes=3, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=64, num_channels=[
                128,
                256,
                512,
            ]),
        fpn_lss_params=dict(in_channels=640, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(type='EfficientNetB0'),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    is_vis=False,
    lambda_dice=1,
    type='EcoBEVVision',
    use_grid_mask=True,
    view_transformer=dict(
        bev_size=[
            -2.0,
            2.0,
            0.0,
            4.0,
            0.05,
        ],
        depth=120,
        depth_grid_quant_scale=0.03125,
        feat_channels=64,
        grid_quant_scale=0.0078125,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=4,
        num_views=2,
        type='LSSTransformer',
        z_range=(
            -0.02,
            0.055,
        )))
numC_Trans = 64
occ_size = [
    80,
    80,
    1,
]
optim_wrapper = dict(
    clip_grad=dict(max_norm=5, norm_type=2),
    optimizer=dict(lr=0.0001, type='AdamW', weight_decay=0.01),
    type='AmpOptimWrapper')
param_scheduler = [
    dict(
        begin=0,
        by_epoch=False,
        end=500,
        start_factor=0.3333333333333333,
        type='LinearLR'),
    dict(by_epoch=True, type='CosineAnnealingLR'),
]
point_cloud_range = [
    -2.0,
    0.0,
    -0.02,
    2.0,
    4.0,
    0.055,
]
resume = False
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0709_val.pkl',
        classes=[
            'drivable_area',
            'stop_line',
            'unknow',
        ],
        data_root='/home/<USER>/indoor/zed_lidar',
        occ_size=[
            80,
            80,
            1,
        ],
        pc_range=[
            -2.0,
            0.0,
            -0.02,
            2.0,
            4.0,
            0.055,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(
                classes=[
                    'drivable_area',
                    'stop_line',
                    'unknow',
                ],
                type='LoadBEVSegmentationECO',
                xbound=[
                    0.0,
                    4.0,
                    0.05,
                ],
                ybound=[
                    -2.0,
                    2.0,
                    0.05,
                ]),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    classes=[
        'drivable_area',
        'stop_line',
        'unknow',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
test_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(
        classes=[
            'drivable_area',
            'stop_line',
            'unknow',
        ],
        type='LoadBEVSegmentationECO',
        xbound=[
            0.0,
            4.0,
            0.05,
        ],
        ybound=[
            -2.0,
            2.0,
            0.05,
        ]),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
train_cfg = dict(max_epochs=24, type='EpochBasedTrainLoop', val_interval=1)
train_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0709_train.pkl',
        classes=[
            'drivable_area',
            'stop_line',
            'unknow',
        ],
        data_root='/home/<USER>/indoor/zed_lidar',
        occ_size=[
            80,
            80,
            1,
        ],
        pc_range=[
            -2.0,
            0.0,
            -0.02,
            2.0,
            4.0,
            0.055,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(
                classes=[
                    'drivable_area',
                    'stop_line',
                    'unknow',
                ],
                type='LoadBEVSegmentationECO',
                xbound=[
                    0.0,
                    4.0,
                    0.05,
                ],
                ybound=[
                    -2.0,
                    2.0,
                    0.05,
                ]),
            dict(type='PhotoMetricDistortionMultiViewImage'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        test_mode=False,
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    prefetch_factor=8,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(
        classes=[
            'drivable_area',
            'stop_line',
            'unknow',
        ],
        type='LoadBEVSegmentationECO',
        xbound=[
            0.0,
            4.0,
            0.05,
        ],
        ybound=[
            -2.0,
            2.0,
            0.05,
        ]),
    dict(type='PhotoMetricDistortionMultiViewImage'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
use_semantic = True
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0709_val.pkl',
        classes=[
            'drivable_area',
            'stop_line',
            'unknow',
        ],
        data_root='/home/<USER>/indoor/zed_lidar',
        occ_size=[
            80,
            80,
            1,
        ],
        pc_range=[
            -2.0,
            0.0,
            -0.02,
            2.0,
            4.0,
            0.055,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(
                classes=[
                    'drivable_area',
                    'stop_line',
                    'unknow',
                ],
                type='LoadBEVSegmentationECO',
                xbound=[
                    0.0,
                    4.0,
                    0.05,
                ],
                ybound=[
                    -2.0,
                    2.0,
                    0.05,
                ]),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    classes=[
        'drivable_area',
        'stop_line',
        'unknow',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
val_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(
        classes=[
            'drivable_area',
            'stop_line',
            'unknow',
        ],
        type='LoadBEVSegmentationECO',
        xbound=[
            0.0,
            4.0,
            0.05,
        ],
        ybound=[
            -2.0,
            2.0,
            0.05,
        ]),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
view_shape = [
    24.0,
    32.0,
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='mmdet3d.Det3DLocalVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = 'work_dirs/zed_lidar_0704/zed_lidar_0709_bev'

2024/07/09 14:20:50 - mmengine - INFO - Autoplay mode, press [SPACE] to pause.
2024/07/09 14:20:50 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2024/07/09 14:20:52 - mmengine - WARNING - The prefix is not set in metric class BEVMetric.
Name of parameter - Initialization information

img_backbone.stem.stem.conv.weight - torch.Size([32, 3, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stem.stem.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stem.stem.norm.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stem.stem.norm.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.0.conv.depth.conv.weight - torch.Size([32, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.0.conv.depth.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.0.conv.depth.norm.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.0.conv.depth.norm.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.0.conv.project.conv.weight - torch.Size([16, 32, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.0.conv.project.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.0.conv.project.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.0.conv.project.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.conv.expand_conv.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.expand_conv.conv.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.conv.expand_conv.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.conv.expand_conv.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.conv.depth.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.depth.conv.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.conv.depth.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.conv.depth.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.conv.project.conv.weight - torch.Size([24, 96, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.project.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.conv.project.norm.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.conv.project.norm.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.expand_conv.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.blocks.0.depth.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.depth.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.blocks.0.depth.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.blocks.0.depth.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.blocks.0.project.conv.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.project.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.blocks.0.project.norm.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.1.blocks.0.project.norm.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.conv.expand_conv.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.expand_conv.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.conv.expand_conv.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.conv.expand_conv.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.conv.depth.conv.weight - torch.Size([144, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.depth.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.conv.depth.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.conv.depth.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.conv.project.conv.weight - torch.Size([40, 144, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.project.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.conv.project.norm.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.conv.project.norm.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.conv.weight - torch.Size([240, 40, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.expand_conv.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.blocks.0.depth.conv.weight - torch.Size([240, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.depth.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.blocks.0.depth.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.blocks.0.depth.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.blocks.0.project.conv.weight - torch.Size([40, 240, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.project.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.blocks.0.project.norm.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.2.blocks.0.project.norm.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.conv.expand_conv.conv.weight - torch.Size([240, 40, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.expand_conv.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.conv.expand_conv.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.conv.expand_conv.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.conv.depth.conv.weight - torch.Size([240, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.depth.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.conv.depth.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.conv.depth.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.conv.project.conv.weight - torch.Size([80, 240, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.conv.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.conv.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.0.depth.conv.weight - torch.Size([480, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.0.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.0.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.0.project.conv.weight - torch.Size([80, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.0.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.0.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.1.depth.conv.weight - torch.Size([480, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.1.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.1.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.1.project.conv.weight - torch.Size([80, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.1.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.3.blocks.1.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.conv.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.conv.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.conv.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.conv.depth.conv.weight - torch.Size([480, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.conv.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.conv.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.conv.project.conv.weight - torch.Size([112, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.conv.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.conv.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.0.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.0.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.0.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.0.project.conv.weight - torch.Size([112, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.0.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.0.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.1.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.1.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.1.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.1.project.conv.weight - torch.Size([112, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.1.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.4.blocks.1.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.conv.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.conv.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.conv.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.conv.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.conv.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.conv.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.conv.project.conv.weight - torch.Size([192, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.conv.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.conv.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.0.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.0.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.0.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.0.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.0.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.0.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.1.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.1.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.1.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.1.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.1.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.1.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.2.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.2.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.2.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.2.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.2.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.5.blocks.2.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.6.conv.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.6.conv.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.6.conv.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.6.conv.depth.conv.weight - torch.Size([1152, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.6.conv.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.6.conv.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.6.conv.project.conv.weight - torch.Size([320, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.project.conv.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.6.conv.project.norm.weight - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.stage_blocks.6.conv.project.norm.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_upconv_0_4.conv.conv.weight - torch.Size([112, 320, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_4.conv.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_upconv_0_3.conv.conv.weight - torch.Size([40, 224, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_3.conv.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_upconv_0_2.conv.conv.weight - torch.Size([24, 80, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_2.conv.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_upconv_0_1.conv.conv.weight - torch.Size([16, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_1.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_upconv_0_0.conv.conv.weight - torch.Size([16, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_0.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_upconv_1_4.conv.conv.weight - torch.Size([224, 224, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_4.conv.conv.bias - torch.Size([224]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_upconv_1_3.conv.conv.weight - torch.Size([80, 80, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_3.conv.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_upconv_1_2.conv.conv.weight - torch.Size([48, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_2.conv.conv.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_upconv_1_1.conv.conv.weight - torch.Size([32, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_1.conv.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_upconv_1_0.conv.conv.weight - torch.Size([16, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_0.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_dispconv2.conv.weight - torch.Size([2, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv2.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_dispconv1.conv.weight - torch.Size([2, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv1.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_dispconv0.conv.weight - torch.Size([2, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv0.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_conf_mask_conv21.conv.weight - torch.Size([2, 66, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv21.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_conf_mask_conv11.conv.weight - torch.Size([2, 44, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv11.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_backbone.decoder.convs_conf_mask_conv01.conv.weight - torch.Size([2, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv01.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.0.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.0.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.0.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.0.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.1.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.1.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.1.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.1.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.2.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.2.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.2.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.2.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.3.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.3.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.3.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.poolings.3.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.project.0.weight - torch.Size([64, 384, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.project.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.project.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.extract.project.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.project.0.weight - torch.Size([64, 112, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.project.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.project.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.project.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.upsample.1.0.0.weight - torch.Size([64, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.upsample.1.0.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.upsample.1.0.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.upsample.1.0.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.upsample.1.1.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.upsample.1.1.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.upsample.1.1.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.upsample.1.1.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.fusion.0.0.weight - torch.Size([64, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.fusion.0.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.fusion.0.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.fusion.0.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.fusion.1.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.fusion.1.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.fusion.1.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

img_neck.seg_convs.0.fusion.1.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

view_transformer.depth_net.0.weight - torch.Size([120, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

view_transformer.feat_net.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.0.0.conv1.weight - torch.Size([128, 64, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.0.0.bn1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.0.0.bn1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.0.0.conv2.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.0.0.bn2.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.0.0.bn2.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.0.0.downsample.weight - torch.Size([128, 64, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.0.0.downsample.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.0.1.conv1.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.0.1.bn1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.0.1.bn1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.0.1.conv2.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.0.1.bn2.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.0.1.bn2.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.1.0.conv1.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.1.0.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.1.0.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.1.0.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.1.0.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.1.0.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.1.0.downsample.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.1.0.downsample.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.1.1.conv1.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.1.1.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.1.1.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.1.1.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.1.1.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.1.1.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.2.0.conv1.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.2.0.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.2.0.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.2.0.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.2.0.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.2.0.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.2.0.downsample.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.2.0.downsample.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.2.1.conv1.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.2.1.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.2.1.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.2.1.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.2.1.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.custom_resnet.layers.2.1.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.fpn_lss.conv.0.weight - torch.Size([512, 640, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.fpn_lss.conv.1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.fpn_lss.conv.1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.fpn_lss.conv.3.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.fpn_lss.conv.4.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.fpn_lss.conv.4.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.fpn_lss.up2.1.weight - torch.Size([256, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.fpn_lss.up2.2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.fpn_lss.up2.2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.fpn_lss.up2.4.weight - torch.Size([256, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_encoder.fpn_lss.up2.4.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_decoder.conv_head.0.weight - torch.Size([128, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_decoder.conv_head.0.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_decoder.conv_head.2.weight - torch.Size([64, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_decoder.conv_head.2.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_decoder.conv_head.4.weight - torch.Size([3, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  

bev_decoder.conv_head.4.bias - torch.Size([3]): 
The value is the same before and after calling `init_weights` of EcoBEVVision  
2024/07/09 14:20:53 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2024/07/09 14:20:53 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2024/07/09 14:20:53 - mmengine - INFO - Checkpoints will be saved to /home/<USER>/ecoaitoolkit/work_dirs/zed_lidar_0704/zed_lidar_0709_bev.
2024/07/09 14:22:58 - mmengine - INFO - Epoch(train)  [1][100/217]  lr: 4.6560e-05  eta: 1:46:45  time: 1.0546  data_time: 0.0011  memory: 2850  grad_norm: 50.2527  loss: 0.9481  loss_ce: 0.3406  loss_dice: 0.6075
2024/07/09 14:24:04 - mmengine - INFO - Epoch(train)  [1][200/217]  lr: 5.9920e-05  eta: 1:19:49  time: 0.6607  data_time: 0.0007  memory: 2850  grad_norm: 50.2747  loss: 0.8223  loss_ce: 0.2825  loss_dice: 0.5398
2024/07/09 14:24:14 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 14:24:14 - mmengine - INFO - Saving checkpoint at 1 epochs
2024/07/09 14:24:20 - mmengine - INFO - Epoch(val) [1][21/21]    by_distance: {'0-1m': {'drivable_area': 0.5742428907806557, 'stop_line': 0.07409082617181154, 'unknow': 0.9546545548880473, 'mIoU': 0.5343294239468381}, '1~2m': {'drivable_area': 0.3399723752874257, 'stop_line': 0.01622546699195281, 'unknow': 0.8753001001419237, 'mIoU': 0.41049931414043406}, '2~3m': {'drivable_area': 0.1232261566027175, 'stop_line': 0.0, 'unknow': 0.8003698736493468, 'mIoU': 0.3078653434173548}, '3~4m': {'drivable_area': 0.08147157228631642, 'stop_line': 0.0, 'unknow': 0.9232488002622178, 'mIoU': 0.3349067908495114}, 'patch_mean': {'drivable_area': 0.2797282487392788, 'stop_line': 0.022579073290941085, 'unknow': 0.888393332235384, 'mIoU': 0.39690021808853465}}  total_overall: {'drivable_area': 0.27620866911749997, 'stop_line': 0.05521987825538652, 'unknow': 0.8873703176262018, 'mIoU': 0.40626628833302947}  data_time: 0.0708  time: 0.1932
2024/07/09 14:25:45 - mmengine - INFO - Epoch(train)  [2][100/217]  lr: 7.5228e-05  eta: 1:13:49  time: 0.6757  data_time: 0.0010  memory: 2850  grad_norm: 28.8117  loss: 0.7943  loss_ce: 0.2793  loss_dice: 0.5150
2024/07/09 14:26:54 - mmengine - INFO - Epoch(train)  [2][200/217]  lr: 8.8531e-05  eta: 1:08:06  time: 0.6808  data_time: 0.0008  memory: 2850  grad_norm: 26.9323  loss: 0.8361  loss_ce: 0.3043  loss_dice: 0.5318
2024/07/09 14:27:05 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 14:27:05 - mmengine - INFO - Saving checkpoint at 2 epochs
2024/07/09 14:27:10 - mmengine - INFO - Epoch(val) [2][21/21]    by_distance: {'0-1m': {'drivable_area': 0.7896379332027502, 'stop_line': 0.18652115864631102, 'unknow': 0.9654830811542858, 'mIoU': 0.6472140576677824}, '1~2m': {'drivable_area': 0.5444259994861762, 'stop_line': 0.0859663059444228, 'unknow': 0.9271582486228516, 'mIoU': 0.5191835180178169}, '2~3m': {'drivable_area': 0.2628017490899695, 'stop_line': 0.0, 'unknow': 0.9444869187638997, 'mIoU': 0.40242955595128976}, '3~4m': {'drivable_area': 0.10159911606891388, 'stop_line': 0.0, 'unknow': 0.955642843615359, 'mIoU': 0.35241398656142425}, 'patch_mean': {'drivable_area': 0.42461619946195245, 'stop_line': 0.06812186614768345, 'unknow': 0.948192773039099, 'mIoU': 0.48031027954957833}}  total_overall: {'drivable_area': 0.522767087243264, 'stop_line': 0.12588989146996002, 'unknow': 0.9481011142446932, 'mIoU': 0.532252697652639}  data_time: 0.0514  time: 0.1596
2024/07/09 14:28:36 - mmengine - INFO - Epoch(train)  [3][100/217]  lr: 9.8296e-05  eta: 1:05:56  time: 0.6858  data_time: 0.0010  memory: 2850  grad_norm: 17.9039  loss: 0.7810  loss_ce: 0.2652  loss_dice: 0.5158
2024/07/09 14:29:46 - mmengine - INFO - Epoch(train)  [3][200/217]  lr: 9.8296e-05  eta: 1:02:48  time: 0.6981  data_time: 0.0007  memory: 2850  grad_norm: 19.1981  loss: 0.7393  loss_ce: 0.2569  loss_dice: 0.4823
2024/07/09 14:29:57 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 14:29:57 - mmengine - INFO - Saving checkpoint at 3 epochs
2024/07/09 14:30:02 - mmengine - INFO - Epoch(val) [3][21/21]    by_distance: {'0-1m': {'drivable_area': 0.7624223417928923, 'stop_line': 0.15507481160581016, 'unknow': 0.9585445971868083, 'mIoU': 0.6253472501951702}, '1~2m': {'drivable_area': 0.5478551935721524, 'stop_line': 0.08252141523447812, 'unknow': 0.926298353113465, 'mIoU': 0.5188916539733651}, '2~3m': {'drivable_area': 0.36487379362223554, 'stop_line': 0.0012591286829513977, 'unknow': 0.9577057582593071, 'mIoU': 0.4412795601881647}, '3~4m': {'drivable_area': 0.22346498997663458, 'stop_line': 0.0, 'unknow': 0.9793100237253687, 'mIoU': 0.40092500456733443}, 'patch_mean': {'drivable_area': 0.4746540797409787, 'stop_line': 0.059713838880809916, 'unknow': 0.9554646830712373, 'mIoU': 0.4966108672310086}}  total_overall: {'drivable_area': 0.5737708668145318, 'stop_line': 0.11257134263592147, 'unknow': 0.9560206255911984, 'mIoU': 0.5474542783472173}  data_time: 0.0485  time: 0.1583
2024/07/09 14:31:28 - mmengine - INFO - Epoch(train)  [4][100/217]  lr: 9.6194e-05  eta: 1:01:15  time: 0.6935  data_time: 0.0010  memory: 2850  grad_norm: 14.5433  loss: 0.7152  loss_ce: 0.2413  loss_dice: 0.4739
2024/07/09 14:32:38 - mmengine - INFO - Epoch(train)  [4][200/217]  lr: 9.6194e-05  eta: 0:58:49  time: 0.7036  data_time: 0.0007  memory: 2850  grad_norm: 14.4750  loss: 0.7084  loss_ce: 0.2413  loss_dice: 0.4671
2024/07/09 14:32:49 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 14:32:49 - mmengine - INFO - Saving checkpoint at 4 epochs
2024/07/09 14:32:55 - mmengine - INFO - Epoch(val) [4][21/21]    by_distance: {'0-1m': {'drivable_area': 0.8052406652974954, 'stop_line': 0.19983751747166953, 'unknow': 0.9733451139815498, 'mIoU': 0.6594744322502382}, '1~2m': {'drivable_area': 0.6114571291438902, 'stop_line': 0.09432494559184708, 'unknow': 0.9432925689533136, 'mIoU': 0.5496915478963503}, '2~3m': {'drivable_area': 0.32157577297235745, 'stop_line': 0.010462868190606163, 'unknow': 0.9617388094998695, 'mIoU': 0.43125915022094435}, '3~4m': {'drivable_area': 0.09834210790441344, 'stop_line': 0.0, 'unknow': 0.9873418989827358, 'mIoU': 0.36189466896238304}, 'patch_mean': {'drivable_area': 0.45915391882953915, 'stop_line': 0.0761563328135307, 'unknow': 0.9664295978543671, 'mIoU': 0.500579949832479}}  total_overall: {'drivable_area': 0.6205578808094026, 'stop_line': 0.1441365311700306, 'unknow': 0.9667682228036955, 'mIoU': 0.5771542115943763}  data_time: 0.0585  time: 0.1754
2024/07/09 14:34:25 - mmengine - INFO - Epoch(train)  [5][100/217]  lr: 9.3301e-05  eta: 0:57:43  time: 0.7033  data_time: 0.0009  memory: 2850  grad_norm: 15.0035  loss: 0.6751  loss_ce: 0.2278  loss_dice: 0.4474
2024/07/09 14:34:48 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 14:35:35 - mmengine - INFO - Epoch(train)  [5][200/217]  lr: 9.3301e-05  eta: 0:55:35  time: 0.6963  data_time: 0.0007  memory: 2850  grad_norm: 12.6336  loss: 0.6626  loss_ce: 0.2259  loss_dice: 0.4367
2024/07/09 14:35:47 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 14:35:47 - mmengine - INFO - Saving checkpoint at 5 epochs
2024/07/09 14:35:52 - mmengine - INFO - Epoch(val) [5][21/21]    by_distance: {'0-1m': {'drivable_area': 0.8193554455454587, 'stop_line': 0.21182774085338726, 'unknow': 0.9732904631111684, 'mIoU': 0.6681578831700047}, '1~2m': {'drivable_area': 0.6055095572606539, 'stop_line': 0.09803383002586463, 'unknow': 0.9385842022902363, 'mIoU': 0.5473758631922516}, '2~3m': {'drivable_area': 0.26516685398390843, 'stop_line': 0.016880330514034432, 'unknow': 0.9572400261812631, 'mIoU': 0.4130957368930687}, '3~4m': {'drivable_area': 0.03802962217655636, 'stop_line': 0.0, 'unknow': 0.985774525911766, 'mIoU': 0.34126804936277405}, 'patch_mean': {'drivable_area': 0.4320153697416444, 'stop_line': 0.08168547534832159, 'unknow': 0.9637223043736084, 'mIoU': 0.4924743831545248}}  total_overall: {'drivable_area': 0.6082157818773569, 'stop_line': 0.1410988033934724, 'unknow': 0.9640411729334919, 'mIoU': 0.5711185860681071}  data_time: 0.0447  time: 0.1525
2024/07/09 14:37:18 - mmengine - INFO - Epoch(train)  [6][100/217]  lr: 8.9668e-05  eta: 0:54:12  time: 0.6984  data_time: 0.0009  memory: 2850  grad_norm: 12.3909  loss: 0.6429  loss_ce: 0.2157  loss_dice: 0.4272
2024/07/09 14:38:28 - mmengine - INFO - Epoch(train)  [6][200/217]  lr: 8.9668e-05  eta: 0:52:18  time: 0.7011  data_time: 0.0008  memory: 2850  grad_norm: 12.1117  loss: 0.6235  loss_ce: 0.2078  loss_dice: 0.4158
2024/07/09 14:38:39 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 14:38:39 - mmengine - INFO - Saving checkpoint at 6 epochs
2024/07/09 14:38:44 - mmengine - INFO - Epoch(val) [6][21/21]    by_distance: {'0-1m': {'drivable_area': 0.7539397256998093, 'stop_line': 0.15526987062210246, 'unknow': 0.9739625150416109, 'mIoU': 0.6277240371211742}, '1~2m': {'drivable_area': 0.3927312359785521, 'stop_line': 0.060966497993987276, 'unknow': 0.9360446219935921, 'mIoU': 0.4632474519887105}, '2~3m': {'drivable_area': 0.1955959573477343, 'stop_line': 0.002547770700636943, 'unknow': 0.9641436471170214, 'mIoU': 0.3874291250551309}, '3~4m': {'drivable_area': 0.015713997560296223, 'stop_line': 0.0, 'unknow': 0.9869273665129852, 'mIoU': 0.33421378802442714}, 'patch_mean': {'drivable_area': 0.33949522914659797, 'stop_line': 0.05469603482918167, 'unknow': 0.9652695376663023, 'mIoU': 0.45315360054736065}}  total_overall: {'drivable_area': 0.5096399048739179, 'stop_line': 0.10296485397094865, 'unknow': 0.9654551402276386, 'mIoU': 0.5260199663575017}  data_time: 0.0499  time: 0.1606
2024/07/09 14:40:10 - mmengine - INFO - Epoch(train)  [7][100/217]  lr: 8.5355e-05  eta: 0:50:54  time: 0.6921  data_time: 0.0009  memory: 2850  grad_norm: 11.1715  loss: 0.6049  loss_ce: 0.1980  loss_dice: 0.4069
2024/07/09 14:41:20 - mmengine - INFO - Epoch(train)  [7][200/217]  lr: 8.5355e-05  eta: 0:49:08  time: 0.6952  data_time: 0.0007  memory: 2850  grad_norm: 12.0892  loss: 0.6236  loss_ce: 0.2134  loss_dice: 0.4102
2024/07/09 14:41:32 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 14:41:32 - mmengine - INFO - Saving checkpoint at 7 epochs
2024/07/09 14:41:37 - mmengine - INFO - Epoch(val) [7][21/21]    by_distance: {'0-1m': {'drivable_area': 0.7011536890345543, 'stop_line': 0.12084862972765305, 'unknow': 0.9739241641437626, 'mIoU': 0.5986421609686566}, '1~2m': {'drivable_area': 0.38342918126208725, 'stop_line': 0.04521302252354805, 'unknow': 0.9386426683776902, 'mIoU': 0.4557616240544418}, '2~3m': {'drivable_area': 0.1115338839933636, 'stop_line': 0.005936349145275655, 'unknow': 0.9616884941824517, 'mIoU': 0.359719575773697}, '3~4m': {'drivable_area': 0.009122895934658233, 'stop_line': 0.0, 'unknow': 0.9869562053833394, 'mIoU': 0.3320263671059992}, 'patch_mean': {'drivable_area': 0.30130991255616585, 'stop_line': 0.04299950034911919, 'unknow': 0.965302883021811, 'mIoU': 0.4365374319756987}}  total_overall: {'drivable_area': 0.47142316458925776, 'stop_line': 0.0829173341706666, 'unknow': 0.9654400259521387, 'mIoU': 0.5065935082373544}  data_time: 0.0418  time: 0.1521
2024/07/09 14:43:03 - mmengine - INFO - Epoch(train)  [8][100/217]  lr: 8.0438e-05  eta: 0:47:45  time: 0.7018  data_time: 0.0009  memory: 2850  grad_norm: 11.4383  loss: 0.6081  loss_ce: 0.2044  loss_dice: 0.4037
2024/07/09 14:44:12 - mmengine - INFO - Epoch(train)  [8][200/217]  lr: 8.0438e-05  eta: 0:46:05  time: 0.6987  data_time: 0.0007  memory: 2850  grad_norm: 9.4882  loss: 0.5738  loss_ce: 0.1846  loss_dice: 0.3893
2024/07/09 14:44:24 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 14:44:24 - mmengine - INFO - Saving checkpoint at 8 epochs
2024/07/09 14:44:29 - mmengine - INFO - Epoch(val) [8][21/21]    by_distance: {'0-1m': {'drivable_area': 0.749129617590573, 'stop_line': 0.15380110514632958, 'unknow': 0.9751314299186672, 'mIoU': 0.6260207175518566}, '1~2m': {'drivable_area': 0.5674826485151296, 'stop_line': 0.08089564960320049, 'unknow': 0.9451716154666872, 'mIoU': 0.5311833045283391}, '2~3m': {'drivable_area': 0.36722363389030055, 'stop_line': 0.018930652794618065, 'unknow': 0.9677355933724168, 'mIoU': 0.45129662668577847}, '3~4m': {'drivable_area': 0.18319502249837485, 'stop_line': 0.0, 'unknow': 0.9877273043839572, 'mIoU': 0.3903074422941107}, 'patch_mean': {'drivable_area': 0.4667577306235945, 'stop_line': 0.06340685188603704, 'unknow': 0.968941485785432, 'mIoU': 0.49970202276502124}}  total_overall: {'drivable_area': 0.595395230062787, 'stop_line': 0.11098933102020751, 'unknow': 0.9692500603155189, 'mIoU': 0.5585448737995045}  data_time: 0.0489  time: 0.1578
2024/07/09 14:45:55 - mmengine - INFO - Epoch(train)  [9][100/217]  lr: 7.5000e-05  eta: 0:44:41  time: 0.6969  data_time: 0.0009  memory: 2850  grad_norm: 10.3402  loss: 0.5475  loss_ce: 0.1803  loss_dice: 0.3672
2024/07/09 14:47:05 - mmengine - INFO - Epoch(train)  [9][200/217]  lr: 7.5000e-05  eta: 0:43:05  time: 0.7027  data_time: 0.0007  memory: 2850  grad_norm: 10.8316  loss: 0.5732  loss_ce: 0.1877  loss_dice: 0.3855
2024/07/09 14:47:16 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 14:47:16 - mmengine - INFO - Saving checkpoint at 9 epochs
2024/07/09 14:47:22 - mmengine - INFO - Epoch(val) [9][21/21]    by_distance: {'0-1m': {'drivable_area': 0.7130241418360995, 'stop_line': 0.13281989700482644, 'unknow': 0.9751075610769854, 'mIoU': 0.6069838666393038}, '1~2m': {'drivable_area': 0.47880050584261424, 'stop_line': 0.04669365491238795, 'unknow': 0.9433290875460805, 'mIoU': 0.4896077494336943}, '2~3m': {'drivable_area': 0.2570152174540341, 'stop_line': 0.02392837026822305, 'unknow': 0.964417056789158, 'mIoU': 0.4151202148371384}, '3~4m': {'drivable_area': 0.07145609123365783, 'stop_line': 0.0, 'unknow': 0.9864395661019444, 'mIoU': 0.35263188577853405}, 'patch_mean': {'drivable_area': 0.38007398909160145, 'stop_line': 0.05086048054635936, 'unknow': 0.967323317878542, 'mIoU': 0.46608592917216757}}  total_overall: {'drivable_area': 0.5263978878293444, 'stop_line': 0.09073905365689292, 'unknow': 0.9675064780780537, 'mIoU': 0.528214473188097}  data_time: 0.0337  time: 0.1389
2024/07/09 14:48:10 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 14:48:47 - mmengine - INFO - Epoch(train) [10][100/217]  lr: 6.9134e-05  eta: 0:41:39  time: 0.6940  data_time: 0.0009  memory: 2850  grad_norm: 9.4108  loss: 0.5491  loss_ce: 0.1772  loss_dice: 0.3719
2024/07/09 14:49:57 - mmengine - INFO - Epoch(train) [10][200/217]  lr: 6.9134e-05  eta: 0:40:06  time: 0.6962  data_time: 0.0007  memory: 2850  grad_norm: 11.1356  loss: 0.5451  loss_ce: 0.1816  loss_dice: 0.3635
2024/07/09 14:50:08 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 14:50:08 - mmengine - INFO - Saving checkpoint at 10 epochs
2024/07/09 14:50:13 - mmengine - INFO - Epoch(val) [10][21/21]    by_distance: {'0-1m': {'drivable_area': 0.7429198510605933, 'stop_line': 0.14399357899710669, 'unknow': 0.975695500080948, 'mIoU': 0.6208696433795493}, '1~2m': {'drivable_area': 0.4770698156987985, 'stop_line': 0.058079680598656126, 'unknow': 0.9463418971227259, 'mIoU': 0.4938304644733935}, '2~3m': {'drivable_area': 0.26823499206550805, 'stop_line': 0.012320328542094453, 'unknow': 0.9658044703625416, 'mIoU': 0.4154532636567147}, '3~4m': {'drivable_area': 0.07314899123017204, 'stop_line': 0.0, 'unknow': 0.9875629001666655, 'mIoU': 0.35357063046561255}, 'patch_mean': {'drivable_area': 0.39034341251376803, 'stop_line': 0.05359839703446431, 'unknow': 0.9688511919332203, 'mIoU': 0.4709310004938176}}  total_overall: {'drivable_area': 0.5444021560858218, 'stop_line': 0.09482260478692171, 'unknow': 0.9690535782811477, 'mIoU': 0.5360927797179637}  data_time: 0.0436  time: 0.1531
2024/07/09 14:51:37 - mmengine - INFO - Epoch(train) [11][100/217]  lr: 6.2941e-05  eta: 0:38:38  time: 0.6872  data_time: 0.0009  memory: 2850  grad_norm: 9.2974  loss: 0.5284  loss_ce: 0.1750  loss_dice: 0.3534
2024/07/09 14:52:47 - mmengine - INFO - Epoch(train) [11][200/217]  lr: 6.2941e-05  eta: 0:37:09  time: 0.7033  data_time: 0.0007  memory: 2850  grad_norm: 8.2249  loss: 0.4988  loss_ce: 0.1614  loss_dice: 0.3374
2024/07/09 14:52:59 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 14:52:59 - mmengine - INFO - Saving checkpoint at 11 epochs
2024/07/09 14:53:04 - mmengine - INFO - Epoch(val) [11][21/21]    by_distance: {'0-1m': {'drivable_area': 0.7654199695463301, 'stop_line': 0.1484982834440518, 'unknow': 0.974967644054124, 'mIoU': 0.6296286323481687}, '1~2m': {'drivable_area': 0.5317899736286228, 'stop_line': 0.07474566071797957, 'unknow': 0.9464550374516291, 'mIoU': 0.5176635572660772}, '2~3m': {'drivable_area': 0.25110066052348456, 'stop_line': 0.019599223357878157, 'unknow': 0.9622178554357639, 'mIoU': 0.41097257977237556}, '3~4m': {'drivable_area': 0.08346043444898113, 'stop_line': 0.002522068095838587, 'unknow': 0.987624555860566, 'mIoU': 0.3578690194684619}, 'patch_mean': {'drivable_area': 0.4079427595368546, 'stop_line': 0.06134130890393703, 'unknow': 0.9678162732005207, 'mIoU': 0.4790334472137707}}  total_overall: {'drivable_area': 0.5690987915427308, 'stop_line': 0.10502614025599423, 'unknow': 0.96800595611999, 'mIoU': 0.5473769626395716}  data_time: 0.0398  time: 0.1467
2024/07/09 14:54:29 - mmengine - INFO - Epoch(train) [12][100/217]  lr: 5.6526e-05  eta: 0:35:42  time: 0.6996  data_time: 0.0009  memory: 2850  grad_norm: 9.8242  loss: 0.5017  loss_ce: 0.1609  loss_dice: 0.3408
2024/07/09 14:55:38 - mmengine - INFO - Epoch(train) [12][200/217]  lr: 5.6526e-05  eta: 0:34:14  time: 0.6970  data_time: 0.0007  memory: 2850  grad_norm: 8.5298  loss: 0.4996  loss_ce: 0.1611  loss_dice: 0.3385
2024/07/09 14:55:50 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 14:55:50 - mmengine - INFO - Saving checkpoint at 12 epochs
2024/07/09 14:55:55 - mmengine - INFO - Epoch(val) [12][21/21]    by_distance: {'0-1m': {'drivable_area': 0.7549450206851467, 'stop_line': 0.14430282015400206, 'unknow': 0.9761899261600347, 'mIoU': 0.6251459223330612}, '1~2m': {'drivable_area': 0.4727026810561784, 'stop_line': 0.05328874846841973, 'unknow': 0.9441027119080244, 'mIoU': 0.49003138047754086}, '2~3m': {'drivable_area': 0.25034605333097093, 'stop_line': 0.015509135850336838, 'unknow': 0.9654747732529313, 'mIoU': 0.41044332081141305}, '3~4m': {'drivable_area': 0.12045353611340215, 'stop_line': 0.010907107798582076, 'unknow': 0.9877277612036057, 'mIoU': 0.37302946837186335}, 'patch_mean': {'drivable_area': 0.3996118227964246, 'stop_line': 0.056001953067835175, 'unknow': 0.9683737931311491, 'mIoU': 0.4746625229984696}}  total_overall: {'drivable_area': 0.5470108424645888, 'stop_line': 0.09486326423076859, 'unknow': 0.9685508499355621, 'mIoU': 0.5368083188769731}  data_time: 0.0451  time: 0.1528
2024/07/09 14:57:20 - mmengine - INFO - Epoch(train) [13][100/217]  lr: 5.0000e-05  eta: 0:32:46  time: 0.6961  data_time: 0.0009  memory: 2850  grad_norm: 8.6792  loss: 0.4566  loss_ce: 0.1405  loss_dice: 0.3160
2024/07/09 14:58:30 - mmengine - INFO - Epoch(train) [13][200/217]  lr: 5.0000e-05  eta: 0:31:21  time: 0.7042  data_time: 0.0007  memory: 2850  grad_norm: 8.6738  loss: 0.4761  loss_ce: 0.1561  loss_dice: 0.3200
2024/07/09 14:58:42 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 14:58:42 - mmengine - INFO - Saving checkpoint at 13 epochs
2024/07/09 14:58:47 - mmengine - INFO - Epoch(val) [13][21/21]    by_distance: {'0-1m': {'drivable_area': 0.7506778773831811, 'stop_line': 0.14053000542886335, 'unknow': 0.976541742735928, 'mIoU': 0.6225832085159908}, '1~2m': {'drivable_area': 0.4584371281767906, 'stop_line': 0.054538837276122985, 'unknow': 0.9425748697210612, 'mIoU': 0.48518361172465824}, '2~3m': {'drivable_area': 0.2900144086370647, 'stop_line': 0.020042194092827006, 'unknow': 0.964765566111734, 'mIoU': 0.4249407229472086}, '3~4m': {'drivable_area': 0.15292297974685595, 'stop_line': 0.009517664139377319, 'unknow': 0.9869466819447766, 'mIoU': 0.3831291086103366}, 'patch_mean': {'drivable_area': 0.41301309848597306, 'stop_line': 0.05615717523429767, 'unknow': 0.967707215128375, 'mIoU': 0.4789591629495486}}  total_overall: {'drivable_area': 0.5415447767283329, 'stop_line': 0.09229234222998255, 'unknow': 0.9678693397116704, 'mIoU': 0.5339021528899953}  data_time: 0.0386  time: 0.1443
2024/07/09 15:00:12 - mmengine - INFO - Epoch(train) [14][100/217]  lr: 4.3474e-05  eta: 0:29:53  time: 0.6946  data_time: 0.0009  memory: 2850  grad_norm: 7.9891  loss: 0.4353  loss_ce: 0.1364  loss_dice: 0.2988
2024/07/09 15:01:07 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 15:01:21 - mmengine - INFO - Epoch(train) [14][200/217]  lr: 4.3474e-05  eta: 0:28:28  time: 0.6851  data_time: 0.0007  memory: 2850  grad_norm: 8.0233  loss: 0.4609  loss_ce: 0.1483  loss_dice: 0.3126
2024/07/09 15:01:33 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 15:01:33 - mmengine - INFO - Saving checkpoint at 14 epochs
2024/07/09 15:01:38 - mmengine - INFO - Epoch(val) [14][21/21]    by_distance: {'0-1m': {'drivable_area': 0.7941700489144123, 'stop_line': 0.17396999106787664, 'unknow': 0.976777426542152, 'mIoU': 0.6483058221748137}, '1~2m': {'drivable_area': 0.48898243188606166, 'stop_line': 0.05580061399256746, 'unknow': 0.9432449128127677, 'mIoU': 0.4960093195637989}, '2~3m': {'drivable_area': 0.2759435035555779, 'stop_line': 0.02021522241101381, 'unknow': 0.962208934549122, 'mIoU': 0.4194558868385712}, '3~4m': {'drivable_area': 0.15816154106116928, 'stop_line': 0.01772867420349435, 'unknow': 0.9870389070066514, 'mIoU': 0.387643040757105}, 'patch_mean': {'drivable_area': 0.4293143813543053, 'stop_line': 0.06692862541873806, 'unknow': 0.9673175452276732, 'mIoU': 0.48785351733357213}}  total_overall: {'drivable_area': 0.567601323776495, 'stop_line': 0.10664514290480427, 'unknow': 0.9674622715548561, 'mIoU': 0.5472362460787185}  data_time: 0.0483  time: 0.1560
2024/07/09 15:03:03 - mmengine - INFO - Epoch(train) [15][100/217]  lr: 3.7059e-05  eta: 0:27:00  time: 0.7019  data_time: 0.0009  memory: 2850  grad_norm: 8.8373  loss: 0.4284  loss_ce: 0.1310  loss_dice: 0.2974
2024/07/09 15:04:13 - mmengine - INFO - Epoch(train) [15][200/217]  lr: 3.7059e-05  eta: 0:25:37  time: 0.6966  data_time: 0.0007  memory: 2850  grad_norm: 9.3162  loss: 0.4365  loss_ce: 0.1400  loss_dice: 0.2965
2024/07/09 15:04:24 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 15:04:24 - mmengine - INFO - Saving checkpoint at 15 epochs
2024/07/09 15:04:30 - mmengine - INFO - Epoch(val) [15][21/21]    by_distance: {'0-1m': {'drivable_area': 0.8022926355503259, 'stop_line': 0.16420315036422517, 'unknow': 0.9778214157001198, 'mIoU': 0.648105733871557}, '1~2m': {'drivable_area': 0.5461261799744426, 'stop_line': 0.07493708176884949, 'unknow': 0.9445496089376927, 'mIoU': 0.5218709568936616}, '2~3m': {'drivable_area': 0.2748481609884524, 'stop_line': 0.019028771502511792, 'unknow': 0.961353118705655, 'mIoU': 0.41841001706553976}, '3~4m': {'drivable_area': 0.1531765253441128, 'stop_line': 0.012437810945273632, 'unknow': 0.9873444413292591, 'mIoU': 0.38431959253954856}, 'patch_mean': {'drivable_area': 0.4441108754643334, 'stop_line': 0.06765170364521503, 'unknow': 0.9677671461681817, 'mIoU': 0.49317657509257673}}  total_overall: {'drivable_area': 0.5904188747190986, 'stop_line': 0.10904790821055323, 'unknow': 0.9679319933404852, 'mIoU': 0.5557995920900457}  data_time: 0.0350  time: 0.1407
2024/07/09 15:05:55 - mmengine - INFO - Epoch(train) [16][100/217]  lr: 3.0866e-05  eta: 0:24:08  time: 0.6962  data_time: 0.0009  memory: 2850  grad_norm: 8.0803  loss: 0.4106  loss_ce: 0.1306  loss_dice: 0.2800
2024/07/09 15:07:04 - mmengine - INFO - Epoch(train) [16][200/217]  lr: 3.0866e-05  eta: 0:22:46  time: 0.6961  data_time: 0.0008  memory: 2850  grad_norm: 9.6095  loss: 0.4223  loss_ce: 0.1343  loss_dice: 0.2880
2024/07/09 15:07:16 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 15:07:16 - mmengine - INFO - Saving checkpoint at 16 epochs
2024/07/09 15:07:21 - mmengine - INFO - Epoch(val) [16][21/21]    by_distance: {'0-1m': {'drivable_area': 0.8060490059531651, 'stop_line': 0.17240110039133638, 'unknow': 0.978017466536951, 'mIoU': 0.6521558576271508}, '1~2m': {'drivable_area': 0.5029023851327388, 'stop_line': 0.07118429783541624, 'unknow': 0.9446565477774669, 'mIoU': 0.506247743581874}, '2~3m': {'drivable_area': 0.22363523246127406, 'stop_line': 0.018510110711416897, 'unknow': 0.9629600489240642, 'mIoU': 0.40170179736558503}, '3~4m': {'drivable_area': 0.09141481628900955, 'stop_line': 0.007986930477400619, 'unknow': 0.9878184806856872, 'mIoU': 0.3624067424840325}, 'patch_mean': {'drivable_area': 0.4060003599590469, 'stop_line': 0.06752060985389254, 'unknow': 0.9683631359810423, 'mIoU': 0.4806280352646606}}  total_overall: {'drivable_area': 0.5740160958214521, 'stop_line': 0.10975192104153579, 'unknow': 0.9685051196729545, 'mIoU': 0.5507577121786474}  data_time: 0.0479  time: 0.1583
2024/07/09 15:08:46 - mmengine - INFO - Epoch(train) [17][100/217]  lr: 2.5000e-05  eta: 0:21:17  time: 0.6836  data_time: 0.0009  memory: 2850  grad_norm: 7.6089  loss: 0.3781  loss_ce: 0.1165  loss_dice: 0.2616
2024/07/09 15:09:55 - mmengine - INFO - Epoch(train) [17][200/217]  lr: 2.5000e-05  eta: 0:19:55  time: 0.6882  data_time: 0.0008  memory: 2850  grad_norm: 8.2948  loss: 0.3775  loss_ce: 0.1172  loss_dice: 0.2603
2024/07/09 15:10:06 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 15:10:06 - mmengine - INFO - Saving checkpoint at 17 epochs
2024/07/09 15:10:12 - mmengine - INFO - Epoch(val) [17][21/21]    by_distance: {'0-1m': {'drivable_area': 0.8042838267860133, 'stop_line': 0.1551940840729677, 'unknow': 0.9772273473015806, 'mIoU': 0.6455684193868539}, '1~2m': {'drivable_area': 0.5364505767265295, 'stop_line': 0.07008792256395978, 'unknow': 0.9432134849368967, 'mIoU': 0.516583994742462}, '2~3m': {'drivable_area': 0.27985315985573767, 'stop_line': 0.013923709674077448, 'unknow': 0.9618684685997047, 'mIoU': 0.41854844604317326}, '3~4m': {'drivable_area': 0.13647981837697587, 'stop_line': 0.0, 'unknow': 0.986044425522716, 'mIoU': 0.37417474796656397}, 'patch_mean': {'drivable_area': 0.4392668454363141, 'stop_line': 0.05980142907775123, 'unknow': 0.9670884315902245, 'mIoU': 0.4887189020347633}}  total_overall: {'drivable_area': 0.5862370248848013, 'stop_line': 0.10253749383717417, 'unknow': 0.9672423615702761, 'mIoU': 0.5520056267640839}  data_time: 0.0353  time: 0.1409
2024/07/09 15:11:37 - mmengine - INFO - Epoch(train) [18][100/217]  lr: 1.9562e-05  eta: 0:18:26  time: 0.6874  data_time: 0.0009  memory: 2850  grad_norm: 7.9669  loss: 0.4046  loss_ce: 0.1345  loss_dice: 0.2701
2024/07/09 15:12:46 - mmengine - INFO - Epoch(train) [18][200/217]  lr: 1.9562e-05  eta: 0:17:05  time: 0.6898  data_time: 0.0007  memory: 2850  grad_norm: 7.6265  loss: 0.3821  loss_ce: 0.1198  loss_dice: 0.2623
2024/07/09 15:12:58 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 15:12:58 - mmengine - INFO - Saving checkpoint at 18 epochs
2024/07/09 15:13:03 - mmengine - INFO - Epoch(val) [18][21/21]    by_distance: {'0-1m': {'drivable_area': 0.7886173604511897, 'stop_line': 0.1479010967386983, 'unknow': 0.9765922007670733, 'mIoU': 0.6377035526523205}, '1~2m': {'drivable_area': 0.507248686870848, 'stop_line': 0.054259567702615745, 'unknow': 0.9417771676520938, 'mIoU': 0.5010951407418526}, '2~3m': {'drivable_area': 0.2686583751531197, 'stop_line': 0.015211463949650466, 'unknow': 0.962246876166256, 'mIoU': 0.41537223842300874}, '3~4m': {'drivable_area': 0.16082290223339046, 'stop_line': 0.011076923076923078, 'unknow': 0.98654568919476, 'mIoU': 0.38614850483502455}, 'patch_mean': {'drivable_area': 0.43133683117713695, 'stop_line': 0.0571122628669719, 'unknow': 0.9667904834450458, 'mIoU': 0.4850798591630516}}  total_overall: {'drivable_area': 0.5701298603940596, 'stop_line': 0.09419565392261638, 'unknow': 0.9669408176283211, 'mIoU': 0.5437554439816656}  data_time: 0.0490  time: 0.1600
2024/07/09 15:14:25 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 15:14:29 - mmengine - INFO - Epoch(train) [19][100/217]  lr: 1.4645e-05  eta: 0:15:36  time: 0.6987  data_time: 0.0010  memory: 2850  grad_norm: 8.5321  loss: 0.3574  loss_ce: 0.1092  loss_dice: 0.2482
2024/07/09 15:15:39 - mmengine - INFO - Epoch(train) [19][200/217]  lr: 1.4645e-05  eta: 0:14:16  time: 0.6959  data_time: 0.0007  memory: 2850  grad_norm: 7.5449  loss: 0.3554  loss_ce: 0.1114  loss_dice: 0.2440
2024/07/09 15:15:50 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 15:15:50 - mmengine - INFO - Saving checkpoint at 19 epochs
2024/07/09 15:15:56 - mmengine - INFO - Epoch(val) [19][21/21]    by_distance: {'0-1m': {'drivable_area': 0.8001507774931006, 'stop_line': 0.1553918871605062, 'unknow': 0.9772414948033719, 'mIoU': 0.6442613864856596}, '1~2m': {'drivable_area': 0.5160021679159053, 'stop_line': 0.057666254885122135, 'unknow': 0.9423706993496558, 'mIoU': 0.5053463740502276}, '2~3m': {'drivable_area': 0.2874806610537604, 'stop_line': 0.01775147928994083, 'unknow': 0.9629413125231858, 'mIoU': 0.42272448428896237}, '3~4m': {'drivable_area': 0.1601158765620013, 'stop_line': 0.009506500768908152, 'unknow': 0.9867833750615537, 'mIoU': 0.385468584130821}, 'patch_mean': {'drivable_area': 0.4409373707561919, 'stop_line': 0.060079030526119334, 'unknow': 0.9673342204344418, 'mIoU': 0.4894502072389177}}  total_overall: {'drivable_area': 0.5821137297164681, 'stop_line': 0.09733390730377386, 'unknow': 0.9674926964002012, 'mIoU': 0.5489801111401477}  data_time: 0.0340  time: 0.1394
2024/07/09 15:17:23 - mmengine - INFO - Epoch(train) [20][100/217]  lr: 1.0332e-05  eta: 0:12:47  time: 0.6975  data_time: 0.0010  memory: 2850  grad_norm: 8.3287  loss: 0.3648  loss_ce: 0.1175  loss_dice: 0.2473
2024/07/09 15:18:32 - mmengine - INFO - Epoch(train) [20][200/217]  lr: 1.0332e-05  eta: 0:11:27  time: 0.6865  data_time: 0.0008  memory: 2850  grad_norm: 9.3598  loss: 0.3499  loss_ce: 0.1088  loss_dice: 0.2411
2024/07/09 15:18:44 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 15:18:44 - mmengine - INFO - Saving checkpoint at 20 epochs
2024/07/09 15:18:49 - mmengine - INFO - Epoch(val) [20][21/21]    by_distance: {'0-1m': {'drivable_area': 0.8074454560846995, 'stop_line': 0.16349934894425147, 'unknow': 0.9779588196355035, 'mIoU': 0.6496345415548181}, '1~2m': {'drivable_area': 0.5399599495938814, 'stop_line': 0.06474657547707487, 'unknow': 0.9437014614075925, 'mIoU': 0.5161359954928496}, '2~3m': {'drivable_area': 0.2941262586751944, 'stop_line': 0.0156645934116563, 'unknow': 0.9608587538417416, 'mIoU': 0.4235498686428641}, '3~4m': {'drivable_area': 0.18192190958672783, 'stop_line': 0.009568031518221472, 'unknow': 0.9862681298311673, 'mIoU': 0.3925860236453722}, 'patch_mean': {'drivable_area': 0.4558633934851257, 'stop_line': 0.06336963733780103, 'unknow': 0.9671967911790011, 'mIoU': 0.49547660733397597}}  total_overall: {'drivable_area': 0.5905110297766655, 'stop_line': 0.10441146861031692, 'unknow': 0.9673269458298459, 'mIoU': 0.5540831480722761}  data_time: 0.0467  time: 0.1555
2024/07/09 15:20:14 - mmengine - INFO - Epoch(train) [21][100/217]  lr: 6.6987e-06  eta: 0:09:57  time: 0.6947  data_time: 0.0009  memory: 2850  grad_norm: 7.7665  loss: 0.3405  loss_ce: 0.1060  loss_dice: 0.2345
2024/07/09 15:21:24 - mmengine - INFO - Epoch(train) [21][200/217]  lr: 6.6987e-06  eta: 0:08:38  time: 0.6988  data_time: 0.0008  memory: 2850  grad_norm: 7.4158  loss: 0.3427  loss_ce: 0.1100  loss_dice: 0.2327
2024/07/09 15:21:35 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 15:21:35 - mmengine - INFO - Saving checkpoint at 21 epochs
2024/07/09 15:21:40 - mmengine - INFO - Epoch(val) [21][21/21]    by_distance: {'0-1m': {'drivable_area': 0.8085220270274065, 'stop_line': 0.16338209179442426, 'unknow': 0.977684396007889, 'mIoU': 0.6498628382765732}, '1~2m': {'drivable_area': 0.5396606348239514, 'stop_line': 0.061208105576672445, 'unknow': 0.944101891867436, 'mIoU': 0.5149902107560199}, '2~3m': {'drivable_area': 0.3080432510173418, 'stop_line': 0.016487000634115408, 'unknow': 0.9629937850530861, 'mIoU': 0.42917467890151445}, '3~4m': {'drivable_area': 0.18361275309808456, 'stop_line': 0.00620347394540943, 'unknow': 0.9868925357757045, 'mIoU': 0.3922362542730662}, 'patch_mean': {'drivable_area': 0.45995966649169606, 'stop_line': 0.06182016798765538, 'unknow': 0.9679181521760288, 'mIoU': 0.49656599555179337}}  total_overall: {'drivable_area': 0.5960327784015673, 'stop_line': 0.10326987763857065, 'unknow': 0.968070883268826, 'mIoU': 0.5557911797696546}  data_time: 0.0398  time: 0.1466
2024/07/09 15:23:05 - mmengine - INFO - Epoch(train) [22][100/217]  lr: 3.8060e-06  eta: 0:07:08  time: 0.6949  data_time: 0.0009  memory: 2850  grad_norm: 8.8014  loss: 0.3646  loss_ce: 0.1187  loss_dice: 0.2459
2024/07/09 15:24:15 - mmengine - INFO - Epoch(train) [22][200/217]  lr: 3.8060e-06  eta: 0:05:49  time: 0.6965  data_time: 0.0008  memory: 2850  grad_norm: 7.9624  loss: 0.3362  loss_ce: 0.1047  loss_dice: 0.2314
2024/07/09 15:24:26 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 15:24:26 - mmengine - INFO - Saving checkpoint at 22 epochs
2024/07/09 15:24:31 - mmengine - INFO - Epoch(val) [22][21/21]    by_distance: {'0-1m': {'drivable_area': 0.7982674241998088, 'stop_line': 0.15208355256234876, 'unknow': 0.9766478034048911, 'mIoU': 0.6423329267223495}, '1~2m': {'drivable_area': 0.5282106989573683, 'stop_line': 0.05303303241479408, 'unknow': 0.9430456301069919, 'mIoU': 0.5080964538263847}, '2~3m': {'drivable_area': 0.29250569943186616, 'stop_line': 0.01312910284463895, 'unknow': 0.9628492245979678, 'mIoU': 0.4228280089581577}, '3~4m': {'drivable_area': 0.17762026624472235, 'stop_line': 0.003265839320705422, 'unknow': 0.987102975252003, 'mIoU': 0.3893296936058102}, 'patch_mean': {'drivable_area': 0.44915102220844144, 'stop_line': 0.05537788178562181, 'unknow': 0.9674114083404635, 'mIoU': 0.4906467707781756}}  total_overall: {'drivable_area': 0.585961502781473, 'stop_line': 0.09523471477816775, 'unknow': 0.9675778865538411, 'mIoU': 0.5495913680378273}  data_time: 0.0484  time: 0.1566
2024/07/09 15:25:56 - mmengine - INFO - Epoch(train) [23][100/217]  lr: 1.7037e-06  eta: 0:04:19  time: 0.6785  data_time: 0.0009  memory: 2850  grad_norm: 7.4475  loss: 0.3253  loss_ce: 0.1014  loss_dice: 0.2239
2024/07/09 15:27:05 - mmengine - INFO - Epoch(train) [23][200/217]  lr: 1.7037e-06  eta: 0:03:01  time: 0.6873  data_time: 0.0008  memory: 2850  grad_norm: 8.2150  loss: 0.3098  loss_ce: 0.0931  loss_dice: 0.2167
2024/07/09 15:27:17 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 15:27:17 - mmengine - INFO - Saving checkpoint at 23 epochs
2024/07/09 15:27:22 - mmengine - INFO - Epoch(val) [23][21/21]    by_distance: {'0-1m': {'drivable_area': 0.8015477895478308, 'stop_line': 0.15403102852849557, 'unknow': 0.9768339433190075, 'mIoU': 0.6441375871317779}, '1~2m': {'drivable_area': 0.5301701478983559, 'stop_line': 0.053576692923159676, 'unknow': 0.9426448221913978, 'mIoU': 0.5087972210043045}, '2~3m': {'drivable_area': 0.27816614950431606, 'stop_line': 0.01154420449733681, 'unknow': 0.9619014042748948, 'mIoU': 0.4172039194255159}, '3~4m': {'drivable_area': 0.1718864860934963, 'stop_line': 0.001374570446735395, 'unknow': 0.9870463617687941, 'mIoU': 0.38676913943634195}, 'patch_mean': {'drivable_area': 0.4454426432609998, 'stop_line': 0.05513162409893186, 'unknow': 0.9671066328885236, 'mIoU': 0.4892269667494851}}  total_overall: {'drivable_area': 0.5850550657175438, 'stop_line': 0.09623903304381334, 'unknow': 0.9672661265780915, 'mIoU': 0.5495200751131496}  data_time: 0.0325  time: 0.1378
2024/07/09 15:27:37 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 15:28:47 - mmengine - INFO - Epoch(train) [24][100/217]  lr: 4.2776e-07  eta: 0:01:30  time: 0.6905  data_time: 0.0009  memory: 2850  grad_norm: 8.2894  loss: 0.3280  loss_ce: 0.1041  loss_dice: 0.2238
2024/07/09 15:29:56 - mmengine - INFO - Epoch(train) [24][200/217]  lr: 4.2776e-07  eta: 0:00:13  time: 0.6993  data_time: 0.0007  memory: 2850  grad_norm: 8.3079  loss: 0.3418  loss_ce: 0.1079  loss_dice: 0.2339
2024/07/09 15:30:08 - mmengine - INFO - Exp name: indoor_zed_lidar_bev_20240709_142042
2024/07/09 15:30:08 - mmengine - INFO - Saving checkpoint at 24 epochs
2024/07/09 15:30:13 - mmengine - INFO - Epoch(val) [24][21/21]    by_distance: {'0-1m': {'drivable_area': 0.803519860411259, 'stop_line': 0.15502405401479774, 'unknow': 0.9770201726719807, 'mIoU': 0.6451880290326791}, '1~2m': {'drivable_area': 0.5336884014026946, 'stop_line': 0.05409361927078833, 'unknow': 0.9432279282231835, 'mIoU': 0.5103366496322221}, '2~3m': {'drivable_area': 0.28658435462278187, 'stop_line': 0.011376162970148594, 'unknow': 0.9623296706661233, 'mIoU': 0.4200967294196846}, '3~4m': {'drivable_area': 0.17417110608382594, 'stop_line': 0.002659132458035566, 'unknow': 0.9870992640192516, 'mIoU': 0.3879765008537044}, 'patch_mean': {'drivable_area': 0.44949093063014034, 'stop_line': 0.055788242178442554, 'unknow': 0.9674192588951347, 'mIoU': 0.4908994772345725}}  total_overall: {'drivable_area': 0.5888982965300675, 'stop_line': 0.09642401552391121, 'unknow': 0.9675809670830673, 'mIoU': 0.5509677597123487}  data_time: 0.0526  time: 0.1600
