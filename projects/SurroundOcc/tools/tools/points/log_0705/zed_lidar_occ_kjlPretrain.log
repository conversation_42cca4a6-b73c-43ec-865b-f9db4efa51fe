2024/07/08 18:58:40 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: linux
    Python: 3.8.10 (default, May 26 2023, 14:05:08) [GCC 9.4.0]
    CUDA available: True
    numpy_random_seed: 1612713364
    GPU 0,1,2,3: NVIDIA TITAN Xp
    CUDA_HOME: /usr/local/cuda
    NVCC: Cuda compilation tools, release 11.6, V11.6.124
    GCC: x86_64-linux-gnu-gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0
    PyTorch: 1.13.0+cu116
    PyTorch compiling details: PyTorch built with:
  - GCC 9.3
  - C++ Version: 201402
  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v2.6.0 (Git Hash 52b5f107dd9cf10910aaa19cb47f3abf9b349815)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX2
  - CUDA Runtime 11.6
  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86
  - CuDNN 8.4
    - Built with CuDNN 8.3.2
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.6, CUDNN_VERSION=8.3.2, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -fabi-version=11 -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -DEDGE_PROFILER_USE_KINETO -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wunused-local-typedefs -Wno-unused-parameter -Wno-unused-function -Wno-unused-result -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Werror=cast-function-type -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.13.0, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, 

    TorchVision: 0.14.0+cu116
    OpenCV: 4.8.1
    MMEngine: 0.10.1

Runtime environment:
    cudnn_benchmark: False
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 1}
    dist_cfg: {'backend': 'nccl'}
    seed: 1612713364
    Distributed launcher: pytorch
    Distributed training: True
    GPU number: 4
------------------------------------------------------------

2024/07/08 18:58:40 - mmengine - INFO - Config:
auto_scale_lr = dict(base_batch_size=4, enable=False)
bn_kwargs = dict(eps=2e-05, momentum=0.1)
class_names = [
    'no_occupancy',
    'occupancy',
]
custom_imports = dict(
    allow_failed_imports=False,
    imports=[
        'projects.SurroundOcc.surroundocc.models',
        'projects.SurroundOcc.surroundocc.datasets',
    ])
data_root = '/home/<USER>/indoor/zed_lidar'
data_shape = (
    3,
    384,
    512,
)
dataset_type = 'EcoAi.KujialeStereoDataset'
default_hooks = dict(
    checkpoint=dict(interval=1, type='CheckpointHook'),
    logger=dict(interval=100, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    timer=dict(type='IterTimerHook'),
    visualization=dict(type='mmdet3d.Det3DVisualizationHook'))
default_scope = 'mmdet'
depoly_model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=64, num_channels=[
                128,
                256,
                512,
            ]),
        fpn_lss_params=dict(in_channels=640, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(type='EfficientNetB0'),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    type='EcoOccVisionDepoly',
    use_grid_mask=False,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depoly=True,
        depth=120,
        feat_channels=64,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=8,
        num_views=2,
        type='LSSTransformer',
        z_range=(
            -0.05,
            0.15,
        )))
depth_quant_scale = 0.03125
depthview_shape = [
    720,
    768.0,
]
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=1))
featview_shape = [
    144.0,
    32.0,
]
find_unused_parameters = True
grid_quant_scale = 0.0078125
img_norm_cfg = dict(
    mean=[
        103.53,
        116.28,
        123.675,
    ], std=[
        1.0,
        1.0,
        1.0,
    ], to_rgb=False)
keys = ('img', )
launcher = 'pytorch'
load_from = '/home/<USER>/ecoaitoolkit/projects/SurroundOcc/depoly/0509_stereo_occ_epc24.pth'
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=50)
lss_bev_size = [
    -2,
    2,
    0,
    4,
    0.05,
]
lss_depth = 120
lss_grid_size = (
    80,
    80,
)
lss_num_points = 8
lss_z_range = (
    -0.05,
    0.15,
)
meta_keys = (
    'lidar2img',
    'img_shape',
    'pc_range',
    'occ_size',
    'gt_occ',
    'occ_path',
    'filename',
)
model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=64, num_channels=[
                128,
                256,
                512,
            ]),
        fpn_lss_params=dict(in_channels=640, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(type='EfficientNetB0'),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    is_vis=False,
    lambda_dice=1,
    type='EcoOccVision',
    use_grid_mask=True,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depth=120,
        feat_channels=64,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=8,
        num_views=2,
        type='LSSTransformer',
        z_range=(
            -0.05,
            0.15,
        )))
numC_Trans = 64
occ_size = [
    80,
    80,
    4,
]
optim_wrapper = dict(
    clip_grad=dict(max_norm=35, norm_type=2),
    optimizer=dict(lr=0.0001, type='AdamW', weight_decay=0.01),
    type='AmpOptimWrapper')
param_scheduler = [
    dict(
        begin=0,
        by_epoch=False,
        end=500,
        start_factor=0.3333333333333333,
        type='LinearLR'),
    dict(by_epoch=True, type='CosineAnnealingLR'),
]
point_cloud_range = [
    -2,
    0,
    -0.05,
    2,
    4,
    0.15,
]
resume = None
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0708_val.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor/zed_lidar',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
test_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
train_cfg = dict(max_epochs=24, type='EpochBasedTrainLoop', val_interval=1)
train_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0708_train.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor/zed_lidar',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(type='PhotoMetricDistortionMultiViewImage'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        test_mode=False,
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    prefetch_factor=8,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(type='PhotoMetricDistortionMultiViewImage'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
use_semantic = True
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0708_val.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor/zed_lidar',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
val_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
view_shape = [
    24.0,
    32.0,
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='mmdet3d.Det3DLocalVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = 'work_dirs/zed_lidar_0704/zed_lidar_0708_kjlPretrain'

2024/07/08 18:58:43 - mmengine - INFO - Autoplay mode, press [SPACE] to pause.
2024/07/08 18:58:43 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2024/07/08 18:58:45 - mmengine - WARNING - The prefix is not set in metric class BEVMetric.
Name of parameter - Initialization information

img_backbone.stem.stem.conv.weight - torch.Size([32, 3, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stem.stem.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stem.stem.norm.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stem.stem.norm.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.depth.conv.weight - torch.Size([32, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.0.conv.depth.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.depth.norm.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.depth.norm.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.project.conv.weight - torch.Size([16, 32, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.0.conv.project.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.project.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.project.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.expand_conv.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.expand_conv.conv.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.expand_conv.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.expand_conv.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.depth.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.depth.conv.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.depth.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.depth.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.project.conv.weight - torch.Size([24, 96, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.project.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.project.norm.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.project.norm.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.expand_conv.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.depth.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.depth.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.depth.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.depth.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.project.conv.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.project.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.project.norm.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.project.norm.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.expand_conv.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.expand_conv.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.expand_conv.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.expand_conv.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.depth.conv.weight - torch.Size([144, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.depth.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.depth.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.depth.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.project.conv.weight - torch.Size([40, 144, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.project.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.project.norm.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.project.norm.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.conv.weight - torch.Size([240, 40, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.expand_conv.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.depth.conv.weight - torch.Size([240, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.depth.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.depth.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.depth.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.project.conv.weight - torch.Size([40, 240, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.project.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.project.norm.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.project.norm.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.expand_conv.conv.weight - torch.Size([240, 40, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.expand_conv.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.expand_conv.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.expand_conv.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.depth.conv.weight - torch.Size([240, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.depth.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.depth.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.depth.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.project.conv.weight - torch.Size([80, 240, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.depth.conv.weight - torch.Size([480, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.project.conv.weight - torch.Size([80, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.depth.conv.weight - torch.Size([480, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.project.conv.weight - torch.Size([80, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.depth.conv.weight - torch.Size([480, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.project.conv.weight - torch.Size([112, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.project.conv.weight - torch.Size([112, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.project.conv.weight - torch.Size([112, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.project.conv.weight - torch.Size([192, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.depth.conv.weight - torch.Size([1152, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.project.conv.weight - torch.Size([320, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.project.conv.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.project.norm.weight - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.project.norm.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_4.conv.conv.weight - torch.Size([112, 320, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_4.conv.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_3.conv.conv.weight - torch.Size([40, 224, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_3.conv.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_2.conv.conv.weight - torch.Size([24, 80, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_2.conv.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_1.conv.conv.weight - torch.Size([16, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_1.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_0.conv.conv.weight - torch.Size([16, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_0.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_4.conv.conv.weight - torch.Size([224, 224, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_4.conv.conv.bias - torch.Size([224]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_3.conv.conv.weight - torch.Size([80, 80, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_3.conv.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_2.conv.conv.weight - torch.Size([48, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_2.conv.conv.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_1.conv.conv.weight - torch.Size([32, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_1.conv.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_0.conv.conv.weight - torch.Size([16, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_0.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_dispconv2.conv.weight - torch.Size([2, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv2.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_dispconv1.conv.weight - torch.Size([2, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv1.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_dispconv0.conv.weight - torch.Size([2, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv0.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_conf_mask_conv21.conv.weight - torch.Size([2, 66, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv21.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_conf_mask_conv11.conv.weight - torch.Size([2, 44, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv11.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_conf_mask_conv01.conv.weight - torch.Size([2, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv01.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.0.weight - torch.Size([64, 384, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.0.weight - torch.Size([64, 112, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.0.weight - torch.Size([64, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.0.weight - torch.Size([64, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

view_transformer.depth_net.0.weight - torch.Size([120, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

view_transformer.feat_net.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.conv1.weight - torch.Size([128, 64, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.conv2.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn2.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn2.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.downsample.weight - torch.Size([128, 64, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.downsample.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.conv1.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.conv2.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn2.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn2.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.conv1.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.downsample.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.downsample.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.conv1.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.conv1.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.downsample.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.downsample.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.conv1.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.0.weight - torch.Size([512, 640, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.3.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.4.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.4.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.1.weight - torch.Size([256, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.4.weight - torch.Size([256, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.4.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.final_conv.conv.weight - torch.Size([256, 256, 3, 3]): 
Initialized by user-defined `init_weights` in ConvModule  

bev_decoder.final_conv.conv.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.0.weight - torch.Size([512, 256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.0.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.2.weight - torch.Size([4, 512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.2.bias - torch.Size([4]): 
The value is the same before and after calling `init_weights` of EcoOccVision  
2024/07/08 18:58:45 - mmengine - INFO - Load checkpoint from /home/<USER>/ecoaitoolkit/projects/SurroundOcc/depoly/0509_stereo_occ_epc24.pth
2024/07/08 18:58:45 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2024/07/08 18:58:45 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2024/07/08 18:58:45 - mmengine - INFO - Checkpoints will be saved to /home/<USER>/ecoaitoolkit/work_dirs/zed_lidar_0704/zed_lidar_0708_kjlPretrain.
2024/07/08 19:00:16 - mmengine - INFO - Epoch(train)  [1][100/195]  lr: 4.6560e-05  eta: 1:09:24  time: 0.7129  data_time: 0.0008  memory: 2924  grad_norm: 11.3417  loss: 0.7894  loss_bce: 0.0386  loss_dice: 0.7508
2024/07/08 19:01:24 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:01:24 - mmengine - INFO - Saving checkpoint at 1 epochs
2024/07/08 19:01:30 - mmengine - INFO - Epoch(val) [1][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9765762297666324, 'occupancy': 0.1550571165980386, 'mIoU': 0.5658166731823355}, '1~2m': {'no_occupancy': 0.9898333010852461, 'occupancy': 0.018731922969832313, 'mIoU': 0.5042826120275392}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.990997417971493, 'occupancy': 0.04344725989196773, 'mIoU': 0.5172223389317303}}  total_overall: {'no_occupancy': 0.9910129229740801, 'occupancy': 0.11079569679314741, 'mIoU': 0.5509043098836137}  data_time: 0.1035  time: 0.2177
2024/07/08 19:02:56 - mmengine - INFO - Epoch(train)  [2][100/195]  lr: 7.2301e-05  eta: 1:00:24  time: 0.7045  data_time: 0.0009  memory: 2924  grad_norm: 9.9596  loss: 0.7138  loss_bce: 0.0357  loss_dice: 0.6782
2024/07/08 19:04:03 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:04:03 - mmengine - INFO - Saving checkpoint at 2 epochs
2024/07/08 19:04:10 - mmengine - INFO - Epoch(val) [2][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9782660246708268, 'occupancy': 0.1394318365208524, 'mIoU': 0.5588489305958396}, '1~2m': {'no_occupancy': 0.9898665513504856, 'occupancy': 0.02057092130982425, 'mIoU': 0.5052187363301549}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9914281792638514, 'occupancy': 0.04000068945766916, 'mIoU': 0.5157144343607603}}  total_overall: {'no_occupancy': 0.9914398198237595, 'occupancy': 0.09812196518605767, 'mIoU': 0.5447808925049086}  data_time: 0.0307  time: 0.1434
2024/07/08 19:05:35 - mmengine - INFO - Epoch(train)  [3][100/195]  lr: 9.6983e-05  eta: 0:56:33  time: 0.7107  data_time: 0.0009  memory: 2924  grad_norm: 6.7962  loss: 0.6879  loss_bce: 0.0343  loss_dice: 0.6536
2024/07/08 19:06:42 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:06:42 - mmengine - INFO - Saving checkpoint at 3 epochs
2024/07/08 19:06:48 - mmengine - INFO - Epoch(val) [3][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9783514531130861, 'occupancy': 0.12846175150154343, 'mIoU': 0.5534066023073148}, '1~2m': {'no_occupancy': 0.9883088840659164, 'occupancy': 0.016950327224482923, 'mIoU': 0.5026296056451997}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9910601195532739, 'occupancy': 0.03635301968150659, 'mIoU': 0.5137065696173903}}  total_overall: {'no_occupancy': 0.9910703726899537, 'occupancy': 0.08656930259746323, 'mIoU': 0.5388198376437084}  data_time: 0.0316  time: 0.1439
2024/07/08 19:08:13 - mmengine - INFO - Epoch(train)  [4][100/195]  lr: 9.6194e-05  eta: 0:53:27  time: 0.7183  data_time: 0.0010  memory: 2924  grad_norm: 6.8122  loss: 0.6607  loss_bce: 0.0343  loss_dice: 0.6265
2024/07/08 19:09:21 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:09:21 - mmengine - INFO - Saving checkpoint at 4 epochs
2024/07/08 19:09:27 - mmengine - INFO - Epoch(val) [4][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.980928894128822, 'occupancy': 0.14203573439874193, 'mIoU': 0.5614823142637819}, '1~2m': {'no_occupancy': 0.9903095513863023, 'occupancy': 0.004426865996589135, 'mIoU': 0.4973682086914457}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9922046466373043, 'occupancy': 0.03661565009883277, 'mIoU': 0.5144101483680685}}  total_overall: {'no_occupancy': 0.9922135461349137, 'occupancy': 0.09298286094644358, 'mIoU': 0.5425982035406787}  data_time: 0.0331  time: 0.1465
2024/07/08 19:10:56 - mmengine - INFO - Epoch(train)  [5][100/195]  lr: 9.3301e-05  eta: 0:50:50  time: 0.7127  data_time: 0.0009  memory: 2924  grad_norm: 7.1250  loss: 0.6466  loss_bce: 0.0336  loss_dice: 0.6130
2024/07/08 19:12:03 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:12:03 - mmengine - INFO - Saving checkpoint at 5 epochs
2024/07/08 19:12:09 - mmengine - INFO - Epoch(val) [5][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9813067810611312, 'occupancy': 0.150695756235779, 'mIoU': 0.5660012686484551}, '1~2m': {'no_occupancy': 0.9897637815902536, 'occupancy': 0.009222064580092648, 'mIoU': 0.4994929230851731}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9921626759213694, 'occupancy': 0.03997945520396791, 'mIoU': 0.5160710655626687}}  total_overall: {'no_occupancy': 0.9921717127421297, 'occupancy': 0.09804997950963981, 'mIoU': 0.5451108461258848}  data_time: 0.0352  time: 0.1502
2024/07/08 19:12:40 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:13:35 - mmengine - INFO - Epoch(train)  [6][100/195]  lr: 8.9668e-05  eta: 0:48:04  time: 0.7177  data_time: 0.0009  memory: 2924  grad_norm: 6.0339  loss: 0.6334  loss_bce: 0.0339  loss_dice: 0.5994
2024/07/08 19:14:42 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:14:42 - mmengine - INFO - Saving checkpoint at 6 epochs
2024/07/08 19:14:48 - mmengine - INFO - Epoch(val) [6][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9807533811854994, 'occupancy': 0.1290058890392278, 'mIoU': 0.5548796351123636}, '1~2m': {'no_occupancy': 0.9897998353569225, 'occupancy': 0.013429334426584219, 'mIoU': 0.5016145848917534}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9920333393941289, 'occupancy': 0.03560880586645301, 'mIoU': 0.513821072630291}}  total_overall: {'no_occupancy': 0.9920414389907709, 'occupancy': 0.08569246099889523, 'mIoU': 0.5388669499948331}  data_time: 0.0357  time: 0.1484
2024/07/08 19:16:14 - mmengine - INFO - Epoch(train)  [7][100/195]  lr: 8.5355e-05  eta: 0:45:20  time: 0.7145  data_time: 0.0009  memory: 2924  grad_norm: 5.4800  loss: 0.5704  loss_bce: 0.0289  loss_dice: 0.5415
2024/07/08 19:17:22 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:17:22 - mmengine - INFO - Saving checkpoint at 7 epochs
2024/07/08 19:17:28 - mmengine - INFO - Epoch(val) [7][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9822891246549711, 'occupancy': 0.13296560571759616, 'mIoU': 0.5576273651862836}, '1~2m': {'no_occupancy': 0.9905096093759176, 'occupancy': 0.008648811518630456, 'mIoU': 0.49957921044727405}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9925947187662455, 'occupancy': 0.035403604309056654, 'mIoU': 0.5139991615376511}}  total_overall: {'no_occupancy': 0.9926017455836116, 'occupancy': 0.08625404334300008, 'mIoU': 0.5394278944633059}  data_time: 0.0334  time: 0.1465
2024/07/08 19:18:54 - mmengine - INFO - Epoch(train)  [8][100/195]  lr: 8.0438e-05  eta: 0:42:40  time: 0.7062  data_time: 0.0009  memory: 2924  grad_norm: 5.1604  loss: 0.5936  loss_bce: 0.0303  loss_dice: 0.5633
2024/07/08 19:20:01 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:20:01 - mmengine - INFO - Saving checkpoint at 8 epochs
2024/07/08 19:20:07 - mmengine - INFO - Epoch(val) [8][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9804198132713254, 'occupancy': 0.13947996372020963, 'mIoU': 0.5599498884957675}, '1~2m': {'no_occupancy': 0.9896617633007757, 'occupancy': 0.013709632125807359, 'mIoU': 0.5016856977132915}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9919154294015485, 'occupancy': 0.03829739896150425, 'mIoU': 0.5151064141815264}}  total_overall: {'no_occupancy': 0.9919246100370359, 'occupancy': 0.09294969884152, 'mIoU': 0.542437154439278}  data_time: 0.0364  time: 0.1504
2024/07/08 19:21:32 - mmengine - INFO - Epoch(train)  [9][100/195]  lr: 7.5000e-05  eta: 0:40:00  time: 0.7065  data_time: 0.0009  memory: 2924  grad_norm: 5.5004  loss: 0.5842  loss_bce: 0.0304  loss_dice: 0.5539
2024/07/08 19:22:40 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:22:40 - mmengine - INFO - Saving checkpoint at 9 epochs
2024/07/08 19:22:46 - mmengine - INFO - Epoch(val) [9][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9814326694188693, 'occupancy': 0.1409015535831851, 'mIoU': 0.5611671115010272}, '1~2m': {'no_occupancy': 0.9900133467774407, 'occupancy': 0.011237395815119192, 'mIoU': 0.5006253712962799}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9922565393076007, 'occupancy': 0.038034737349576075, 'mIoU': 0.5151456383285884}}  total_overall: {'no_occupancy': 0.9922648247921475, 'occupancy': 0.09245296874662519, 'mIoU': 0.5423588967693863}  data_time: 0.0309  time: 0.1453
2024/07/08 19:24:12 - mmengine - INFO - Epoch(train) [10][100/195]  lr: 6.9134e-05  eta: 0:37:23  time: 0.7118  data_time: 0.0009  memory: 2924  grad_norm: 4.8723  loss: 0.5647  loss_bce: 0.0297  loss_dice: 0.5349
2024/07/08 19:25:20 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:25:20 - mmengine - INFO - Saving checkpoint at 10 epochs
2024/07/08 19:25:25 - mmengine - INFO - Epoch(val) [10][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9825019630286367, 'occupancy': 0.13166715723530162, 'mIoU': 0.5570845601319692}, '1~2m': {'no_occupancy': 0.9901755460057691, 'occupancy': 0.010670587119415726, 'mIoU': 0.5004230665625924}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9925644125171248, 'occupancy': 0.03558443608867934, 'mIoU': 0.5140744243029021}}  total_overall: {'no_occupancy': 0.9925711373431789, 'occupancy': 0.08482074018710577, 'mIoU': 0.5386959387651423}  data_time: 0.0315  time: 0.1444
2024/07/08 19:26:16 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:26:51 - mmengine - INFO - Epoch(train) [11][100/195]  lr: 6.2941e-05  eta: 0:34:47  time: 0.7137  data_time: 0.0009  memory: 2924  grad_norm: 4.5928  loss: 0.5491  loss_bce: 0.0287  loss_dice: 0.5204
2024/07/08 19:27:59 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:27:59 - mmengine - INFO - Saving checkpoint at 11 epochs
2024/07/08 19:28:05 - mmengine - INFO - Epoch(val) [11][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9838554474121568, 'occupancy': 0.13943136490040395, 'mIoU': 0.5616434061562804}, '1~2m': {'no_occupancy': 0.9899433302304911, 'occupancy': 0.015897339881612488, 'mIoU': 0.5029203350560518}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9928447296691852, 'occupancy': 0.03883217619550411, 'mIoU': 0.5158384529323446}}  total_overall: {'no_occupancy': 0.9928507147912029, 'occupancy': 0.08840505692134659, 'mIoU': 0.5406278858562747}  data_time: 0.0285  time: 0.1425
2024/07/08 19:29:33 - mmengine - INFO - Epoch(train) [12][100/195]  lr: 5.6526e-05  eta: 0:32:13  time: 0.7180  data_time: 0.0010  memory: 2924  grad_norm: 5.5530  loss: 0.5252  loss_bce: 0.0279  loss_dice: 0.4973
2024/07/08 19:30:41 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:30:41 - mmengine - INFO - Saving checkpoint at 12 epochs
2024/07/08 19:30:46 - mmengine - INFO - Epoch(val) [12][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9836158875740869, 'occupancy': 0.1461495394951366, 'mIoU': 0.5648827135346117}, '1~2m': {'no_occupancy': 0.9899356102426035, 'occupancy': 0.020442544559589947, 'mIoU': 0.5051890774010968}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9927829097126959, 'occupancy': 0.04164802101368163, 'mIoU': 0.5172154653631887}}  total_overall: {'no_occupancy': 0.9927894731280862, 'occupancy': 0.09446515825722858, 'mIoU': 0.5436273156926574}  data_time: 0.0316  time: 0.1456
2024/07/08 19:32:11 - mmengine - INFO - Epoch(train) [13][100/195]  lr: 5.0000e-05  eta: 0:29:36  time: 0.7127  data_time: 0.0009  memory: 2924  grad_norm: 4.4992  loss: 0.5378  loss_bce: 0.0290  loss_dice: 0.5088
2024/07/08 19:33:19 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:33:19 - mmengine - INFO - Saving checkpoint at 13 epochs
2024/07/08 19:33:25 - mmengine - INFO - Epoch(val) [13][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9822958126981794, 'occupancy': 0.13958599004781008, 'mIoU': 0.5609409013729948}, '1~2m': {'no_occupancy': 0.9895424620740616, 'occupancy': 0.01407555620925734, 'mIoU': 0.5018090091416595}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9923546039515835, 'occupancy': 0.03841538656426686, 'mIoU': 0.5153849952579251}}  total_overall: {'no_occupancy': 0.992361916363436, 'occupancy': 0.08983280614550383, 'mIoU': 0.5410973612544699}  data_time: 0.0319  time: 0.1463
2024/07/08 19:34:51 - mmengine - INFO - Epoch(train) [14][100/195]  lr: 4.3474e-05  eta: 0:27:01  time: 0.7118  data_time: 0.0009  memory: 2924  grad_norm: 5.2898  loss: 0.5273  loss_bce: 0.0277  loss_dice: 0.4997
2024/07/08 19:35:59 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:35:59 - mmengine - INFO - Saving checkpoint at 14 epochs
2024/07/08 19:36:05 - mmengine - INFO - Epoch(val) [14][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9830393059929127, 'occupancy': 0.13084448476600893, 'mIoU': 0.5569418953794608}, '1~2m': {'no_occupancy': 0.9899894734481105, 'occupancy': 0.012950187345269641, 'mIoU': 0.50146983039669}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9926522301187791, 'occupancy': 0.035948668027819645, 'mIoU': 0.5143004490732994}}  total_overall: {'no_occupancy': 0.9926584422355438, 'occupancy': 0.08362665215848956, 'mIoU': 0.5381425471970167}  data_time: 0.0326  time: 0.1462
2024/07/08 19:37:31 - mmengine - INFO - Epoch(train) [15][100/195]  lr: 3.7059e-05  eta: 0:24:26  time: 0.7037  data_time: 0.0009  memory: 2924  grad_norm: 4.4714  loss: 0.4995  loss_bce: 0.0266  loss_dice: 0.4728
2024/07/08 19:38:39 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:38:39 - mmengine - INFO - Saving checkpoint at 15 epochs
2024/07/08 19:38:44 - mmengine - INFO - Epoch(val) [15][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9844085210004013, 'occupancy': 0.14526268450035265, 'mIoU': 0.564835602750377}, '1~2m': {'no_occupancy': 0.9903765773632071, 'occupancy': 0.013422413741075362, 'mIoU': 0.5018994955521412}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9930913098494254, 'occupancy': 0.039671274560357, 'mIoU': 0.5163812922048912}}  total_overall: {'no_occupancy': 0.9930971393926278, 'occupancy': 0.0913417131647481, 'mIoU': 0.5422194262786879}  data_time: 0.0351  time: 0.1508
2024/07/08 19:39:54 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:40:12 - mmengine - INFO - Epoch(train) [16][100/195]  lr: 3.0866e-05  eta: 0:21:52  time: 0.7130  data_time: 0.0009  memory: 2924  grad_norm: 4.3268  loss: 0.5019  loss_bce: 0.0272  loss_dice: 0.4746
2024/07/08 19:41:20 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:41:20 - mmengine - INFO - Saving checkpoint at 16 epochs
2024/07/08 19:41:26 - mmengine - INFO - Epoch(val) [16][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9844094857435173, 'occupancy': 0.14360483869660567, 'mIoU': 0.5640071622200615}, '1~2m': {'no_occupancy': 0.9902563489992688, 'occupancy': 0.016729888932786364, 'mIoU': 0.5034931189660276}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9930614939442198, 'occupancy': 0.04008368190734801, 'mIoU': 0.5165725879257839}}  total_overall: {'no_occupancy': 0.993067254170586, 'occupancy': 0.09097061518217195, 'mIoU': 0.542018934676379}  data_time: 0.0306  time: 0.1447
2024/07/08 19:42:52 - mmengine - INFO - Epoch(train) [17][100/195]  lr: 2.5000e-05  eta: 0:19:17  time: 0.7044  data_time: 0.0009  memory: 2924  grad_norm: 5.0602  loss: 0.4827  loss_bce: 0.0260  loss_dice: 0.4567
2024/07/08 19:44:00 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:44:00 - mmengine - INFO - Saving checkpoint at 17 epochs
2024/07/08 19:44:06 - mmengine - INFO - Epoch(val) [17][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9838771606342347, 'occupancy': 0.13596861569405047, 'mIoU': 0.5599228881641426}, '1~2m': {'no_occupancy': 0.990022338075445, 'occupancy': 0.014857096356058799, 'mIoU': 0.5024397172157519}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9928699099359432, 'occupancy': 0.03770642801252732, 'mIoU': 0.5152881689742352}}  total_overall: {'no_occupancy': 0.9928757105424318, 'occupancy': 0.08604849076759889, 'mIoU': 0.5394621006550153}  data_time: 0.0319  time: 0.1458
2024/07/08 19:45:33 - mmengine - INFO - Epoch(train) [18][100/195]  lr: 1.9562e-05  eta: 0:16:43  time: 0.7182  data_time: 0.0009  memory: 2924  grad_norm: 4.2642  loss: 0.4886  loss_bce: 0.0268  loss_dice: 0.4618
2024/07/08 19:46:40 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:46:40 - mmengine - INFO - Saving checkpoint at 18 epochs
2024/07/08 19:46:46 - mmengine - INFO - Epoch(val) [18][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9842969404729988, 'occupancy': 0.1407824909062435, 'mIoU': 0.5625397156896211}, '1~2m': {'no_occupancy': 0.9899670009665219, 'occupancy': 0.018154268067846608, 'mIoU': 0.5040606345171843}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9929610206184035, 'occupancy': 0.03973418974352253, 'mIoU': 0.516347605180963}}  total_overall: {'no_occupancy': 0.9929667221089571, 'occupancy': 0.08915477459575223, 'mIoU': 0.5410607483523546}  data_time: 0.0371  time: 0.1512
2024/07/08 19:48:12 - mmengine - INFO - Epoch(train) [19][100/195]  lr: 1.4645e-05  eta: 0:14:08  time: 0.7151  data_time: 0.0009  memory: 2924  grad_norm: 4.5286  loss: 0.4589  loss_bce: 0.0251  loss_dice: 0.4338
2024/07/08 19:49:20 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:49:20 - mmengine - INFO - Saving checkpoint at 19 epochs
2024/07/08 19:49:26 - mmengine - INFO - Epoch(val) [19][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9840142585857004, 'occupancy': 0.12980672720995032, 'mIoU': 0.5569104928978253}, '1~2m': {'no_occupancy': 0.9900217307695816, 'occupancy': 0.01331330806890088, 'mIoU': 0.5016675194192413}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9929040325973438, 'occupancy': 0.0357800088197128, 'mIoU': 0.5143420207085283}}  total_overall: {'no_occupancy': 0.9929094200010137, 'occupancy': 0.08145490337459298, 'mIoU': 0.5371821616878033}  data_time: 0.0302  time: 0.1435
2024/07/08 19:50:52 - mmengine - INFO - Epoch(train) [20][100/195]  lr: 1.0332e-05  eta: 0:11:33  time: 0.7206  data_time: 0.0009  memory: 2924  grad_norm: 4.1200  loss: 0.4709  loss_bce: 0.0259  loss_dice: 0.4449
2024/07/08 19:52:00 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:52:00 - mmengine - INFO - Saving checkpoint at 20 epochs
2024/07/08 19:52:06 - mmengine - INFO - Epoch(val) [20][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9838623106801209, 'occupancy': 0.1356304799954485, 'mIoU': 0.5597463953377847}, '1~2m': {'no_occupancy': 0.9898683836841499, 'occupancy': 0.016639381965812695, 'mIoU': 0.5032538828249813}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.992827708849591, 'occupancy': 0.0380674654903153, 'mIoU': 0.5154475871699532}}  total_overall: {'no_occupancy': 0.9928335007930241, 'occupancy': 0.08605245119672487, 'mIoU': 0.5394429759948745}  data_time: 0.0300  time: 0.1437
2024/07/08 19:53:32 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:53:32 - mmengine - INFO - Epoch(train) [21][100/195]  lr: 6.6987e-06  eta: 0:08:58  time: 0.7212  data_time: 0.0009  memory: 2924  grad_norm: 4.6174  loss: 0.4602  loss_bce: 0.0252  loss_dice: 0.4351
2024/07/08 19:54:40 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:54:40 - mmengine - INFO - Saving checkpoint at 21 epochs
2024/07/08 19:54:46 - mmengine - INFO - Epoch(val) [21][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9846374837666453, 'occupancy': 0.1362089872617699, 'mIoU': 0.5604232355142076}, '1~2m': {'no_occupancy': 0.9901819316623893, 'occupancy': 0.013673474819101467, 'mIoU': 0.5019277032407454}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.993099889115782, 'occupancy': 0.03747061552021784, 'mIoU': 0.5152852523179999}}  total_overall: {'no_occupancy': 0.9931051043277328, 'occupancy': 0.08476630262161303, 'mIoU': 0.5389357034746729}  data_time: 0.0317  time: 0.1456
2024/07/08 19:56:12 - mmengine - INFO - Epoch(train) [22][100/195]  lr: 3.8060e-06  eta: 0:06:24  time: 0.7126  data_time: 0.0009  memory: 2924  grad_norm: 4.1298  loss: 0.4629  loss_bce: 0.0248  loss_dice: 0.4381
2024/07/08 19:57:20 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 19:57:20 - mmengine - INFO - Saving checkpoint at 22 epochs
2024/07/08 19:57:27 - mmengine - INFO - Epoch(val) [22][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9846846908778265, 'occupancy': 0.1388115142335264, 'mIoU': 0.5617481025556764}, '1~2m': {'no_occupancy': 0.9900780493697675, 'occupancy': 0.013878608979553625, 'mIoU': 0.5019783291746606}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9930857203204219, 'occupancy': 0.03817253080327, 'mIoU': 0.5156291255618459}}  total_overall: {'no_occupancy': 0.9930910006947699, 'occupancy': 0.08610656899189625, 'mIoU': 0.539598784843333}  data_time: 0.0281  time: 0.1414
2024/07/08 19:58:53 - mmengine - INFO - Epoch(train) [23][100/195]  lr: 1.7037e-06  eta: 0:03:49  time: 0.7144  data_time: 0.0009  memory: 2924  grad_norm: 4.0261  loss: 0.4469  loss_bce: 0.0243  loss_dice: 0.4227
2024/07/08 20:00:01 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 20:00:01 - mmengine - INFO - Saving checkpoint at 23 epochs
2024/07/08 20:00:07 - mmengine - INFO - Epoch(val) [23][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9848359916860094, 'occupancy': 0.13683491076166615, 'mIoU': 0.5608354512238378}, '1~2m': {'no_occupancy': 0.990150144174672, 'occupancy': 0.014489936562350904, 'mIoU': 0.5023200403685114}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9931415692236937, 'occupancy': 0.03783121183100426, 'mIoU': 0.515486390527349}}  total_overall: {'no_occupancy': 0.9931466602326539, 'occupancy': 0.084937739063417, 'mIoU': 0.5390421996480355}  data_time: 0.0388  time: 0.1521
2024/07/08 20:01:34 - mmengine - INFO - Epoch(train) [24][100/195]  lr: 4.2776e-07  eta: 0:01:15  time: 0.7105  data_time: 0.0010  memory: 2924  grad_norm: 4.2027  loss: 0.4642  loss_bce: 0.0255  loss_dice: 0.4387
2024/07/08 20:02:42 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_185837
2024/07/08 20:02:42 - mmengine - INFO - Saving checkpoint at 24 epochs
2024/07/08 20:02:48 - mmengine - INFO - Epoch(val) [24][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9846024701885719, 'occupancy': 0.1362022651552391, 'mIoU': 0.5604023676719055}, '1~2m': {'no_occupancy': 0.9900424918204805, 'occupancy': 0.014404461187493991, 'mIoU': 0.5022234765039872}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9930562757607865, 'occupancy': 0.03765168158568327, 'mIoU': 0.5153539786732348}}  total_overall: {'no_occupancy': 0.9930615074838159, 'occupancy': 0.08472531794939032, 'mIoU': 0.5388934127166032}  data_time: 0.0353  time: 0.1496
