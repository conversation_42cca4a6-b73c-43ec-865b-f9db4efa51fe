2024/07/04 15:03:16 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: linux
    Python: 3.8.10 (default, May 26 2023, 14:05:08) [GCC 9.4.0]
    CUDA available: True
    numpy_random_seed: 847685523
    GPU 0,1,2,3: NVIDIA TITAN Xp
    CUDA_HOME: /usr/local/cuda
    NVCC: Cuda compilation tools, release 11.6, V11.6.124
    GCC: x86_64-linux-gnu-gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0
    PyTorch: 1.13.0+cu116
    PyTorch compiling details: PyTorch built with:
  - GCC 9.3
  - C++ Version: 201402
  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v2.6.0 (Git Hash 52b5f107dd9cf10910aaa19cb47f3abf9b349815)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX2
  - CUDA Runtime 11.6
  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86
  - CuDNN 8.4
    - Built with CuDNN 8.3.2
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.6, CUDNN_VERSION=8.3.2, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -fabi-version=11 -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -DEDGE_PROFILER_USE_KINETO -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wunused-local-typedefs -Wno-unused-parameter -Wno-unused-function -Wno-unused-result -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Werror=cast-function-type -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.13.0, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, 

    TorchVision: 0.14.0+cu116
    OpenCV: 4.8.1
    MMEngine: 0.10.1

Runtime environment:
    cudnn_benchmark: False
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 1}
    dist_cfg: {'backend': 'nccl'}
    seed: 847685523
    Distributed launcher: pytorch
    Distributed training: True
    GPU number: 4
------------------------------------------------------------

2024/07/04 15:03:17 - mmengine - INFO - Config:
auto_scale_lr = dict(base_batch_size=4, enable=False)
bn_kwargs = dict(eps=2e-05, momentum=0.1)
class_names = [
    'no_occupancy',
    'occupancy',
]
custom_imports = dict(
    allow_failed_imports=False,
    imports=[
        'projects.SurroundOcc.surroundocc.models',
        'projects.SurroundOcc.surroundocc.datasets',
    ])
data_root = '/home/<USER>/indoor'
data_shape = (
    3,
    384,
    512,
)
dataset_type = 'EcoAi.KujialeFusionDataset'
default_hooks = dict(
    checkpoint=dict(interval=1, type='CheckpointHook'),
    logger=dict(interval=100, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    timer=dict(type='IterTimerHook'),
    visualization=dict(type='mmdet3d.Det3DVisualizationHook'))
default_scope = 'mmdet'
depoly_model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=128, num_channels=[
                256,
                512,
                1024,
            ]),
        fpn_lss_params=dict(in_channels=1280, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(
        activation='relu',
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        include_top=False,
        model_type='b0',
        num_classes=1000,
        type='efficientnet',
        use_se_block=False),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    lidar_config=dict(
        max_num_points=10,
        max_voxels=[
            90000,
            120000,
        ],
        point_cloud_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        voxel_size=[
            0.05,
            0.05,
            0.2,
        ]),
    norm_range=[
        -2,
        0,
        -0.05,
        2,
        4,
        0.15,
    ],
    reader=dict(
        bn_kwargs=None,
        hw_reverse=True,
        num_filters=(64, ),
        num_input_features=3,
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pool_size=(
            10,
            1,
        ),
        quantize=False,
        type='PillarFeatureNet_ld',
        use_4dim=True,
        use_conv=True,
        voxel_size=[
            0.05,
            0.05,
            0.2,
        ],
        with_distance=False),
    scatter=dict(
        num_input_features=64,
        quantize=True,
        type='PointPillarScatter_dpx',
        use_horizon_pillar_scatter=True),
    type='EcoOccFusionDepoly',
    use_grid_mask=False,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depoly=True,
        depth=120,
        depth_grid_quant_scale=0.03125,
        feat_channels=64,
        grid_quant_scale=0.0078125,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=4,
        num_views=1,
        type='LSSTransformer_Fusion',
        z_range=(
            -0.05,
            0.15,
        )))
depth_quant_scale = 0.03125
depthview_shape = [
    720,
    768.0,
]
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=1))
featview_shape = [
    144.0,
    32.0,
]
find_unused_parameters = True
grid_quant_scale = 0.0078125
img_norm_cfg = dict(
    mean=[
        103.53,
        116.28,
        123.675,
    ], std=[
        1.0,
        1.0,
        1.0,
    ], to_rgb=False)
keys = (
    'img',
    'points',
)
launcher = 'pytorch'
load_from = None
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=50)
lss_bev_size = [
    -2,
    2,
    0,
    4,
    0.05,
]
lss_depth = 120
lss_grid_size = (
    80,
    80,
)
lss_num_points = 4
lss_z_range = (
    -0.05,
    0.15,
)
meta_keys = (
    'lidar2img',
    'img_shape',
    'pc_range',
    'occ_size',
    'gt_occ',
    'occ_path',
    'filename',
)
model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=128, num_channels=[
                256,
                512,
                1024,
            ]),
        fpn_lss_params=dict(in_channels=1280, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(
        activation='relu',
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        include_top=False,
        model_type='b0',
        num_classes=1000,
        type='efficientnet',
        use_se_block=False),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    is_vis=False,
    lambda_dice=1,
    lidar_config=dict(
        max_num_points=10,
        max_voxels=[
            90000,
            120000,
        ],
        point_cloud_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        voxel_size=[
            0.05,
            0.05,
            0.2,
        ]),
    norm_range=[
        -2,
        0,
        -0.05,
        2,
        4,
        0.15,
    ],
    reader=dict(
        bn_kwargs=None,
        hw_reverse=True,
        num_filters=(64, ),
        num_input_features=3,
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pool_size=(
            10,
            1,
        ),
        quantize=False,
        type='PillarFeatureNet_ld',
        use_4dim=True,
        use_conv=True,
        voxel_size=[
            0.05,
            0.05,
            0.2,
        ],
        with_distance=False),
    scatter=dict(
        num_input_features=64,
        quantize=True,
        type='PointPillarScatter_dpx',
        use_horizon_pillar_scatter=True),
    type='EcoOccFusion',
    use_grid_mask=True,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depth=120,
        depth_grid_quant_scale=0.03125,
        feat_channels=64,
        grid_quant_scale=0.0078125,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=4,
        num_views=1,
        type='LSSTransformer_Fusion',
        z_range=(
            -0.05,
            0.15,
        )))
numC_Trans = 128
occ_size = [
    80,
    80,
    4,
]
optim_wrapper = dict(
    clip_grad=dict(max_norm=5, norm_type=2),
    optimizer=dict(lr=0.0001, type='AdamW', weight_decay=0.01),
    type='AmpOptimWrapper')
param_scheduler = [
    dict(
        begin=0,
        by_epoch=False,
        end=500,
        start_factor=0.3333333333333333,
        type='LinearLR'),
    dict(by_epoch=True, type='CosineAnnealingLR'),
]
point_cloud_range = [
    -2,
    0,
    -0.05,
    2,
    4,
    0.15,
]
resume = False
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_oms_dk_0704_val.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(
                coord_type='LIDAR',
                load_dim=3,
                reduce_beams=32,
                type='LoadPointsFromFileSingle',
                use_dim=3),
            dict(
                point_cloud_range=[
                    -2,
                    0,
                    -0.05,
                    2,
                    4,
                    0.15,
                ],
                type='PointsRangeFilter'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=(
                    'img',
                    'points',
                ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeFusionDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
test_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(
        coord_type='LIDAR',
        load_dim=3,
        reduce_beams=32,
        type='LoadPointsFromFileSingle',
        use_dim=3),
    dict(
        point_cloud_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        type='PointsRangeFilter'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=(
            'img',
            'points',
        ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
train_cfg = dict(max_epochs=24, type='EpochBasedTrainLoop', val_interval=1)
train_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_oms_dk_0704_train.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(
                coord_type='LIDAR',
                load_dim=3,
                reduce_beams=32,
                type='LoadPointsFromFileSingle',
                use_dim=3),
            dict(
                point_cloud_range=[
                    -2,
                    0,
                    -0.05,
                    2,
                    4,
                    0.15,
                ],
                type='PointsRangeFilter'),
            dict(type='PhotoMetricDistortionMultiViewImage'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=(
                    'img',
                    'points',
                ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        test_mode=False,
        type='EcoAi.KujialeFusionDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    prefetch_factor=8,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(
        coord_type='LIDAR',
        load_dim=3,
        reduce_beams=32,
        type='LoadPointsFromFileSingle',
        use_dim=3),
    dict(
        point_cloud_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        type='PointsRangeFilter'),
    dict(type='PhotoMetricDistortionMultiViewImage'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=(
            'img',
            'points',
        ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
use_semantic = True
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_oms_dk_0704_val.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(
                coord_type='LIDAR',
                load_dim=3,
                reduce_beams=32,
                type='LoadPointsFromFileSingle',
                use_dim=3),
            dict(
                point_cloud_range=[
                    -2,
                    0,
                    -0.05,
                    2,
                    4,
                    0.15,
                ],
                type='PointsRangeFilter'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=(
                    'img',
                    'points',
                ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeFusionDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
val_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(
        coord_type='LIDAR',
        load_dim=3,
        reduce_beams=32,
        type='LoadPointsFromFileSingle',
        use_dim=3),
    dict(
        point_cloud_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        type='PointsRangeFilter'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=(
            'img',
            'points',
        ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
view_shape = [
    24.0,
    32.0,
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='mmdet3d.Det3DLocalVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
voxel_size = [
    0.05,
    0.05,
    0.2,
]
work_dir = 'work_dirs/indoor_oms_dk_occ/'

2024/07/04 15:03:23 - mmengine - INFO - Autoplay mode, press [SPACE] to pause.
2024/07/04 15:03:23 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2024/07/04 15:03:25 - mmengine - WARNING - The prefix is not set in metric class BEVMetric.
Name of parameter - Initialization information

img_backbone.mod1.0.0.weight - torch.Size([32, 3, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone.mod1.0.1.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone.mod1.0.1.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._depthwise_conv.0.weight - torch.Size([32, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._depthwise_conv.1.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._depthwise_conv.1.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._project_conv.0.weight - torch.Size([16, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._project_conv.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._project_conv.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._expand_conv.0.weight - torch.Size([96, 16, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._expand_conv.1.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._expand_conv.1.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._depthwise_conv.0.weight - torch.Size([96, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._depthwise_conv.1.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._depthwise_conv.1.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._project_conv.0.weight - torch.Size([24, 96, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._project_conv.1.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._project_conv.1.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._expand_conv.0.weight - torch.Size([144, 24, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._expand_conv.1.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._expand_conv.1.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._depthwise_conv.0.weight - torch.Size([144, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._depthwise_conv.1.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._depthwise_conv.1.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._project_conv.0.weight - torch.Size([24, 144, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._project_conv.1.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._project_conv.1.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._expand_conv.0.weight - torch.Size([144, 24, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._expand_conv.1.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._expand_conv.1.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._depthwise_conv.0.weight - torch.Size([144, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._depthwise_conv.1.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._depthwise_conv.1.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._project_conv.0.weight - torch.Size([40, 144, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._project_conv.1.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._project_conv.1.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._expand_conv.0.weight - torch.Size([240, 40, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._expand_conv.1.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._expand_conv.1.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._depthwise_conv.0.weight - torch.Size([240, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._depthwise_conv.1.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._depthwise_conv.1.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._project_conv.0.weight - torch.Size([40, 240, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._project_conv.1.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._project_conv.1.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._expand_conv.0.weight - torch.Size([240, 40, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._expand_conv.1.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._expand_conv.1.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._depthwise_conv.0.weight - torch.Size([240, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._depthwise_conv.1.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._depthwise_conv.1.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._project_conv.0.weight - torch.Size([80, 240, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._project_conv.1.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._project_conv.1.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._expand_conv.0.weight - torch.Size([480, 80, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._expand_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._expand_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._depthwise_conv.0.weight - torch.Size([480, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._depthwise_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._depthwise_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._project_conv.0.weight - torch.Size([80, 480, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._project_conv.1.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._project_conv.1.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._expand_conv.0.weight - torch.Size([480, 80, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._expand_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._expand_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._depthwise_conv.0.weight - torch.Size([480, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._depthwise_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._depthwise_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._project_conv.0.weight - torch.Size([80, 480, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._project_conv.1.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._project_conv.1.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._expand_conv.0.weight - torch.Size([480, 80, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._expand_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._expand_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._depthwise_conv.0.weight - torch.Size([480, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._depthwise_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._depthwise_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._project_conv.0.weight - torch.Size([112, 480, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._project_conv.1.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._project_conv.1.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._expand_conv.0.weight - torch.Size([672, 112, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._expand_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._expand_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._depthwise_conv.0.weight - torch.Size([672, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._depthwise_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._depthwise_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._project_conv.0.weight - torch.Size([112, 672, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._project_conv.1.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._project_conv.1.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._expand_conv.0.weight - torch.Size([672, 112, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._expand_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._expand_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._depthwise_conv.0.weight - torch.Size([672, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._depthwise_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._depthwise_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._project_conv.0.weight - torch.Size([112, 672, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._project_conv.1.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._project_conv.1.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._expand_conv.0.weight - torch.Size([672, 112, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._expand_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._expand_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._depthwise_conv.0.weight - torch.Size([672, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._depthwise_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._depthwise_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._project_conv.0.weight - torch.Size([192, 672, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._project_conv.1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._project_conv.1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._expand_conv.0.weight - torch.Size([1152, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._expand_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._expand_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._depthwise_conv.0.weight - torch.Size([1152, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._depthwise_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._depthwise_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._project_conv.0.weight - torch.Size([192, 1152, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._project_conv.1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._project_conv.1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._expand_conv.0.weight - torch.Size([1152, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._expand_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._expand_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._depthwise_conv.0.weight - torch.Size([1152, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._depthwise_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._depthwise_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._project_conv.0.weight - torch.Size([192, 1152, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._project_conv.1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._project_conv.1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._expand_conv.0.weight - torch.Size([1152, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._expand_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._expand_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._depthwise_conv.0.weight - torch.Size([1152, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._depthwise_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._depthwise_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._project_conv.0.weight - torch.Size([192, 1152, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._project_conv.1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._project_conv.1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._expand_conv.0.weight - torch.Size([1152, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._expand_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._expand_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._depthwise_conv.0.weight - torch.Size([1152, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._depthwise_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._depthwise_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._project_conv.0.weight - torch.Size([320, 1152, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._project_conv.1.weight - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._project_conv.1.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.0.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.0.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.0.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.0.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.1.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.1.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.1.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.1.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.2.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.2.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.2.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.2.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.3.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.3.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.3.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.3.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.project.0.weight - torch.Size([64, 384, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.project.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.project.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.project.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.project.0.weight - torch.Size([64, 112, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.project.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.project.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.project.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.0.0.weight - torch.Size([64, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.0.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.0.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.0.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.1.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.1.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.1.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.1.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.0.0.weight - torch.Size([64, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.0.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.0.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.0.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.1.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.1.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.1.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.1.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

view_transformer.depth_net.0.weight - torch.Size([120, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

view_transformer.feat_net.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.conv1.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.downsample.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.downsample.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.conv1.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.conv1.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.downsample.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.downsample.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.conv1.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.conv1.weight - torch.Size([1024, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.bn1.weight - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.bn1.bias - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.conv2.weight - torch.Size([1024, 1024, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.bn2.weight - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.bn2.bias - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.downsample.weight - torch.Size([1024, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.downsample.bias - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.conv1.weight - torch.Size([1024, 1024, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.bn1.weight - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.bn1.bias - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.conv2.weight - torch.Size([1024, 1024, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.bn2.weight - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.bn2.bias - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.0.weight - torch.Size([512, 1280, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.3.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.4.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.4.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.up2.1.weight - torch.Size([256, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.up2.2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.up2.2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.up2.4.weight - torch.Size([256, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.up2.4.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_decoder.final_conv.conv.weight - torch.Size([256, 256, 3, 3]): 
Initialized by user-defined `init_weights` in ConvModule  

bev_decoder.final_conv.conv.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_decoder.predicter.0.weight - torch.Size([512, 256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_decoder.predicter.0.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_decoder.predicter.2.weight - torch.Size([4, 512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_decoder.predicter.2.bias - torch.Size([4]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

lidarmodel.reader.pfn_layers.0.linear.weight - torch.Size([64, 3, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

lidarmodel.reader.pfn_layers.0.norm.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

lidarmodel.reader.pfn_layers.0.norm.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

fuser.0.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

fuser.1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

fuser.1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  
2024/07/04 15:03:27 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2024/07/04 15:03:27 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2024/07/04 15:03:27 - mmengine - INFO - Checkpoints will be saved to /home/<USER>/work_dirs/indoor_oms_dk_occ.
2024/07/04 15:04:33 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:04:33 - mmengine - INFO - Epoch(train)  [1][91/91]  lr: 4.5357e-05  eta: 0:25:28  time: 0.5976  data_time: 0.0006  memory: 2946  grad_norm: 0.7983  loss: 0.9726  loss_bce: 0.0482  loss_dice: 0.9244
2024/07/04 15:04:33 - mmengine - INFO - Saving checkpoint at 1 epochs
2024/07/04 15:04:45 - mmengine - INFO - Epoch(val) [1][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9903883630942883, 'occupancy': 0.0013654606722943155, 'mIoU': 0.4958769118832913}, '1~2m': {'no_occupancy': 0.9947205872689552, 'occupancy': 0.0017749378771742986, 'mIoU': 0.49824776257306475}, '2~3m': {'no_occupancy': 0.9993064734154399, 'occupancy': 0.0, 'mIoU': 0.49965323670771994}, '3~4m': {'no_occupancy': 0.9998546860919196, 'occupancy': 0.0, 'mIoU': 0.4999273430459598}, 'patch_mean': {'no_occupancy': 0.9960675274676508, 'occupancy': 0.0007850996373671535, 'mIoU': 0.498426313552509}}  total_overall: {'no_occupancy': 0.9960675492877888, 'occupancy': 0.001430227257151881, 'mIoU': 0.4987488882724704}  data_time: 0.0491  time: 0.1474
2024/07/04 15:05:48 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:05:48 - mmengine - INFO - Epoch(train)  [2][91/91]  lr: 5.7269e-05  eta: 0:23:48  time: 0.6035  data_time: 0.0007  memory: 2946  grad_norm: 1.1153  loss: 0.6906  loss_bce: 0.0289  loss_dice: 0.6617
2024/07/04 15:05:48 - mmengine - INFO - Saving checkpoint at 2 epochs
2024/07/04 15:05:59 - mmengine - INFO - Epoch(val) [2][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9897908361733785, 'occupancy': 0.14513722386919733, 'mIoU': 0.5674640300212879}, '1~2m': {'no_occupancy': 0.9943644401357973, 'occupancy': 0.11165077296688977, 'mIoU': 0.5530076065513435}, '2~3m': {'no_occupancy': 0.9992689210333334, 'occupancy': 0.0066326530612244895, 'mIoU': 0.5029507870472789}, '3~4m': {'no_occupancy': 0.9998546860919196, 'occupancy': 0.0, 'mIoU': 0.4999273430459598}, 'patch_mean': {'no_occupancy': 0.9958197208586073, 'occupancy': 0.0658551624743279, 'mIoU': 0.5308374416664676}}  total_overall: {'no_occupancy': 0.9958225838472826, 'occupancy': 0.12762081301291434, 'mIoU': 0.5617216984300984}  data_time: 0.0235  time: 0.1179
2024/07/04 15:07:04 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:07:04 - mmengine - INFO - Epoch(train)  [3][91/91]  lr: 6.8486e-05  eta: 0:22:39  time: 0.6028  data_time: 0.0007  memory: 2946  grad_norm: inf  loss: 0.6855  loss_bce: 0.0267  loss_dice: 0.6588
2024/07/04 15:07:04 - mmengine - INFO - Saving checkpoint at 3 epochs
2024/07/04 15:07:08 - mmengine - INFO - Epoch(val) [3][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9904134404779344, 'occupancy': 0.14794076352209667, 'mIoU': 0.5691771020000156}, '1~2m': {'no_occupancy': 0.9936905447958173, 'occupancy': 0.07297013854390903, 'mIoU': 0.5333303416698632}, '2~3m': {'no_occupancy': 0.9991183336656481, 'occupancy': 0.0253217102532171, 'mIoU': 0.5122200219594326}, '3~4m': {'no_occupancy': 0.9998546860919196, 'occupancy': 0.0, 'mIoU': 0.4999273430459598}, 'patch_mean': {'no_occupancy': 0.9957692512578299, 'occupancy': 0.06155815307980571, 'mIoU': 0.5286637021688178}}  total_overall: {'no_occupancy': 0.995771716357621, 'occupancy': 0.1142798119109922, 'mIoU': 0.5550257641343066}  data_time: 0.0234  time: 0.1175
2024/07/04 15:08:12 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:08:12 - mmengine - INFO - Epoch(train)  [4][91/91]  lr: 7.8716e-05  eta: 0:21:31  time: 0.5969  data_time: 0.0007  memory: 2946  grad_norm: 1.4546  loss: 0.6520  loss_bce: 0.0269  loss_dice: 0.6251
2024/07/04 15:08:12 - mmengine - INFO - Saving checkpoint at 4 epochs
2024/07/04 15:08:16 - mmengine - INFO - Epoch(val) [4][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9891168489839965, 'occupancy': 0.22469837287385164, 'mIoU': 0.6069076109289241}, '1~2m': {'no_occupancy': 0.9934177951869603, 'occupancy': 0.15863328367533278, 'mIoU': 0.5760255394311465}, '2~3m': {'no_occupancy': 0.9991362644262616, 'occupancy': 0.1314199395770393, 'mIoU': 0.5652781020016504}, '3~4m': {'no_occupancy': 0.9998546860919196, 'occupancy': 0.0, 'mIoU': 0.4999273430459598}, 'patch_mean': {'no_occupancy': 0.9953813986722845, 'occupancy': 0.12868789903155592, 'mIoU': 0.5620346488519202}}  total_overall: {'no_occupancy': 0.9953868149058379, 'occupancy': 0.19673747404808897, 'mIoU': 0.5960621444769635}  data_time: 0.0230  time: 0.1190
2024/07/04 15:09:21 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:09:21 - mmengine - INFO - Epoch(train)  [5][91/91]  lr: 8.7692e-05  eta: 0:20:27  time: 0.6035  data_time: 0.0009  memory: 2946  grad_norm: 1.4850  loss: 0.6240  loss_bce: 0.0253  loss_dice: 0.5987
2024/07/04 15:09:21 - mmengine - INFO - Saving checkpoint at 5 epochs
2024/07/04 15:09:26 - mmengine - INFO - Epoch(val) [5][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9871268683708473, 'occupancy': 0.23939689257671104, 'mIoU': 0.6132618804737792}, '1~2m': {'no_occupancy': 0.9929851121178757, 'occupancy': 0.17129403402780863, 'mIoU': 0.5821395730728421}, '2~3m': {'no_occupancy': 0.9991235116314966, 'occupancy': 0.11423149905123338, 'mIoU': 0.556677505341365}, '3~4m': {'no_occupancy': 0.9998513066987083, 'occupancy': 0.0, 'mIoU': 0.4999256533493541}, 'patch_mean': {'no_occupancy': 0.994771699704732, 'occupancy': 0.13123060641393827, 'mIoU': 0.5630011530593352}}  total_overall: {'no_occupancy': 0.9947799474959608, 'occupancy': 0.21160262371423091, 'mIoU': 0.6031912856050958}  data_time: 0.0238  time: 0.1205
2024/07/04 15:10:30 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:10:30 - mmengine - INFO - Epoch(train)  [6][91/91]  lr: 8.9668e-05  eta: 0:19:20  time: 0.6035  data_time: 0.0007  memory: 2946  grad_norm: 1.3403  loss: 0.6171  loss_bce: 0.0247  loss_dice: 0.5924
2024/07/04 15:10:30 - mmengine - INFO - Saving checkpoint at 6 epochs
2024/07/04 15:10:34 - mmengine - INFO - Epoch(val) [6][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9868957453784876, 'occupancy': 0.24684922896867106, 'mIoU': 0.6168724871735793}, '1~2m': {'no_occupancy': 0.9931328913185951, 'occupancy': 0.16888140529716938, 'mIoU': 0.5810071483078822}, '2~3m': {'no_occupancy': 0.9990874415051882, 'occupancy': 0.12809472551130247, 'mIoU': 0.5635910835082454}, '3~4m': {'no_occupancy': 0.9998054967811218, 'occupancy': 0.00956022944550669, 'mIoU': 0.5046828631133142}, 'patch_mean': {'no_occupancy': 0.9947303937458482, 'occupancy': 0.13834639730566242, 'mIoU': 0.5665383955257552}}  total_overall: {'no_occupancy': 0.9947391910804747, 'occupancy': 0.21649037081632796, 'mIoU': 0.6056147809484014}  data_time: 0.0234  time: 0.1183
2024/07/04 15:11:38 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:11:38 - mmengine - INFO - Epoch(train)  [7][91/91]  lr: 8.5355e-05  eta: 0:18:14  time: 0.5987  data_time: 0.0007  memory: 2946  grad_norm: 1.3920  loss: 0.5867  loss_bce: 0.0257  loss_dice: 0.5610
2024/07/04 15:11:38 - mmengine - INFO - Saving checkpoint at 7 epochs
2024/07/04 15:11:42 - mmengine - INFO - Epoch(val) [7][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9875256249345562, 'occupancy': 0.24318196763024946, 'mIoU': 0.6153537962824028}, '1~2m': {'no_occupancy': 0.9929898012624071, 'occupancy': 0.15272413323033582, 'mIoU': 0.5728569672463715}, '2~3m': {'no_occupancy': 0.9990183468495283, 'occupancy': 0.11659344373099019, 'mIoU': 0.5578058952902593}, '3~4m': {'no_occupancy': 0.9998437969360169, 'occupancy': 0.0, 'mIoU': 0.49992189846800844}, 'patch_mean': {'no_occupancy': 0.9948443924956272, 'occupancy': 0.12812488614789388, 'mIoU': 0.5614846393217605}}  total_overall: {'no_occupancy': 0.994852157549939, 'occupancy': 0.20748986572652928, 'mIoU': 0.6011710116382342}  data_time: 0.0223  time: 0.1167
2024/07/04 15:12:46 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:12:46 - mmengine - INFO - Epoch(train)  [8][91/91]  lr: 8.0438e-05  eta: 0:17:08  time: 0.5995  data_time: 0.0007  memory: 2946  grad_norm: 1.4109  loss: 0.5788  loss_bce: 0.0243  loss_dice: 0.5545
2024/07/04 15:12:46 - mmengine - INFO - Saving checkpoint at 8 epochs
2024/07/04 15:12:51 - mmengine - INFO - Epoch(val) [8][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9883143788022358, 'occupancy': 0.24787947342404767, 'mIoU': 0.6180969261131417}, '1~2m': {'no_occupancy': 0.9941082791175985, 'occupancy': 0.14633482191482408, 'mIoU': 0.5702215505162113}, '2~3m': {'no_occupancy': 0.9991115130039798, 'occupancy': 0.09418070444104135, 'mIoU': 0.5466461087225106}, '3~4m': {'no_occupancy': 0.9998216428012219, 'occupancy': 0.010416666666666664, 'mIoU': 0.5051191547339443}, 'patch_mean': {'no_occupancy': 0.995338953431259, 'occupancy': 0.12470291661164494, 'mIoU': 0.560020935021452}}  total_overall: {'no_occupancy': 0.9953459206820079, 'occupancy': 0.20990456435673183, 'mIoU': 0.6026252425193699}  data_time: 0.0240  time: 0.1189
2024/07/04 15:13:57 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:13:57 - mmengine - INFO - Epoch(train)  [9][91/91]  lr: 7.5000e-05  eta: 0:16:07  time: 0.6057  data_time: 0.0007  memory: 2946  grad_norm: 1.5238  loss: 0.5695  loss_bce: 0.0231  loss_dice: 0.5464
2024/07/04 15:13:57 - mmengine - INFO - Saving checkpoint at 9 epochs
2024/07/04 15:14:01 - mmengine - INFO - Epoch(val) [9][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9888710195598492, 'occupancy': 0.24682148003621662, 'mIoU': 0.6178462497980329}, '1~2m': {'no_occupancy': 0.9940246479045294, 'occupancy': 0.1484358260124277, 'mIoU': 0.5712302369584785}, '2~3m': {'no_occupancy': 0.999129524697202, 'occupancy': 0.11017274472168907, 'mIoU': 0.5546511347094455}, '3~4m': {'no_occupancy': 0.999797611059523, 'occupancy': 0.02, 'mIoU': 0.5098988055297615}, 'patch_mean': {'no_occupancy': 0.9954557008052759, 'occupancy': 0.13135751269258333, 'mIoU': 0.5634066067489296}}  total_overall: {'no_occupancy': 0.9954619589083834, 'occupancy': 0.2088511670203092, 'mIoU': 0.6021565629643463}  data_time: 0.0231  time: 0.1177
2024/07/04 15:15:05 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:15:05 - mmengine - INFO - Epoch(train) [10][91/91]  lr: 6.9134e-05  eta: 0:15:01  time: 0.6021  data_time: 0.0007  memory: 2946  grad_norm: 1.5519  loss: 0.5227  loss_bce: 0.0213  loss_dice: 0.5015
2024/07/04 15:15:05 - mmengine - INFO - Saving checkpoint at 10 epochs
2024/07/04 15:15:09 - mmengine - INFO - Epoch(val) [10][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9876412501435649, 'occupancy': 0.2490055414751493, 'mIoU': 0.618323395809357}, '1~2m': {'no_occupancy': 0.9934713078887141, 'occupancy': 0.1512974637149978, 'mIoU': 0.5723843858018559}, '2~3m': {'no_occupancy': 0.9991179349840354, 'occupancy': 0.052822580645161286, 'mIoU': 0.5259702578145984}, '3~4m': {'no_occupancy': 0.999846049807018, 'occupancy': 0.0024330900243309003, 'mIoU': 0.5011395699156744}, 'patch_mean': {'no_occupancy': 0.9950191357058331, 'occupancy': 0.11388966896490982, 'mIoU': 0.5544544023353715}}  total_overall: {'no_occupancy': 0.9950270725543184, 'occupancy': 0.21039071521985314, 'mIoU': 0.6027088938870858}  data_time: 0.0230  time: 0.1181
2024/07/04 15:16:13 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:16:13 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:16:13 - mmengine - INFO - Epoch(train) [11][91/91]  lr: 6.2941e-05  eta: 0:13:56  time: 0.5975  data_time: 0.0007  memory: 2946  grad_norm: 1.4968  loss: 0.5041  loss_bce: 0.0218  loss_dice: 0.4823
2024/07/04 15:16:13 - mmengine - INFO - Saving checkpoint at 11 epochs
2024/07/04 15:16:17 - mmengine - INFO - Epoch(val) [11][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9881560440210762, 'occupancy': 0.24585383415336612, 'mIoU': 0.6170049390872211}, '1~2m': {'no_occupancy': 0.9934658776954243, 'occupancy': 0.14119855738352846, 'mIoU': 0.5673322175394764}, '2~3m': {'no_occupancy': 0.9990311300178452, 'occupancy': 0.10540915395284327, 'mIoU': 0.5522201419853442}, '3~4m': {'no_occupancy': 0.9998081246986695, 'occupancy': 0.022944550669216062, 'mIoU': 0.5113763376839427}, 'patch_mean': {'no_occupancy': 0.9951152941082538, 'occupancy': 0.12885152403973849, 'mIoU': 0.5619834090739961}}  total_overall: {'no_occupancy': 0.9951223203038331, 'occupancy': 0.20543383399935702, 'mIoU': 0.6002780771515951}  data_time: 0.0231  time: 0.1183
2024/07/04 15:17:21 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:17:21 - mmengine - INFO - Epoch(train) [12][91/91]  lr: 5.6526e-05  eta: 0:12:51  time: 0.6028  data_time: 0.0007  memory: 2946  grad_norm: 1.5258  loss: 0.5062  loss_bce: 0.0205  loss_dice: 0.4858
2024/07/04 15:17:21 - mmengine - INFO - Saving checkpoint at 12 epochs
2024/07/04 15:17:25 - mmengine - INFO - Epoch(val) [12][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9886909422493892, 'occupancy': 0.24145666405479868, 'mIoU': 0.6150738031520939}, '1~2m': {'no_occupancy': 0.9938503953317287, 'occupancy': 0.1562306581390551, 'mIoU': 0.5750405267353919}, '2~3m': {'no_occupancy': 0.9991028715994835, 'occupancy': 0.097809667673716, 'mIoU': 0.5484562696365998}, '3~4m': {'no_occupancy': 0.999788974479805, 'occupancy': 0.025996533795493933, 'mIoU': 0.5128927541376495}, 'patch_mean': {'no_occupancy': 0.9953582959151016, 'occupancy': 0.13037338091576592, 'mIoU': 0.5628658384154338}}  total_overall: {'no_occupancy': 0.9953646138700394, 'occupancy': 0.20676386387030365, 'mIoU': 0.6010642388701715}  data_time: 0.0230  time: 0.1178
2024/07/04 15:18:29 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:18:29 - mmengine - INFO - Epoch(train) [13][91/91]  lr: 5.0000e-05  eta: 0:11:46  time: 0.5935  data_time: 0.0007  memory: 2946  grad_norm: 1.5411  loss: 0.4835  loss_bce: 0.0204  loss_dice: 0.4631
2024/07/04 15:18:29 - mmengine - INFO - Saving checkpoint at 13 epochs
2024/07/04 15:18:33 - mmengine - INFO - Epoch(val) [13][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9888344849267834, 'occupancy': 0.2446069761142183, 'mIoU': 0.6167207305205008}, '1~2m': {'no_occupancy': 0.9937073346765706, 'occupancy': 0.150253807106599, 'mIoU': 0.5719805708915848}, '2~3m': {'no_occupancy': 0.998946285336618, 'occupancy': 0.07879185817465528, 'mIoU': 0.5388690717556366}, '3~4m': {'no_occupancy': 0.9997810900065521, 'occupancy': 0.008503401360544217, 'mIoU': 0.5041422456835482}, 'patch_mean': {'no_occupancy': 0.995317298736631, 'occupancy': 0.1205390106890042, 'mIoU': 0.5579281547128176}}  total_overall: {'no_occupancy': 0.9953235083183616, 'occupancy': 0.2046009946908525, 'mIoU': 0.599962251504607}  data_time: 0.0236  time: 0.1187
2024/07/04 15:19:37 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:19:37 - mmengine - INFO - Epoch(train) [14][91/91]  lr: 4.3474e-05  eta: 0:10:41  time: 0.5983  data_time: 0.0007  memory: 2946  grad_norm: 1.5866  loss: 0.4627  loss_bce: 0.0191  loss_dice: 0.4437
2024/07/04 15:19:37 - mmengine - INFO - Saving checkpoint at 14 epochs
2024/07/04 15:19:42 - mmengine - INFO - Epoch(val) [14][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9890716111559883, 'occupancy': 0.24071062302227486, 'mIoU': 0.6148911170891316}, '1~2m': {'no_occupancy': 0.9939420296174123, 'occupancy': 0.14521347122779102, 'mIoU': 0.5695777504226016}, '2~3m': {'no_occupancy': 0.9990754392039315, 'occupancy': 0.11311239193083572, 'mIoU': 0.5560939155673836}, '3~4m': {'no_occupancy': 0.9998051215118063, 'occupancy': 0.003838771593090211, 'mIoU': 0.5018219465524483}, 'patch_mean': {'no_occupancy': 0.9954735503722846, 'occupancy': 0.12571881444349797, 'mIoU': 0.5605961824078913}}  total_overall: {'no_occupancy': 0.9954793694955341, 'occupancy': 0.20296101754945564, 'mIoU': 0.5992201935224949}  data_time: 0.0241  time: 0.1199
2024/07/04 15:20:45 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:20:45 - mmengine - INFO - Epoch(train) [15][91/91]  lr: 3.7059e-05  eta: 0:09:37  time: 0.5968  data_time: 0.0007  memory: 2946  grad_norm: 1.6173  loss: 0.4385  loss_bce: 0.0190  loss_dice: 0.4195
2024/07/04 15:20:45 - mmengine - INFO - Saving checkpoint at 15 epochs
2024/07/04 15:20:50 - mmengine - INFO - Epoch(val) [15][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9892167717211701, 'occupancy': 0.241283096397919, 'mIoU': 0.6152499340595445}, '1~2m': {'no_occupancy': 0.9939202609736527, 'occupancy': 0.12895757053629117, 'mIoU': 0.561438915754972}, '2~3m': {'no_occupancy': 0.999143830793928, 'occupancy': 0.07128309572301426, 'mIoU': 0.5352134632584711}, '3~4m': {'no_occupancy': 0.9998250221350754, 'occupancy': 0.01271186440677966, 'mIoU': 0.5062684432709276}, 'patch_mean': {'no_occupancy': 0.9955264714059566, 'occupancy': 0.11355890676600104, 'mIoU': 0.5545426890859788}}  total_overall: {'no_occupancy': 0.9955321677764729, 'occupancy': 0.197187617411274, 'mIoU': 0.5963598925938735}  data_time: 0.0238  time: 0.1191
2024/07/04 15:21:54 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:21:54 - mmengine - INFO - Epoch(train) [16][91/91]  lr: 3.0866e-05  eta: 0:08:33  time: 0.6040  data_time: 0.0007  memory: 2946  grad_norm: 1.5549  loss: 0.4180  loss_bce: 0.0178  loss_dice: 0.4002
2024/07/04 15:21:54 - mmengine - INFO - Saving checkpoint at 16 epochs
2024/07/04 15:21:58 - mmengine - INFO - Epoch(val) [16][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9894967688505145, 'occupancy': 0.23473651708522025, 'mIoU': 0.6121166429678674}, '1~2m': {'no_occupancy': 0.994161034423399, 'occupancy': 0.14459556191839656, 'mIoU': 0.5693782981708978}, '2~3m': {'no_occupancy': 0.9991051375170438, 'occupancy': 0.0848694316436252, 'mIoU': 0.5419872845803345}, '3~4m': {'no_occupancy': 0.9998115043902202, 'occupancy': 0.01568627450980392, 'mIoU': 0.5077488894500121}, 'patch_mean': {'no_occupancy': 0.9956436112952943, 'occupancy': 0.11997194628926149, 'mIoU': 0.5578077787922779}}  total_overall: {'no_occupancy': 0.9956488424288557, 'occupancy': 0.1976715473921331, 'mIoU': 0.5966601949104944}  data_time: 0.0243  time: 0.1192
2024/07/04 15:23:01 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:23:01 - mmengine - INFO - Epoch(train) [17][91/91]  lr: 2.5000e-05  eta: 0:07:28  time: 0.5986  data_time: 0.0007  memory: 2946  grad_norm: 1.5279  loss: 0.4020  loss_bce: 0.0181  loss_dice: 0.3839
2024/07/04 15:23:01 - mmengine - INFO - Saving checkpoint at 17 epochs
2024/07/04 15:23:05 - mmengine - INFO - Epoch(val) [17][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9890920106653887, 'occupancy': 0.2401077484382382, 'mIoU': 0.6145998795518134}, '1~2m': {'no_occupancy': 0.9940577760070961, 'occupancy': 0.14029363784665577, 'mIoU': 0.5671757069268759}, '2~3m': {'no_occupancy': 0.9990277790816662, 'occupancy': 0.07995735607675906, 'mIoU': 0.5394925675792126}, '3~4m': {'no_occupancy': 0.9998054965620206, 'occupancy': 0.015209125475285171, 'mIoU': 0.5075073110186529}, 'patch_mean': {'no_occupancy': 0.995495765579043, 'occupancy': 0.11889196695923455, 'mIoU': 0.5571938662691388}}  total_overall: {'no_occupancy': 0.9955015414373733, 'occupancy': 0.1999168449555392, 'mIoU': 0.5977091931964563}  data_time: 0.0230  time: 0.1171
2024/07/04 15:24:09 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:24:09 - mmengine - INFO - Epoch(train) [18][91/91]  lr: 1.9562e-05  eta: 0:06:24  time: 0.5995  data_time: 0.0007  memory: 2946  grad_norm: 1.4938  loss: 0.3851  loss_bce: 0.0168  loss_dice: 0.3683
2024/07/04 15:24:09 - mmengine - INFO - Saving checkpoint at 18 epochs
2024/07/04 15:24:13 - mmengine - INFO - Epoch(val) [18][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9894293798760097, 'occupancy': 0.24002992967879766, 'mIoU': 0.6147296547774037}, '1~2m': {'no_occupancy': 0.9940462029086821, 'occupancy': 0.13842053736538668, 'mIoU': 0.5662333701370343}, '2~3m': {'no_occupancy': 0.9990874935927407, 'occupancy': 0.07779886148007589, 'mIoU': 0.5384431775364084}, '3~4m': {'no_occupancy': 0.9998062476860491, 'occupancy': 0.011494252873563216, 'mIoU': 0.5056502502798061}, 'patch_mean': {'no_occupancy': 0.9955923310158703, 'occupancy': 0.11693589534945587, 'mIoU': 0.5562641131826631}}  total_overall: {'no_occupancy': 0.9955977635332502, 'occupancy': 0.198722266053457, 'mIoU': 0.5971600147933536}  data_time: 0.0235  time: 0.1172
2024/07/04 15:25:17 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:25:17 - mmengine - INFO - Epoch(train) [19][91/91]  lr: 1.4645e-05  eta: 0:05:20  time: 0.6018  data_time: 0.0007  memory: 2946  grad_norm: 1.5297  loss: 0.3756  loss_bce: 0.0164  loss_dice: 0.3593
2024/07/04 15:25:17 - mmengine - INFO - Saving checkpoint at 19 epochs
2024/07/04 15:25:21 - mmengine - INFO - Epoch(val) [19][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9894353304233929, 'occupancy': 0.23512405991967977, 'mIoU': 0.6122796951715364}, '1~2m': {'no_occupancy': 0.9941670196623944, 'occupancy': 0.14533538936006168, 'mIoU': 0.5697512045112281}, '2~3m': {'no_occupancy': 0.9991329353873517, 'occupancy': 0.07676929228308677, 'mIoU': 0.5379511138352192}, '3~4m': {'no_occupancy': 0.999809627158357, 'occupancy': 0.009765624999999998, 'mIoU': 0.5047876260791785}, 'patch_mean': {'no_occupancy': 0.9956362281578741, 'occupancy': 0.11674859164070706, 'mIoU': 0.5561924098992905}}  total_overall: {'no_occupancy': 0.9956415510711378, 'occupancy': 0.1980920435536229, 'mIoU': 0.5968667973123803}  data_time: 0.0239  time: 0.1189
2024/07/04 15:26:25 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:26:25 - mmengine - INFO - Epoch(train) [20][91/91]  lr: 1.0332e-05  eta: 0:04:16  time: 0.5994  data_time: 0.0008  memory: 2946  grad_norm: 1.4674  loss: 0.3566  loss_bce: 0.0160  loss_dice: 0.3406
2024/07/04 15:26:25 - mmengine - INFO - Saving checkpoint at 20 epochs
2024/07/04 15:26:29 - mmengine - INFO - Epoch(val) [20][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9894471288715729, 'occupancy': 0.23518631643249843, 'mIoU': 0.6123167226520356}, '1~2m': {'no_occupancy': 0.9940552098379003, 'occupancy': 0.13888283972125434, 'mIoU': 0.5664690247795773}, '2~3m': {'no_occupancy': 0.9990965066802556, 'occupancy': 0.0778075891146033, 'mIoU': 0.5384520478974295}, '3~4m': {'no_occupancy': 0.9998085006199321, 'occupancy': 0.011627906976744186, 'mIoU': 0.5057182037983381}, 'patch_mean': {'no_occupancy': 0.9956018365024152, 'occupancy': 0.11587616306127507, 'mIoU': 0.5557389997818452}}  total_overall: {'no_occupancy': 0.9956071205449976, 'occupancy': 0.19570206057634199, 'mIoU': 0.5956545905606698}  data_time: 0.0247  time: 0.1195
2024/07/04 15:27:34 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:27:34 - mmengine - INFO - Epoch(train) [21][91/91]  lr: 6.6987e-06  eta: 0:03:12  time: 0.6066  data_time: 0.0008  memory: 2946  grad_norm: 1.4175  loss: 0.3834  loss_bce: 0.0177  loss_dice: 0.3656
2024/07/04 15:27:34 - mmengine - INFO - Saving checkpoint at 21 epochs
2024/07/04 15:27:39 - mmengine - INFO - Epoch(val) [21][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9893894948714503, 'occupancy': 0.235425453583026, 'mIoU': 0.6124074742272382}, '1~2m': {'no_occupancy': 0.9940815681457795, 'occupancy': 0.1383782009192383, 'mIoU': 0.5662298845325089}, '2~3m': {'no_occupancy': 0.9991269297668509, 'occupancy': 0.07333599043443603, 'mIoU': 0.5362314601006435}, '3~4m': {'no_occupancy': 0.9998081252029984, 'occupancy': 0.009689922480620155, 'mIoU': 0.5047490238418093}, 'patch_mean': {'no_occupancy': 0.9956015294967698, 'occupancy': 0.11420739185433013, 'mIoU': 0.55490446067555}}  total_overall: {'no_occupancy': 0.9956068906005093, 'occupancy': 0.1959205963230757, 'mIoU': 0.5957637434617925}  data_time: 0.0240  time: 0.1198
2024/07/04 15:28:42 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:28:43 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:28:43 - mmengine - INFO - Epoch(train) [22][91/91]  lr: 3.8060e-06  eta: 0:02:08  time: 0.6014  data_time: 0.0007  memory: 2946  grad_norm: 1.3809  loss: 0.3591  loss_bce: 0.0156  loss_dice: 0.3436
2024/07/04 15:28:43 - mmengine - INFO - Saving checkpoint at 22 epochs
2024/07/04 15:28:47 - mmengine - INFO - Epoch(val) [22][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9895188950038988, 'occupancy': 0.23463798563426866, 'mIoU': 0.6120784403190838}, '1~2m': {'no_occupancy': 0.9941403255048994, 'occupancy': 0.13685434313237002, 'mIoU': 0.5654973343186347}, '2~3m': {'no_occupancy': 0.999120168200708, 'occupancy': 0.07537490134175216, 'mIoU': 0.5372475347712301}, '3~4m': {'no_occupancy': 0.9998073741529908, 'occupancy': 0.011560693641618497, 'mIoU': 0.5056840338973047}, 'patch_mean': {'no_occupancy': 0.9956466907156243, 'occupancy': 0.11460698093750234, 'mIoU': 0.5551268358265633}}  total_overall: {'no_occupancy': 0.9956518878127453, 'occupancy': 0.19486731091724144, 'mIoU': 0.5952595993649934}  data_time: 0.0228  time: 0.1177
2024/07/04 15:29:51 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:29:51 - mmengine - INFO - Epoch(train) [23][91/91]  lr: 1.7037e-06  eta: 0:01:04  time: 0.5955  data_time: 0.0007  memory: 2946  grad_norm: 1.4482  loss: 0.3725  loss_bce: 0.0167  loss_dice: 0.3557
2024/07/04 15:29:51 - mmengine - INFO - Saving checkpoint at 23 epochs
2024/07/04 15:29:56 - mmengine - INFO - Epoch(val) [23][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9894788985140646, 'occupancy': 0.23456020389985888, 'mIoU': 0.6120195512069617}, '1~2m': {'no_occupancy': 0.9940892094529051, 'occupancy': 0.13587912087912088, 'mIoU': 0.564984165166013}, '2~3m': {'no_occupancy': 0.9991164113910457, 'occupancy': 0.07689289917614751, 'mIoU': 0.5380046552835966}, '3~4m': {'no_occupancy': 0.9998085006199321, 'occupancy': 0.011627906976744186, 'mIoU': 0.5057182037983381}, 'patch_mean': {'no_occupancy': 0.9956232549944868, 'occupancy': 0.11474003273296787, 'mIoU': 0.5551816438637274}}  total_overall: {'no_occupancy': 0.9956284865491486, 'occupancy': 0.1945120209164813, 'mIoU': 0.595070253732815}  data_time: 0.0226  time: 0.1198
2024/07/04 15:31:00 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_150313
2024/07/04 15:31:00 - mmengine - INFO - Epoch(train) [24][91/91]  lr: 4.2776e-07  eta: 0:00:00  time: 0.6016  data_time: 0.0007  memory: 2946  grad_norm: 1.3836  loss: 0.3378  loss_bce: 0.0155  loss_dice: 0.3223
2024/07/04 15:31:00 - mmengine - INFO - Saving checkpoint at 24 epochs
2024/07/04 15:31:05 - mmengine - INFO - Epoch(val) [24][14/14]    by_distance: {'0-1m': {'no_occupancy': 0.9895113299964682, 'occupancy': 0.23467466518240607, 'mIoU': 0.6120929975894371}, '1~2m': {'no_occupancy': 0.9940986319965993, 'occupancy': 0.13549523757088586, 'mIoU': 0.5647969347837426}, '2~3m': {'no_occupancy': 0.9991182899631884, 'occupancy': 0.0759543486816214, 'mIoU': 0.5375363193224049}, '3~4m': {'no_occupancy': 0.9998077496419713, 'occupancy': 0.011583011583011582, 'mIoU': 0.5056953806124914}, 'patch_mean': {'no_occupancy': 0.9956340003995567, 'occupancy': 0.11442681575448121, 'mIoU': 0.555030408077019}}  total_overall: {'no_occupancy': 0.9956392022127025, 'occupancy': 0.19439489616561423, 'mIoU': 0.5950170491891584}  data_time: 0.0246  time: 0.1206
