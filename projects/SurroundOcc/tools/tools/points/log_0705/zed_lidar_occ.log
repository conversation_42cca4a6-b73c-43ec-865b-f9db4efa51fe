2024/07/08 09:18:39 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: linux
    Python: 3.8.10 (default, May 26 2023, 14:05:08) [GCC 9.4.0]
    CUDA available: True
    numpy_random_seed: 583748535
    GPU 0,1,2,3: NVIDIA TITAN Xp
    CUDA_HOME: /usr/local/cuda
    NVCC: Cuda compilation tools, release 11.6, V11.6.124
    GCC: x86_64-linux-gnu-gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0
    PyTorch: 1.13.0+cu116
    PyTorch compiling details: PyTorch built with:
  - GCC 9.3
  - C++ Version: 201402
  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v2.6.0 (Git Hash 52b5f107dd9cf10910aaa19cb47f3abf9b349815)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX2
  - CUDA Runtime 11.6
  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86
  - CuDNN 8.4
    - Built with CuDNN 8.3.2
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.6, CUDNN_VERSION=8.3.2, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -fabi-version=11 -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -DEDGE_PROFILER_USE_KINETO -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wunused-local-typedefs -Wno-unused-parameter -Wno-unused-function -Wno-unused-result -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Werror=cast-function-type -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.13.0, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, 

    TorchVision: 0.14.0+cu116
    OpenCV: 4.8.1
    MMEngine: 0.10.1

Runtime environment:
    cudnn_benchmark: False
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 1}
    dist_cfg: {'backend': 'nccl'}
    seed: 583748535
    Distributed launcher: pytorch
    Distributed training: True
    GPU number: 4
------------------------------------------------------------

2024/07/08 09:18:39 - mmengine - INFO - Config:
auto_scale_lr = dict(base_batch_size=4, enable=False)
bn_kwargs = dict(eps=2e-05, momentum=0.1)
class_names = [
    'no_occupancy',
    'occupancy',
]
custom_imports = dict(
    allow_failed_imports=False,
    imports=[
        'projects.SurroundOcc.surroundocc.models',
        'projects.SurroundOcc.surroundocc.datasets',
    ])
data_root = '/home/<USER>/indoor'
data_shape = (
    3,
    384,
    512,
)
dataset_type = 'EcoAi.KujialeStereoDataset'
default_hooks = dict(
    checkpoint=dict(interval=1, type='CheckpointHook'),
    logger=dict(interval=100, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    timer=dict(type='IterTimerHook'),
    visualization=dict(type='mmdet3d.Det3DVisualizationHook'))
default_scope = 'mmdet'
depoly_model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=64, num_channels=[
                128,
                256,
                512,
            ]),
        fpn_lss_params=dict(in_channels=640, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(type='EfficientNetB0'),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    type='EcoOccVisionDepoly',
    use_grid_mask=False,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depoly=True,
        depth=80,
        feat_channels=64,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=8,
        num_views=2,
        type='LSSTransformer',
        z_range=(
            -0.05,
            0.15,
        )))
depth_quant_scale = 0.03125
depthview_shape = [
    480,
    768.0,
]
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=1))
featview_shape = [
    144.0,
    32.0,
]
find_unused_parameters = True
grid_quant_scale = 0.0078125
img_norm_cfg = dict(
    mean=[
        103.53,
        116.28,
        123.675,
    ], std=[
        1.0,
        1.0,
        1.0,
    ], to_rgb=False)
keys = ('img', )
launcher = 'pytorch'
load_from = None
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=50)
lss_bev_size = [
    -2,
    2,
    0,
    4,
    0.05,
]
lss_depth = 80
lss_grid_size = (
    80,
    80,
)
lss_num_points = 8
lss_z_range = (
    -0.05,
    0.15,
)
meta_keys = (
    'lidar2img',
    'img_shape',
    'pc_range',
    'occ_size',
    'gt_occ',
    'occ_path',
    'filename',
)
model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=128, num_channels=[
                128,
                256,
                512,
            ]),
        fpn_lss_params=dict(in_channels=640, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(type='EfficientNetB0'),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            128,
            128,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    is_vis=False,
    lambda_dice=1,
    type='EcoOccVision',
    use_grid_mask=True,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depth=80,
        feat_channels=128,
        grid_size=(
            80,
            80,
        ),
        in_channels=128,
        num_points=8,
        num_views=2,
        type='LSSTransformer',
        z_range=(
            -0.05,
            0.15,
        )))
numC_Trans = 64
occ_size = [
    80,
    80,
    4,
]
optim_wrapper = dict(
    clip_grad=dict(max_norm=35, norm_type=2),
    optimizer=dict(lr=0.0001, type='AdamW', weight_decay=0.01),
    type='AmpOptimWrapper')
param_scheduler = [
    dict(
        begin=0,
        by_epoch=False,
        end=500,
        start_factor=0.3333333333333333,
        type='LinearLR'),
    dict(by_epoch=True, type='CosineAnnealingLR'),
]
point_cloud_range = [
    -2,
    0,
    -0.05,
    2,
    4,
    0.15,
]
resume = False
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0704_val.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
test_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
train_cfg = dict(max_epochs=24, type='EpochBasedTrainLoop', val_interval=1)
train_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0704_train.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(type='PhotoMetricDistortionMultiViewImage'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        test_mode=False,
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    prefetch_factor=8,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(type='PhotoMetricDistortionMultiViewImage'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
use_semantic = True
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0704_val.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
val_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
view_shape = [
    24.0,
    32.0,
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='mmdet3d.Det3DLocalVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = 'work_dirs/zed_lidar_0704/zed_lidar'

2024/07/08 09:18:42 - mmengine - INFO - Autoplay mode, press [SPACE] to pause.
2024/07/08 09:18:42 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2024/07/08 09:18:43 - mmengine - WARNING - The prefix is not set in metric class BEVMetric.
Name of parameter - Initialization information

img_backbone.stem.stem.conv.weight - torch.Size([32, 3, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stem.stem.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stem.stem.norm.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stem.stem.norm.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.depth.conv.weight - torch.Size([32, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.0.conv.depth.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.depth.norm.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.depth.norm.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.project.conv.weight - torch.Size([16, 32, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.0.conv.project.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.project.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.project.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.expand_conv.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.expand_conv.conv.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.expand_conv.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.expand_conv.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.depth.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.depth.conv.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.depth.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.depth.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.project.conv.weight - torch.Size([24, 96, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.project.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.project.norm.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.project.norm.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.expand_conv.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.depth.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.depth.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.depth.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.depth.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.project.conv.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.project.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.project.norm.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.project.norm.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.expand_conv.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.expand_conv.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.expand_conv.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.expand_conv.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.depth.conv.weight - torch.Size([144, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.depth.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.depth.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.depth.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.project.conv.weight - torch.Size([40, 144, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.project.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.project.norm.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.project.norm.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.conv.weight - torch.Size([240, 40, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.expand_conv.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.depth.conv.weight - torch.Size([240, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.depth.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.depth.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.depth.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.project.conv.weight - torch.Size([40, 240, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.project.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.project.norm.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.project.norm.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.expand_conv.conv.weight - torch.Size([240, 40, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.expand_conv.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.expand_conv.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.expand_conv.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.depth.conv.weight - torch.Size([240, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.depth.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.depth.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.depth.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.project.conv.weight - torch.Size([80, 240, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.depth.conv.weight - torch.Size([480, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.project.conv.weight - torch.Size([80, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.depth.conv.weight - torch.Size([480, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.project.conv.weight - torch.Size([80, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.depth.conv.weight - torch.Size([480, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.project.conv.weight - torch.Size([112, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.project.conv.weight - torch.Size([112, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.project.conv.weight - torch.Size([112, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.project.conv.weight - torch.Size([192, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.depth.conv.weight - torch.Size([1152, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.project.conv.weight - torch.Size([320, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.project.conv.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.project.norm.weight - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.project.norm.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_4.conv.conv.weight - torch.Size([112, 320, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_4.conv.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_3.conv.conv.weight - torch.Size([40, 224, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_3.conv.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_2.conv.conv.weight - torch.Size([24, 80, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_2.conv.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_1.conv.conv.weight - torch.Size([16, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_1.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_0.conv.conv.weight - torch.Size([16, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_0.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_4.conv.conv.weight - torch.Size([224, 224, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_4.conv.conv.bias - torch.Size([224]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_3.conv.conv.weight - torch.Size([80, 80, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_3.conv.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_2.conv.conv.weight - torch.Size([48, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_2.conv.conv.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_1.conv.conv.weight - torch.Size([32, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_1.conv.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_0.conv.conv.weight - torch.Size([16, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_0.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_dispconv2.conv.weight - torch.Size([2, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv2.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_dispconv1.conv.weight - torch.Size([2, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv1.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_dispconv0.conv.weight - torch.Size([2, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv0.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_conf_mask_conv21.conv.weight - torch.Size([2, 66, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv21.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_conf_mask_conv11.conv.weight - torch.Size([2, 44, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv11.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_conf_mask_conv01.conv.weight - torch.Size([2, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv01.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.0.weight - torch.Size([32, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.0.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.1.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.1.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.0.weight - torch.Size([32, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.0.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.1.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.1.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.0.weight - torch.Size([32, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.0.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.1.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.1.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.0.weight - torch.Size([32, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.0.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.1.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.1.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.0.weight - torch.Size([128, 448, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.0.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.0.weight - torch.Size([128, 112, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.0.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.0.weight - torch.Size([128, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.0.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.0.weight - torch.Size([128, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.0.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.0.weight - torch.Size([128, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.0.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.0.weight - torch.Size([128, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.0.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

view_transformer.depth_net.0.weight - torch.Size([80, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

view_transformer.feat_net.0.weight - torch.Size([128, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.conv1.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.conv2.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn2.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn2.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.downsample.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.downsample.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.conv1.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.conv2.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn2.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn2.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.conv1.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.downsample.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.downsample.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.conv1.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.conv1.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.downsample.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.downsample.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.conv1.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.0.weight - torch.Size([512, 640, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.3.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.4.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.4.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.1.weight - torch.Size([256, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.4.weight - torch.Size([256, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.4.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.final_conv.conv.weight - torch.Size([256, 256, 3, 3]): 
Initialized by user-defined `init_weights` in ConvModule  

bev_decoder.final_conv.conv.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.0.weight - torch.Size([512, 256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.0.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.2.weight - torch.Size([4, 512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.2.bias - torch.Size([4]): 
The value is the same before and after calling `init_weights` of EcoOccVision  
2024/07/08 09:18:45 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2024/07/08 09:18:45 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2024/07/08 09:18:45 - mmengine - INFO - Checkpoints will be saved to /home/<USER>/ecoaitoolkit/work_dirs/zed_lidar_0704/zed_lidar.
2024/07/08 09:20:13 - mmengine - INFO - Epoch(train)  [1][100/147]  lr: 4.6560e-05  eta: 0:50:30  time: 0.6824  data_time: 0.0007  memory: 3093  grad_norm: 4.8951  loss: 1.0106  loss_bce: 0.0532  loss_dice: 0.9574
2024/07/08 09:20:46 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:20:46 - mmengine - INFO - Saving checkpoint at 1 epochs
2024/07/08 09:20:53 - mmengine - INFO - Epoch(val) [1][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9371609117634059, 'occupancy': 0.06725409501541638, 'mIoU': 0.5022075033894111}, '1~2m': {'no_occupancy': 0.9830507915694484, 'occupancy': 0.01953607984059131, 'mIoU': 0.5012934357050198}, '2~3m': {'no_occupancy': 0.9922924718549537, 'occupancy': 0.000152716638095929, 'mIoU': 0.4962225942465248}, '3~4m': {'no_occupancy': 0.9993775899416086, 'occupancy': 0.0, 'mIoU': 0.4996887949708043}, 'patch_mean': {'no_occupancy': 0.9779704412823542, 'occupancy': 0.021735722873525903, 'mIoU': 0.49985308207794005}}  total_overall: {'no_occupancy': 0.9780160811654541, 'occupancy': 0.05232349323766659, 'mIoU': 0.5151697872015604}  data_time: 0.0781  time: 0.1923
2024/07/08 09:22:21 - mmengine - INFO - Epoch(train)  [2][100/147]  lr: 6.5916e-05  eta: 0:46:10  time: 0.7218  data_time: 0.0008  memory: 3093  grad_norm: 14.8563  loss: 0.8476  loss_bce: 0.0406  loss_dice: 0.8070
2024/07/08 09:22:55 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:22:55 - mmengine - INFO - Saving checkpoint at 2 epochs
2024/07/08 09:23:02 - mmengine - INFO - Epoch(val) [2][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9671978939930267, 'occupancy': 0.0703054051748792, 'mIoU': 0.5187516495839529}, '1~2m': {'no_occupancy': 0.991367312582407, 'occupancy': 0.0, 'mIoU': 0.4956836562912035}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9890363369023817, 'occupancy': 0.0175763512937198, 'mIoU': 0.5033063440980508}}  total_overall: {'no_occupancy': 0.9890498546581455, 'occupancy': 0.053503483851589, 'mIoU': 0.5212766692548673}  data_time: 0.0286  time: 0.1433
2024/07/08 09:24:30 - mmengine - INFO - Epoch(train)  [3][100/147]  lr: 8.4376e-05  eta: 0:43:47  time: 0.7378  data_time: 0.0008  memory: 3093  grad_norm: 10.4310  loss: 0.8066  loss_bce: 0.0400  loss_dice: 0.7666
2024/07/08 09:25:04 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:25:04 - mmengine - INFO - Saving checkpoint at 3 epochs
2024/07/08 09:25:11 - mmengine - INFO - Epoch(val) [3][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9631231623961579, 'occupancy': 0.11679136642746696, 'mIoU': 0.5399572644118125}, '1~2m': {'no_occupancy': 0.9866891390709024, 'occupancy': 0.01151337361301774, 'mIoU': 0.49910125634196006}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9868481106252883, 'occupancy': 0.032076185010121175, 'mIoU': 0.5094621478177047}}  total_overall: {'no_occupancy': 0.9868769357191689, 'occupancy': 0.08719125996679443, 'mIoU': 0.5370340978429816}  data_time: 0.0270  time: 0.1428
2024/07/08 09:26:39 - mmengine - INFO - Epoch(train)  [4][100/147]  lr: 9.6194e-05  eta: 0:41:41  time: 0.7389  data_time: 0.0008  memory: 3093  grad_norm: 10.3517  loss: 0.7910  loss_bce: 0.0392  loss_dice: 0.7518
2024/07/08 09:27:14 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:27:14 - mmengine - INFO - Saving checkpoint at 4 epochs
2024/07/08 09:27:21 - mmengine - INFO - Epoch(val) [4][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9739438896239689, 'occupancy': 0.10311058093373314, 'mIoU': 0.538527235278851}, '1~2m': {'no_occupancy': 0.9885916591491024, 'occupancy': 0.006971238836026009, 'mIoU': 0.4977814489925642}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9900289224517912, 'occupancy': 0.027520454942439786, 'mIoU': 0.5087746886971155}}  total_overall: {'no_occupancy': 0.990040970293831, 'occupancy': 0.07153036735187028, 'mIoU': 0.5307856688228506}  data_time: 0.0302  time: 0.1461
2024/07/08 09:28:50 - mmengine - INFO - Epoch(train)  [5][100/147]  lr: 9.3301e-05  eta: 0:39:42  time: 0.7493  data_time: 0.0008  memory: 3093  grad_norm: 10.1574  loss: 0.7704  loss_bce: 0.0371  loss_dice: 0.7333
2024/07/08 09:29:24 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:29:24 - mmengine - INFO - Saving checkpoint at 5 epochs
2024/07/08 09:29:31 - mmengine - INFO - Epoch(val) [5][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9773317022035132, 'occupancy': 0.12026136601898033, 'mIoU': 0.5487965341112467}, '1~2m': {'no_occupancy': 0.9898932206787452, 'occupancy': 0.0020430107526881723, 'mIoU': 0.4959681157157167}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.991201265979088, 'occupancy': 0.030576094192917125, 'mIoU': 0.5108886800860025}}  total_overall: {'no_occupancy': 0.9912119925893897, 'occupancy': 0.08133736683293305, 'mIoU': 0.5362746797111614}  data_time: 0.0300  time: 0.1451
2024/07/08 09:31:00 - mmengine - INFO - Epoch(train)  [6][100/147]  lr: 8.9668e-05  eta: 0:37:39  time: 0.7387  data_time: 0.0008  memory: 3093  grad_norm: 8.1042  loss: 0.7468  loss_bce: 0.0374  loss_dice: 0.7094
2024/07/08 09:31:35 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:31:35 - mmengine - INFO - Saving checkpoint at 6 epochs
2024/07/08 09:31:42 - mmengine - INFO - Epoch(val) [6][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9676850370504262, 'occupancy': 0.1145232711818288, 'mIoU': 0.5411041541161274}, '1~2m': {'no_occupancy': 0.9887642096347218, 'occupancy': 0.004685275667332391, 'mIoU': 0.49672474265102706}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9885073469298103, 'occupancy': 0.029802136712290296, 'mIoU': 0.5091547418210504}}  total_overall: {'no_occupancy': 0.9885290323392284, 'occupancy': 0.08421405449269226, 'mIoU': 0.5363715434159604}  data_time: 0.0279  time: 0.1430
2024/07/08 09:33:11 - mmengine - INFO - Epoch(train)  [7][100/147]  lr: 8.5355e-05  eta: 0:35:37  time: 0.7416  data_time: 0.0008  memory: 3093  grad_norm: 8.4791  loss: 0.7213  loss_bce: 0.0348  loss_dice: 0.6865
2024/07/08 09:33:25 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:33:46 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:33:46 - mmengine - INFO - Saving checkpoint at 7 epochs
2024/07/08 09:33:52 - mmengine - INFO - Epoch(val) [7][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9728026590414606, 'occupancy': 0.111154863242419, 'mIoU': 0.5419787611419398}, '1~2m': {'no_occupancy': 0.9883853025894359, 'occupancy': 0.002945991834094676, 'mIoU': 0.4956656472117653}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9896920256662475, 'occupancy': 0.028525213769128418, 'mIoU': 0.509108619717688}}  total_overall: {'no_occupancy': 0.9897063613871425, 'occupancy': 0.07683371495482953, 'mIoU': 0.5332700381709861}  data_time: 0.0337  time: 0.1481
2024/07/08 09:35:21 - mmengine - INFO - Epoch(train)  [8][100/147]  lr: 8.0438e-05  eta: 0:33:35  time: 0.7450  data_time: 0.0008  memory: 3093  grad_norm: 8.1742  loss: 0.7061  loss_bce: 0.0351  loss_dice: 0.6710
2024/07/08 09:35:55 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:35:55 - mmengine - INFO - Saving checkpoint at 8 epochs
2024/07/08 09:36:01 - mmengine - INFO - Epoch(val) [8][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9741342956162515, 'occupancy': 0.11636984359662195, 'mIoU': 0.5452520696064367}, '1~2m': {'no_occupancy': 0.9898210961630198, 'occupancy': 0.0013398245522849206, 'mIoU': 0.49558046035765235}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9903838832033411, 'occupancy': 0.029427417037226716, 'mIoU': 0.5099056501202839}}  total_overall: {'no_occupancy': 0.9903976880138998, 'occupancy': 0.08157187794505245, 'mIoU': 0.5359847829794762}  data_time: 0.0358  time: 0.1525
2024/07/08 09:37:30 - mmengine - INFO - Epoch(train)  [9][100/147]  lr: 7.5000e-05  eta: 0:31:30  time: 0.7417  data_time: 0.0008  memory: 3093  grad_norm: 7.5206  loss: 0.7160  loss_bce: 0.0367  loss_dice: 0.6793
2024/07/08 09:38:05 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:38:05 - mmengine - INFO - Saving checkpoint at 9 epochs
2024/07/08 09:38:10 - mmengine - INFO - Epoch(val) [9][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9807413890009182, 'occupancy': 0.1329329587407736, 'mIoU': 0.556837173870846}, '1~2m': {'no_occupancy': 0.990428539673704, 'occupancy': 0.009310204056762998, 'mIoU': 0.4998693718652335}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9921875174271789, 'occupancy': 0.03556079069938415, 'mIoU': 0.5138741540632815}}  total_overall: {'no_occupancy': 0.9921959874960199, 'occupancy': 0.08864114194810498, 'mIoU': 0.5404185647220624}  data_time: 0.0315  time: 0.1471
2024/07/08 09:39:40 - mmengine - INFO - Epoch(train) [10][100/147]  lr: 6.9134e-05  eta: 0:29:28  time: 0.7436  data_time: 0.0008  memory: 3093  grad_norm: 7.8352  loss: 0.6851  loss_bce: 0.0337  loss_dice: 0.6514
2024/07/08 09:40:14 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:40:14 - mmengine - INFO - Saving checkpoint at 10 epochs
2024/07/08 09:40:20 - mmengine - INFO - Epoch(val) [10][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9767652354350118, 'occupancy': 0.10536955306373832, 'mIoU': 0.5410673942493751}, '1~2m': {'no_occupancy': 0.9898542464165245, 'occupancy': 0.00152932214533774, 'mIoU': 0.49569178428093114}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9910499057214074, 'occupancy': 0.026724718802269017, 'mIoU': 0.5088873122618383}}  total_overall: {'no_occupancy': 0.9910596632120292, 'occupancy': 0.07132422899133313, 'mIoU': 0.5311919461016812}  data_time: 0.0289  time: 0.1451
2024/07/08 09:41:48 - mmengine - INFO - Epoch(train) [11][100/147]  lr: 6.2941e-05  eta: 0:27:23  time: 0.7381  data_time: 0.0008  memory: 3093  grad_norm: 7.9834  loss: 0.6864  loss_bce: 0.0344  loss_dice: 0.6520
2024/07/08 09:42:23 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:42:23 - mmengine - INFO - Saving checkpoint at 11 epochs
2024/07/08 09:42:29 - mmengine - INFO - Epoch(val) [11][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9786127956675382, 'occupancy': 0.10902774619198236, 'mIoU': 0.5438202709297603}, '1~2m': {'no_occupancy': 0.99018168520973, 'occupancy': 0.005699024727567541, 'mIoU': 0.4979403549686488}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9915936554778403, 'occupancy': 0.028681692729887475, 'mIoU': 0.5101376741038639}}  total_overall: {'no_occupancy': 0.9916021520161907, 'occupancy': 0.07359011304436816, 'mIoU': 0.5325961325302795}  data_time: 0.0275  time: 0.1450
2024/07/08 09:43:57 - mmengine - INFO - Epoch(train) [12][100/147]  lr: 5.6526e-05  eta: 0:25:19  time: 0.7352  data_time: 0.0008  memory: 3093  grad_norm: 8.1526  loss: 0.6722  loss_bce: 0.0339  loss_dice: 0.6383
2024/07/08 09:44:31 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:44:31 - mmengine - INFO - Saving checkpoint at 12 epochs
2024/07/08 09:44:37 - mmengine - INFO - Epoch(val) [12][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9795009026073649, 'occupancy': 0.13515324775624857, 'mIoU': 0.5573270751818067}, '1~2m': {'no_occupancy': 0.9898415404166393, 'occupancy': 0.011859359789268737, 'mIoU': 0.500850450102954}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9917306460145244, 'occupancy': 0.03675315188637933, 'mIoU': 0.5142418989504519}}  total_overall: {'no_occupancy': 0.9917404748925204, 'occupancy': 0.09126152709383553, 'mIoU': 0.541501000993178}  data_time: 0.0405  time: 0.1572
2024/07/08 09:46:05 - mmengine - INFO - Epoch(train) [13][100/147]  lr: 5.0000e-05  eta: 0:23:15  time: 0.7332  data_time: 0.0008  memory: 3093  grad_norm: 7.4169  loss: 0.6413  loss_bce: 0.0321  loss_dice: 0.6092
2024/07/08 09:46:40 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:46:40 - mmengine - INFO - Saving checkpoint at 13 epochs
2024/07/08 09:46:45 - mmengine - INFO - Epoch(val) [13][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9783688034217423, 'occupancy': 0.11747230608236586, 'mIoU': 0.547920554752054}, '1~2m': {'no_occupancy': 0.9899013375764837, 'occupancy': 0.0009550069441815912, 'mIoU': 0.49542817226033264}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9914625705080798, 'occupancy': 0.029606828256636863, 'mIoU': 0.5105346993823584}}  total_overall: {'no_occupancy': 0.9914719792293675, 'occupancy': 0.0779234943965047, 'mIoU': 0.5346977368129361}  data_time: 0.0379  time: 0.1538
2024/07/08 09:48:06 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:48:15 - mmengine - INFO - Epoch(train) [14][100/147]  lr: 4.3474e-05  eta: 0:21:12  time: 0.7396  data_time: 0.0009  memory: 3093  grad_norm: 8.7629  loss: 0.6509  loss_bce: 0.0334  loss_dice: 0.6175
2024/07/08 09:48:49 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:48:49 - mmengine - INFO - Saving checkpoint at 14 epochs
2024/07/08 09:48:55 - mmengine - INFO - Epoch(val) [14][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9809944674995632, 'occupancy': 0.13313156580309626, 'mIoU': 0.5570630166513297}, '1~2m': {'no_occupancy': 0.9898598022490434, 'occupancy': 0.005007855459544384, 'mIoU': 0.4974338288542939}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.992108602695675, 'occupancy': 0.03453485531566016, 'mIoU': 0.5133217290056676}}  total_overall: {'no_occupancy': 0.992116723847134, 'occupancy': 0.08590851110697498, 'mIoU': 0.5390126174770545}  data_time: 0.0353  time: 0.1529
2024/07/08 09:50:25 - mmengine - INFO - Epoch(train) [15][100/147]  lr: 3.7059e-05  eta: 0:19:10  time: 0.7431  data_time: 0.0008  memory: 3093  grad_norm: 8.0931  loss: 0.6430  loss_bce: 0.0318  loss_dice: 0.6112
2024/07/08 09:51:00 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:51:00 - mmengine - INFO - Saving checkpoint at 15 epochs
2024/07/08 09:51:06 - mmengine - INFO - Epoch(val) [15][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9819797083173937, 'occupancy': 0.1403607586915809, 'mIoU': 0.5611702335044872}, '1~2m': {'no_occupancy': 0.9902681274404147, 'occupancy': 0.005462245536358824, 'mIoU': 0.4978651864883868}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9924569941979754, 'occupancy': 0.03645575105698493, 'mIoU': 0.5144563726274801}}  total_overall: {'no_occupancy': 0.9924647135065606, 'occupancy': 0.09023029634930722, 'mIoU': 0.5413475049279339}  data_time: 0.0286  time: 0.1450
2024/07/08 09:52:35 - mmengine - INFO - Epoch(train) [16][100/147]  lr: 3.0866e-05  eta: 0:17:07  time: 0.7397  data_time: 0.0009  memory: 3093  grad_norm: 7.7495  loss: 0.6289  loss_bce: 0.0324  loss_dice: 0.5964
2024/07/08 09:53:10 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:53:10 - mmengine - INFO - Saving checkpoint at 16 epochs
2024/07/08 09:53:16 - mmengine - INFO - Epoch(val) [16][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9804790743862511, 'occupancy': 0.11004751868534776, 'mIoU': 0.5452632965357994}, '1~2m': {'no_occupancy': 0.9907139063428836, 'occupancy': 0.001082765039986323, 'mIoU': 0.49589833569143493}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.992193280440807, 'occupancy': 0.02778257093133352, 'mIoU': 0.5099879256860703}}  total_overall: {'no_occupancy': 0.9922003405506891, 'occupancy': 0.07197243109525778, 'mIoU': 0.5320863858229734}  data_time: 0.0310  time: 0.1475
2024/07/08 09:54:45 - mmengine - INFO - Epoch(train) [17][100/147]  lr: 2.5000e-05  eta: 0:15:03  time: 0.7382  data_time: 0.0008  memory: 3093  grad_norm: 7.5467  loss: 0.6139  loss_bce: 0.0315  loss_dice: 0.5824
2024/07/08 09:55:19 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:55:19 - mmengine - INFO - Saving checkpoint at 17 epochs
2024/07/08 09:55:25 - mmengine - INFO - Epoch(val) [17][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.981179729699394, 'occupancy': 0.10811834683478834, 'mIoU': 0.5446490382670912}, '1~2m': {'no_occupancy': 0.9904643015991976, 'occupancy': 0.003273302282082362, 'mIoU': 0.49686880194064}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9923060430831713, 'occupancy': 0.027847912279217676, 'mIoU': 0.5100769776811944}}  total_overall: {'no_occupancy': 0.9923123928543109, 'occupancy': 0.06984224544150787, 'mIoU': 0.5310773191479095}  data_time: 0.0298  time: 0.1462
2024/07/08 09:56:54 - mmengine - INFO - Epoch(train) [18][100/147]  lr: 1.9562e-05  eta: 0:13:00  time: 0.7440  data_time: 0.0008  memory: 3093  grad_norm: 8.5225  loss: 0.6167  loss_bce: 0.0320  loss_dice: 0.5847
2024/07/08 09:57:29 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:57:29 - mmengine - INFO - Saving checkpoint at 18 epochs
2024/07/08 09:57:35 - mmengine - INFO - Epoch(val) [18][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9816768704474707, 'occupancy': 0.12859985853187525, 'mIoU': 0.5551383644896729}, '1~2m': {'no_occupancy': 0.9899974863692929, 'occupancy': 0.008287784564293072, 'mIoU': 0.49914263546679294}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9923136244627141, 'occupancy': 0.03422191077404208, 'mIoU': 0.5132677676183781}}  total_overall: {'no_occupancy': 0.9923208491987716, 'occupancy': 0.0830571239170686, 'mIoU': 0.5376889865579201}  data_time: 0.0318  time: 0.1474
2024/07/08 09:59:03 - mmengine - INFO - Epoch(train) [19][100/147]  lr: 1.4645e-05  eta: 0:10:56  time: 0.7377  data_time: 0.0009  memory: 3093  grad_norm: 7.5190  loss: 0.5954  loss_bce: 0.0305  loss_dice: 0.5649
2024/07/08 09:59:38 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 09:59:38 - mmengine - INFO - Saving checkpoint at 19 epochs
2024/07/08 09:59:44 - mmengine - INFO - Epoch(val) [19][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9824049411659053, 'occupancy': 0.12102677775305042, 'mIoU': 0.5517158594594779}, '1~2m': {'no_occupancy': 0.9904287320107357, 'occupancy': 0.0037497587471394304, 'mIoU': 0.49708924537893756}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9926034535526835, 'occupancy': 0.03119413412504746, 'mIoU': 0.5118987938388655}}  total_overall: {'no_occupancy': 0.9926096389424853, 'occupancy': 0.07666078279690162, 'mIoU': 0.5346352108696935}  data_time: 0.0409  time: 0.1583
2024/07/08 10:01:12 - mmengine - INFO - Epoch(train) [20][100/147]  lr: 1.0332e-05  eta: 0:08:53  time: 0.7370  data_time: 0.0009  memory: 3093  grad_norm: 8.5128  loss: 0.5829  loss_bce: 0.0294  loss_dice: 0.5535
2024/07/08 10:01:47 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 10:01:47 - mmengine - INFO - Saving checkpoint at 20 epochs
2024/07/08 10:01:53 - mmengine - INFO - Epoch(val) [20][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9825817407047845, 'occupancy': 0.12415103744781605, 'mIoU': 0.5533663890763003}, '1~2m': {'no_occupancy': 0.990163375685196, 'occupancy': 0.0057121436604130596, 'mIoU': 0.4979377596728045}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9925813143560184, 'occupancy': 0.03246579527705728, 'mIoU': 0.5125235548165378}}  total_overall: {'no_occupancy': 0.9925875095191566, 'occupancy': 0.07836379364796024, 'mIoU': 0.5354756515835585}  data_time: 0.0274  time: 0.1443
2024/07/08 10:02:52 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 10:03:22 - mmengine - INFO - Epoch(train) [21][100/147]  lr: 6.6987e-06  eta: 0:06:49  time: 0.7444  data_time: 0.0009  memory: 3093  grad_norm: 8.8213  loss: 0.5802  loss_bce: 0.0302  loss_dice: 0.5501
2024/07/08 10:03:56 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 10:03:56 - mmengine - INFO - Saving checkpoint at 21 epochs
2024/07/08 10:04:02 - mmengine - INFO - Epoch(val) [21][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9823856165283559, 'occupancy': 0.12923088144123118, 'mIoU': 0.5558082489847935}, '1~2m': {'no_occupancy': 0.990403749154584, 'occupancy': 0.005268970735398208, 'mIoU': 0.4978363599449911}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9925923766792583, 'occupancy': 0.033624963044157345, 'mIoU': 0.5131086698617078}}  total_overall: {'no_occupancy': 0.992599062044448, 'occupancy': 0.08244349227851036, 'mIoU': 0.5375212771614792}  data_time: 0.0351  time: 0.1512
2024/07/08 10:05:31 - mmengine - INFO - Epoch(train) [22][100/147]  lr: 3.8060e-06  eta: 0:04:46  time: 0.7374  data_time: 0.0009  memory: 3093  grad_norm: 7.8902  loss: 0.5631  loss_bce: 0.0290  loss_dice: 0.5341
2024/07/08 10:06:05 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 10:06:05 - mmengine - INFO - Saving checkpoint at 22 epochs
2024/07/08 10:06:11 - mmengine - INFO - Epoch(val) [22][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9830114711741964, 'occupancy': 0.12538674532450156, 'mIoU': 0.5541991082493489}, '1~2m': {'no_occupancy': 0.9903164986756386, 'occupancy': 0.005970167291580432, 'mIoU': 0.4981433329836095}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.992727027720982, 'occupancy': 0.032839228154020496, 'mIoU': 0.5127831279375012}}  total_overall: {'no_occupancy': 0.9927329676926354, 'occupancy': 0.07888172500580919, 'mIoU': 0.5358073463492223}  data_time: 0.0344  time: 0.1511
2024/07/08 10:07:41 - mmengine - INFO - Epoch(train) [23][100/147]  lr: 1.7037e-06  eta: 0:02:42  time: 0.7452  data_time: 0.0008  memory: 3093  grad_norm: 8.3987  loss: 0.5591  loss_bce: 0.0291  loss_dice: 0.5299
2024/07/08 10:08:16 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 10:08:16 - mmengine - INFO - Saving checkpoint at 23 epochs
2024/07/08 10:08:22 - mmengine - INFO - Epoch(val) [23][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9830369958173781, 'occupancy': 0.1265597987415269, 'mIoU': 0.5547983972794526}, '1~2m': {'no_occupancy': 0.9904653666326272, 'occupancy': 0.004498884497901135, 'mIoU': 0.49748212556526417}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9927706258710246, 'occupancy': 0.03276467080985701, 'mIoU': 0.5127676483404409}}  total_overall: {'no_occupancy': 0.9927766208655164, 'occupancy': 0.07953237332452694, 'mIoU': 0.5361544970950217}  data_time: 0.0348  time: 0.1515
2024/07/08 10:09:50 - mmengine - INFO - Epoch(train) [24][100/147]  lr: 4.2776e-07  eta: 0:00:39  time: 0.7394  data_time: 0.0009  memory: 3093  grad_norm: 8.1460  loss: 0.5506  loss_bce: 0.0286  loss_dice: 0.5221
2024/07/08 10:10:25 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_091836
2024/07/08 10:10:25 - mmengine - INFO - Saving checkpoint at 24 epochs
2024/07/08 10:10:31 - mmengine - INFO - Epoch(val) [24][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9829553432216971, 'occupancy': 0.12599546142208773, 'mIoU': 0.5544754023218924}, '1~2m': {'no_occupancy': 0.9903951590933118, 'occupancy': 0.005534088277240757, 'mIoU': 0.4979646236852763}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9927326608372755, 'occupancy': 0.03288238742483212, 'mIoU': 0.5128075241310538}}  total_overall: {'no_occupancy': 0.9927386871758159, 'occupancy': 0.07943014964965385, 'mIoU': 0.5360844184127349}  data_time: 0.0290  time: 0.1446
