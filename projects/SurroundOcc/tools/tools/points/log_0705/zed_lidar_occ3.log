2024/07/09 10:57:52 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: linux
    Python: 3.8.10 (default, May 26 2023, 14:05:08) [GCC 9.4.0]
    CUDA available: True
    numpy_random_seed: 569767683
    GPU 0,1,2,3: NVIDIA TITAN Xp
    CUDA_HOME: /usr/local/cuda
    NVCC: Cuda compilation tools, release 11.6, V11.6.124
    GCC: x86_64-linux-gnu-gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0
    PyTorch: 1.13.0+cu116
    PyTorch compiling details: PyTorch built with:
  - GCC 9.3
  - C++ Version: 201402
  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v2.6.0 (Git Hash 52b5f107dd9cf10910aaa19cb47f3abf9b349815)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX2
  - CUDA Runtime 11.6
  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86
  - CuDNN 8.4
    - Built with CuDNN 8.3.2
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.6, CUDNN_VERSION=8.3.2, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -fabi-version=11 -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -DEDGE_PROFILER_USE_KINETO -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wunused-local-typedefs -Wno-unused-parameter -Wno-unused-function -Wno-unused-result -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Werror=cast-function-type -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.13.0, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, 

    TorchVision: 0.14.0+cu116
    OpenCV: 4.8.1
    MMEngine: 0.10.1

Runtime environment:
    cudnn_benchmark: False
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 1}
    dist_cfg: {'backend': 'nccl'}
    seed: 569767683
    Distributed launcher: pytorch
    Distributed training: True
    GPU number: 4
------------------------------------------------------------

2024/07/09 10:57:53 - mmengine - INFO - Config:
auto_scale_lr = dict(base_batch_size=4, enable=False)
bn_kwargs = dict(eps=2e-05, momentum=0.1)
class_names = [
    'no_occupancy',
    'occupancy',
]
custom_imports = dict(
    allow_failed_imports=False,
    imports=[
        'projects.SurroundOcc.surroundocc.models',
        'projects.SurroundOcc.surroundocc.datasets',
    ])
data_root = '/home/<USER>/indoor/zed_lidar'
data_shape = (
    3,
    384,
    512,
)
dataset_type = 'EcoAi.KujialeStereoDataset'
default_hooks = dict(
    checkpoint=dict(interval=1, type='CheckpointHook'),
    logger=dict(interval=100, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    timer=dict(type='IterTimerHook'),
    visualization=dict(type='mmdet3d.Det3DVisualizationHook'))
default_scope = 'mmdet'
depoly_model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=64, num_channels=[
                128,
                256,
                512,
            ]),
        fpn_lss_params=dict(in_channels=640, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(type='EfficientNetB0'),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    type='EcoOccVisionDepoly',
    use_grid_mask=False,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depoly=True,
        depth=120,
        feat_channels=64,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=8,
        num_views=2,
        type='LSSTransformer',
        z_range=(
            -0.05,
            0.15,
        )))
depth_quant_scale = 0.03125
depthview_shape = [
    720,
    768.0,
]
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=1))
featview_shape = [
    144.0,
    32.0,
]
find_unused_parameters = True
grid_quant_scale = 0.0078125
img_norm_cfg = dict(
    mean=[
        103.53,
        116.28,
        123.675,
    ], std=[
        1.0,
        1.0,
        1.0,
    ], to_rgb=False)
keys = ('img', )
launcher = 'pytorch'
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=50)
lss_bev_size = [
    -2,
    2,
    0,
    4,
    0.05,
]
lss_depth = 120
lss_grid_size = (
    80,
    80,
)
lss_num_points = 8
lss_z_range = (
    -0.05,
    0.15,
)
meta_keys = (
    'lidar2img',
    'img_shape',
    'pc_range',
    'occ_size',
    'gt_occ',
    'occ_path',
    'filename',
)
model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=64, num_channels=[
                128,
                256,
                512,
            ]),
        fpn_lss_params=dict(in_channels=640, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(type='EfficientNetB0'),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    is_vis=False,
    lambda_dice=1,
    type='EcoOccVision',
    use_grid_mask=True,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depth=120,
        feat_channels=64,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=8,
        num_views=2,
        type='LSSTransformer',
        z_range=(
            -0.05,
            0.15,
        )))
numC_Trans = 64
occ_size = [
    80,
    80,
    4,
]
optim_wrapper = dict(
    clip_grad=dict(max_norm=35, norm_type=2),
    optimizer=dict(lr=0.0001, type='AdamW', weight_decay=0.01),
    type='AmpOptimWrapper')
param_scheduler = [
    dict(
        begin=0,
        by_epoch=False,
        end=500,
        start_factor=0.3333333333333333,
        type='LinearLR'),
    dict(by_epoch=True, type='CosineAnnealingLR'),
]
point_cloud_range = [
    -2,
    0,
    -0.05,
    2,
    4,
    0.15,
]
resume = None
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0709_val.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor/zed_lidar',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
test_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
train_cfg = dict(max_epochs=24, type='EpochBasedTrainLoop', val_interval=1)
train_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0709_train.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor/zed_lidar',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(type='PhotoMetricDistortionMultiViewImage'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        test_mode=False,
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    prefetch_factor=8,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(type='PhotoMetricDistortionMultiViewImage'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
use_semantic = True
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0709_val.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor/zed_lidar',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
val_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
view_shape = [
    24.0,
    32.0,
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='mmdet3d.Det3DLocalVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = 'work_dirs/zed_lidar_0704/zed_lidar_0709'

2024/07/09 10:57:55 - mmengine - INFO - Autoplay mode, press [SPACE] to pause.
2024/07/09 10:57:55 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2024/07/09 10:57:57 - mmengine - WARNING - The prefix is not set in metric class BEVMetric.
Name of parameter - Initialization information

img_backbone.stem.stem.conv.weight - torch.Size([32, 3, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stem.stem.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stem.stem.norm.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stem.stem.norm.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.depth.conv.weight - torch.Size([32, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.0.conv.depth.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.depth.norm.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.depth.norm.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.project.conv.weight - torch.Size([16, 32, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.0.conv.project.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.project.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.project.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.expand_conv.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.expand_conv.conv.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.expand_conv.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.expand_conv.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.depth.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.depth.conv.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.depth.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.depth.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.project.conv.weight - torch.Size([24, 96, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.project.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.project.norm.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.project.norm.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.expand_conv.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.depth.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.depth.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.depth.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.depth.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.project.conv.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.project.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.project.norm.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.project.norm.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.expand_conv.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.expand_conv.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.expand_conv.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.expand_conv.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.depth.conv.weight - torch.Size([144, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.depth.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.depth.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.depth.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.project.conv.weight - torch.Size([40, 144, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.project.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.project.norm.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.project.norm.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.conv.weight - torch.Size([240, 40, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.expand_conv.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.depth.conv.weight - torch.Size([240, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.depth.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.depth.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.depth.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.project.conv.weight - torch.Size([40, 240, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.project.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.project.norm.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.project.norm.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.expand_conv.conv.weight - torch.Size([240, 40, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.expand_conv.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.expand_conv.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.expand_conv.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.depth.conv.weight - torch.Size([240, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.depth.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.depth.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.depth.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.project.conv.weight - torch.Size([80, 240, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.depth.conv.weight - torch.Size([480, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.project.conv.weight - torch.Size([80, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.depth.conv.weight - torch.Size([480, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.project.conv.weight - torch.Size([80, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.depth.conv.weight - torch.Size([480, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.project.conv.weight - torch.Size([112, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.project.conv.weight - torch.Size([112, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.project.conv.weight - torch.Size([112, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.project.conv.weight - torch.Size([192, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.depth.conv.weight - torch.Size([1152, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.project.conv.weight - torch.Size([320, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.project.conv.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.project.norm.weight - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.project.norm.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_4.conv.conv.weight - torch.Size([112, 320, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_4.conv.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_3.conv.conv.weight - torch.Size([40, 224, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_3.conv.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_2.conv.conv.weight - torch.Size([24, 80, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_2.conv.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_1.conv.conv.weight - torch.Size([16, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_1.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_0.conv.conv.weight - torch.Size([16, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_0.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_4.conv.conv.weight - torch.Size([224, 224, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_4.conv.conv.bias - torch.Size([224]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_3.conv.conv.weight - torch.Size([80, 80, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_3.conv.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_2.conv.conv.weight - torch.Size([48, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_2.conv.conv.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_1.conv.conv.weight - torch.Size([32, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_1.conv.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_0.conv.conv.weight - torch.Size([16, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_0.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_dispconv2.conv.weight - torch.Size([2, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv2.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_dispconv1.conv.weight - torch.Size([2, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv1.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_dispconv0.conv.weight - torch.Size([2, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv0.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_conf_mask_conv21.conv.weight - torch.Size([2, 66, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv21.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_conf_mask_conv11.conv.weight - torch.Size([2, 44, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv11.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_conf_mask_conv01.conv.weight - torch.Size([2, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv01.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.0.weight - torch.Size([64, 384, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.0.weight - torch.Size([64, 112, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.0.weight - torch.Size([64, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.0.weight - torch.Size([64, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

view_transformer.depth_net.0.weight - torch.Size([120, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

view_transformer.feat_net.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.conv1.weight - torch.Size([128, 64, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.conv2.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn2.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn2.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.downsample.weight - torch.Size([128, 64, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.downsample.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.conv1.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.conv2.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn2.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn2.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.conv1.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.downsample.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.downsample.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.conv1.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.conv1.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.downsample.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.downsample.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.conv1.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.0.weight - torch.Size([512, 640, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.3.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.4.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.4.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.1.weight - torch.Size([256, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.4.weight - torch.Size([256, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.4.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.final_conv.conv.weight - torch.Size([256, 256, 3, 3]): 
Initialized by user-defined `init_weights` in ConvModule  

bev_decoder.final_conv.conv.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.0.weight - torch.Size([512, 256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.0.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.2.weight - torch.Size([4, 512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.2.bias - torch.Size([4]): 
The value is the same before and after calling `init_weights` of EcoOccVision  
2024/07/09 10:57:58 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2024/07/09 10:57:58 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2024/07/09 10:57:58 - mmengine - INFO - Checkpoints will be saved to /home/<USER>/ecoaitoolkit/work_dirs/zed_lidar_0704/zed_lidar_0709.
2024/07/09 10:59:26 - mmengine - INFO - Epoch(train)  [1][100/217]  lr: 4.6560e-05  eta: 1:15:07  time: 0.6615  data_time: 0.0009  memory: 2924  grad_norm: 5.7242  loss: 1.0085  loss_bce: 0.0475  loss_dice: 0.9609
2024/07/09 11:00:34 - mmengine - INFO - Epoch(train)  [1][200/217]  lr: 5.9920e-05  eta: 1:05:09  time: 0.6795  data_time: 0.0007  memory: 2924  grad_norm: inf  loss: 0.8761  loss_bce: 0.0422  loss_dice: 0.8339
2024/07/09 11:00:45 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:00:45 - mmengine - INFO - Saving checkpoint at 1 epochs
2024/07/09 11:00:53 - mmengine - INFO - Epoch(val) [1][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9586336030588681, 'occupancy': 0.08294115412675564, 'mIoU': 0.5207873785928119}, '1~2m': {'no_occupancy': 0.9912398488693808, 'occupancy': 0.00034928396786587494, 'mIoU': 0.4957945664186233}, '2~3m': {'no_occupancy': 0.9978253859816272, 'occupancy': 0.0010275269049808016, 'mIoU': 0.49942645644330397}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9868023862409838, 'occupancy': 0.021079491249900578, 'mIoU': 0.5039409387454422}}  total_overall: {'no_occupancy': 0.9868286498335946, 'occupancy': 0.06621802880937971, 'mIoU': 0.5265233393214872}  data_time: 0.1483  time: 0.2973
2024/07/09 11:02:19 - mmengine - INFO - Epoch(train)  [2][100/217]  lr: 7.5228e-05  eta: 1:04:58  time: 0.6959  data_time: 0.0009  memory: 2924  grad_norm: 12.2321  loss: 0.8259  loss_bce: 0.0392  loss_dice: 0.7868
2024/07/09 11:03:30 - mmengine - INFO - Epoch(train)  [2][200/217]  lr: 8.8531e-05  eta: 1:01:53  time: 0.7074  data_time: 0.0007  memory: 2924  grad_norm: 9.8819  loss: 0.8296  loss_bce: 0.0396  loss_dice: 0.7900
2024/07/09 11:03:41 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:03:41 - mmengine - INFO - Saving checkpoint at 2 epochs
2024/07/09 11:03:47 - mmengine - INFO - Epoch(val) [2][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9656695321607675, 'occupancy': 0.10355988553184564, 'mIoU': 0.5346147088463066}, '1~2m': {'no_occupancy': 0.9910048497435385, 'occupancy': 0.003124765561038935, 'mIoU': 0.4970648076522887}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9885636307345999, 'occupancy': 0.026671162773221145, 'mIoU': 0.5076173967539105}}  total_overall: {'no_occupancy': 0.9885862457081643, 'occupancy': 0.0802309949937236, 'mIoU': 0.5344086203509439}  data_time: 0.0288  time: 0.1428
2024/07/09 11:05:14 - mmengine - INFO - Epoch(train)  [3][100/217]  lr: 9.8296e-05  eta: 1:01:27  time: 0.6967  data_time: 0.0009  memory: 2924  grad_norm: 9.0826  loss: 0.8116  loss_bce: 0.0401  loss_dice: 0.7715
2024/07/09 11:06:25 - mmengine - INFO - Epoch(train)  [3][200/217]  lr: 9.8296e-05  eta: 0:59:12  time: 0.7044  data_time: 0.0007  memory: 2924  grad_norm: 8.3111  loss: 0.7924  loss_bce: 0.0387  loss_dice: 0.7537
2024/07/09 11:06:36 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:06:36 - mmengine - INFO - Saving checkpoint at 3 epochs
2024/07/09 11:06:42 - mmengine - INFO - Epoch(val) [3][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9754464575637128, 'occupancy': 0.10884452863179367, 'mIoU': 0.5421454930977533}, '1~2m': {'no_occupancy': 0.9901544369950841, 'occupancy': 0.010694775945035475, 'mIoU': 0.5004246064700598}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9907952588982226, 'occupancy': 0.029884826144207285, 'mIoU': 0.5103400425212149}}  total_overall: {'no_occupancy': 0.9908067579625383, 'occupancy': 0.0777189594724968, 'mIoU': 0.5342628587175176}  data_time: 0.0367  time: 0.1533
2024/07/09 11:08:09 - mmengine - INFO - Epoch(train)  [4][100/217]  lr: 9.6194e-05  eta: 0:58:24  time: 0.7054  data_time: 0.0009  memory: 2924  grad_norm: 7.6498  loss: 0.7920  loss_bce: 0.0391  loss_dice: 0.7529
2024/07/09 11:09:20 - mmengine - INFO - Epoch(train)  [4][200/217]  lr: 9.6194e-05  eta: 0:56:27  time: 0.7091  data_time: 0.0007  memory: 2924  grad_norm: 7.8800  loss: 0.7667  loss_bce: 0.0366  loss_dice: 0.7302
2024/07/09 11:09:32 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:09:32 - mmengine - INFO - Saving checkpoint at 4 epochs
2024/07/09 11:09:38 - mmengine - INFO - Epoch(val) [4][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9794332719624459, 'occupancy': 0.1841509858864181, 'mIoU': 0.581792128924432}, '1~2m': {'no_occupancy': 0.9885118739537312, 'occupancy': 0.029014718873253454, 'mIoU': 0.5087632964134923}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9913813217375677, 'occupancy': 0.05329142618991789, 'mIoU': 0.5223363739637428}}  total_overall: {'no_occupancy': 0.9913953877825757, 'occupancy': 0.12618232337250282, 'mIoU': 0.5587888555775393}  data_time: 0.0368  time: 0.1510
2024/07/09 11:11:05 - mmengine - INFO - Epoch(train)  [5][100/217]  lr: 9.3301e-05  eta: 0:55:30  time: 0.7095  data_time: 0.0009  memory: 2924  grad_norm: 7.3330  loss: 0.7810  loss_bce: 0.0377  loss_dice: 0.7433
2024/07/09 11:11:28 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:12:17 - mmengine - INFO - Epoch(train)  [5][200/217]  lr: 9.3301e-05  eta: 0:53:45  time: 0.7125  data_time: 0.0007  memory: 2924  grad_norm: 6.8678  loss: 0.7783  loss_bce: 0.0391  loss_dice: 0.7392
2024/07/09 11:12:28 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:12:28 - mmengine - INFO - Saving checkpoint at 5 epochs
2024/07/09 11:12:34 - mmengine - INFO - Epoch(val) [5][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9780347432021496, 'occupancy': 0.12051963647667463, 'mIoU': 0.5492771898394121}, '1~2m': {'no_occupancy': 0.989596617682933, 'occupancy': 0.007815022021856235, 'mIoU': 0.49870581985239465}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.991302875479794, 'occupancy': 0.03208366462463272, 'mIoU': 0.5116932700522133}}  total_overall: {'no_occupancy': 0.9913128724762188, 'occupancy': 0.08154854259714395, 'mIoU': 0.5364307075366814}  data_time: 0.0355  time: 0.1509
2024/07/09 11:14:00 - mmengine - INFO - Epoch(train)  [6][100/217]  lr: 8.9668e-05  eta: 0:52:37  time: 0.7128  data_time: 0.0009  memory: 2924  grad_norm: 6.5686  loss: 0.7423  loss_bce: 0.0353  loss_dice: 0.7071
2024/07/09 11:15:11 - mmengine - INFO - Epoch(train)  [6][200/217]  lr: 8.9668e-05  eta: 0:50:55  time: 0.7036  data_time: 0.0007  memory: 2924  grad_norm: 6.0232  loss: 0.7604  loss_bce: 0.0362  loss_dice: 0.7242
2024/07/09 11:15:23 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:15:23 - mmengine - INFO - Saving checkpoint at 6 epochs
2024/07/09 11:15:29 - mmengine - INFO - Epoch(val) [6][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9790626771215344, 'occupancy': 0.1338826480945186, 'mIoU': 0.5564726626080265}, '1~2m': {'no_occupancy': 0.9894116625354, 'occupancy': 0.024412009623756684, 'mIoU': 0.5069118360795783}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9915136201727569, 'occupancy': 0.03957366442956882, 'mIoU': 0.5155436423011629}}  total_overall: {'no_occupancy': 0.9915238099670655, 'occupancy': 0.0934240384244283, 'mIoU': 0.5424739241957469}  data_time: 0.0294  time: 0.1445
2024/07/09 11:16:56 - mmengine - INFO - Epoch(train)  [7][100/217]  lr: 8.5355e-05  eta: 0:49:44  time: 0.7132  data_time: 0.0009  memory: 2924  grad_norm: 6.6542  loss: 0.7291  loss_bce: 0.0351  loss_dice: 0.6939
2024/07/09 11:18:07 - mmengine - INFO - Epoch(train)  [7][200/217]  lr: 8.5355e-05  eta: 0:48:08  time: 0.7086  data_time: 0.0008  memory: 2924  grad_norm: 6.6695  loss: 0.7700  loss_bce: 0.0388  loss_dice: 0.7312
2024/07/09 11:18:18 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:18:18 - mmengine - INFO - Saving checkpoint at 7 epochs
2024/07/09 11:18:24 - mmengine - INFO - Epoch(val) [7][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9725339701058637, 'occupancy': 0.12253548644676819, 'mIoU': 0.547534728276316}, '1~2m': {'no_occupancy': 0.9899331028076332, 'occupancy': 0.008925001014204662, 'mIoU': 0.49942905191091896}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9900118034868975, 'occupancy': 0.032865121865243214, 'mIoU': 0.5114384626760704}}  total_overall: {'no_occupancy': 0.9900285168760851, 'occupancy': 0.08938948404905005, 'mIoU': 0.5397090004625675}  data_time: 0.0350  time: 0.1498
2024/07/09 11:19:50 - mmengine - INFO - Epoch(train)  [8][100/217]  lr: 8.0438e-05  eta: 0:46:52  time: 0.7069  data_time: 0.0009  memory: 2924  grad_norm: 6.5371  loss: 0.7278  loss_bce: 0.0362  loss_dice: 0.6916
2024/07/09 11:21:02 - mmengine - INFO - Epoch(train)  [8][200/217]  lr: 8.0438e-05  eta: 0:45:20  time: 0.7172  data_time: 0.0007  memory: 2924  grad_norm: 6.6756  loss: 0.7443  loss_bce: 0.0370  loss_dice: 0.7073
2024/07/09 11:21:14 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:21:14 - mmengine - INFO - Saving checkpoint at 8 epochs
2024/07/09 11:21:19 - mmengine - INFO - Epoch(val) [8][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.973191924478475, 'occupancy': 0.11016265541457773, 'mIoU': 0.5416772899465264}, '1~2m': {'no_occupancy': 0.9886049029066873, 'occupancy': 0.008774445292216937, 'mIoU': 0.49868967409945214}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9898442421048138, 'occupancy': 0.029734275176698667, 'mIoU': 0.5097892586407562}}  total_overall: {'no_occupancy': 0.9898580561157747, 'occupancy': 0.07757383918201899, 'mIoU': 0.5337159476488968}  data_time: 0.0299  time: 0.1453
2024/07/09 11:22:46 - mmengine - INFO - Epoch(train)  [9][100/217]  lr: 7.5000e-05  eta: 0:44:01  time: 0.7139  data_time: 0.0009  memory: 2924  grad_norm: 6.1661  loss: 0.7368  loss_bce: 0.0374  loss_dice: 0.6994
2024/07/09 11:23:57 - mmengine - INFO - Epoch(train)  [9][200/217]  lr: 7.5000e-05  eta: 0:42:31  time: 0.7133  data_time: 0.0007  memory: 2924  grad_norm: 5.8767  loss: 0.7139  loss_bce: 0.0355  loss_dice: 0.6783
2024/07/09 11:24:09 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:24:09 - mmengine - INFO - Saving checkpoint at 9 epochs
2024/07/09 11:24:15 - mmengine - INFO - Epoch(val) [9][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9811266276882682, 'occupancy': 0.1334763506462883, 'mIoU': 0.5573014891672783}, '1~2m': {'no_occupancy': 0.990560999226102, 'occupancy': 0.004797577006386425, 'mIoU': 0.4976792881162442}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9923169419871158, 'occupancy': 0.03456848191316868, 'mIoU': 0.5134427119501422}}  total_overall: {'no_occupancy': 0.9923250774659234, 'occupancy': 0.08757122235907186, 'mIoU': 0.5399481499124976}  data_time: 0.0300  time: 0.1449
2024/07/09 11:25:05 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:25:43 - mmengine - INFO - Epoch(train) [10][100/217]  lr: 6.9134e-05  eta: 0:41:13  time: 0.7180  data_time: 0.0009  memory: 2924  grad_norm: 6.2664  loss: 0.7135  loss_bce: 0.0356  loss_dice: 0.6778
2024/07/09 11:26:54 - mmengine - INFO - Epoch(train) [10][200/217]  lr: 6.9134e-05  eta: 0:39:45  time: 0.7152  data_time: 0.0008  memory: 2924  grad_norm: 6.5455  loss: 0.6985  loss_bce: 0.0348  loss_dice: 0.6637
2024/07/09 11:27:06 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:27:06 - mmengine - INFO - Saving checkpoint at 10 epochs
2024/07/09 11:27:12 - mmengine - INFO - Epoch(val) [10][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9754541750328908, 'occupancy': 0.13457454066683122, 'mIoU': 0.555014357849861}, '1~2m': {'no_occupancy': 0.989081283594519, 'occupancy': 0.01908084035035755, 'mIoU': 0.5040810619724383}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9905288999153757, 'occupancy': 0.038413845254297196, 'mIoU': 0.5144713725848364}}  total_overall: {'no_occupancy': 0.9905433212248042, 'occupancy': 0.09603824886544604, 'mIoU': 0.5432907850451251}  data_time: 0.0276  time: 0.1425
2024/07/09 11:28:38 - mmengine - INFO - Epoch(train) [11][100/217]  lr: 6.2941e-05  eta: 0:38:22  time: 0.7102  data_time: 0.0009  memory: 2924  grad_norm: 7.0423  loss: 0.6848  loss_bce: 0.0339  loss_dice: 0.6509
2024/07/09 11:29:50 - mmengine - INFO - Epoch(train) [11][200/217]  lr: 6.2941e-05  eta: 0:36:55  time: 0.7148  data_time: 0.0007  memory: 2924  grad_norm: 6.4760  loss: 0.6854  loss_bce: 0.0341  loss_dice: 0.6513
2024/07/09 11:30:02 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:30:02 - mmengine - INFO - Saving checkpoint at 11 epochs
2024/07/09 11:30:07 - mmengine - INFO - Epoch(val) [11][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9791281094761335, 'occupancy': 0.13996488192368206, 'mIoU': 0.5595464956999078}, '1~2m': {'no_occupancy': 0.9895809056079777, 'occupancy': 0.02222516900245043, 'mIoU': 0.5059030373052141}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9915722890295512, 'occupancy': 0.04054751273153312, 'mIoU': 0.5160599008805422}}  total_overall: {'no_occupancy': 0.9915829482532291, 'occupancy': 0.09721034094077631, 'mIoU': 0.5443966445970028}  data_time: 0.0360  time: 0.1510
2024/07/09 11:31:35 - mmengine - INFO - Epoch(train) [12][100/217]  lr: 5.6526e-05  eta: 0:35:33  time: 0.7117  data_time: 0.0009  memory: 2924  grad_norm: 6.4417  loss: 0.6699  loss_bce: 0.0334  loss_dice: 0.6364
2024/07/09 11:32:46 - mmengine - INFO - Epoch(train) [12][200/217]  lr: 5.6526e-05  eta: 0:34:07  time: 0.7126  data_time: 0.0008  memory: 2924  grad_norm: 6.5077  loss: 0.6915  loss_bce: 0.0361  loss_dice: 0.6554
2024/07/09 11:32:58 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:32:58 - mmengine - INFO - Saving checkpoint at 12 epochs
2024/07/09 11:33:03 - mmengine - INFO - Epoch(val) [12][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.97875948301167, 'occupancy': 0.1340781517652823, 'mIoU': 0.5564188173884762}, '1~2m': {'no_occupancy': 0.9903843881272475, 'occupancy': 0.006770622733118286, 'mIoU': 0.49857750543018287}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9916810030432527, 'occupancy': 0.035212193624600145, 'mIoU': 0.5134465983339264}}  total_overall: {'no_occupancy': 0.9916916225826604, 'occupancy': 0.0914822053067216, 'mIoU': 0.541586913944691}  data_time: 0.0301  time: 0.1456
2024/07/09 11:34:31 - mmengine - INFO - Epoch(train) [13][100/217]  lr: 5.0000e-05  eta: 0:32:43  time: 0.7149  data_time: 0.0009  memory: 2924  grad_norm: 6.8227  loss: 0.6638  loss_bce: 0.0331  loss_dice: 0.6306
2024/07/09 11:35:42 - mmengine - INFO - Epoch(train) [13][200/217]  lr: 5.0000e-05  eta: 0:31:18  time: 0.7083  data_time: 0.0008  memory: 2924  grad_norm: 6.3569  loss: 0.6868  loss_bce: 0.0357  loss_dice: 0.6511
2024/07/09 11:35:54 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:35:54 - mmengine - INFO - Saving checkpoint at 13 epochs
2024/07/09 11:36:00 - mmengine - INFO - Epoch(val) [13][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9782112887967329, 'occupancy': 0.13793812601950914, 'mIoU': 0.558074707408121}, '1~2m': {'no_occupancy': 0.9879371660992459, 'occupancy': 0.034981556421333306, 'mIoU': 0.5114593612602896}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9909321489825179, 'occupancy': 0.04322992061021061, 'mIoU': 0.5170810347963642}}  total_overall: {'no_occupancy': 0.9909435363668978, 'occupancy': 0.0975360405353734, 'mIoU': 0.5442397884511356}  data_time: 0.0330  time: 0.1491
2024/07/09 11:37:27 - mmengine - INFO - Epoch(train) [14][100/217]  lr: 4.3474e-05  eta: 0:29:53  time: 0.7183  data_time: 0.0009  memory: 2924  grad_norm: 6.3610  loss: 0.6650  loss_bce: 0.0340  loss_dice: 0.6309
2024/07/09 11:38:24 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:38:39 - mmengine - INFO - Epoch(train) [14][200/217]  lr: 4.3474e-05  eta: 0:28:29  time: 0.7128  data_time: 0.0008  memory: 2924  grad_norm: 6.3983  loss: 0.6614  loss_bce: 0.0331  loss_dice: 0.6283
2024/07/09 11:38:50 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:38:50 - mmengine - INFO - Saving checkpoint at 14 epochs
2024/07/09 11:38:56 - mmengine - INFO - Epoch(val) [14][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9806328215779222, 'occupancy': 0.13115583345913148, 'mIoU': 0.5558943275185269}, '1~2m': {'no_occupancy': 0.989674576686587, 'occupancy': 0.03852870941298072, 'mIoU': 0.5141016430497839}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9919718848246506, 'occupancy': 0.04242113571802805, 'mIoU': 0.5171965102713393}}  total_overall: {'no_occupancy': 0.9919803928945544, 'occupancy': 0.09408138819105928, 'mIoU': 0.5430308905428068}  data_time: 0.0280  time: 0.1426
2024/07/09 11:40:25 - mmengine - INFO - Epoch(train) [15][100/217]  lr: 3.7059e-05  eta: 0:27:04  time: 0.7191  data_time: 0.0010  memory: 2924  grad_norm: 5.8295  loss: 0.6282  loss_bce: 0.0314  loss_dice: 0.5968
2024/07/09 11:41:36 - mmengine - INFO - Epoch(train) [15][200/217]  lr: 3.7059e-05  eta: 0:25:41  time: 0.7141  data_time: 0.0008  memory: 2924  grad_norm: 6.4813  loss: 0.6469  loss_bce: 0.0329  loss_dice: 0.6140
2024/07/09 11:41:48 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:41:48 - mmengine - INFO - Saving checkpoint at 15 epochs
2024/07/09 11:41:53 - mmengine - INFO - Epoch(val) [15][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9795556999788326, 'occupancy': 0.14157150119654308, 'mIoU': 0.5605636005876878}, '1~2m': {'no_occupancy': 0.9891499901005923, 'occupancy': 0.04154441600599289, 'mIoU': 0.5153472030532926}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9915714577783795, 'occupancy': 0.04577897930063399, 'mIoU': 0.5186752185395067}}  total_overall: {'no_occupancy': 0.9915818465265483, 'occupancy': 0.10222382452576922, 'mIoU': 0.5469028355261588}  data_time: 0.0384  time: 0.1544
2024/07/09 11:43:20 - mmengine - INFO - Epoch(train) [16][100/217]  lr: 3.0866e-05  eta: 0:24:13  time: 0.7140  data_time: 0.0009  memory: 2924  grad_norm: 6.0180  loss: 0.6297  loss_bce: 0.0324  loss_dice: 0.5973
2024/07/09 11:44:32 - mmengine - INFO - Epoch(train) [16][200/217]  lr: 3.0866e-05  eta: 0:22:51  time: 0.7192  data_time: 0.0008  memory: 2924  grad_norm: 6.2275  loss: 0.6303  loss_bce: 0.0323  loss_dice: 0.5980
2024/07/09 11:44:44 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:44:44 - mmengine - INFO - Saving checkpoint at 16 epochs
2024/07/09 11:44:50 - mmengine - INFO - Epoch(val) [16][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9805615784102476, 'occupancy': 0.13307705472689213, 'mIoU': 0.5568193165685699}, '1~2m': {'no_occupancy': 0.9891926536344703, 'occupancy': 0.04079205954297423, 'mIoU': 0.5149923565887222}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9918335932697028, 'occupancy': 0.043467278567466586, 'mIoU': 0.5176504359185847}}  total_overall: {'no_occupancy': 0.9918422876547586, 'occupancy': 0.09530168017058963, 'mIoU': 0.5435719839126741}  data_time: 0.0355  time: 0.1507
2024/07/09 11:46:16 - mmengine - INFO - Epoch(train) [17][100/217]  lr: 2.5000e-05  eta: 0:21:23  time: 0.7049  data_time: 0.0009  memory: 2924  grad_norm: 6.6649  loss: 0.6281  loss_bce: 0.0325  loss_dice: 0.5956
2024/07/09 11:47:28 - mmengine - INFO - Epoch(train) [17][200/217]  lr: 2.5000e-05  eta: 0:20:01  time: 0.7154  data_time: 0.0008  memory: 2924  grad_norm: 7.2856  loss: 0.6261  loss_bce: 0.0325  loss_dice: 0.5935
2024/07/09 11:47:39 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:47:39 - mmengine - INFO - Saving checkpoint at 17 epochs
2024/07/09 11:47:45 - mmengine - INFO - Epoch(val) [17][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9803313763698469, 'occupancy': 0.1208286615045707, 'mIoU': 0.5505800189372088}, '1~2m': {'no_occupancy': 0.9893973881544611, 'occupancy': 0.040054169765699775, 'mIoU': 0.5147257789600804}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9918272263896003, 'occupancy': 0.04022070781756762, 'mIoU': 0.516023967103584}}  total_overall: {'no_occupancy': 0.9918352491170791, 'occupancy': 0.08772547612857362, 'mIoU': 0.5397803626228264}  data_time: 0.0291  time: 0.1451
2024/07/09 11:49:13 - mmengine - INFO - Epoch(train) [18][100/217]  lr: 1.9562e-05  eta: 0:18:33  time: 0.7151  data_time: 0.0010  memory: 2924  grad_norm: 7.4212  loss: 0.6083  loss_bce: 0.0319  loss_dice: 0.5764
2024/07/09 11:50:24 - mmengine - INFO - Epoch(train) [18][200/217]  lr: 1.9562e-05  eta: 0:17:12  time: 0.7116  data_time: 0.0008  memory: 2924  grad_norm: 6.2964  loss: 0.5995  loss_bce: 0.0313  loss_dice: 0.5681
2024/07/09 11:50:35 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:50:35 - mmengine - INFO - Saving checkpoint at 18 epochs
2024/07/09 11:50:41 - mmengine - INFO - Epoch(val) [18][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9804033238771862, 'occupancy': 0.13069689121206327, 'mIoU': 0.5555501075446247}, '1~2m': {'no_occupancy': 0.9891932799135283, 'occupancy': 0.04373983824571643, 'mIoU': 0.5164665590796224}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.991794186206202, 'occupancy': 0.04360918236444492, 'mIoU': 0.5177016842853235}}  total_overall: {'no_occupancy': 0.9918028804835193, 'occupancy': 0.09481922292816489, 'mIoU': 0.543311051705842}  data_time: 0.0305  time: 0.1455
2024/07/09 11:52:04 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:52:08 - mmengine - INFO - Epoch(train) [19][100/217]  lr: 1.4645e-05  eta: 0:15:42  time: 0.7088  data_time: 0.0010  memory: 2924  grad_norm: 6.3837  loss: 0.6020  loss_bce: 0.0311  loss_dice: 0.5709
2024/07/09 11:53:19 - mmengine - INFO - Epoch(train) [19][200/217]  lr: 1.4645e-05  eta: 0:14:22  time: 0.7146  data_time: 0.0008  memory: 2924  grad_norm: 7.2014  loss: 0.5616  loss_bce: 0.0282  loss_dice: 0.5334
2024/07/09 11:53:31 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:53:31 - mmengine - INFO - Saving checkpoint at 19 epochs
2024/07/09 11:53:37 - mmengine - INFO - Epoch(val) [19][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9815047201119015, 'occupancy': 0.12368578522934447, 'mIoU': 0.5525952526706229}, '1~2m': {'no_occupancy': 0.9892949593377771, 'occupancy': 0.04457929164049585, 'mIoU': 0.5169371254891365}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9920949551209429, 'occupancy': 0.04206626921746008, 'mIoU': 0.5170806121692015}}  total_overall: {'no_occupancy': 0.9921022036233285, 'occupancy': 0.0894999108651177, 'mIoU': 0.5408010572442231}  data_time: 0.0313  time: 0.1476
2024/07/09 11:55:04 - mmengine - INFO - Epoch(train) [20][100/217]  lr: 1.0332e-05  eta: 0:12:52  time: 0.7178  data_time: 0.0009  memory: 2924  grad_norm: 7.0876  loss: 0.5990  loss_bce: 0.0311  loss_dice: 0.5679
2024/07/09 11:56:16 - mmengine - INFO - Epoch(train) [20][200/217]  lr: 1.0332e-05  eta: 0:11:32  time: 0.7166  data_time: 0.0008  memory: 2924  grad_norm: 7.0711  loss: 0.5733  loss_bce: 0.0295  loss_dice: 0.5438
2024/07/09 11:56:28 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:56:28 - mmengine - INFO - Saving checkpoint at 20 epochs
2024/07/09 11:56:34 - mmengine - INFO - Epoch(val) [20][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9805519376540314, 'occupancy': 0.11535245539698795, 'mIoU': 0.5479521965255096}, '1~2m': {'no_occupancy': 0.9896040388708818, 'occupancy': 0.03618417460993372, 'mIoU': 0.5128941067404077}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9919340293897515, 'occupancy': 0.037884157501730416, 'mIoU': 0.514909093445741}}  total_overall: {'no_occupancy': 0.9919414597903776, 'occupancy': 0.08310867077398415, 'mIoU': 0.5375250652821809}  data_time: 0.0289  time: 0.1452
2024/07/09 11:58:01 - mmengine - INFO - Epoch(train) [21][100/217]  lr: 6.6987e-06  eta: 0:10:02  time: 0.7108  data_time: 0.0010  memory: 2924  grad_norm: 6.7068  loss: 0.5839  loss_bce: 0.0303  loss_dice: 0.5535
2024/07/09 11:59:11 - mmengine - INFO - Epoch(train) [21][200/217]  lr: 6.6987e-06  eta: 0:08:42  time: 0.7083  data_time: 0.0008  memory: 2924  grad_norm: 7.1218  loss: 0.5613  loss_bce: 0.0286  loss_dice: 0.5327
2024/07/09 11:59:23 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 11:59:23 - mmengine - INFO - Saving checkpoint at 21 epochs
2024/07/09 11:59:29 - mmengine - INFO - Epoch(val) [21][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9811130749875561, 'occupancy': 0.12528974731847056, 'mIoU': 0.5532014111530134}, '1~2m': {'no_occupancy': 0.9891130471242667, 'occupancy': 0.04018441218615663, 'mIoU': 0.5146487296552117}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9919515657864789, 'occupancy': 0.041368539876156796, 'mIoU': 0.5166600528313179}}  total_overall: {'no_occupancy': 0.9919592056299039, 'occupancy': 0.08935825278100468, 'mIoU': 0.5406587292054543}  data_time: 0.0326  time: 0.1487
2024/07/09 12:00:57 - mmengine - INFO - Epoch(train) [22][100/217]  lr: 3.8060e-06  eta: 0:07:12  time: 0.7120  data_time: 0.0010  memory: 2924  grad_norm: 6.6460  loss: 0.5567  loss_bce: 0.0283  loss_dice: 0.5284
2024/07/09 12:02:08 - mmengine - INFO - Epoch(train) [22][200/217]  lr: 3.8060e-06  eta: 0:05:53  time: 0.7110  data_time: 0.0008  memory: 2924  grad_norm: 6.6885  loss: 0.5612  loss_bce: 0.0284  loss_dice: 0.5328
2024/07/09 12:02:20 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 12:02:20 - mmengine - INFO - Saving checkpoint at 22 epochs
2024/07/09 12:02:26 - mmengine - INFO - Epoch(val) [22][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9818908292569554, 'occupancy': 0.1290191636789949, 'mIoU': 0.5554549964679751}, '1~2m': {'no_occupancy': 0.9889911242451969, 'occupancy': 0.04126962961443385, 'mIoU': 0.5151303769298153}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9921155236340614, 'occupancy': 0.042572198323357194, 'mIoU': 0.5173438609787093}}  total_overall: {'no_occupancy': 0.9921227379543449, 'occupancy': 0.09092461897336318, 'mIoU': 0.5415236784638541}  data_time: 0.0326  time: 0.1483
2024/07/09 12:03:57 - mmengine - INFO - Epoch(train) [23][100/217]  lr: 1.7037e-06  eta: 0:04:22  time: 0.7103  data_time: 0.0009  memory: 2924  grad_norm: 6.5753  loss: 0.5857  loss_bce: 0.0305  loss_dice: 0.5552
2024/07/09 12:05:08 - mmengine - INFO - Epoch(train) [23][200/217]  lr: 1.7037e-06  eta: 0:03:03  time: 0.7104  data_time: 0.0008  memory: 2924  grad_norm: 6.3796  loss: 0.5689  loss_bce: 0.0292  loss_dice: 0.5396
2024/07/09 12:05:20 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 12:05:20 - mmengine - INFO - Saving checkpoint at 23 epochs
2024/07/09 12:05:26 - mmengine - INFO - Epoch(val) [23][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9817627029418022, 'occupancy': 0.12611297992288145, 'mIoU': 0.5539378414323418}, '1~2m': {'no_occupancy': 0.9893892569447066, 'occupancy': 0.041266488238079775, 'mIoU': 0.5153278725913932}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9921830252301506, 'occupancy': 0.041844867040240305, 'mIoU': 0.5170139461351955}}  total_overall: {'no_occupancy': 0.9921901878359317, 'occupancy': 0.08984757808598166, 'mIoU': 0.5410188829609567}  data_time: 0.0282  time: 0.1433
2024/07/09 12:05:55 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 12:07:08 - mmengine - INFO - Epoch(train) [24][100/217]  lr: 4.2776e-07  eta: 0:01:32  time: 0.7168  data_time: 0.0011  memory: 2924  grad_norm: 6.2793  loss: 0.5581  loss_bce: 0.0291  loss_dice: 0.5290
2024/07/09 12:08:20 - mmengine - INFO - Epoch(train) [24][200/217]  lr: 4.2776e-07  eta: 0:00:13  time: 0.7180  data_time: 0.0008  memory: 2924  grad_norm: 7.2599  loss: 0.5908  loss_bce: 0.0311  loss_dice: 0.5597
2024/07/09 12:08:32 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240709_105749
2024/07/09 12:08:32 - mmengine - INFO - Saving checkpoint at 24 epochs
2024/07/09 12:08:38 - mmengine - INFO - Epoch(val) [24][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.98137576153307, 'occupancy': 0.1250485216156866, 'mIoU': 0.5532121415743784}, '1~2m': {'no_occupancy': 0.9892384523989941, 'occupancy': 0.04156242624498465, 'mIoU': 0.5154004393219894}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9920485887415393, 'occupancy': 0.041652736965167816, 'mIoU': 0.5168506628533536}}  total_overall: {'no_occupancy': 0.9920560055697774, 'occupancy': 0.08950280999156873, 'mIoU': 0.540779407780673}  data_time: 0.0346  time: 0.1506
