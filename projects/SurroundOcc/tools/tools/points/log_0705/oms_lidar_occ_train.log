2024/07/04 19:44:39 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: linux
    Python: 3.8.10 (default, May 26 2023, 14:05:08) [GCC 9.4.0]
    CUDA available: True
    numpy_random_seed: 1301846152
    GPU 0,1,2,3: NVIDIA TITAN Xp
    CUDA_HOME: /usr/local/cuda
    NVCC: Cuda compilation tools, release 11.6, V11.6.124
    GCC: x86_64-linux-gnu-gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0
    PyTorch: 1.13.0+cu116
    PyTorch compiling details: PyTorch built with:
  - GCC 9.3
  - C++ Version: 201402
  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v2.6.0 (Git Hash 52b5f107dd9cf10910aaa19cb47f3abf9b349815)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX2
  - CUDA Runtime 11.6
  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86
  - CuDNN 8.4
    - Built with CuDNN 8.3.2
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.6, CUDNN_VERSION=8.3.2, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -fabi-version=11 -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -DEDGE_PROFILER_USE_KINETO -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wunused-local-typedefs -Wno-unused-parameter -Wno-unused-function -Wno-unused-result -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Werror=cast-function-type -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.13.0, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, 

    TorchVision: 0.14.0+cu116
    OpenCV: 4.8.1
    MMEngine: 0.10.1

Runtime environment:
    cudnn_benchmark: False
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 1}
    dist_cfg: {'backend': 'nccl'}
    seed: 1301846152
    Distributed launcher: pytorch
    Distributed training: True
    GPU number: 4
------------------------------------------------------------

2024/07/04 19:44:40 - mmengine - INFO - Config:
auto_scale_lr = dict(base_batch_size=4, enable=False)
bn_kwargs = dict(eps=2e-05, momentum=0.1)
class_names = [
    'no_occupancy',
    'occupancy',
]
custom_imports = dict(
    allow_failed_imports=False,
    imports=[
        'projects.SurroundOcc.surroundocc.models',
        'projects.SurroundOcc.surroundocc.datasets',
    ])
data_root = '/home/<USER>/indoor'
data_shape = (
    3,
    384,
    512,
)
dataset_type = 'EcoAi.KujialeFusionDataset'
default_hooks = dict(
    checkpoint=dict(interval=1, type='CheckpointHook'),
    logger=dict(interval=100, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    timer=dict(type='IterTimerHook'),
    visualization=dict(type='mmdet3d.Det3DVisualizationHook'))
default_scope = 'mmdet'
depoly_model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=128, num_channels=[
                256,
                512,
                1024,
            ]),
        fpn_lss_params=dict(in_channels=1280, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(
        activation='relu',
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        include_top=False,
        model_type='b0',
        num_classes=1000,
        type='efficientnet',
        use_se_block=False),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    lidar_config=dict(
        max_num_points=10,
        max_voxels=[
            90000,
            120000,
        ],
        point_cloud_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        voxel_size=[
            0.05,
            0.05,
            0.2,
        ]),
    norm_range=[
        -2,
        0,
        -0.05,
        2,
        4,
        0.15,
    ],
    reader=dict(
        bn_kwargs=None,
        hw_reverse=True,
        num_filters=(64, ),
        num_input_features=3,
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pool_size=(
            10,
            1,
        ),
        quantize=False,
        type='PillarFeatureNet_ld',
        use_4dim=True,
        use_conv=True,
        voxel_size=[
            0.05,
            0.05,
            0.2,
        ],
        with_distance=False),
    scatter=dict(
        num_input_features=64,
        quantize=True,
        type='PointPillarScatter_dpx',
        use_horizon_pillar_scatter=True),
    type='EcoOccFusionDepoly',
    use_grid_mask=False,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depoly=True,
        depth=120,
        depth_grid_quant_scale=0.03125,
        feat_channels=64,
        grid_quant_scale=0.0078125,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=4,
        num_views=1,
        type='LSSTransformer_Fusion',
        z_range=(
            -0.05,
            0.15,
        )))
depth_quant_scale = 0.03125
depthview_shape = [
    720,
    768.0,
]
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=1))
featview_shape = [
    144.0,
    32.0,
]
grid_quant_scale = 0.0078125
img_norm_cfg = dict(
    mean=[
        103.53,
        116.28,
        123.675,
    ], std=[
        1.0,
        1.0,
        1.0,
    ], to_rgb=False)
keys = (
    'img',
    'points',
)
launcher = 'pytorch'
load_from = None
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=50)
lss_bev_size = [
    -2,
    2,
    0,
    4,
    0.05,
]
lss_depth = 120
lss_grid_size = (
    80,
    80,
)
lss_num_points = 4
lss_z_range = (
    -0.05,
    0.15,
)
meta_keys = (
    'lidar2img',
    'img_shape',
    'pc_range',
    'occ_size',
    'gt_occ',
    'occ_path',
    'filename',
)
model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=128, num_channels=[
                256,
                512,
                1024,
            ]),
        fpn_lss_params=dict(in_channels=1280, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(
        activation='relu',
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        include_top=False,
        model_type='b0',
        num_classes=1000,
        type='efficientnet',
        use_se_block=False),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    is_vis=True,
    lambda_dice=1,
    lidar_config=dict(
        max_num_points=10,
        max_voxels=[
            90000,
            120000,
        ],
        point_cloud_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        voxel_size=[
            0.05,
            0.05,
            0.2,
        ]),
    norm_range=[
        -2,
        0,
        -0.05,
        2,
        4,
        0.15,
    ],
    reader=dict(
        bn_kwargs=None,
        hw_reverse=True,
        num_filters=(64, ),
        num_input_features=3,
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pool_size=(
            10,
            1,
        ),
        quantize=False,
        type='PillarFeatureNet_ld',
        use_4dim=True,
        use_conv=True,
        voxel_size=[
            0.05,
            0.05,
            0.2,
        ],
        with_distance=False),
    scatter=dict(
        num_input_features=64,
        quantize=True,
        type='PointPillarScatter_dpx',
        use_horizon_pillar_scatter=True),
    type='EcoOccFusion',
    use_grid_mask=True,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depth=120,
        depth_grid_quant_scale=0.03125,
        feat_channels=64,
        grid_quant_scale=0.0078125,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=4,
        num_views=1,
        type='LSSTransformer_Fusion',
        z_range=(
            -0.05,
            0.15,
        )))
numC_Trans = 128
occ_size = [
    80,
    80,
    4,
]
optim_wrapper = dict(
    clip_grad=dict(max_norm=5, norm_type=2),
    optimizer=dict(lr=0.0001, type='AdamW', weight_decay=0.01),
    type='AmpOptimWrapper')
param_scheduler = [
    dict(
        begin=0,
        by_epoch=False,
        end=500,
        start_factor=0.3333333333333333,
        type='LinearLR'),
    dict(by_epoch=True, type='CosineAnnealingLR'),
]
point_cloud_range = [
    -2,
    0,
    -0.05,
    2,
    4,
    0.15,
]
point_cloud_range2 = [
    -2,
    0,
    -0.03,
    2,
    4,
    0.15,
]
resume = False
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_oms_dk_0704_train.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(
                coord_type='LIDAR',
                load_dim=3,
                reduce_beams=32,
                type='LoadPointsFromFileSingle',
                use_dim=3),
            dict(
                point_cloud_range=[
                    -2,
                    0,
                    -0.03,
                    2,
                    4,
                    0.15,
                ],
                type='PointsRangeFilter'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=(
                    'img',
                    'points',
                ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeFusionDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
test_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(
        coord_type='LIDAR',
        load_dim=3,
        reduce_beams=32,
        type='LoadPointsFromFileSingle',
        use_dim=3),
    dict(
        point_cloud_range=[
            -2,
            0,
            -0.03,
            2,
            4,
            0.15,
        ],
        type='PointsRangeFilter'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=(
            'img',
            'points',
        ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
train_cfg = dict(max_epochs=120, type='EpochBasedTrainLoop', val_interval=1)
train_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_oms_dk_0704_train.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(
                coord_type='LIDAR',
                load_dim=3,
                reduce_beams=32,
                type='LoadPointsFromFileSingle',
                use_dim=3),
            dict(
                point_cloud_range=[
                    -2,
                    0,
                    -0.03,
                    2,
                    4,
                    0.15,
                ],
                type='PointsRangeFilter'),
            dict(type='PhotoMetricDistortionMultiViewImage'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=(
                    'img',
                    'points',
                ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        test_mode=False,
        type='EcoAi.KujialeFusionDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    prefetch_factor=8,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(
        coord_type='LIDAR',
        load_dim=3,
        reduce_beams=32,
        type='LoadPointsFromFileSingle',
        use_dim=3),
    dict(
        point_cloud_range=[
            -2,
            0,
            -0.03,
            2,
            4,
            0.15,
        ],
        type='PointsRangeFilter'),
    dict(type='PhotoMetricDistortionMultiViewImage'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=(
            'img',
            'points',
        ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
use_semantic = True
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_oms_dk_0704_train.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(
                coord_type='LIDAR',
                load_dim=3,
                reduce_beams=32,
                type='LoadPointsFromFileSingle',
                use_dim=3),
            dict(
                point_cloud_range=[
                    -2,
                    0,
                    -0.03,
                    2,
                    4,
                    0.15,
                ],
                type='PointsRangeFilter'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=(
                    'img',
                    'points',
                ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeFusionDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
val_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(
        coord_type='LIDAR',
        load_dim=3,
        reduce_beams=32,
        type='LoadPointsFromFileSingle',
        use_dim=3),
    dict(
        point_cloud_range=[
            -2,
            0,
            -0.03,
            2,
            4,
            0.15,
        ],
        type='PointsRangeFilter'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=(
            'img',
            'points',
        ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
view_shape = [
    24.0,
    32.0,
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='mmdet3d.Det3DLocalVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
voxel_size = [
    0.05,
    0.05,
    0.2,
]
work_dir = 'work_dirs/indoor_oms_dk_occ/tof_crop_3cm_train'

2024/07/04 19:44:46 - mmengine - INFO - Autoplay mode, press [SPACE] to pause.
2024/07/04 19:44:46 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2024/07/04 19:44:48 - mmengine - WARNING - The prefix is not set in metric class BEVMetric.
Name of parameter - Initialization information

img_backbone.mod1.0.0.weight - torch.Size([32, 3, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone.mod1.0.1.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone.mod1.0.1.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._depthwise_conv.0.weight - torch.Size([32, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._depthwise_conv.1.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._depthwise_conv.1.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._project_conv.0.weight - torch.Size([16, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._project_conv.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.0._project_conv.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._expand_conv.0.weight - torch.Size([96, 16, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._expand_conv.1.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._expand_conv.1.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._depthwise_conv.0.weight - torch.Size([96, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._depthwise_conv.1.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._depthwise_conv.1.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._project_conv.0.weight - torch.Size([24, 96, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._project_conv.1.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.1._project_conv.1.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._expand_conv.0.weight - torch.Size([144, 24, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._expand_conv.1.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._expand_conv.1.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._depthwise_conv.0.weight - torch.Size([144, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._depthwise_conv.1.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._depthwise_conv.1.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._project_conv.0.weight - torch.Size([24, 144, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._project_conv.1.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.2._project_conv.1.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._expand_conv.0.weight - torch.Size([144, 24, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._expand_conv.1.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._expand_conv.1.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._depthwise_conv.0.weight - torch.Size([144, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._depthwise_conv.1.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._depthwise_conv.1.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._project_conv.0.weight - torch.Size([40, 144, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._project_conv.1.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.3._project_conv.1.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._expand_conv.0.weight - torch.Size([240, 40, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._expand_conv.1.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._expand_conv.1.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._depthwise_conv.0.weight - torch.Size([240, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._depthwise_conv.1.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._depthwise_conv.1.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._project_conv.0.weight - torch.Size([40, 240, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._project_conv.1.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.4._project_conv.1.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._expand_conv.0.weight - torch.Size([240, 40, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._expand_conv.1.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._expand_conv.1.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._depthwise_conv.0.weight - torch.Size([240, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._depthwise_conv.1.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._depthwise_conv.1.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._project_conv.0.weight - torch.Size([80, 240, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._project_conv.1.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.5._project_conv.1.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._expand_conv.0.weight - torch.Size([480, 80, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._expand_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._expand_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._depthwise_conv.0.weight - torch.Size([480, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._depthwise_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._depthwise_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._project_conv.0.weight - torch.Size([80, 480, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._project_conv.1.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.6._project_conv.1.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._expand_conv.0.weight - torch.Size([480, 80, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._expand_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._expand_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._depthwise_conv.0.weight - torch.Size([480, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._depthwise_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._depthwise_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._project_conv.0.weight - torch.Size([80, 480, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._project_conv.1.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.7._project_conv.1.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._expand_conv.0.weight - torch.Size([480, 80, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._expand_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._expand_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._depthwise_conv.0.weight - torch.Size([480, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._depthwise_conv.1.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._depthwise_conv.1.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._project_conv.0.weight - torch.Size([112, 480, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._project_conv.1.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.8._project_conv.1.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._expand_conv.0.weight - torch.Size([672, 112, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._expand_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._expand_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._depthwise_conv.0.weight - torch.Size([672, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._depthwise_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._depthwise_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._project_conv.0.weight - torch.Size([112, 672, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._project_conv.1.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.9._project_conv.1.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._expand_conv.0.weight - torch.Size([672, 112, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._expand_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._expand_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._depthwise_conv.0.weight - torch.Size([672, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._depthwise_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._depthwise_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._project_conv.0.weight - torch.Size([112, 672, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._project_conv.1.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.10._project_conv.1.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._expand_conv.0.weight - torch.Size([672, 112, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._expand_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._expand_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._depthwise_conv.0.weight - torch.Size([672, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._depthwise_conv.1.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._depthwise_conv.1.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._project_conv.0.weight - torch.Size([192, 672, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._project_conv.1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.11._project_conv.1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._expand_conv.0.weight - torch.Size([1152, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._expand_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._expand_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._depthwise_conv.0.weight - torch.Size([1152, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._depthwise_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._depthwise_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._project_conv.0.weight - torch.Size([192, 1152, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._project_conv.1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.12._project_conv.1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._expand_conv.0.weight - torch.Size([1152, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._expand_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._expand_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._depthwise_conv.0.weight - torch.Size([1152, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._depthwise_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._depthwise_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._project_conv.0.weight - torch.Size([192, 1152, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._project_conv.1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.13._project_conv.1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._expand_conv.0.weight - torch.Size([1152, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._expand_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._expand_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._depthwise_conv.0.weight - torch.Size([1152, 1, 5, 5]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._depthwise_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._depthwise_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._project_conv.0.weight - torch.Size([192, 1152, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._project_conv.1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.14._project_conv.1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._expand_conv.0.weight - torch.Size([1152, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._expand_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._expand_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._depthwise_conv.0.weight - torch.Size([1152, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._depthwise_conv.1.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._depthwise_conv.1.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._project_conv.0.weight - torch.Size([320, 1152, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._project_conv.1.weight - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_backbone._blocks_conv.15._project_conv.1.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.0.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.0.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.0.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.0.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.1.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.1.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.1.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.1.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.2.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.2.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.2.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.2.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.3.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.3.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.3.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.poolings.3.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.project.0.weight - torch.Size([64, 384, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.project.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.project.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.extract.project.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.project.0.weight - torch.Size([64, 112, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.project.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.project.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.project.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.0.0.weight - torch.Size([64, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.0.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.0.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.0.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.1.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.1.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.1.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.upsample.1.1.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.0.0.weight - torch.Size([64, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.0.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.0.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.0.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.1.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.1.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.1.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

img_neck.seg_convs.0.fusion.1.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

view_transformer.depth_net.0.weight - torch.Size([120, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

view_transformer.feat_net.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.conv1.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.downsample.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.0.downsample.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.conv1.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.0.1.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.conv1.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.downsample.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.0.downsample.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.conv1.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.1.1.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.conv1.weight - torch.Size([1024, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.bn1.weight - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.bn1.bias - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.conv2.weight - torch.Size([1024, 1024, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.bn2.weight - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.bn2.bias - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.downsample.weight - torch.Size([1024, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.0.downsample.bias - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.conv1.weight - torch.Size([1024, 1024, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.bn1.weight - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.bn1.bias - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.conv2.weight - torch.Size([1024, 1024, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.bn2.weight - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.custom_resnet.layers.2.1.bn2.bias - torch.Size([1024]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.0.weight - torch.Size([512, 1280, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.3.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.4.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.conv.4.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.up2.1.weight - torch.Size([256, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.up2.2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.up2.2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.up2.4.weight - torch.Size([256, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_encoder.fpn_lss.up2.4.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_decoder.final_conv.conv.weight - torch.Size([256, 256, 3, 3]): 
Initialized by user-defined `init_weights` in ConvModule  

bev_decoder.final_conv.conv.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_decoder.predicter.0.weight - torch.Size([512, 256]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_decoder.predicter.0.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_decoder.predicter.2.weight - torch.Size([4, 512]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

bev_decoder.predicter.2.bias - torch.Size([4]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

lidarmodel.reader.pfn_layers.0.linear.weight - torch.Size([64, 3, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

lidarmodel.reader.pfn_layers.0.norm.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

lidarmodel.reader.pfn_layers.0.norm.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

fuser.0.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

fuser.1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  

fuser.1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccFusion  
2024/07/04 19:44:49 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2024/07/04 19:44:49 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2024/07/04 19:44:49 - mmengine - INFO - Checkpoints will be saved to /home/<USER>/work_dirs/indoor_oms_dk_occ/tof_crop_3cm_train.
2024/07/04 19:45:54 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 19:45:54 - mmengine - INFO - Epoch(train)   [1][91/91]  lr: 4.5357e-05  eta: 2:07:50  time: 0.5755  data_time: 0.0006  memory: 2942  grad_norm: 0.7116  loss: 0.9593  loss_bce: 0.0410  loss_dice: 0.9183
2024/07/04 19:45:54 - mmengine - INFO - Saving checkpoint at 1 epochs
2024/07/04 19:46:09 - mmengine - INFO - Epoch(val) [1][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9912711207270519, 'occupancy': 0.0, 'mIoU': 0.49563556036352596}, '1~2m': {'no_occupancy': 0.9942282800841575, 'occupancy': 0.0, 'mIoU': 0.49711414004207877}, '2~3m': {'no_occupancy': 0.9989154954017007, 'occupancy': 0.0, 'mIoU': 0.49945774770085033}, '3~4m': {'no_occupancy': 0.9998493623112964, 'occupancy': 0.0, 'mIoU': 0.4999246811556482}, 'patch_mean': {'no_occupancy': 0.9960660646310516, 'occupancy': 0.0, 'mIoU': 0.4980330323155258}}  total_overall: {'no_occupancy': 0.9960660646310514, 'occupancy': 0.0, 'mIoU': 0.4980330323155257}  data_time: 0.0198  time: 0.1349
2024/07/04 19:47:11 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 19:47:11 - mmengine - INFO - Epoch(train)   [2][91/91]  lr: 5.7505e-05  eta: 2:04:34  time: 0.5854  data_time: 0.0007  memory: 2942  grad_norm: 1.2928  loss: 0.6941  loss_bce: 0.0296  loss_dice: 0.6645
2024/07/04 19:47:11 - mmengine - INFO - Saving checkpoint at 2 epochs
2024/07/04 19:47:26 - mmengine - INFO - Epoch(val) [2][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9910170290153161, 'occupancy': 0.18667744901190875, 'mIoU': 0.5888472390136124}, '1~2m': {'no_occupancy': 0.9940413384531588, 'occupancy': 0.1075818266967503, 'mIoU': 0.5508115825749546}, '2~3m': {'no_occupancy': 0.9988952694627146, 'occupancy': 0.042489189697311525, 'mIoU': 0.520692229580013}, '3~4m': {'no_occupancy': 0.999845024292903, 'occupancy': 0.0, 'mIoU': 0.4999225121464515}, 'patch_mean': {'no_occupancy': 0.995949665306023, 'occupancy': 0.08418711635149265, 'mIoU': 0.5400683908287578}}  total_overall: {'no_occupancy': 0.9959525109801903, 'occupancy': 0.14862956090733406, 'mIoU': 0.5722910359437622}  data_time: 0.0166  time: 0.1333
2024/07/04 19:48:29 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 19:48:29 - mmengine - INFO - Epoch(train)   [3][91/91]  lr: 6.9625e-05  eta: 2:03:25  time: 0.5918  data_time: 0.0007  memory: 2942  grad_norm: 1.2572  loss: 0.6744  loss_bce: 0.0307  loss_dice: 0.6437
2024/07/04 19:48:29 - mmengine - INFO - Saving checkpoint at 3 epochs
2024/07/04 19:48:44 - mmengine - INFO - Epoch(val) [3][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9913610158340447, 'occupancy': 0.20658838239046762, 'mIoU': 0.5989746991122562}, '1~2m': {'no_occupancy': 0.993554246242752, 'occupancy': 0.1383397309377993, 'mIoU': 0.5659469885902757}, '2~3m': {'no_occupancy': 0.9988655831238421, 'occupancy': 0.0580421469740634, 'mIoU': 0.5284538650489528}, '3~4m': {'no_occupancy': 0.9998433975360056, 'occupancy': 0.0, 'mIoU': 0.4999216987680028}, 'patch_mean': {'no_occupancy': 0.9959060606841611, 'occupancy': 0.10074256507558257, 'mIoU': 0.5483243128798718}}  total_overall: {'no_occupancy': 0.9959091695853525, 'occupancy': 0.1699723508330021, 'mIoU': 0.5829407602091773}  data_time: 0.0153  time: 0.1311
2024/07/04 19:49:48 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 19:49:48 - mmengine - INFO - Epoch(train)   [4][91/91]  lr: 8.1704e-05  eta: 2:02:23  time: 0.5974  data_time: 0.0009  memory: 2942  grad_norm: inf  loss: 0.6518  loss_bce: 0.0289  loss_dice: 0.6229
2024/07/04 19:49:48 - mmengine - INFO - Saving checkpoint at 4 epochs
2024/07/04 19:50:02 - mmengine - INFO - Epoch(val) [4][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9906165441621563, 'occupancy': 0.3043687760191851, 'mIoU': 0.6474926600906707}, '1~2m': {'no_occupancy': 0.9934930742099514, 'occupancy': 0.17080313688451357, 'mIoU': 0.5821481055472325}, '2~3m': {'no_occupancy': 0.9987466499105025, 'occupancy': 0.05792198263563365, 'mIoU': 0.5283343162730681}, '3~4m': {'no_occupancy': 0.9998343961119199, 'occupancy': 0.0013080444735120993, 'mIoU': 0.5005712202927161}, 'patch_mean': {'no_occupancy': 0.9956726660986326, 'occupancy': 0.1336004850032111, 'mIoU': 0.5646365755509218}}  total_overall: {'no_occupancy': 0.9956785125420266, 'occupancy': 0.24178545207924834, 'mIoU': 0.6187319823106374}  data_time: 0.0155  time: 0.1332
2024/07/04 19:51:06 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 19:51:06 - mmengine - INFO - Epoch(train)   [5][91/91]  lr: 9.3731e-05  eta: 2:01:32  time: 0.6013  data_time: 0.0007  memory: 2942  grad_norm: 1.3970  loss: 0.6255  loss_bce: 0.0270  loss_dice: 0.5984
2024/07/04 19:51:06 - mmengine - INFO - Saving checkpoint at 5 epochs
2024/07/04 19:51:21 - mmengine - INFO - Epoch(val) [5][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9903333287939863, 'occupancy': 0.32023197665677433, 'mIoU': 0.6552826527253803}, '1~2m': {'no_occupancy': 0.9938002129055096, 'occupancy': 0.20242111634057025, 'mIoU': 0.5981106646230399}, '2~3m': {'no_occupancy': 0.9987662332520909, 'occupancy': 0.08575791673364411, 'mIoU': 0.5422620749928675}, '3~4m': {'no_occupancy': 0.9998238217694476, 'occupancy': 0.014558689717925387, 'mIoU': 0.5071912557436865}, 'patch_mean': {'no_occupancy': 0.9956808991802586, 'occupancy': 0.1557424248622285, 'mIoU': 0.5757116620212436}}  total_overall: {'no_occupancy': 0.9956876167584647, 'occupancy': 0.2654076693371356, 'mIoU': 0.6305476430478002}  data_time: 0.0156  time: 0.1307
2024/07/04 19:52:24 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 19:52:24 - mmengine - INFO - Epoch(train)   [6][91/91]  lr: 9.9572e-05  eta: 2:00:29  time: 0.5958  data_time: 0.0007  memory: 2942  grad_norm: 1.5336  loss: 0.5993  loss_bce: 0.0270  loss_dice: 0.5723
2024/07/04 19:52:24 - mmengine - INFO - Saving checkpoint at 6 epochs
2024/07/04 19:52:39 - mmengine - INFO - Epoch(val) [6][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9907442870826714, 'occupancy': 0.3438815081760787, 'mIoU': 0.6673128976293751}, '1~2m': {'no_occupancy': 0.9942439264057034, 'occupancy': 0.21092966250435063, 'mIoU': 0.6025867944550269}, '2~3m': {'no_occupancy': 0.9988872046704973, 'occupancy': 0.10666550568157082, 'mIoU': 0.5527763551760341}, '3~4m': {'no_occupancy': 0.9998227358586433, 'occupancy': 0.056020791221484255, 'mIoU': 0.5279217635400638}, 'patch_mean': {'no_occupancy': 0.9959245385043789, 'occupancy': 0.1793743668958711, 'mIoU': 0.587649452700125}}  total_overall: {'no_occupancy': 0.9959313384988588, 'occupancy': 0.28596721893540483, 'mIoU': 0.6409492787171318}  data_time: 0.0179  time: 0.1367
2024/07/04 19:53:43 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 19:53:43 - mmengine - INFO - Epoch(train)   [7][91/91]  lr: 9.9384e-05  eta: 1:59:38  time: 0.6036  data_time: 0.0007  memory: 2942  grad_norm: 1.2803  loss: 0.5826  loss_bce: 0.0259  loss_dice: 0.5567
2024/07/04 19:53:43 - mmengine - INFO - Saving checkpoint at 7 epochs
2024/07/04 19:53:59 - mmengine - INFO - Epoch(val) [7][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.991026213137489, 'occupancy': 0.357504012704092, 'mIoU': 0.6742651129207905}, '1~2m': {'no_occupancy': 0.993626356183513, 'occupancy': 0.23549422718019364, 'mIoU': 0.6145602916818533}, '2~3m': {'no_occupancy': 0.9989006214861795, 'occupancy': 0.0935026606448151, 'mIoU': 0.5462016410654973}, '3~4m': {'no_occupancy': 0.9998414451096147, 'occupancy': 0.012495778453225262, 'mIoU': 0.50616861178142}, 'patch_mean': {'no_occupancy': 0.995848658979199, 'occupancy': 0.1747491697455815, 'mIoU': 0.5852989143623902}}  total_overall: {'no_occupancy': 0.9958556614712182, 'occupancy': 0.2985535486384498, 'mIoU': 0.647204605054834}  data_time: 0.0187  time: 0.1393
2024/07/04 19:55:04 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 19:55:04 - mmengine - INFO - Epoch(train)   [8][91/91]  lr: 9.9163e-05  eta: 1:59:02  time: 0.6031  data_time: 0.0007  memory: 2942  grad_norm: 1.3791  loss: 0.5728  loss_bce: 0.0250  loss_dice: 0.5478
2024/07/04 19:55:04 - mmengine - INFO - Saving checkpoint at 8 epochs
2024/07/04 19:55:19 - mmengine - INFO - Epoch(val) [8][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9925534435370257, 'occupancy': 0.39508752826638205, 'mIoU': 0.6938204859017039}, '1~2m': {'no_occupancy': 0.9944601540794751, 'occupancy': 0.24818847424513998, 'mIoU': 0.6213243141623075}, '2~3m': {'no_occupancy': 0.998812125335176, 'occupancy': 0.11284377658066345, 'mIoU': 0.5558279509579197}, '3~4m': {'no_occupancy': 0.9998195356850557, 'occupancy': 0.07786090329731227, 'mIoU': 0.538840219491184}, 'patch_mean': {'no_occupancy': 0.9964113146591831, 'occupancy': 0.20849517059737444, 'mIoU': 0.6024532426282787}}  total_overall: {'no_occupancy': 0.9964167788507412, 'occupancy': 0.3231671774272671, 'mIoU': 0.6597919781390041}  data_time: 0.0204  time: 0.1349
2024/07/04 19:56:23 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 19:56:23 - mmengine - INFO - Epoch(train)   [9][91/91]  lr: 9.8907e-05  eta: 1:57:54  time: 0.5986  data_time: 0.0007  memory: 2942  grad_norm: 1.4138  loss: 0.5585  loss_bce: 0.0248  loss_dice: 0.5337
2024/07/04 19:56:23 - mmengine - INFO - Saving checkpoint at 9 epochs
2024/07/04 19:56:37 - mmengine - INFO - Epoch(val) [9][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9923535100687578, 'occupancy': 0.39624680366745874, 'mIoU': 0.6943001568681083}, '1~2m': {'no_occupancy': 0.994047974899005, 'occupancy': 0.25527716425558816, 'mIoU': 0.6246625695772966}, '2~3m': {'no_occupancy': 0.9987724358979576, 'occupancy': 0.13256428927298508, 'mIoU': 0.5656683625854714}, '3~4m': {'no_occupancy': 0.9998375390528517, 'occupancy': 0.07559395248380128, 'mIoU': 0.5377157457683265}, 'patch_mean': {'no_occupancy': 0.9962528649796429, 'occupancy': 0.2149205524199583, 'mIoU': 0.6055867086998006}}  total_overall: {'no_occupancy': 0.9962587356160976, 'occupancy': 0.3261553467224982, 'mIoU': 0.6612070411692978}  data_time: 0.0170  time: 0.1332
2024/07/04 19:57:41 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 19:57:41 - mmengine - INFO - Epoch(train)  [10][91/91]  lr: 9.8618e-05  eta: 1:56:46  time: 0.5996  data_time: 0.0007  memory: 2942  grad_norm: 1.3274  loss: 0.5510  loss_bce: 0.0234  loss_dice: 0.5276
2024/07/04 19:57:41 - mmengine - INFO - Saving checkpoint at 10 epochs
2024/07/04 19:57:56 - mmengine - INFO - Epoch(val) [10][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9924182874045892, 'occupancy': 0.41247530702196744, 'mIoU': 0.7024467972132783}, '1~2m': {'no_occupancy': 0.9945317343055443, 'occupancy': 0.2663660998540295, 'mIoU': 0.6304489170797869}, '2~3m': {'no_occupancy': 0.9988799401246322, 'occupancy': 0.13777555110220438, 'mIoU': 0.5683277456134183}, '3~4m': {'no_occupancy': 0.9998186135470246, 'occupancy': 0.08506564551422319, 'mIoU': 0.5424421295306239}, 'patch_mean': {'no_occupancy': 0.9964121438454475, 'occupancy': 0.22542065087310614, 'mIoU': 0.6109163973592768}}  total_overall: {'no_occupancy': 0.9964182484561895, 'occupancy': 0.3431857571192456, 'mIoU': 0.6698020027877176}  data_time: 0.0154  time: 0.1318
2024/07/04 19:58:59 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 19:58:59 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 19:58:59 - mmengine - INFO - Epoch(train)  [11][91/91]  lr: 9.8296e-05  eta: 1:55:43  time: 0.6045  data_time: 0.0007  memory: 2942  grad_norm: 1.3693  loss: 0.5129  loss_bce: 0.0223  loss_dice: 0.4905
2024/07/04 19:58:59 - mmengine - INFO - Saving checkpoint at 11 epochs
2024/07/04 19:59:14 - mmengine - INFO - Epoch(val) [11][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9930071667344081, 'occupancy': 0.429063451302227, 'mIoU': 0.7110353090183176}, '1~2m': {'no_occupancy': 0.9949249055104304, 'occupancy': 0.29212800809453476, 'mIoU': 0.6435264568024825}, '2~3m': {'no_occupancy': 0.9989474003913625, 'occupancy': 0.15056022408963585, 'mIoU': 0.5747538122404992}, '3~4m': {'no_occupancy': 0.9998525596730695, 'occupancy': 0.08141891891891892, 'mIoU': 0.5406357392959942}, 'patch_mean': {'no_occupancy': 0.9966830080773177, 'occupancy': 0.23829265060132915, 'mIoU': 0.6174878293393234}}  total_overall: {'no_occupancy': 0.9966886253543344, 'occupancy': 0.3624919799694528, 'mIoU': 0.6795903026618937}  data_time: 0.0176  time: 0.1328
2024/07/04 20:00:17 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:00:17 - mmengine - INFO - Epoch(train)  [12][91/91]  lr: 9.7941e-05  eta: 1:54:36  time: 0.5994  data_time: 0.0007  memory: 2942  grad_norm: 1.3745  loss: 0.5044  loss_bce: 0.0228  loss_dice: 0.4817
2024/07/04 20:00:17 - mmengine - INFO - Saving checkpoint at 12 epochs
2024/07/04 20:00:32 - mmengine - INFO - Epoch(val) [12][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9931275356703403, 'occupancy': 0.43741266445579324, 'mIoU': 0.7152701000630668}, '1~2m': {'no_occupancy': 0.9948049723883257, 'occupancy': 0.28656352490364273, 'mIoU': 0.6406842486459842}, '2~3m': {'no_occupancy': 0.9989068635248805, 'occupancy': 0.15964809873248834, 'mIoU': 0.5792774811286844}, '3~4m': {'no_occupancy': 0.9998515832037739, 'occupancy': 0.09610303830911493, 'mIoU': 0.5479773107564444}, 'patch_mean': {'no_occupancy': 0.99667273869683, 'occupancy': 0.2449318316002598, 'mIoU': 0.620802285148545}}  total_overall: {'no_occupancy': 0.9966783037629582, 'occupancy': 0.36493764774962906, 'mIoU': 0.6808079757562936}  data_time: 0.0151  time: 0.1299
2024/07/04 20:01:37 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:01:37 - mmengine - INFO - Epoch(train)  [13][91/91]  lr: 9.7553e-05  eta: 1:53:38  time: 0.6091  data_time: 0.0007  memory: 2942  grad_norm: 1.3845  loss: 0.5029  loss_bce: 0.0230  loss_dice: 0.4799
2024/07/04 20:01:37 - mmengine - INFO - Saving checkpoint at 13 epochs
2024/07/04 20:01:52 - mmengine - INFO - Epoch(val) [13][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9934662318371925, 'occupancy': 0.4521973834116664, 'mIoU': 0.7228318076244294}, '1~2m': {'no_occupancy': 0.9950199882353179, 'occupancy': 0.30564663851776924, 'mIoU': 0.6503333133765435}, '2~3m': {'no_occupancy': 0.9989794676505834, 'occupancy': 0.14472069451388575, 'mIoU': 0.5718500810822346}, '3~4m': {'no_occupancy': 0.9998540782842077, 'occupancy': 0.07078729281767956, 'mIoU': 0.5353206855509436}, 'patch_mean': {'no_occupancy': 0.9968299415018254, 'occupancy': 0.2433380023152502, 'mIoU': 0.6200839719085378}}  total_overall: {'no_occupancy': 0.9968353517102018, 'occupancy': 0.37976140087474797, 'mIoU': 0.6882983762924748}  data_time: 0.0204  time: 0.1428
2024/07/04 20:02:57 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:02:57 - mmengine - INFO - Epoch(train)  [14][91/91]  lr: 9.7132e-05  eta: 1:52:39  time: 0.6044  data_time: 0.0007  memory: 2942  grad_norm: 1.3853  loss: 0.4727  loss_bce: 0.0222  loss_dice: 0.4505
2024/07/04 20:02:57 - mmengine - INFO - Saving checkpoint at 14 epochs
2024/07/04 20:03:11 - mmengine - INFO - Epoch(val) [14][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9939279954870307, 'occupancy': 0.4675365883390579, 'mIoU': 0.7307322919130443}, '1~2m': {'no_occupancy': 0.9951473745184216, 'occupancy': 0.31963606888097473, 'mIoU': 0.6573917216996982}, '2~3m': {'no_occupancy': 0.998984479027816, 'occupancy': 0.16771125038894075, 'mIoU': 0.5833478647083783}, '3~4m': {'no_occupancy': 0.9998579817586541, 'occupancy': 0.1049213943950786, 'mIoU': 0.5523896880768664}, 'patch_mean': {'no_occupancy': 0.9969794576979807, 'occupancy': 0.26495132550101297, 'mIoU': 0.6309653915994968}}  total_overall: {'no_occupancy': 0.9969844404153849, 'occupancy': 0.39314465221849443, 'mIoU': 0.6950645463169397}  data_time: 0.0161  time: 0.1293
2024/07/04 20:04:16 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:04:16 - mmengine - INFO - Epoch(train)  [15][91/91]  lr: 9.6679e-05  eta: 1:51:39  time: 0.6078  data_time: 0.0007  memory: 2942  grad_norm: 1.2887  loss: 0.4682  loss_bce: 0.0214  loss_dice: 0.4468
2024/07/04 20:04:16 - mmengine - INFO - Saving checkpoint at 15 epochs
2024/07/04 20:04:30 - mmengine - INFO - Epoch(val) [15][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9942648279061905, 'occupancy': 0.493079661905307, 'mIoU': 0.7436722449057488}, '1~2m': {'no_occupancy': 0.9953252853076411, 'occupancy': 0.3286603236796609, 'mIoU': 0.6619928044936509}, '2~3m': {'no_occupancy': 0.9989536659846044, 'occupancy': 0.16791028682337716, 'mIoU': 0.5834319764039908}, '3~4m': {'no_occupancy': 0.9998511493385878, 'occupancy': 0.09792967466316135, 'mIoU': 0.5488904120008745}, 'patch_mean': {'no_occupancy': 0.9970987321342559, 'occupancy': 0.2718949867678766, 'mIoU': 0.6344968594510663}}  total_overall: {'no_occupancy': 0.997103575582744, 'occupancy': 0.41070233027645914, 'mIoU': 0.7039029529296016}  data_time: 0.0163  time: 0.1300
2024/07/04 20:05:34 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:05:34 - mmengine - INFO - Epoch(train)  [16][91/91]  lr: 9.6194e-05  eta: 1:50:32  time: 0.5966  data_time: 0.0007  memory: 2942  grad_norm: 1.2756  loss: 0.4697  loss_bce: 0.0204  loss_dice: 0.4493
2024/07/04 20:05:34 - mmengine - INFO - Saving checkpoint at 16 epochs
2024/07/04 20:05:48 - mmengine - INFO - Epoch(val) [16][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9943997910199696, 'occupancy': 0.49721488779084877, 'mIoU': 0.7458073394054092}, '1~2m': {'no_occupancy': 0.9954205815226944, 'occupancy': 0.3320325699523659, 'mIoU': 0.6637265757375301}, '2~3m': {'no_occupancy': 0.9990203943143386, 'occupancy': 0.1649946835560076, 'mIoU': 0.5820075389351731}, '3~4m': {'no_occupancy': 0.9998597172763567, 'occupancy': 0.09450472523626181, 'mIoU': 0.5471822212563092}, 'patch_mean': {'no_occupancy': 0.9971751210333398, 'occupancy': 0.27218671663387106, 'mIoU': 0.6346809188336054}}  total_overall: {'no_occupancy': 0.9971798489758912, 'occupancy': 0.41501507424417455, 'mIoU': 0.7060974616100328}  data_time: 0.0162  time: 0.1308
2024/07/04 20:06:52 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:06:52 - mmengine - INFO - Epoch(train)  [17][91/91]  lr: 9.5677e-05  eta: 1:49:27  time: 0.5988  data_time: 0.0007  memory: 2942  grad_norm: 1.2682  loss: 0.4356  loss_bce: 0.0209  loss_dice: 0.4147
2024/07/04 20:06:52 - mmengine - INFO - Saving checkpoint at 17 epochs
2024/07/04 20:07:07 - mmengine - INFO - Epoch(val) [17][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9945097776616045, 'occupancy': 0.5059061107512318, 'mIoU': 0.7502079442064182}, '1~2m': {'no_occupancy': 0.9955389693664108, 'occupancy': 0.3437515303975686, 'mIoU': 0.6696452498819897}, '2~3m': {'no_occupancy': 0.9990250956382537, 'occupancy': 0.17806026795921168, 'mIoU': 0.5885426817987327}, '3~4m': {'no_occupancy': 0.9998597709700962, 'occupancy': 0.11619958988380041, 'mIoU': 0.5580296804269482}, 'patch_mean': {'no_occupancy': 0.9972334034090913, 'occupancy': 0.2859793747479531, 'mIoU': 0.6416063890785222}}  total_overall: {'no_occupancy': 0.997238100154367, 'occupancy': 0.42505884394751237, 'mIoU': 0.7111484720509397}  data_time: 0.0167  time: 0.1318
2024/07/04 20:08:11 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:08:11 - mmengine - INFO - Epoch(train)  [18][91/91]  lr: 9.5129e-05  eta: 1:48:26  time: 0.6036  data_time: 0.0007  memory: 2942  grad_norm: 1.2130  loss: 0.4516  loss_bce: 0.0209  loss_dice: 0.4307
2024/07/04 20:08:11 - mmengine - INFO - Saving checkpoint at 18 epochs
2024/07/04 20:08:25 - mmengine - INFO - Epoch(val) [18][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9945892821452058, 'occupancy': 0.5175341384846481, 'mIoU': 0.756061710314927}, '1~2m': {'no_occupancy': 0.995533510773606, 'occupancy': 0.3506713381622136, 'mIoU': 0.6731024244679098}, '2~3m': {'no_occupancy': 0.99893409331706, 'occupancy': 0.19346630550767466, 'mIoU': 0.5962001994123673}, '3~4m': {'no_occupancy': 0.9998554870274258, 'occupancy': 0.11579296615792965, 'mIoU': 0.5578242265926777}, 'patch_mean': {'no_occupancy': 0.9972280933158244, 'occupancy': 0.29436618707811646, 'mIoU': 0.6457971401969704}}  total_overall: {'no_occupancy': 0.9972328080775061, 'occupancy': 0.43347615014817065, 'mIoU': 0.7153544791128383}  data_time: 0.0167  time: 0.1307
2024/07/04 20:09:29 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:09:29 - mmengine - INFO - Epoch(train)  [19][91/91]  lr: 9.4550e-05  eta: 1:47:22  time: 0.6005  data_time: 0.0007  memory: 2942  grad_norm: 1.2786  loss: 0.4559  loss_bce: 0.0204  loss_dice: 0.4355
2024/07/04 20:09:29 - mmengine - INFO - Saving checkpoint at 19 epochs
2024/07/04 20:09:44 - mmengine - INFO - Epoch(val) [19][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9947866957547454, 'occupancy': 0.5301778987268195, 'mIoU': 0.7624822972407825}, '1~2m': {'no_occupancy': 0.9955942309403631, 'occupancy': 0.35516008572380425, 'mIoU': 0.6753771583320837}, '2~3m': {'no_occupancy': 0.9990339021587826, 'occupancy': 0.20325639649311147, 'mIoU': 0.6011451493259471}, '3~4m': {'no_occupancy': 0.9998544568232666, 'occupancy': 0.11155246607083749, 'mIoU': 0.5557034614470521}, 'patch_mean': {'no_occupancy': 0.9973173214192894, 'occupancy': 0.3000367117536432, 'mIoU': 0.6486770165864664}}  total_overall: {'no_occupancy': 0.9973219569194948, 'occupancy': 0.443940029688971, 'mIoU': 0.7206309933042329}  data_time: 0.0168  time: 0.1306
2024/07/04 20:10:48 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:10:48 - mmengine - INFO - Epoch(train)  [20][91/91]  lr: 9.3941e-05  eta: 1:46:21  time: 0.6077  data_time: 0.0007  memory: 2942  grad_norm: 1.3466  loss: 0.4089  loss_bce: 0.0186  loss_dice: 0.3903
2024/07/04 20:10:48 - mmengine - INFO - Saving checkpoint at 20 epochs
2024/07/04 20:11:03 - mmengine - INFO - Epoch(val) [20][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9951073283478324, 'occupancy': 0.5445813165829082, 'mIoU': 0.7698443224653704}, '1~2m': {'no_occupancy': 0.995720686372795, 'occupancy': 0.3618328391964382, 'mIoU': 0.6787767627846166}, '2~3m': {'no_occupancy': 0.9990518932763777, 'occupancy': 0.2174419125218248, 'mIoU': 0.6082469028991012}, '3~4m': {'no_occupancy': 0.9998582523282082, 'occupancy': 0.12692050768203073, 'mIoU': 0.5633893800051195}, 'patch_mean': {'no_occupancy': 0.9974345400813033, 'occupancy': 0.3126941439958005, 'mIoU': 0.6550643420385519}}  total_overall: {'no_occupancy': 0.9974388514390696, 'occupancy': 0.4546031826392292, 'mIoU': 0.7260210170391495}  data_time: 0.0162  time: 0.1295
2024/07/04 20:12:06 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:12:06 - mmengine - INFO - Epoch(train)  [21][91/91]  lr: 9.3301e-05  eta: 1:45:18  time: 0.5999  data_time: 0.0007  memory: 2942  grad_norm: 1.2438  loss: 0.4043  loss_bce: 0.0191  loss_dice: 0.3852
2024/07/04 20:12:06 - mmengine - INFO - Saving checkpoint at 21 epochs
2024/07/04 20:12:21 - mmengine - INFO - Epoch(val) [21][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9952149950615337, 'occupancy': 0.5554870741094526, 'mIoU': 0.7753510345854931}, '1~2m': {'no_occupancy': 0.995788250035722, 'occupancy': 0.36369739342160506, 'mIoU': 0.6797428217286635}, '2~3m': {'no_occupancy': 0.9990440814381435, 'occupancy': 0.2169547251966055, 'mIoU': 0.6079994033173745}, '3~4m': {'no_occupancy': 0.9998578184788801, 'occupancy': 0.12803458596607917, 'mIoU': 0.5639462022224796}, 'patch_mean': {'no_occupancy': 0.9974762862535699, 'occupancy': 0.3160434446734356, 'mIoU': 0.6567598654635027}}  total_overall: {'no_occupancy': 0.9974805530427864, 'occupancy': 0.4618789383941127, 'mIoU': 0.7296797457184495}  data_time: 0.0158  time: 0.1332
2024/07/04 20:13:24 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:13:25 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:13:25 - mmengine - INFO - Epoch(train)  [22][91/91]  lr: 9.2632e-05  eta: 1:44:13  time: 0.6015  data_time: 0.0007  memory: 2942  grad_norm: 1.2179  loss: 0.4116  loss_bce: 0.0198  loss_dice: 0.3917
2024/07/04 20:13:25 - mmengine - INFO - Saving checkpoint at 22 epochs
2024/07/04 20:13:40 - mmengine - INFO - Epoch(val) [22][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9953948886321776, 'occupancy': 0.5619416566461586, 'mIoU': 0.778668272639168}, '1~2m': {'no_occupancy': 0.9958691868445034, 'occupancy': 0.3702392753888912, 'mIoU': 0.6830542311166973}, '2~3m': {'no_occupancy': 0.9990566061785597, 'occupancy': 0.2223464186711974, 'mIoU': 0.6107015124248786}, '3~4m': {'no_occupancy': 0.9998592826822207, 'occupancy': 0.12567385444743934, 'mIoU': 0.56276656856483}, 'patch_mean': {'no_occupancy': 0.9975449910843653, 'occupancy': 0.32005030128842166, 'mIoU': 0.6587976461863935}}  total_overall: {'no_occupancy': 0.9975490579251843, 'occupancy': 0.4674039432698755, 'mIoU': 0.7324765005975299}  data_time: 0.0160  time: 0.1324
2024/07/04 20:14:43 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:14:43 - mmengine - INFO - Epoch(train)  [23][91/91]  lr: 9.1934e-05  eta: 1:43:08  time: 0.5972  data_time: 0.0007  memory: 2942  grad_norm: 1.2210  loss: 0.4214  loss_bce: 0.0190  loss_dice: 0.4025
2024/07/04 20:14:43 - mmengine - INFO - Saving checkpoint at 23 epochs
2024/07/04 20:14:58 - mmengine - INFO - Epoch(val) [23][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9950119792718889, 'occupancy': 0.548514127913508, 'mIoU': 0.7717630535926985}, '1~2m': {'no_occupancy': 0.9957793079411019, 'occupancy': 0.3735911203265938, 'mIoU': 0.6846852141338479}, '2~3m': {'no_occupancy': 0.999069216499971, 'occupancy': 0.24335288152034923, 'mIoU': 0.6212110490101601}, '3~4m': {'no_occupancy': 0.999860909147699, 'occupancy': 0.13955048641395507, 'mIoU': 0.569705697780827}, 'patch_mean': {'no_occupancy': 0.9974303532151653, 'occupancy': 0.3262521540436015, 'mIoU': 0.6618412536293834}}  total_overall: {'no_occupancy': 0.9974349051374665, 'occupancy': 0.46382501222943484, 'mIoU': 0.7306299586834506}  data_time: 0.0164  time: 0.1308
2024/07/04 20:16:02 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:16:02 - mmengine - INFO - Epoch(train)  [24][91/91]  lr: 9.1206e-05  eta: 1:42:06  time: 0.6036  data_time: 0.0007  memory: 2942  grad_norm: 1.2699  loss: 0.3955  loss_bce: 0.0189  loss_dice: 0.3766
2024/07/04 20:16:02 - mmengine - INFO - Saving checkpoint at 24 epochs
2024/07/04 20:16:17 - mmengine - INFO - Epoch(val) [24][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.995614834344384, 'occupancy': 0.5787107703958565, 'mIoU': 0.7871628023701203}, '1~2m': {'no_occupancy': 0.9959357548197257, 'occupancy': 0.3810385204855135, 'mIoU': 0.6884871376526196}, '2~3m': {'no_occupancy': 0.9990621836259648, 'occupancy': 0.22967253285809758, 'mIoU': 0.6143673582420311}, '3~4m': {'no_occupancy': 0.9998638376345943, 'occupancy': 0.13174273858921162, 'mIoU': 0.565803288111903}, 'patch_mean': {'no_occupancy': 0.9976191526061671, 'occupancy': 0.3302911405821698, 'mIoU': 0.6639551465941684}}  total_overall: {'no_occupancy': 0.9976230994754448, 'occupancy': 0.48075830790491464, 'mIoU': 0.7391907036901797}  data_time: 0.0156  time: 0.1304
2024/07/04 20:17:24 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:17:24 - mmengine - INFO - Epoch(train)  [25][91/91]  lr: 9.0451e-05  eta: 1:41:12  time: 0.6032  data_time: 0.0007  memory: 2942  grad_norm: 1.2353  loss: 0.3812  loss_bce: 0.0174  loss_dice: 0.3638
2024/07/04 20:17:24 - mmengine - INFO - Saving checkpoint at 25 epochs
2024/07/04 20:17:38 - mmengine - INFO - Epoch(val) [25][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9956303020686962, 'occupancy': 0.5815735704831302, 'mIoU': 0.7886019362759131}, '1~2m': {'no_occupancy': 0.9959202486653281, 'occupancy': 0.38912354858371484, 'mIoU': 0.6925218986245215}, '2~3m': {'no_occupancy': 0.9990887556844968, 'occupancy': 0.23882017126546148, 'mIoU': 0.6189544634749792}, '3~4m': {'no_occupancy': 0.9998631327588376, 'occupancy': 0.12845303867403318, 'mIoU': 0.5641580857164354}, 'patch_mean': {'no_occupancy': 0.9976256097943397, 'occupancy': 0.3344925822515849, 'mIoU': 0.6660590960229623}}  total_overall: {'no_occupancy': 0.9976296193274217, 'occupancy': 0.48571608597541827, 'mIoU': 0.74167285265142}  data_time: 0.0178  time: 0.1304
2024/07/04 20:18:43 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:18:43 - mmengine - INFO - Epoch(train)  [26][91/91]  lr: 8.9668e-05  eta: 1:40:11  time: 0.6052  data_time: 0.0007  memory: 2942  grad_norm: 1.2302  loss: 0.3661  loss_bce: 0.0172  loss_dice: 0.3490
2024/07/04 20:18:43 - mmengine - INFO - Saving checkpoint at 26 epochs
2024/07/04 20:18:57 - mmengine - INFO - Epoch(val) [26][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9955916327168638, 'occupancy': 0.5881473929043668, 'mIoU': 0.7918695128106152}, '1~2m': {'no_occupancy': 0.9959946935795974, 'occupancy': 0.3941968594502753, 'mIoU': 0.6950957765149364}, '2~3m': {'no_occupancy': 0.999079046143699, 'occupancy': 0.2372074217170583, 'mIoU': 0.6181432339303786}, '3~4m': {'no_occupancy': 0.999861234897704, 'occupancy': 0.12452959288402325, 'mIoU': 0.5621954138908636}, 'patch_mean': {'no_occupancy': 0.9976316518344661, 'occupancy': 0.33602031673893096, 'mIoU': 0.6668259842866986}}  total_overall: {'no_occupancy': 0.9976358011359951, 'occupancy': 0.49264365520272874, 'mIoU': 0.7451397281693619}  data_time: 0.0166  time: 0.1302
2024/07/04 20:20:01 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:20:01 - mmengine - INFO - Epoch(train)  [27][91/91]  lr: 8.8857e-05  eta: 1:39:07  time: 0.6037  data_time: 0.0007  memory: 2942  grad_norm: 1.1812  loss: 0.3740  loss_bce: 0.0173  loss_dice: 0.3567
2024/07/04 20:20:01 - mmengine - INFO - Saving checkpoint at 27 epochs
2024/07/04 20:20:16 - mmengine - INFO - Epoch(val) [27][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9958979128328453, 'occupancy': 0.6045385084770726, 'mIoU': 0.8002182106549589}, '1~2m': {'no_occupancy': 0.9961260421691218, 'occupancy': 0.4067196296408087, 'mIoU': 0.7014228359049652}, '2~3m': {'no_occupancy': 0.999101423158906, 'occupancy': 0.25869244193851526, 'mIoU': 0.6288969325487106}, '3~4m': {'no_occupancy': 0.9998665487639543, 'occupancy': 0.1419107391910739, 'mIoU': 0.570888643977514}, 'patch_mean': {'no_occupancy': 0.9977479817312068, 'occupancy': 0.3529653298118676, 'mIoU': 0.6753566557715373}}  total_overall: {'no_occupancy': 0.9977518291500826, 'occupancy': 0.5067093997186325, 'mIoU': 0.7522306144343576}  data_time: 0.0171  time: 0.1324
2024/07/04 20:21:20 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:21:20 - mmengine - INFO - Epoch(train)  [28][91/91]  lr: 8.8020e-05  eta: 1:38:02  time: 0.5956  data_time: 0.0007  memory: 2942  grad_norm: 1.1367  loss: 0.3478  loss_bce: 0.0169  loss_dice: 0.3309
2024/07/04 20:21:20 - mmengine - INFO - Saving checkpoint at 28 epochs
2024/07/04 20:21:35 - mmengine - INFO - Epoch(val) [28][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9960753879555228, 'occupancy': 0.6175396660269761, 'mIoU': 0.8068075269912494}, '1~2m': {'no_occupancy': 0.9961790670427197, 'occupancy': 0.41115408825781263, 'mIoU': 0.7036665776502662}, '2~3m': {'no_occupancy': 0.9991240878129634, 'occupancy': 0.2696517412935323, 'mIoU': 0.6343879145532478}, '3~4m': {'no_occupancy': 0.9998613971782613, 'occupancy': 0.14026236125126135, 'mIoU': 0.5700618792147614}, 'patch_mean': {'no_occupancy': 0.9978099849973667, 'occupancy': 0.35965196420739554, 'mIoU': 0.6787309746023811}}  total_overall: {'no_occupancy': 0.9978136913838712, 'occupancy': 0.5160522864808288, 'mIoU': 0.75693298893235}  data_time: 0.0163  time: 0.1343
2024/07/04 20:22:39 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:22:39 - mmengine - INFO - Epoch(train)  [29][91/91]  lr: 8.7157e-05  eta: 1:36:58  time: 0.5947  data_time: 0.0007  memory: 2942  grad_norm: inf  loss: 0.3522  loss_bce: 0.0168  loss_dice: 0.3355
2024/07/04 20:22:39 - mmengine - INFO - Saving checkpoint at 29 epochs
2024/07/04 20:22:53 - mmengine - INFO - Epoch(val) [29][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9962097430280714, 'occupancy': 0.6276660046049477, 'mIoU': 0.8119378738165095}, '1~2m': {'no_occupancy': 0.9962447210855665, 'occupancy': 0.41792992872357687, 'mIoU': 0.7070873249045717}, '2~3m': {'no_occupancy': 0.9991443283479274, 'occupancy': 0.2684906097843728, 'mIoU': 0.6338174690661501}, '3~4m': {'no_occupancy': 0.9998671993586525, 'occupancy': 0.14758092586146884, 'mIoU': 0.5737240626100607}, 'patch_mean': {'no_occupancy': 0.9978664979550544, 'occupancy': 0.36541686724359157, 'mIoU': 0.681641682599323}}  total_overall: {'no_occupancy': 0.99787011454128, 'occupancy': 0.5244858506056334, 'mIoU': 0.7611779825734567}  data_time: 0.0170  time: 0.1323
2024/07/04 20:23:58 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:23:58 - mmengine - INFO - Epoch(train)  [30][91/91]  lr: 8.6269e-05  eta: 1:35:54  time: 0.6048  data_time: 0.0007  memory: 2942  grad_norm: 1.1928  loss: 0.3429  loss_bce: 0.0166  loss_dice: 0.3263
2024/07/04 20:23:58 - mmengine - INFO - Saving checkpoint at 30 epochs
2024/07/04 20:24:12 - mmengine - INFO - Epoch(val) [30][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9962105568976264, 'occupancy': 0.6296075762920149, 'mIoU': 0.8129090665948207}, '1~2m': {'no_occupancy': 0.9962105230277084, 'occupancy': 0.41682068210272644, 'mIoU': 0.7065156025652174}, '2~3m': {'no_occupancy': 0.9991192587257908, 'occupancy': 0.26959064327485377, 'mIoU': 0.6343549510003222}, '3~4m': {'no_occupancy': 0.9998642167699013, 'occupancy': 0.15003394433129666, 'mIoU': 0.574949080550599}, 'patch_mean': {'no_occupancy': 0.9978511388552567, 'occupancy': 0.3665132115002229, 'mIoU': 0.6821821751777398}}  total_overall: {'no_occupancy': 0.9978547652503704, 'occupancy': 0.524697690662243, 'mIoU': 0.7612762279563068}  data_time: 0.0171  time: 0.1308
2024/07/04 20:25:16 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:25:16 - mmengine - INFO - Epoch(train)  [31][91/91]  lr: 8.5355e-05  eta: 1:34:50  time: 0.5974  data_time: 0.0007  memory: 2942  grad_norm: 1.1373  loss: 0.3553  loss_bce: 0.0172  loss_dice: 0.3382
2024/07/04 20:25:16 - mmengine - INFO - Saving checkpoint at 31 epochs
2024/07/04 20:25:31 - mmengine - INFO - Epoch(val) [31][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9962627885520111, 'occupancy': 0.6295245939107232, 'mIoU': 0.8128936912313671}, '1~2m': {'no_occupancy': 0.9963048373245027, 'occupancy': 0.4216493364891099, 'mIoU': 0.7089770869068063}, '2~3m': {'no_occupancy': 0.9991326594402752, 'occupancy': 0.2706955530216648, 'mIoU': 0.63491410623097}, '3~4m': {'no_occupancy': 0.9998653013297101, 'occupancy': 0.14989733059548255, 'mIoU': 0.5748813159625963}, 'patch_mean': {'no_occupancy': 0.9978913966616247, 'occupancy': 0.3679417035042451, 'mIoU': 0.682916550082935}}  total_overall: {'no_occupancy': 0.9978949282511675, 'occupancy': 0.5265458818441899, 'mIoU': 0.7622204050476787}  data_time: 0.0162  time: 0.1295
2024/07/04 20:26:35 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:26:35 - mmengine - INFO - Epoch(train)  [32][91/91]  lr: 8.4418e-05  eta: 1:33:45  time: 0.6014  data_time: 0.0007  memory: 2942  grad_norm: 1.2128  loss: 0.3538  loss_bce: 0.0165  loss_dice: 0.3373
2024/07/04 20:26:35 - mmengine - INFO - Saving checkpoint at 32 epochs
2024/07/04 20:26:49 - mmengine - INFO - Epoch(val) [32][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9962597274954065, 'occupancy': 0.6393563416818036, 'mIoU': 0.817808034588605}, '1~2m': {'no_occupancy': 0.996240667761971, 'occupancy': 0.4254748712451257, 'mIoU': 0.7108577695035483}, '2~3m': {'no_occupancy': 0.999147792271559, 'occupancy': 0.27482114008769903, 'mIoU': 0.636984466179629}, '3~4m': {'no_occupancy': 0.9998671448872309, 'occupancy': 0.1574965612104539, 'mIoU': 0.5786818530488425}, 'patch_mean': {'no_occupancy': 0.9978788331040418, 'occupancy': 0.37428722855627056, 'mIoU': 0.6860830308301562}}  total_overall: {'no_occupancy': 0.9978825306508433, 'occupancy': 0.5347414309521453, 'mIoU': 0.7663119808014943}  data_time: 0.0157  time: 0.1311
2024/07/04 20:27:51 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:27:53 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:27:53 - mmengine - INFO - Epoch(train)  [33][91/91]  lr: 8.3457e-05  eta: 1:32:40  time: 0.6066  data_time: 0.0007  memory: 2942  grad_norm: 1.2475  loss: 0.3456  loss_bce: 0.0169  loss_dice: 0.3287
2024/07/04 20:27:53 - mmengine - INFO - Saving checkpoint at 33 epochs
2024/07/04 20:28:08 - mmengine - INFO - Epoch(val) [33][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9964258235271951, 'occupancy': 0.6509327150995688, 'mIoU': 0.823679269313382}, '1~2m': {'no_occupancy': 0.9963392435720921, 'occupancy': 0.4333269501501451, 'mIoU': 0.7148330968611186}, '2~3m': {'no_occupancy': 0.9991383988365021, 'occupancy': 0.2793103448275862, 'mIoU': 0.6392243718320442}, '3~4m': {'no_occupancy': 0.9998661688596518, 'occupancy': 0.1545049674546077, 'mIoU': 0.5771855681571297}, 'patch_mean': {'no_occupancy': 0.9979424086988603, 'occupancy': 0.37951874438297695, 'mIoU': 0.6887305765409186}}  total_overall: {'no_occupancy': 0.9979459348517705, 'occupancy': 0.5438168336348254, 'mIoU': 0.770881384243298}  data_time: 0.0172  time: 0.1329
2024/07/04 20:29:12 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:29:12 - mmengine - INFO - Epoch(train)  [34][91/91]  lr: 8.2472e-05  eta: 1:31:37  time: 0.6067  data_time: 0.0007  memory: 2942  grad_norm: 1.2359  loss: 0.3349  loss_bce: 0.0158  loss_dice: 0.3191
2024/07/04 20:29:12 - mmengine - INFO - Saving checkpoint at 34 epochs
2024/07/04 20:29:27 - mmengine - INFO - Epoch(val) [34][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9965601143124744, 'occupancy': 0.6577493023126358, 'mIoU': 0.8271547083125551}, '1~2m': {'no_occupancy': 0.9963964410491614, 'occupancy': 0.4370402776996125, 'mIoU': 0.7167183593743869}, '2~3m': {'no_occupancy': 0.9991514765436397, 'occupancy': 0.2787588178339251, 'mIoU': 0.6389551471887824}, '3~4m': {'no_occupancy': 0.9998653012858847, 'occupancy': 0.15163934426229508, 'mIoU': 0.5757523227740899}, 'patch_mean': {'no_occupancy': 0.9979933332977899, 'occupancy': 0.3812969355271172, 'mIoU': 0.6896451344124536}}  total_overall: {'no_occupancy': 0.997996702167476, 'occupancy': 0.5484494328332606, 'mIoU': 0.7732230675003684}  data_time: 0.0170  time: 0.1316
2024/07/04 20:30:30 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:30:30 - mmengine - INFO - Epoch(train)  [35][91/91]  lr: 8.1466e-05  eta: 1:30:32  time: 0.6009  data_time: 0.0007  memory: 2942  grad_norm: 1.2035  loss: 0.3091  loss_bce: 0.0154  loss_dice: 0.2937
2024/07/04 20:30:30 - mmengine - INFO - Saving checkpoint at 35 epochs
2024/07/04 20:30:45 - mmengine - INFO - Epoch(val) [35][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9966990989929749, 'occupancy': 0.6687017495320906, 'mIoU': 0.8327004242625328}, '1~2m': {'no_occupancy': 0.996482014992753, 'occupancy': 0.44368857842130893, 'mIoU': 0.720085296707031}, '2~3m': {'no_occupancy': 0.9991754513740606, 'occupancy': 0.28499529633113835, 'mIoU': 0.6420853738525995}, '3~4m': {'no_occupancy': 0.9998655183378761, 'occupancy': 0.14600550964187325, 'mIoU': 0.5729355139898746}, 'patch_mean': {'no_occupancy': 0.9980555209244161, 'occupancy': 0.3858477834816028, 'mIoU': 0.6919516522030095}}  total_overall: {'no_occupancy': 0.9980587714930448, 'occupancy': 0.5577270496663164, 'mIoU': 0.7778929105796806}  data_time: 0.0169  time: 0.1316
2024/07/04 20:31:50 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:31:50 - mmengine - INFO - Epoch(train)  [36][91/91]  lr: 8.0438e-05  eta: 1:29:29  time: 0.6031  data_time: 0.0007  memory: 2942  grad_norm: 1.2378  loss: 0.3256  loss_bce: 0.0156  loss_dice: 0.3101
2024/07/04 20:31:50 - mmengine - INFO - Saving checkpoint at 36 epochs
2024/07/04 20:32:05 - mmengine - INFO - Epoch(val) [36][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9967727072632212, 'occupancy': 0.6754417808618698, 'mIoU': 0.8361072440625454}, '1~2m': {'no_occupancy': 0.996487078159911, 'occupancy': 0.44728714006425485, 'mIoU': 0.7218871091120829}, '2~3m': {'no_occupancy': 0.9991697935696188, 'occupancy': 0.2957065942662557, 'mIoU': 0.6474381939179372}, '3~4m': {'no_occupancy': 0.9998701273348612, 'occupancy': 0.16112084063047286, 'mIoU': 0.580495483982667}, 'patch_mean': {'no_occupancy': 0.9980749265819031, 'occupancy': 0.39488908895571334, 'mIoU': 0.6964820077688082}}  total_overall: {'no_occupancy': 0.9980781251731117, 'occupancy': 0.5630042839877124, 'mIoU': 0.7805412045804121}  data_time: 0.0150  time: 0.1350
2024/07/04 20:33:08 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:33:08 - mmengine - INFO - Epoch(train)  [37][91/91]  lr: 7.9389e-05  eta: 1:28:25  time: 0.6001  data_time: 0.0007  memory: 2942  grad_norm: 1.2350  loss: 0.3002  loss_bce: 0.0147  loss_dice: 0.2856
2024/07/04 20:33:08 - mmengine - INFO - Saving checkpoint at 37 epochs
2024/07/04 20:33:23 - mmengine - INFO - Epoch(val) [37][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9967564243812149, 'occupancy': 0.6764862228774492, 'mIoU': 0.8366213236293321}, '1~2m': {'no_occupancy': 0.9964749089262984, 'occupancy': 0.4537764936012969, 'mIoU': 0.7251257012637977}, '2~3m': {'no_occupancy': 0.9991559690993561, 'occupancy': 0.28676200953428677, 'mIoU': 0.6429589893168215}, '3~4m': {'no_occupancy': 0.9998685547853513, 'occupancy': 0.15862547726483858, 'mIoU': 0.579247016025095}, 'patch_mean': {'no_occupancy': 0.9980639642980551, 'occupancy': 0.39391255081946785, 'mIoU': 0.6959882575587615}}  total_overall: {'no_occupancy': 0.9980672308350997, 'occupancy': 0.565107869669587, 'mIoU': 0.7815875502523434}  data_time: 0.0168  time: 0.1309
2024/07/04 20:34:27 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:34:27 - mmengine - INFO - Epoch(train)  [38][91/91]  lr: 7.8320e-05  eta: 1:27:21  time: 0.6069  data_time: 0.0007  memory: 2942  grad_norm: 1.3274  loss: 0.3034  loss_bce: 0.0147  loss_dice: 0.2887
2024/07/04 20:34:27 - mmengine - INFO - Saving checkpoint at 38 epochs
2024/07/04 20:34:42 - mmengine - INFO - Epoch(val) [38][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9968714741576261, 'occupancy': 0.6838299187054667, 'mIoU': 0.8403506964315464}, '1~2m': {'no_occupancy': 0.996549865761947, 'occupancy': 0.45754579850794125, 'mIoU': 0.7270478321349442}, '2~3m': {'no_occupancy': 0.9991744663566967, 'occupancy': 0.2911834567556239, 'mIoU': 0.6451789615561603}, '3~4m': {'no_occupancy': 0.9998683378861961, 'occupancy': 0.1581137309292649, 'mIoU': 0.5789910344077305}, 'patch_mean': {'no_occupancy': 0.9981160360406165, 'occupancy': 0.3976682262245742, 'mIoU': 0.6978921311325954}}  total_overall: {'no_occupancy': 0.9981191706451137, 'occupancy': 0.5709487633385211, 'mIoU': 0.7845339669918174}  data_time: 0.0156  time: 0.1326
2024/07/04 20:35:46 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:35:46 - mmengine - INFO - Epoch(train)  [39][91/91]  lr: 7.7232e-05  eta: 1:26:16  time: 0.6003  data_time: 0.0007  memory: 2942  grad_norm: 1.3281  loss: 0.3148  loss_bce: 0.0158  loss_dice: 0.2990
2024/07/04 20:35:46 - mmengine - INFO - Saving checkpoint at 39 epochs
2024/07/04 20:36:01 - mmengine - INFO - Epoch(val) [39][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9970311586734513, 'occupancy': 0.6964632333958961, 'mIoU': 0.8467471960346737}, '1~2m': {'no_occupancy': 0.9966012151132154, 'occupancy': 0.4597701149425288, 'mIoU': 0.7281856650278721}, '2~3m': {'no_occupancy': 0.9991893910468395, 'occupancy': 0.289091860520432, 'mIoU': 0.6441406257836357}, '3~4m': {'no_occupancy': 0.9998707781099093, 'occupancy': 0.15943562610229275, 'mIoU': 0.579653202106101}, 'patch_mean': {'no_occupancy': 0.9981731357358539, 'occupancy': 0.4011902087402874, 'mIoU': 0.6996816722380707}}  total_overall: {'no_occupancy': 0.9981761141363189, 'occupancy': 0.5788960081330916, 'mIoU': 0.7885360611347052}  data_time: 0.0177  time: 0.1320
2024/07/04 20:37:05 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:37:05 - mmengine - INFO - Epoch(train)  [40][91/91]  lr: 7.6125e-05  eta: 1:25:13  time: 0.6003  data_time: 0.0007  memory: 2942  grad_norm: 1.2347  loss: 0.3031  loss_bce: 0.0152  loss_dice: 0.2879
2024/07/04 20:37:05 - mmengine - INFO - Saving checkpoint at 40 epochs
2024/07/04 20:37:20 - mmengine - INFO - Epoch(val) [40][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9970480633146429, 'occupancy': 0.7006581770364729, 'mIoU': 0.8488531201755579}, '1~2m': {'no_occupancy': 0.9965963459699608, 'occupancy': 0.46389783268142104, 'mIoU': 0.7302470893256909}, '2~3m': {'no_occupancy': 0.9992010398794958, 'occupancy': 0.30247205910210273, 'mIoU': 0.6508365494907993}, '3~4m': {'no_occupancy': 0.9998706153039427, 'occupancy': 0.1645658263305322, 'mIoU': 0.5822182208172375}, 'patch_mean': {'no_occupancy': 0.9981790161170105, 'occupancy': 0.4078984737876323, 'mIoU': 0.7030387449523214}}  total_overall: {'no_occupancy': 0.9981820265853606, 'occupancy': 0.5837092190718491, 'mIoU': 0.7909456228286049}  data_time: 0.0166  time: 0.1324
2024/07/04 20:38:24 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:38:24 - mmengine - INFO - Epoch(train)  [41][91/91]  lr: 7.5000e-05  eta: 1:24:09  time: 0.6033  data_time: 0.0007  memory: 2942  grad_norm: 1.2167  loss: 0.3022  loss_bce: 0.0143  loss_dice: 0.2879
2024/07/04 20:38:24 - mmengine - INFO - Saving checkpoint at 41 epochs
2024/07/04 20:38:38 - mmengine - INFO - Epoch(val) [41][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9971252380366421, 'occupancy': 0.7071383624840516, 'mIoU': 0.8521318002603468}, '1~2m': {'no_occupancy': 0.9966860148916061, 'occupancy': 0.4760108570046492, 'mIoU': 0.7363484359481276}, '2~3m': {'no_occupancy': 0.999198647578788, 'occupancy': 0.3060409620443442, 'mIoU': 0.6526198048115661}, '3~4m': {'no_occupancy': 0.9998709948972221, 'occupancy': 0.16467696629213482, 'mIoU': 0.5822739805946785}, 'patch_mean': {'no_occupancy': 0.9982202238510645, 'occupancy': 0.41346678695629496, 'mIoU': 0.7058435054036797}}  total_overall: {'no_occupancy': 0.9982231728220404, 'occupancy': 0.5917094853250614, 'mIoU': 0.7949663290735509}  data_time: 0.0155  time: 0.1297
2024/07/04 20:39:43 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:39:43 - mmengine - INFO - Epoch(train)  [42][91/91]  lr: 7.3858e-05  eta: 1:23:06  time: 0.5975  data_time: 0.0007  memory: 2942  grad_norm: 1.0979  loss: 0.2912  loss_bce: 0.0144  loss_dice: 0.2768
2024/07/04 20:39:43 - mmengine - INFO - Saving checkpoint at 42 epochs
2024/07/04 20:39:57 - mmengine - INFO - Epoch(val) [42][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9971405392826462, 'occupancy': 0.7115060142225723, 'mIoU': 0.8543232767526092}, '1~2m': {'no_occupancy': 0.9966844718276816, 'occupancy': 0.477079596009852, 'mIoU': 0.7368820339187668}, '2~3m': {'no_occupancy': 0.9991824869180822, 'occupancy': 0.2987623301693653, 'mIoU': 0.6489724085437237}, '3~4m': {'no_occupancy': 0.9998712116290638, 'occupancy': 0.17218543046357615, 'mIoU': 0.58602832104632}, 'patch_mean': {'no_occupancy': 0.9982196774143685, 'occupancy': 0.4148833427163415, 'mIoU': 0.706551510065355}}  total_overall: {'no_occupancy': 0.9982226369449498, 'occupancy': 0.5944428916284781, 'mIoU': 0.796332764286714}  data_time: 0.0164  time: 0.1319
2024/07/04 20:41:02 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:41:02 - mmengine - INFO - Epoch(train)  [43][91/91]  lr: 7.2700e-05  eta: 1:22:02  time: 0.6031  data_time: 0.0007  memory: 2942  grad_norm: 1.1485  loss: 0.2878  loss_bce: 0.0143  loss_dice: 0.2735
2024/07/04 20:41:02 - mmengine - INFO - Saving checkpoint at 43 epochs
2024/07/04 20:41:17 - mmengine - INFO - Epoch(val) [43][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9972341092338969, 'occupancy': 0.7182180553295474, 'mIoU': 0.8577260822817221}, '1~2m': {'no_occupancy': 0.9967023705273954, 'occupancy': 0.47950581301763967, 'mIoU': 0.7381040917725176}, '2~3m': {'no_occupancy': 0.9992111164289198, 'occupancy': 0.31497880357983987, 'mIoU': 0.6570949600043798}, '3~4m': {'no_occupancy': 0.9998689885766281, 'occupancy': 0.1599443671766342, 'mIoU': 0.5799066778766312}, 'patch_mean': {'no_occupancy': 0.99825414619171, 'occupancy': 0.4181617597759153, 'mIoU': 0.7082079529838127}}  total_overall: {'no_occupancy': 0.99825701701477, 'occupancy': 0.5996798718863209, 'mIoU': 0.7989684444505454}  data_time: 0.0168  time: 0.1336
2024/07/04 20:42:17 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:42:20 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:42:20 - mmengine - INFO - Epoch(train)  [44][91/91]  lr: 7.1526e-05  eta: 1:20:57  time: 0.5983  data_time: 0.0007  memory: 2942  grad_norm: 1.1528  loss: 0.2650  loss_bce: 0.0125  loss_dice: 0.2524
2024/07/04 20:42:20 - mmengine - INFO - Saving checkpoint at 44 epochs
2024/07/04 20:42:35 - mmengine - INFO - Epoch(val) [44][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9972459367148061, 'occupancy': 0.7201727007906782, 'mIoU': 0.8587093187527421}, '1~2m': {'no_occupancy': 0.9967327079878918, 'occupancy': 0.4861987558779314, 'mIoU': 0.7414657319329117}, '2~3m': {'no_occupancy': 0.9992177230190521, 'occupancy': 0.3253017685037896, 'mIoU': 0.6622597457614209}, '3~4m': {'no_occupancy': 0.9998724046575934, 'occupancy': 0.171770503343893, 'mIoU': 0.5858214540007431}, 'patch_mean': {'no_occupancy': 0.9982671930948359, 'occupancy': 0.425860932129073, 'mIoU': 0.7120640626119544}}  total_overall: {'no_occupancy': 0.9982700798042616, 'occupancy': 0.6039385435961907, 'mIoU': 0.8011043117002261}  data_time: 0.0157  time: 0.1310
2024/07/04 20:43:38 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:43:38 - mmengine - INFO - Epoch(train)  [45][91/91]  lr: 7.0337e-05  eta: 1:19:53  time: 0.5997  data_time: 0.0007  memory: 2942  grad_norm: 1.1758  loss: 0.2655  loss_bce: 0.0128  loss_dice: 0.2527
2024/07/04 20:43:38 - mmengine - INFO - Saving checkpoint at 45 epochs
2024/07/04 20:43:53 - mmengine - INFO - Epoch(val) [45][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9974106429468216, 'occupancy': 0.7306157085388237, 'mIoU': 0.8640131757428227}, '1~2m': {'no_occupancy': 0.9967844826242835, 'occupancy': 0.4877933611306654, 'mIoU': 0.7422889218774744}, '2~3m': {'no_occupancy': 0.9992203458858752, 'occupancy': 0.31147305389221563, 'mIoU': 0.6553466998890454}, '3~4m': {'no_occupancy': 0.9998722419152478, 'occupancy': 0.17420259376095337, 'mIoU': 0.5870374178381006}, 'patch_mean': {'no_occupancy': 0.998321928343057, 'occupancy': 0.4260211793306645, 'mIoU': 0.7121715538368607}}  total_overall: {'no_occupancy': 0.9983246078795582, 'occupancy': 0.6088945418576823, 'mIoU': 0.8036095748686203}  data_time: 0.0175  time: 0.1329
2024/07/04 20:44:56 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:44:56 - mmengine - INFO - Epoch(train)  [46][91/91]  lr: 6.9134e-05  eta: 1:18:48  time: 0.6003  data_time: 0.0007  memory: 2942  grad_norm: 1.1333  loss: 0.2872  loss_bce: 0.0134  loss_dice: 0.2738
2024/07/04 20:44:56 - mmengine - INFO - Saving checkpoint at 46 epochs
2024/07/04 20:45:11 - mmengine - INFO - Epoch(val) [46][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9973969032654945, 'occupancy': 0.7331541367879403, 'mIoU': 0.8652755200267174}, '1~2m': {'no_occupancy': 0.9968191394392305, 'occupancy': 0.49682485049262143, 'mIoU': 0.7468219949659259}, '2~3m': {'no_occupancy': 0.9992208752701213, 'occupancy': 0.3216680834986304, 'mIoU': 0.6604444793843758}, '3~4m': {'no_occupancy': 0.9998728384226435, 'occupancy': 0.17429577464788734, 'mIoU': 0.5870843065352654}, 'patch_mean': {'no_occupancy': 0.9983274390993725, 'occupancy': 0.4314857113567699, 'mIoU': 0.7149065752280712}}  total_overall: {'no_occupancy': 0.9983301860870036, 'occupancy': 0.614713092308579, 'mIoU': 0.8065216391977913}  data_time: 0.0160  time: 0.1305
2024/07/04 20:46:15 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:46:15 - mmengine - INFO - Epoch(train)  [47][91/91]  lr: 6.7918e-05  eta: 1:17:44  time: 0.6052  data_time: 0.0007  memory: 2942  grad_norm: 1.0743  loss: 0.2551  loss_bce: 0.0132  loss_dice: 0.2419
2024/07/04 20:46:15 - mmengine - INFO - Saving checkpoint at 47 epochs
2024/07/04 20:46:30 - mmengine - INFO - Epoch(val) [47][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.997545839944972, 'occupancy': 0.7433227239430948, 'mIoU': 0.8704342819440334}, '1~2m': {'no_occupancy': 0.9968946569467005, 'occupancy': 0.49976616039753835, 'mIoU': 0.7483304086721194}, '2~3m': {'no_occupancy': 0.9992337308573752, 'occupancy': 0.325727923627685, 'mIoU': 0.6624808272425301}, '3~4m': {'no_occupancy': 0.9998717538958888, 'occupancy': 0.17278768800279817, 'mIoU': 0.5863297209493434}, 'patch_mean': {'no_occupancy': 0.9983864954112343, 'occupancy': 0.435401123992779, 'mIoU': 0.7168938097020067}}  total_overall: {'no_occupancy': 0.9983890504477705, 'occupancy': 0.6213605725245862, 'mIoU': 0.8098748114861783}  data_time: 0.0162  time: 0.1327
2024/07/04 20:47:34 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:47:34 - mmengine - INFO - Epoch(train)  [48][91/91]  lr: 6.6690e-05  eta: 1:16:40  time: 0.6021  data_time: 0.0007  memory: 2942  grad_norm: 1.1437  loss: 0.2665  loss_bce: 0.0135  loss_dice: 0.2529
2024/07/04 20:47:34 - mmengine - INFO - Saving checkpoint at 48 epochs
2024/07/04 20:47:49 - mmengine - INFO - Epoch(val) [48][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9975813266621915, 'occupancy': 0.7472602021490373, 'mIoU': 0.8724207644056143}, '1~2m': {'no_occupancy': 0.99689415157065, 'occupancy': 0.50572766071645, 'mIoU': 0.75131090614355}, '2~3m': {'no_occupancy': 0.9992315559180854, 'occupancy': 0.32903898072277743, 'mIoU': 0.6641352683204315}, '3~4m': {'no_occupancy': 0.9998728925459397, 'occupancy': 0.17869656622284516, 'mIoU': 0.5892847293843925}, 'patch_mean': {'no_occupancy': 0.9983949816742166, 'occupancy': 0.4401808524527775, 'mIoU': 0.719287917063497}}  total_overall: {'no_occupancy': 0.9983975323578788, 'occupancy': 0.6253927715341672, 'mIoU': 0.8118951519460229}  data_time: 0.0172  time: 0.1314
2024/07/04 20:48:52 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:48:52 - mmengine - INFO - Epoch(train)  [49][91/91]  lr: 6.5451e-05  eta: 1:15:35  time: 0.6007  data_time: 0.0007  memory: 2942  grad_norm: 1.1154  loss: 0.2721  loss_bce: 0.0136  loss_dice: 0.2585
2024/07/04 20:48:52 - mmengine - INFO - Saving checkpoint at 49 epochs
2024/07/04 20:49:07 - mmengine - INFO - Epoch(val) [49][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.997626277577104, 'occupancy': 0.7511162551661246, 'mIoU': 0.8743712663716143}, '1~2m': {'no_occupancy': 0.996970253181604, 'occupancy': 0.5144078713623395, 'mIoU': 0.7556890622719717}, '2~3m': {'no_occupancy': 0.9992413163925459, 'occupancy': 0.3347602739726027, 'mIoU': 0.6670007951825743}, '3~4m': {'no_occupancy': 0.9998743024204594, 'occupancy': 0.1812080536912752, 'mIoU': 0.5905411780558673}, 'patch_mean': {'no_occupancy': 0.9984280373929284, 'occupancy': 0.4453731135480855, 'mIoU': 0.721900575470507}}  total_overall: {'no_occupancy': 0.9984305481199487, 'occupancy': 0.6313275401560421, 'mIoU': 0.8148790441379954}  data_time: 0.0168  time: 0.1328
2024/07/04 20:50:11 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:50:11 - mmengine - INFO - Epoch(train)  [50][91/91]  lr: 6.4201e-05  eta: 1:14:31  time: 0.6022  data_time: 0.0007  memory: 2942  grad_norm: 1.0333  loss: 0.2583  loss_bce: 0.0126  loss_dice: 0.2457
2024/07/04 20:50:11 - mmengine - INFO - Saving checkpoint at 50 epochs
2024/07/04 20:50:26 - mmengine - INFO - Epoch(val) [50][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9977067142345911, 'occupancy': 0.7573789151566495, 'mIoU': 0.8775428146956203}, '1~2m': {'no_occupancy': 0.9970074580724816, 'occupancy': 0.5156456355984435, 'mIoU': 0.7563265468354625}, '2~3m': {'no_occupancy': 0.9992344846856527, 'occupancy': 0.33026434435954627, 'mIoU': 0.6647494145225995}, '3~4m': {'no_occupancy': 0.9998730010407192, 'occupancy': 0.1770906535488405, 'mIoU': 0.5884818272947798}, 'patch_mean': {'no_occupancy': 0.9984554145083612, 'occupancy': 0.44509488716587, 'mIoU': 0.7217751508371155}}  total_overall: {'no_occupancy': 0.9984578177696843, 'occupancy': 0.6346786758310469, 'mIoU': 0.8165682468003657}  data_time: 0.0162  time: 0.1310
2024/07/04 20:51:29 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:51:29 - mmengine - INFO - Epoch(train)  [51][91/91]  lr: 6.2941e-05  eta: 1:13:27  time: 0.6023  data_time: 0.0007  memory: 2942  grad_norm: 1.0546  loss: 0.2781  loss_bce: 0.0132  loss_dice: 0.2649
2024/07/04 20:51:29 - mmengine - INFO - Saving checkpoint at 51 epochs
2024/07/04 20:51:45 - mmengine - INFO - Epoch(val) [51][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9977425663770417, 'occupancy': 0.7631912175054002, 'mIoU': 0.880466891941221}, '1~2m': {'no_occupancy': 0.9970319362460364, 'occupancy': 0.5234167450118418, 'mIoU': 0.7602243406289391}, '2~3m': {'no_occupancy': 0.9992373536676757, 'occupancy': 0.33571158571158577, 'mIoU': 0.6674744696896308}, '3~4m': {'no_occupancy': 0.9998746819464677, 'occupancy': 0.18425697140840103, 'mIoU': 0.5920658266774343}, 'patch_mean': {'no_occupancy': 0.9984716345593054, 'occupancy': 0.4516441299093072, 'mIoU': 0.7250578822343063}}  total_overall: {'no_occupancy': 0.998474043348459, 'occupancy': 0.6413521100211776, 'mIoU': 0.8199130766848184}  data_time: 0.0159  time: 0.1332
2024/07/04 20:52:49 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:52:49 - mmengine - INFO - Epoch(train)  [52][91/91]  lr: 6.1672e-05  eta: 1:12:23  time: 0.6004  data_time: 0.0007  memory: 2942  grad_norm: 1.0939  loss: 0.2716  loss_bce: 0.0135  loss_dice: 0.2581
2024/07/04 20:52:49 - mmengine - INFO - Saving checkpoint at 52 epochs
2024/07/04 20:53:03 - mmengine - INFO - Epoch(val) [52][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9977814181094988, 'occupancy': 0.7661231525498142, 'mIoU': 0.8819522853296565}, '1~2m': {'no_occupancy': 0.9970607619565433, 'occupancy': 0.5272809002527559, 'mIoU': 0.7621708311046496}, '2~3m': {'no_occupancy': 0.9992437016955958, 'occupancy': 0.3366322500832659, 'mIoU': 0.6679379758894308}, '3~4m': {'no_occupancy': 0.9998737061043985, 'occupancy': 0.1729403409090909, 'mIoU': 0.5864070235067447}, 'patch_mean': {'no_occupancy': 0.9984898969665091, 'occupancy': 0.4507441609487317, 'mIoU': 0.7246170289576204}}  total_overall: {'no_occupancy': 0.9984922674462884, 'occupancy': 0.6442683611289287, 'mIoU': 0.8213803142876086}  data_time: 0.0163  time: 0.1337
2024/07/04 20:54:08 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:54:08 - mmengine - INFO - Epoch(train)  [53][91/91]  lr: 6.0396e-05  eta: 1:11:21  time: 0.6031  data_time: 0.0007  memory: 2942  grad_norm: 1.0790  loss: 0.2544  loss_bce: 0.0126  loss_dice: 0.2419
2024/07/04 20:54:08 - mmengine - INFO - Saving checkpoint at 53 epochs
2024/07/04 20:54:23 - mmengine - INFO - Epoch(val) [53][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9978260863776913, 'occupancy': 0.7690911451024928, 'mIoU': 0.8834586157400921}, '1~2m': {'no_occupancy': 0.9970461193473003, 'occupancy': 0.5274791023928358, 'mIoU': 0.7622626108700681}, '2~3m': {'no_occupancy': 0.9992634406219388, 'occupancy': 0.3478072914164946, 'mIoU': 0.6735353660192167}, '3~4m': {'no_occupancy': 0.9998736516653417, 'occupancy': 0.18188202247191013, 'mIoU': 0.590877837068626}, 'patch_mean': {'no_occupancy': 0.9985023245030681, 'occupancy': 0.45656489034593334, 'mIoU': 0.7275336074245007}}  total_overall: {'no_occupancy': 0.9985046583180264, 'occupancy': 0.6462215740182036, 'mIoU': 0.822363116168115}  data_time: 0.0168  time: 0.1317
2024/07/04 20:55:26 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:55:26 - mmengine - INFO - Epoch(train)  [54][91/91]  lr: 5.9112e-05  eta: 1:10:16  time: 0.6034  data_time: 0.0007  memory: 2942  grad_norm: inf  loss: 0.2423  loss_bce: 0.0126  loss_dice: 0.2297
2024/07/04 20:55:26 - mmengine - INFO - Saving checkpoint at 54 epochs
2024/07/04 20:55:42 - mmengine - INFO - Epoch(val) [54][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9978687843052922, 'occupancy': 0.7749920884307988, 'mIoU': 0.8864304363680455}, '1~2m': {'no_occupancy': 0.9971290314044351, 'occupancy': 0.5395288412137951, 'mIoU': 0.768328936309115}, '2~3m': {'no_occupancy': 0.9992528533483435, 'occupancy': 0.3515842003672144, 'mIoU': 0.6754185268577789}, '3~4m': {'no_occupancy': 0.9998751157267259, 'occupancy': 0.18621908127208478, 'mIoU': 0.5930470984994054}, 'patch_mean': {'no_occupancy': 0.9985314461961992, 'occupancy': 0.4630810528209733, 'mIoU': 0.7308062495085862}}  total_overall: {'no_occupancy': 0.99853375259195, 'occupancy': 0.6543681690404873, 'mIoU': 0.8264509608162187}  data_time: 0.0164  time: 0.1327
2024/07/04 20:56:43 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:56:46 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:56:46 - mmengine - INFO - Epoch(train)  [55][91/91]  lr: 5.7822e-05  eta: 1:09:13  time: 0.6069  data_time: 0.0007  memory: 2942  grad_norm: 1.0164  loss: 0.2384  loss_bce: 0.0120  loss_dice: 0.2265
2024/07/04 20:56:46 - mmengine - INFO - Saving checkpoint at 55 epochs
2024/07/04 20:57:00 - mmengine - INFO - Epoch(val) [55][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9979549534663115, 'occupancy': 0.781705307485491, 'mIoU': 0.8898301304759013}, '1~2m': {'no_occupancy': 0.9971924032468084, 'occupancy': 0.5446959536960243, 'mIoU': 0.7709441784714164}, '2~3m': {'no_occupancy': 0.999274063293739, 'occupancy': 0.3587310714970289, 'mIoU': 0.6790025673953839}, '3~4m': {'no_occupancy': 0.9998748989213653, 'occupancy': 0.18162468960624334, 'mIoU': 0.5907497942638043}, 'patch_mean': {'no_occupancy': 0.9985740797320559, 'occupancy': 0.4666892555711969, 'mIoU': 0.7326316676516265}}  total_overall: {'no_occupancy': 0.9985762873464354, 'occupancy': 0.6605508043857246, 'mIoU': 0.8295635458660799}  data_time: 0.0167  time: 0.1314
2024/07/04 20:58:04 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:58:04 - mmengine - INFO - Epoch(train)  [56][91/91]  lr: 5.6526e-05  eta: 1:08:09  time: 0.6025  data_time: 0.0007  memory: 2942  grad_norm: 1.0332  loss: 0.2366  loss_bce: 0.0122  loss_dice: 0.2244
2024/07/04 20:58:04 - mmengine - INFO - Saving checkpoint at 56 epochs
2024/07/04 20:58:19 - mmengine - INFO - Epoch(val) [56][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9979855131197083, 'occupancy': 0.7852237304373274, 'mIoU': 0.8916046217785178}, '1~2m': {'no_occupancy': 0.9971918950093396, 'occupancy': 0.5476334472784644, 'mIoU': 0.772412671143902}, '2~3m': {'no_occupancy': 0.9992817036615593, 'occupancy': 0.3678204822153259, 'mIoU': 0.6835510929384426}, '3~4m': {'no_occupancy': 0.9998757122752187, 'occupancy': 0.18463180362860196, 'mIoU': 0.5922537579519103}, 'patch_mean': {'no_occupancy': 0.9985837060164564, 'occupancy': 0.4713273658899299, 'mIoU': 0.7349555359531932}}  total_overall: {'no_occupancy': 0.9985859023673711, 'occupancy': 0.6639606207322638, 'mIoU': 0.8312732615498175}  data_time: 0.0163  time: 0.1331
2024/07/04 20:59:23 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 20:59:23 - mmengine - INFO - Epoch(train)  [57][91/91]  lr: 5.5226e-05  eta: 1:07:05  time: 0.5987  data_time: 0.0007  memory: 2942  grad_norm: 1.0512  loss: 0.2233  loss_bce: 0.0114  loss_dice: 0.2118
2024/07/04 20:59:23 - mmengine - INFO - Saving checkpoint at 57 epochs
2024/07/04 20:59:38 - mmengine - INFO - Epoch(val) [57][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9980195947421752, 'occupancy': 0.7883708491944835, 'mIoU': 0.8931952219683293}, '1~2m': {'no_occupancy': 0.9971983947602545, 'occupancy': 0.5484420132970124, 'mIoU': 0.7728202040286334}, '2~3m': {'no_occupancy': 0.9992700547388426, 'occupancy': 0.35298360340433727, 'mIoU': 0.6761268290715899}, '3~4m': {'no_occupancy': 0.9998748446403357, 'occupancy': 0.18387553041018387, 'mIoU': 0.5918751875252598}, 'patch_mean': {'no_occupancy': 0.9985907222204019, 'occupancy': 0.4684179990765042, 'mIoU': 0.733504360648453}}  total_overall: {'no_occupancy': 0.9985928776281066, 'occupancy': 0.6649809323216772, 'mIoU': 0.8317869049748918}  data_time: 0.0180  time: 0.1335
2024/07/04 21:00:43 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:00:43 - mmengine - INFO - Epoch(train)  [58][91/91]  lr: 5.3923e-05  eta: 1:06:03  time: 0.6075  data_time: 0.0007  memory: 2942  grad_norm: 1.0228  loss: 0.2280  loss_bce: 0.0120  loss_dice: 0.2160
2024/07/04 21:00:43 - mmengine - INFO - Saving checkpoint at 58 epochs
2024/07/04 21:00:58 - mmengine - INFO - Epoch(val) [58][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9980441302329951, 'occupancy': 0.7905001312843706, 'mIoU': 0.8942721307586828}, '1~2m': {'no_occupancy': 0.9972443855448778, 'occupancy': 0.5530519311467743, 'mIoU': 0.7751481583458261}, '2~3m': {'no_occupancy': 0.9992730903527536, 'occupancy': 0.3556762994662691, 'mIoU': 0.6774746949095114}, '3~4m': {'no_occupancy': 0.9998755494734768, 'occupancy': 0.18961864406779663, 'mIoU': 0.5947470967706368}, 'patch_mean': {'no_occupancy': 0.9986092889010258, 'occupancy': 0.4722117514913027, 'mIoU': 0.7354105201961643}}  total_overall: {'no_occupancy': 0.9986114136815475, 'occupancy': 0.6681977147158866, 'mIoU': 0.8334045641987171}  data_time: 0.0163  time: 0.1299
2024/07/04 21:02:02 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:02:02 - mmengine - INFO - Epoch(train)  [59][91/91]  lr: 5.2617e-05  eta: 1:04:59  time: 0.6020  data_time: 0.0007  memory: 2942  grad_norm: 1.0753  loss: 0.2347  loss_bce: 0.0122  loss_dice: 0.2225
2024/07/04 21:02:02 - mmengine - INFO - Saving checkpoint at 59 epochs
2024/07/04 21:02:17 - mmengine - INFO - Epoch(val) [59][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9980579740384091, 'occupancy': 0.7925841326518707, 'mIoU': 0.8953210533451399}, '1~2m': {'no_occupancy': 0.9972462852690065, 'occupancy': 0.5567303439184722, 'mIoU': 0.7769883145937393}, '2~3m': {'no_occupancy': 0.9992810036655033, 'occupancy': 0.3635228582404918, 'mIoU': 0.6814019309529975}, '3~4m': {'no_occupancy': 0.9998757121606433, 'occupancy': 0.18953323903818955, 'mIoU': 0.5947044755994164}, 'patch_mean': {'no_occupancy': 0.9986152437833905, 'occupancy': 0.47559264346225605, 'mIoU': 0.7371039436228233}}  total_overall: {'no_occupancy': 0.9986173781351738, 'occupancy': 0.6710919498411313, 'mIoU': 0.8348546639881526}  data_time: 0.0164  time: 0.1325
2024/07/04 21:03:21 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:03:21 - mmengine - INFO - Epoch(train)  [60][91/91]  lr: 5.1309e-05  eta: 1:03:55  time: 0.6031  data_time: 0.0007  memory: 2942  grad_norm: 1.0615  loss: 0.2080  loss_bce: 0.0112  loss_dice: 0.1968
2024/07/04 21:03:21 - mmengine - INFO - Saving checkpoint at 60 epochs
2024/07/04 21:03:36 - mmengine - INFO - Epoch(val) [60][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9981359070329059, 'occupancy': 0.7997953817889792, 'mIoU': 0.8989656444109426}, '1~2m': {'no_occupancy': 0.9973033781880113, 'occupancy': 0.5689761649513231, 'mIoU': 0.7831397715696672}, '2~3m': {'no_occupancy': 0.9992901564747929, 'occupancy': 0.37823711095272033, 'mIoU': 0.6887636337137566}, '3~4m': {'no_occupancy': 0.9998767966633587, 'occupancy': 0.1923213650906505, 'mIoU': 0.5960990808770046}, 'patch_mean': {'no_occupancy': 0.9986515595897673, 'occupancy': 0.4848325056959183, 'mIoU': 0.7417420326428428}}  total_overall: {'no_occupancy': 0.9986536360351151, 'occupancy': 0.6797778763090024, 'mIoU': 0.8392157561720588}  data_time: 0.0160  time: 0.1309
2024/07/04 21:04:40 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:04:40 - mmengine - INFO - Epoch(train)  [61][91/91]  lr: 5.0000e-05  eta: 1:02:51  time: 0.6006  data_time: 0.0007  memory: 2942  grad_norm: 0.9530  loss: 0.2131  loss_bce: 0.0112  loss_dice: 0.2019
2024/07/04 21:04:40 - mmengine - INFO - Saving checkpoint at 61 epochs
2024/07/04 21:04:55 - mmengine - INFO - Epoch(val) [61][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.998178929388839, 'occupancy': 0.8028417869842608, 'mIoU': 0.9005103581865499}, '1~2m': {'no_occupancy': 0.9973836642921424, 'occupancy': 0.5729777377128895, 'mIoU': 0.785180701002516}, '2~3m': {'no_occupancy': 0.9992951508679851, 'occupancy': 0.3771631273668568, 'mIoU': 0.688229139117421}, '3~4m': {'no_occupancy': 0.9998769594439023, 'occupancy': 0.18819320214669052, 'mIoU': 0.5940350807952964}, 'patch_mean': {'no_occupancy': 0.9986836759982172, 'occupancy': 0.4852939635526744, 'mIoU': 0.7419888197754458}}  total_overall: {'no_occupancy': 0.9986856737340937, 'occupancy': 0.6833677286290996, 'mIoU': 0.8410267011815966}  data_time: 0.0169  time: 0.1325
2024/07/04 21:05:59 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:05:59 - mmengine - INFO - Epoch(train)  [62][91/91]  lr: 4.8691e-05  eta: 1:01:47  time: 0.6032  data_time: 0.0007  memory: 2942  grad_norm: 0.9851  loss: 0.2059  loss_bce: 0.0108  loss_dice: 0.1951
2024/07/04 21:05:59 - mmengine - INFO - Saving checkpoint at 62 epochs
2024/07/04 21:06:13 - mmengine - INFO - Epoch(val) [62][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9982049593144348, 'occupancy': 0.8059182572010581, 'mIoU': 0.9020616082577464}, '1~2m': {'no_occupancy': 0.997390522479953, 'occupancy': 0.5766475233626295, 'mIoU': 0.7870190229212912}, '2~3m': {'no_occupancy': 0.9992785978737573, 'occupancy': 0.3771720294131423, 'mIoU': 0.6882253136434497}, '3~4m': {'no_occupancy': 0.9998759289871316, 'occupancy': 0.1932299012693935, 'mIoU': 0.5965529151282626}, 'patch_mean': {'no_occupancy': 0.9986875021638192, 'occupancy': 0.48824192781155584, 'mIoU': 0.7434647149876875}}  total_overall: {'no_occupancy': 0.9986894753830315, 'occupancy': 0.6857554186669207, 'mIoU': 0.8422224470249762}  data_time: 0.0168  time: 0.1320
2024/07/04 21:07:17 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:07:17 - mmengine - INFO - Epoch(train)  [63][91/91]  lr: 4.7383e-05  eta: 1:00:43  time: 0.6082  data_time: 0.0007  memory: 2942  grad_norm: 1.0025  loss: 0.2017  loss_bce: 0.0106  loss_dice: 0.1911
2024/07/04 21:07:17 - mmengine - INFO - Saving checkpoint at 63 epochs
2024/07/04 21:07:33 - mmengine - INFO - Epoch(val) [63][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9982754230549253, 'occupancy': 0.8126916809627752, 'mIoU': 0.9054835520088502}, '1~2m': {'no_occupancy': 0.9974300471873504, 'occupancy': 0.580816671160211, 'mIoU': 0.7891233591737807}, '2~3m': {'no_occupancy': 0.999300790081711, 'occupancy': 0.3811101507730722, 'mIoU': 0.6902054704273917}, '3~4m': {'no_occupancy': 0.9998772305245262, 'occupancy': 0.1908506075768406, 'mIoU': 0.5953639190506834}, 'patch_mean': {'no_occupancy': 0.9987208727121283, 'occupancy': 0.49136727761822474, 'mIoU': 0.7450440751651766}}  total_overall: {'no_occupancy': 0.9987227795333308, 'occupancy': 0.6917956297051446, 'mIoU': 0.8452592046192378}  data_time: 0.0170  time: 0.1342
2024/07/04 21:08:37 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:08:37 - mmengine - INFO - Epoch(train)  [64][91/91]  lr: 4.6077e-05  eta: 0:59:39  time: 0.6033  data_time: 0.0007  memory: 2942  grad_norm: inf  loss: 0.1944  loss_bce: 0.0103  loss_dice: 0.1842
2024/07/04 21:08:37 - mmengine - INFO - Saving checkpoint at 64 epochs
2024/07/04 21:08:51 - mmengine - INFO - Epoch(val) [64][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9983033967166579, 'occupancy': 0.8154633952857531, 'mIoU': 0.9068833960012055}, '1~2m': {'no_occupancy': 0.9974713467080383, 'occupancy': 0.5857815416330057, 'mIoU': 0.791626444170522}, '2~3m': {'no_occupancy': 0.9993045844264019, 'occupancy': 0.38479627585544934, 'mIoU': 0.6920504301409256}, '3~4m': {'no_occupancy': 0.9998786402239007, 'occupancy': 0.20128479657387582, 'mIoU': 0.6005817183988883}, 'patch_mean': {'no_occupancy': 0.9987394920187497, 'occupancy': 0.49683150233702095, 'mIoU': 0.7477854971778853}}  total_overall: {'no_occupancy': 0.9987413682406421, 'occupancy': 0.695570483702374, 'mIoU': 0.847155925971508}  data_time: 0.0163  time: 0.1319
2024/07/04 21:09:55 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:09:55 - mmengine - INFO - Epoch(train)  [65][91/91]  lr: 4.4774e-05  eta: 0:58:35  time: 0.6001  data_time: 0.0007  memory: 2942  grad_norm: 0.9342  loss: 0.1970  loss_bce: 0.0102  loss_dice: 0.1868
2024/07/04 21:09:55 - mmengine - INFO - Saving checkpoint at 65 epochs
2024/07/04 21:10:10 - mmengine - INFO - Epoch(val) [65][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.998317750274691, 'occupancy': 0.8171570363013817, 'mIoU': 0.9077373932880364}, '1~2m': {'no_occupancy': 0.9974878093297773, 'occupancy': 0.5895416467993966, 'mIoU': 0.7935147280645869}, '2~3m': {'no_occupancy': 0.9993066428346955, 'occupancy': 0.38788314176245203, 'mIoU': 0.6935948922985737}, '3~4m': {'no_occupancy': 0.999877881047464, 'occupancy': 0.2002840909090909, 'mIoU': 0.6000809859782774}, 'patch_mean': {'no_occupancy': 0.998747520871657, 'occupancy': 0.49871647894308024, 'mIoU': 0.7487319999073686}}  total_overall: {'no_occupancy': 0.9987493902402853, 'occupancy': 0.6979743390006641, 'mIoU': 0.8483618646204747}  data_time: 0.0151  time: 0.1321
2024/07/04 21:11:11 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:11:14 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:11:14 - mmengine - INFO - Epoch(train)  [66][91/91]  lr: 4.3474e-05  eta: 0:57:31  time: 0.6059  data_time: 0.0007  memory: 2942  grad_norm: 0.9325  loss: 0.2023  loss_bce: 0.0107  loss_dice: 0.1916
2024/07/04 21:11:14 - mmengine - INFO - Saving checkpoint at 66 epochs
2024/07/04 21:11:29 - mmengine - INFO - Epoch(val) [66][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9983452457618147, 'occupancy': 0.820240682118139, 'mIoU': 0.9092929639399769}, '1~2m': {'no_occupancy': 0.9975011988008887, 'occupancy': 0.593329701913581, 'mIoU': 0.7954154503572348}, '2~3m': {'no_occupancy': 0.999293734011842, 'occupancy': 0.3816376935499192, 'mIoU': 0.6904657137808806}, '3~4m': {'no_occupancy': 0.9998780437678089, 'occupancy': 0.1987887424296402, 'mIoU': 0.5993333930987246}, 'patch_mean': {'no_occupancy': 0.9987545555855886, 'occupancy': 0.4984992050028199, 'mIoU': 0.7486268802942042}}  total_overall: {'no_occupancy': 0.9987563987484701, 'occupancy': 0.7003478656120816, 'mIoU': 0.8495521321802759}  data_time: 0.0165  time: 0.1319
2024/07/04 21:12:33 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:12:33 - mmengine - INFO - Epoch(train)  [67][91/91]  lr: 4.2178e-05  eta: 0:56:27  time: 0.6046  data_time: 0.0007  memory: 2942  grad_norm: 0.8103  loss: 0.1978  loss_bce: 0.0109  loss_dice: 0.1869
2024/07/04 21:12:33 - mmengine - INFO - Saving checkpoint at 67 epochs
2024/07/04 21:12:48 - mmengine - INFO - Epoch(val) [67][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9983948311042852, 'occupancy': 0.8250398810751179, 'mIoU': 0.9117173560897016}, '1~2m': {'no_occupancy': 0.997530972826898, 'occupancy': 0.5973678751188428, 'mIoU': 0.7974494239728704}, '2~3m': {'no_occupancy': 0.9993128777111114, 'occupancy': 0.3929838013994058, 'mIoU': 0.6961483395552586}, '3~4m': {'no_occupancy': 0.9998791824534747, 'occupancy': 0.20371694067190854, 'mIoU': 0.6017980615626917}, 'patch_mean': {'no_occupancy': 0.9987794660239424, 'occupancy': 0.5047771245663187, 'mIoU': 0.7517782952951306}}  total_overall: {'no_occupancy': 0.9987812650468509, 'occupancy': 0.7053833847832569, 'mIoU': 0.852082324915054}  data_time: 0.0163  time: 0.1321
2024/07/04 21:13:52 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:13:52 - mmengine - INFO - Epoch(train)  [68][91/91]  lr: 4.0888e-05  eta: 0:55:23  time: 0.5941  data_time: 0.0007  memory: 2942  grad_norm: 0.8643  loss: 0.1988  loss_bce: 0.0107  loss_dice: 0.1881
2024/07/04 21:13:52 - mmengine - INFO - Saving checkpoint at 68 epochs
2024/07/04 21:14:06 - mmengine - INFO - Epoch(val) [68][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9984329434552945, 'occupancy': 0.8287629471472058, 'mIoU': 0.9135979453012502}, '1~2m': {'no_occupancy': 0.9975583515760386, 'occupancy': 0.601048226185646, 'mIoU': 0.7993032888808422}, '2~3m': {'no_occupancy': 0.9993307764029894, 'occupancy': 0.40218076084322757, 'mIoU': 0.7007557686231085}, '3~4m': {'no_occupancy': 0.999879236673832, 'occupancy': 0.20407433881343817, 'mIoU': 0.6019767877436351}, 'patch_mean': {'no_occupancy': 0.9988003270270387, 'occupancy': 0.5090165682473794, 'mIoU': 0.7539084476372091}}  total_overall: {'no_occupancy': 0.9988020934682725, 'occupancy': 0.709581524206793, 'mIoU': 0.8541918088375328}  data_time: 0.0161  time: 0.1309
2024/07/04 21:15:10 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:15:10 - mmengine - INFO - Epoch(train)  [69][91/91]  lr: 3.9604e-05  eta: 0:54:19  time: 0.6030  data_time: 0.0007  memory: 2942  grad_norm: 0.7923  loss: 0.1834  loss_bce: 0.0097  loss_dice: 0.1737
2024/07/04 21:15:10 - mmengine - INFO - Saving checkpoint at 69 epochs
2024/07/04 21:15:25 - mmengine - INFO - Epoch(val) [69][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9984502858105085, 'occupancy': 0.8303935946135436, 'mIoU': 0.914421940212026}, '1~2m': {'no_occupancy': 0.9976118324046421, 'occupancy': 0.6081458055888272, 'mIoU': 0.8028788189967346}, '2~3m': {'no_occupancy': 0.9993252419144445, 'occupancy': 0.4010113171201542, 'mIoU': 0.7001682795172993}, '3~4m': {'no_occupancy': 0.9998789655261608, 'occupancy': 0.2042780748663102, 'mIoU': 0.6020785201962355}, 'patch_mean': {'no_occupancy': 0.9988165814139391, 'occupancy': 0.5109571980472087, 'mIoU': 0.7548868897305739}}  total_overall: {'no_occupancy': 0.9988183226012644, 'occupancy': 0.7129520112853426, 'mIoU': 0.8558851669433034}  data_time: 0.0157  time: 0.1325
2024/07/04 21:16:29 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:16:29 - mmengine - INFO - Epoch(train)  [70][91/91]  lr: 3.8328e-05  eta: 0:53:16  time: 0.6073  data_time: 0.0007  memory: 2942  grad_norm: 0.8260  loss: 0.1896  loss_bce: 0.0103  loss_dice: 0.1793
2024/07/04 21:16:29 - mmengine - INFO - Saving checkpoint at 70 epochs
2024/07/04 21:16:45 - mmengine - INFO - Epoch(val) [70][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9984904835554416, 'occupancy': 0.8344091918474498, 'mIoU': 0.9164498377014456}, '1~2m': {'no_occupancy': 0.9976192669625451, 'occupancy': 0.60966275290186, 'mIoU': 0.8036410099322026}, '2~3m': {'no_occupancy': 0.999337608084892, 'occupancy': 0.40768409818569906, 'mIoU': 0.7035108531352956}, '3~4m': {'no_occupancy': 0.9998789654736542, 'occupancy': 0.20654105936722358, 'mIoU': 0.6032100124204389}, 'patch_mean': {'no_occupancy': 0.9988315810191332, 'occupancy': 0.5145742755755581, 'mIoU': 0.7567029282973456}}  total_overall: {'no_occupancy': 0.9988332866657402, 'occupancy': 0.7161678087600644, 'mIoU': 0.8575005477129023}  data_time: 0.0167  time: 0.1352
2024/07/04 21:17:49 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:17:49 - mmengine - INFO - Epoch(train)  [71][91/91]  lr: 3.7059e-05  eta: 0:52:12  time: 0.6040  data_time: 0.0007  memory: 2942  grad_norm: 0.8109  loss: 0.1853  loss_bce: 0.0099  loss_dice: 0.1754
2024/07/04 21:17:49 - mmengine - INFO - Saving checkpoint at 71 epochs
2024/07/04 21:18:03 - mmengine - INFO - Epoch(val) [71][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9985085915390973, 'occupancy': 0.8359573013619394, 'mIoU': 0.9172329464505183}, '1~2m': {'no_occupancy': 0.997645140516422, 'occupancy': 0.6128042436947531, 'mIoU': 0.8052246921055874}, '2~3m': {'no_occupancy': 0.9993306657976571, 'occupancy': 0.4038173471853104, 'mIoU': 0.7015740064914837}, '3~4m': {'no_occupancy': 0.9998797245333917, 'occupancy': 0.212637557685481, 'mIoU': 0.6062586411094364}, 'patch_mean': {'no_occupancy': 0.998841030596642, 'occupancy': 0.5163041124818709, 'mIoU': 0.7575725715392565}}  total_overall: {'no_occupancy': 0.9988427086266346, 'occupancy': 0.7178260285825105, 'mIoU': 0.8583343686045726}  data_time: 0.0164  time: 0.1309
2024/07/04 21:19:07 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:19:07 - mmengine - INFO - Epoch(train)  [72][91/91]  lr: 3.5799e-05  eta: 0:51:08  time: 0.5976  data_time: 0.0007  memory: 2942  grad_norm: 0.8153  loss: 0.1910  loss_bce: 0.0099  loss_dice: 0.1811
2024/07/04 21:19:07 - mmengine - INFO - Saving checkpoint at 72 epochs
2024/07/04 21:19:22 - mmengine - INFO - Epoch(val) [72][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9985191800483543, 'occupancy': 0.837566349636833, 'mIoU': 0.9180427648425936}, '1~2m': {'no_occupancy': 0.9976745852932427, 'occupancy': 0.6176517966811569, 'mIoU': 0.8076631909871999}, '2~3m': {'no_occupancy': 0.9993340790924162, 'occupancy': 0.4085762466875452, 'mIoU': 0.7039551628899807}, '3~4m': {'no_occupancy': 0.9998798330655697, 'occupancy': 0.20941848019978596, 'mIoU': 0.6046491566326778}, 'patch_mean': {'no_occupancy': 0.9988519193748958, 'occupancy': 0.5183032183013303, 'mIoU': 0.7585775688381131}}  total_overall: {'no_occupancy': 0.9988535925948443, 'occupancy': 0.7209415950438013, 'mIoU': 0.8598975938193227}  data_time: 0.0175  time: 0.1333
2024/07/04 21:20:26 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:20:26 - mmengine - INFO - Epoch(train)  [73][91/91]  lr: 3.4549e-05  eta: 0:50:04  time: 0.5969  data_time: 0.0007  memory: 2942  grad_norm: 0.8382  loss: 0.1744  loss_bce: 0.0093  loss_dice: 0.1650
2024/07/04 21:20:26 - mmengine - INFO - Saving checkpoint at 73 epochs
2024/07/04 21:20:40 - mmengine - INFO - Epoch(val) [73][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9985541802949405, 'occupancy': 0.8409981154453114, 'mIoU': 0.919776147870126}, '1~2m': {'no_occupancy': 0.9976799222808935, 'occupancy': 0.6186682620534639, 'mIoU': 0.8081740921671787}, '2~3m': {'no_occupancy': 0.9993397769010125, 'occupancy': 0.40942398214199066, 'mIoU': 0.7043818795215016}, '3~4m': {'no_occupancy': 0.9998804837177503, 'occupancy': 0.21341898643825838, 'mIoU': 0.6066497350780043}, 'patch_mean': {'no_occupancy': 0.9988635907986492, 'occupancy': 0.5206273365197561, 'mIoU': 0.7597454636592027}}  total_overall: {'no_occupancy': 0.9988652296609559, 'occupancy': 0.7232821880701207, 'mIoU': 0.8610737088655382}  data_time: 0.0157  time: 0.1297
2024/07/04 21:21:44 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:21:44 - mmengine - INFO - Epoch(train)  [74][91/91]  lr: 3.3310e-05  eta: 0:49:00  time: 0.5998  data_time: 0.0007  memory: 2942  grad_norm: 0.8527  loss: 0.1736  loss_bce: 0.0096  loss_dice: 0.1640
2024/07/04 21:21:44 - mmengine - INFO - Saving checkpoint at 74 epochs
2024/07/04 21:21:59 - mmengine - INFO - Epoch(val) [74][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9985944979691762, 'occupancy': 0.8449920191390012, 'mIoU': 0.9217932585540887}, '1~2m': {'no_occupancy': 0.9977040262421651, 'occupancy': 0.6226497590213247, 'mIoU': 0.810176892631745}, '2~3m': {'no_occupancy': 0.9993315858325489, 'occupancy': 0.4059019239114712, 'mIoU': 0.70261675487201}, '3~4m': {'no_occupancy': 0.9998805921587767, 'occupancy': 0.2141327623126338, 'mIoU': 0.6070066772357052}, 'patch_mean': {'no_occupancy': 0.9988776755506668, 'occupancy': 0.5219191160961077, 'mIoU': 0.7603983958233873}}  total_overall: {'no_occupancy': 0.9988792659941265, 'occupancy': 0.7263994881644248, 'mIoU': 0.8626393770792756}  data_time: 0.0166  time: 0.1316
2024/07/04 21:23:03 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:23:03 - mmengine - INFO - Epoch(train)  [75][91/91]  lr: 3.2082e-05  eta: 0:47:56  time: 0.6008  data_time: 0.0007  memory: 2942  grad_norm: 0.8249  loss: 0.1625  loss_bce: 0.0087  loss_dice: 0.1538
2024/07/04 21:23:03 - mmengine - INFO - Saving checkpoint at 75 epochs
2024/07/04 21:23:18 - mmengine - INFO - Epoch(val) [75][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9986026818852569, 'occupancy': 0.8461555815583423, 'mIoU': 0.9223791317217995}, '1~2m': {'no_occupancy': 0.9977180963316983, 'occupancy': 0.626310358967045, 'mIoU': 0.8120142276493716}, '2~3m': {'no_occupancy': 0.9993429157041467, 'occupancy': 0.416710811461594, 'mIoU': 0.7080268635828704}, '3~4m': {'no_occupancy': 0.9998809174819969, 'occupancy': 0.21627408993576017, 'mIoU': 0.6080775037088786}, 'patch_mean': {'no_occupancy': 0.9988861528507746, 'occupancy': 0.5263627104806854, 'mIoU': 0.76262443166573}}  total_overall: {'no_occupancy': 0.9988877489086401, 'occupancy': 0.7291072998610795, 'mIoU': 0.8639975243848598}  data_time: 0.0170  time: 0.1339
2024/07/04 21:24:21 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:24:21 - mmengine - INFO - Epoch(train)  [76][91/91]  lr: 3.0866e-05  eta: 0:46:52  time: 0.5974  data_time: 0.0007  memory: 2942  grad_norm: 0.9208  loss: 0.1817  loss_bce: 0.0098  loss_dice: 0.1719
2024/07/04 21:24:21 - mmengine - INFO - Saving checkpoint at 76 epochs
2024/07/04 21:24:36 - mmengine - INFO - Epoch(val) [76][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9986230644051269, 'occupancy': 0.8481294259454573, 'mIoU': 0.923376245175292}, '1~2m': {'no_occupancy': 0.9977721200782949, 'occupancy': 0.633403422501086, 'mIoU': 0.8155877712896904}, '2~3m': {'no_occupancy': 0.9993456842994288, 'occupancy': 0.4162996660697865, 'mIoU': 0.7078226751846077}, '3~4m': {'no_occupancy': 0.9998808090150465, 'occupancy': 0.21667854597291517, 'mIoU': 0.6082796774939808}, 'patch_mean': {'no_occupancy': 0.9989054194494743, 'occupancy': 0.5286277651223112, 'mIoU': 0.7637665922858927}}  total_overall: {'no_occupancy': 0.9989069898482749, 'occupancy': 0.7329693144541793, 'mIoU': 0.865938152151227}  data_time: 0.0162  time: 0.1309
2024/07/04 21:25:37 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:25:41 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:25:41 - mmengine - INFO - Epoch(train)  [77][91/91]  lr: 2.9663e-05  eta: 0:45:48  time: 0.6076  data_time: 0.0007  memory: 2942  grad_norm: 0.8017  loss: 0.1922  loss_bce: 0.0098  loss_dice: 0.1825
2024/07/04 21:25:41 - mmengine - INFO - Saving checkpoint at 77 epochs
2024/07/04 21:25:55 - mmengine - INFO - Epoch(val) [77][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9986514414472776, 'occupancy': 0.850741947213606, 'mIoU': 0.9246966943304418}, '1~2m': {'no_occupancy': 0.997807109028348, 'occupancy': 0.6372164856775246, 'mIoU': 0.8175117973529363}, '2~3m': {'no_occupancy': 0.9993521943284641, 'occupancy': 0.4187597352024923, 'mIoU': 0.7090559647654782}, '3~4m': {'no_occupancy': 0.9998811344158272, 'occupancy': 0.21546170365068004, 'mIoU': 0.6076714190332536}, 'patch_mean': {'no_occupancy': 0.9989229698049792, 'occupancy': 0.5305449679360758, 'mIoU': 0.7647339688705275}}  total_overall: {'no_occupancy': 0.9989245046926298, 'occupancy': 0.7361049366394955, 'mIoU': 0.8675147206660627}  data_time: 0.0160  time: 0.1318
2024/07/04 21:27:00 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:27:00 - mmengine - INFO - Epoch(train)  [78][91/91]  lr: 2.8474e-05  eta: 0:44:44  time: 0.6061  data_time: 0.0007  memory: 2942  grad_norm: 0.7442  loss: 0.1709  loss_bce: 0.0092  loss_dice: 0.1618
2024/07/04 21:27:00 - mmengine - INFO - Saving checkpoint at 78 epochs
2024/07/04 21:27:15 - mmengine - INFO - Epoch(val) [78][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9986770126073949, 'occupancy': 0.8532575633031025, 'mIoU': 0.9259672879552487}, '1~2m': {'no_occupancy': 0.9978460417045144, 'occupancy': 0.6419508299815861, 'mIoU': 0.8198984358430503}, '2~3m': {'no_occupancy': 0.9993538730104944, 'occupancy': 0.4218727246250183, 'mIoU': 0.7106132988177564}, '3~4m': {'no_occupancy': 0.9998816224267316, 'occupancy': 0.2175627240143369, 'mIoU': 0.6087221732205342}, 'patch_mean': {'no_occupancy': 0.9989396374372839, 'occupancy': 0.533660960481011, 'mIoU': 0.7663002989591474}}  total_overall: {'no_occupancy': 0.9989411383644605, 'occupancy': 0.7394637761829502, 'mIoU': 0.8692024572737054}  data_time: 0.0158  time: 0.1310
2024/07/04 21:28:18 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:28:18 - mmengine - INFO - Epoch(train)  [79][91/91]  lr: 2.7300e-05  eta: 0:43:40  time: 0.6024  data_time: 0.0007  memory: 2942  grad_norm: 0.7480  loss: 0.1644  loss_bce: 0.0092  loss_dice: 0.1553
2024/07/04 21:28:18 - mmengine - INFO - Saving checkpoint at 79 epochs
2024/07/04 21:28:33 - mmengine - INFO - Epoch(val) [79][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.998693630939733, 'occupancy': 0.8555072955394195, 'mIoU': 0.9271004632395763}, '1~2m': {'no_occupancy': 0.9978576657230294, 'occupancy': 0.646138957950249, 'mIoU': 0.8219983118366392}, '2~3m': {'no_occupancy': 0.9993532153691913, 'occupancy': 0.42688203057398333, 'mIoU': 0.7131176229715873}, '3~4m': {'no_occupancy': 0.9998817308551077, 'occupancy': 0.21883954154727797, 'mIoU': 0.6093606362011929}, 'patch_mean': {'no_occupancy': 0.9989465607217654, 'occupancy': 0.5368419564027325, 'mIoU': 0.7678942585622489}}  total_overall: {'no_occupancy': 0.9989480546117506, 'occupancy': 0.7423438132915808, 'mIoU': 0.8706459339516657}  data_time: 0.0165  time: 0.1320
2024/07/04 21:29:37 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:29:37 - mmengine - INFO - Epoch(train)  [80][91/91]  lr: 2.6142e-05  eta: 0:42:36  time: 0.6074  data_time: 0.0007  memory: 2942  grad_norm: 0.7386  loss: 0.1685  loss_bce: 0.0089  loss_dice: 0.1596
2024/07/04 21:29:37 - mmengine - INFO - Saving checkpoint at 80 epochs
2024/07/04 21:29:52 - mmengine - INFO - Epoch(val) [80][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9987299005397726, 'occupancy': 0.8588779237574671, 'mIoU': 0.9288039121486198}, '1~2m': {'no_occupancy': 0.9978937849846002, 'occupancy': 0.6503916000822281, 'mIoU': 0.8241426925334141}, '2~3m': {'no_occupancy': 0.9993508868924886, 'occupancy': 0.42259434417527264, 'mIoU': 0.7109726155338807}, '3~4m': {'no_occupancy': 0.9998818934913553, 'occupancy': 0.22103004291845496, 'mIoU': 0.6104559682049051}, 'patch_mean': {'no_occupancy': 0.9989641164770542, 'occupancy': 0.5382234777333557, 'mIoU': 0.7685937971052049}}  total_overall: {'no_occupancy': 0.9989655607541618, 'occupancy': 0.745467875205397, 'mIoU': 0.8722167179797794}  data_time: 0.0174  time: 0.1330
2024/07/04 21:30:55 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:30:55 - mmengine - INFO - Epoch(train)  [81][91/91]  lr: 2.5000e-05  eta: 0:41:32  time: 0.6041  data_time: 0.0007  memory: 2942  grad_norm: 0.7360  loss: 0.1615  loss_bce: 0.0084  loss_dice: 0.1531
2024/07/04 21:30:55 - mmengine - INFO - Saving checkpoint at 81 epochs
2024/07/04 21:31:11 - mmengine - INFO - Epoch(val) [81][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.99874645595817, 'occupancy': 0.8607389183869066, 'mIoU': 0.9297426871725383}, '1~2m': {'no_occupancy': 0.9979115302756695, 'occupancy': 0.6538597312370651, 'mIoU': 0.8258856307563673}, '2~3m': {'no_occupancy': 0.9993652570689884, 'occupancy': 0.43316699772297856, 'mIoU': 0.7162661273959835}, '3~4m': {'no_occupancy': 0.999882327269446, 'occupancy': 0.22333571939871152, 'mIoU': 0.6116090233340787}, 'patch_mean': {'no_occupancy': 0.9989763926430685, 'occupancy': 0.5427753416864155, 'mIoU': 0.770875867164742}}  total_overall: {'no_occupancy': 0.9989778302896075, 'occupancy': 0.7485775061603075, 'mIoU': 0.8737776682249575}  data_time: 0.0168  time: 0.1338
2024/07/04 21:32:15 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:32:15 - mmengine - INFO - Epoch(train)  [82][91/91]  lr: 2.3875e-05  eta: 0:40:29  time: 0.6041  data_time: 0.0007  memory: 2942  grad_norm: 0.6956  loss: 0.1659  loss_bce: 0.0092  loss_dice: 0.1566
2024/07/04 21:32:15 - mmengine - INFO - Saving checkpoint at 82 epochs
2024/07/04 21:32:30 - mmengine - INFO - Epoch(val) [82][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9987566187190555, 'occupancy': 0.8619826320095414, 'mIoU': 0.9303696253642985}, '1~2m': {'no_occupancy': 0.9979416647124848, 'occupancy': 0.6583981966272592, 'mIoU': 0.828169930669872}, '2~3m': {'no_occupancy': 0.9993726326226371, 'occupancy': 0.43817713647184575, 'mIoU': 0.7187748845472415}, '3~4m': {'no_occupancy': 0.9998823272885892, 'occupancy': 0.22250089573629528, 'mIoU': 0.6111916115124423}, 'patch_mean': {'no_occupancy': 0.9989883108356916, 'occupancy': 0.5452647152112353, 'mIoU': 0.7721265130234635}}  total_overall: {'no_occupancy': 0.9989897409731517, 'occupancy': 0.7514455483967554, 'mIoU': 0.8752176446849536}  data_time: 0.0173  time: 0.1331
2024/07/04 21:33:34 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:33:34 - mmengine - INFO - Epoch(train)  [83][91/91]  lr: 2.2768e-05  eta: 0:39:25  time: 0.6005  data_time: 0.0007  memory: 2942  grad_norm: 0.7707  loss: 0.1818  loss_bce: 0.0095  loss_dice: 0.1723
2024/07/04 21:33:34 - mmengine - INFO - Saving checkpoint at 83 epochs
2024/07/04 21:33:49 - mmengine - INFO - Epoch(val) [83][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9987699194749325, 'occupancy': 0.8634968860103943, 'mIoU': 0.9311334027426634}, '1~2m': {'no_occupancy': 0.9979565381724209, 'occupancy': 0.6610357182365969, 'mIoU': 0.8294961282045089}, '2~3m': {'no_occupancy': 0.9993675278443995, 'occupancy': 0.4402189570728897, 'mIoU': 0.7197932424586446}, '3~4m': {'no_occupancy': 0.9998823273013513, 'occupancy': 0.2219433488705629, 'mIoU': 0.6109128380859571}, 'patch_mean': {'no_occupancy': 0.9989940781982761, 'occupancy': 0.546673727547611, 'mIoU': 0.7728339028729436}}  total_overall: {'no_occupancy': 0.9989954909094743, 'occupancy': 0.7531413872814021, 'mIoU': 0.8760684390954382}  data_time: 0.0171  time: 0.1346
2024/07/04 21:34:53 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:34:53 - mmengine - INFO - Epoch(train)  [84][91/91]  lr: 2.1680e-05  eta: 0:38:21  time: 0.6008  data_time: 0.0007  memory: 2942  grad_norm: 0.8143  loss: 0.1703  loss_bce: 0.0091  loss_dice: 0.1612
2024/07/04 21:34:53 - mmengine - INFO - Saving checkpoint at 84 epochs
2024/07/04 21:35:08 - mmengine - INFO - Epoch(val) [84][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9987691344898993, 'occupancy': 0.8633298039203788, 'mIoU': 0.9310494692051391}, '1~2m': {'no_occupancy': 0.9979674398810416, 'occupancy': 0.6624264364321485, 'mIoU': 0.830196938156595}, '2~3m': {'no_occupancy': 0.9993776724994882, 'occupancy': 0.44461121332429554, 'mIoU': 0.7219944429118919}, '3~4m': {'no_occupancy': 0.9998833032680182, 'occupancy': 0.22867383512544803, 'mIoU': 0.6142785691967332}, 'patch_mean': {'no_occupancy': 0.9989993875346118, 'occupancy': 0.5497603222005677, 'mIoU': 0.7743798548675898}}  total_overall: {'no_occupancy': 0.999000806146869, 'occupancy': 0.7541157156152292, 'mIoU': 0.8765582608810492}  data_time: 0.0165  time: 0.1319
2024/07/04 21:36:11 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:36:11 - mmengine - INFO - Epoch(train)  [85][91/91]  lr: 2.0611e-05  eta: 0:37:17  time: 0.5950  data_time: 0.0007  memory: 2942  grad_norm: 0.7490  loss: 0.1628  loss_bce: 0.0089  loss_dice: 0.1539
2024/07/04 21:36:11 - mmengine - INFO - Saving checkpoint at 85 epochs
2024/07/04 21:36:26 - mmengine - INFO - Epoch(val) [85][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.998796934663379, 'occupancy': 0.8662762445369753, 'mIoU': 0.9325365896001772}, '1~2m': {'no_occupancy': 0.9979937685491987, 'occupancy': 0.6668666815830333, 'mIoU': 0.832430225066116}, '2~3m': {'no_occupancy': 0.9993795175470885, 'occupancy': 0.4449674851984859, 'mIoU': 0.7221735013727872}, '3~4m': {'no_occupancy': 0.9998830321388313, 'occupancy': 0.22798854688618467, 'mIoU': 0.613935789512508}, 'patch_mean': {'no_occupancy': 0.9990133132246244, 'occupancy': 0.5515247395511698, 'mIoU': 0.7752690263878971}}  total_overall: {'no_occupancy': 0.9990147023337242, 'occupancy': 0.7573751553886915, 'mIoU': 0.8781949288612079}  data_time: 0.0157  time: 0.1326
2024/07/04 21:37:31 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:37:31 - mmengine - INFO - Epoch(train)  [86][91/91]  lr: 1.9562e-05  eta: 0:36:13  time: 0.6034  data_time: 0.0007  memory: 2942  grad_norm: 0.6562  loss: 0.1595  loss_bce: 0.0090  loss_dice: 0.1505
2024/07/04 21:37:31 - mmengine - INFO - Saving checkpoint at 86 epochs
2024/07/04 21:37:45 - mmengine - INFO - Epoch(val) [86][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9988223251027156, 'occupancy': 0.8688173695752417, 'mIoU': 0.9338198473389787}, '1~2m': {'no_occupancy': 0.99804283497956, 'occupancy': 0.6738785794962283, 'mIoU': 0.8359607072378942}, '2~3m': {'no_occupancy': 0.9993853206320573, 'occupancy': 0.448822728157229, 'mIoU': 0.7241040243946432}, '3~4m': {'no_occupancy': 0.99988362858017, 'occupancy': 0.2313753581661891, 'mIoU': 0.6156294933731795}, 'patch_mean': {'no_occupancy': 0.9990335273236257, 'occupancy': 0.555723508848722, 'mIoU': 0.7773785180861739}}  total_overall: {'no_occupancy': 0.9990348862990802, 'occupancy': 0.7617140713222749, 'mIoU': 0.8803744788106775}  data_time: 0.0178  time: 0.1338
2024/07/04 21:38:50 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:38:50 - mmengine - INFO - Epoch(train)  [87][91/91]  lr: 1.8534e-05  eta: 0:35:09  time: 0.6006  data_time: 0.0007  memory: 2942  grad_norm: 0.7173  loss: 0.1478  loss_bce: 0.0086  loss_dice: 0.1391
2024/07/04 21:38:50 - mmengine - INFO - Saving checkpoint at 87 epochs
2024/07/04 21:39:05 - mmengine - INFO - Epoch(val) [87][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9988371173442823, 'occupancy': 0.8704694003332748, 'mIoU': 0.9346532588387786}, '1~2m': {'no_occupancy': 0.9980649495275226, 'occupancy': 0.6772326344348457, 'mIoU': 0.8376487919811841}, '2~3m': {'no_occupancy': 0.999393998775845, 'occupancy': 0.4541634089132134, 'mIoU': 0.7267787038445293}, '3~4m': {'no_occupancy': 0.9998839539177754, 'occupancy': 0.23297491039426527, 'mIoU': 0.6164294321560203}, 'patch_mean': {'no_occupancy': 0.9990450048913564, 'occupancy': 0.5587100885188998, 'mIoU': 0.7788775467051281}}  total_overall: {'no_occupancy': 0.9990463514480531, 'occupancy': 0.7643840932112423, 'mIoU': 0.8817152223296477}  data_time: 0.0163  time: 0.1336
2024/07/04 21:40:04 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:40:09 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:40:09 - mmengine - INFO - Epoch(train)  [88][91/91]  lr: 1.7528e-05  eta: 0:34:06  time: 0.6032  data_time: 0.0007  memory: 2942  grad_norm: 0.6901  loss: 0.1536  loss_bce: 0.0084  loss_dice: 0.1452
2024/07/04 21:40:09 - mmengine - INFO - Saving checkpoint at 88 epochs
2024/07/04 21:40:23 - mmengine - INFO - Epoch(val) [88][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9988587648960665, 'occupancy': 0.8726626787766811, 'mIoU': 0.9357607218363738}, '1~2m': {'no_occupancy': 0.9980941316291995, 'occupancy': 0.682091365256681, 'mIoU': 0.8400927484429402}, '2~3m': {'no_occupancy': 0.9993989314880096, 'occupancy': 0.4596664065548186, 'mIoU': 0.7295326690214141}, '3~4m': {'no_occupancy': 0.9998843877160972, 'occupancy': 0.23447037701974865, 'mIoU': 0.617177382367923}, 'patch_mean': {'no_occupancy': 0.9990590539323432, 'occupancy': 0.5622227069019824, 'mIoU': 0.7806408804171627}}  total_overall: {'no_occupancy': 0.9990603782895956, 'occupancy': 0.7676542487550143, 'mIoU': 0.883357313522305}  data_time: 0.0157  time: 0.1298
2024/07/04 21:41:28 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:41:28 - mmengine - INFO - Epoch(train)  [89][91/91]  lr: 1.6543e-05  eta: 0:33:02  time: 0.6050  data_time: 0.0007  memory: 2942  grad_norm: 0.7036  loss: 0.1704  loss_bce: 0.0089  loss_dice: 0.1615
2024/07/04 21:41:28 - mmengine - INFO - Saving checkpoint at 89 epochs
2024/07/04 21:41:42 - mmengine - INFO - Epoch(val) [89][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9988634205279289, 'occupancy': 0.873242398200801, 'mIoU': 0.9360529093643649}, '1~2m': {'no_occupancy': 0.9980953528614827, 'occupancy': 0.6821469905764205, 'mIoU': 0.8401211717189516}, '2~3m': {'no_occupancy': 0.9993944839938644, 'occupancy': 0.4568327817792486, 'mIoU': 0.7281136328865565}, '3~4m': {'no_occupancy': 0.9998844419244481, 'occupancy': 0.235378543236455, 'mIoU': 0.6176314925804516}, 'patch_mean': {'no_occupancy': 0.9990594248269309, 'occupancy': 0.5619001784482311, 'mIoU': 0.780479801637581}}  total_overall: {'no_occupancy': 0.9990607407932961, 'occupancy': 0.7677999256213454, 'mIoU': 0.8834303332073208}  data_time: 0.0157  time: 0.1326
2024/07/04 21:42:47 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:42:47 - mmengine - INFO - Epoch(train)  [90][91/91]  lr: 1.5582e-05  eta: 0:31:58  time: 0.6043  data_time: 0.0007  memory: 2942  grad_norm: 0.6625  loss: 0.1492  loss_bce: 0.0083  loss_dice: 0.1410
2024/07/04 21:42:47 - mmengine - INFO - Saving checkpoint at 90 epochs
2024/07/04 21:43:02 - mmengine - INFO - Epoch(val) [90][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9988787973508031, 'occupancy': 0.8747458729150894, 'mIoU': 0.9368123351329463}, '1~2m': {'no_occupancy': 0.998127129444912, 'occupancy': 0.6863590307039491, 'mIoU': 0.8422430800744305}, '2~3m': {'no_occupancy': 0.9993993109979963, 'occupancy': 0.4600341380151182, 'mIoU': 0.7297167245065572}, '3~4m': {'no_occupancy': 0.9998847672624064, 'occupancy': 0.236983842010772, 'mIoU': 0.6184343046365892}, 'patch_mean': {'no_occupancy': 0.9990725012640294, 'occupancy': 0.5645307209112322, 'mIoU': 0.7818016110876308}}  total_overall: {'no_occupancy': 0.9990737971628928, 'occupancy': 0.7704837310825683, 'mIoU': 0.8847787641227305}  data_time: 0.0159  time: 0.1314
2024/07/04 21:44:06 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:44:06 - mmengine - INFO - Epoch(train)  [91][91/91]  lr: 1.4645e-05  eta: 0:30:54  time: 0.6048  data_time: 0.0007  memory: 2942  grad_norm: 0.6384  loss: 0.1567  loss_bce: 0.0086  loss_dice: 0.1481
2024/07/04 21:44:06 - mmengine - INFO - Saving checkpoint at 91 epochs
2024/07/04 21:44:21 - mmengine - INFO - Epoch(val) [91][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.998895024846485, 'occupancy': 0.8765508325039839, 'mIoU': 0.9377229286752344}, '1~2m': {'no_occupancy': 0.9981368357671068, 'occupancy': 0.6884051867399392, 'mIoU': 0.843271011253523}, '2~3m': {'no_occupancy': 0.9993941533821131, 'occupancy': 0.4607658505963591, 'mIoU': 0.7300800019892362}, '3~4m': {'no_occupancy': 0.9998847672561576, 'occupancy': 0.23725771715721464, 'mIoU': 0.6185712422066861}, 'patch_mean': {'no_occupancy': 0.9990776953129655, 'occupancy': 0.5657448967493742, 'mIoU': 0.7824112960311699}}  total_overall: {'no_occupancy': 0.9990789708666962, 'occupancy': 0.7720277754870897, 'mIoU': 0.885553373176893}  data_time: 0.0169  time: 0.1339
2024/07/04 21:45:25 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:45:25 - mmengine - INFO - Epoch(train)  [92][91/91]  lr: 1.3731e-05  eta: 0:29:50  time: 0.6017  data_time: 0.0007  memory: 2942  grad_norm: 0.6475  loss: 0.1400  loss_bce: 0.0080  loss_dice: 0.1320
2024/07/04 21:45:25 - mmengine - INFO - Saving checkpoint at 92 epochs
2024/07/04 21:45:40 - mmengine - INFO - Epoch(val) [92][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9989086232616398, 'occupancy': 0.8780055896637038, 'mIoU': 0.9384571064626718}, '1~2m': {'no_occupancy': 0.9981779105656486, 'occupancy': 0.6943178587291998, 'mIoU': 0.8462478846474242}, '2~3m': {'no_occupancy': 0.9994062536722593, 'occupancy': 0.4643171806167401, 'mIoU': 0.7318617171444997}, '3~4m': {'no_occupancy': 0.9998851468027754, 'occupancy': 0.239770279971285, 'mIoU': 0.6198277133870302}, 'patch_mean': {'no_occupancy': 0.9990944835755808, 'occupancy': 0.5691027272452321, 'mIoU': 0.7840986054104064}}  total_overall: {'no_occupancy': 0.9990957468206552, 'occupancy': 0.7756432019845917, 'mIoU': 0.8873694744026235}  data_time: 0.0166  time: 0.1316
2024/07/04 21:46:44 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:46:44 - mmengine - INFO - Epoch(train)  [93][91/91]  lr: 1.2843e-05  eta: 0:28:46  time: 0.6088  data_time: 0.0007  memory: 2942  grad_norm: 0.6453  loss: 0.1476  loss_bce: 0.0085  loss_dice: 0.1391
2024/07/04 21:46:44 - mmengine - INFO - Saving checkpoint at 93 epochs
2024/07/04 21:46:59 - mmengine - INFO - Epoch(val) [93][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9989176393699423, 'occupancy': 0.8790802783556927, 'mIoU': 0.9389989588628175}, '1~2m': {'no_occupancy': 0.9981964292850058, 'occupancy': 0.6978578375869858, 'mIoU': 0.8480271334359959}, '2~3m': {'no_occupancy': 0.9994128676175986, 'occupancy': 0.47114303865513363, 'mIoU': 0.7352779531363661}, '3~4m': {'no_occupancy': 0.9998855805582822, 'occupancy': 0.2431850789096126, 'mIoU': 0.6215353297339474}, 'patch_mean': {'no_occupancy': 0.9991031292077073, 'occupancy': 0.5728165583768562, 'mIoU': 0.7859598437922817}}  total_overall: {'no_occupancy': 0.9991043878594925, 'occupancy': 0.7779975856249884, 'mIoU': 0.8885509867422405}  data_time: 0.0164  time: 0.1319
2024/07/04 21:48:03 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:48:03 - mmengine - INFO - Epoch(train)  [94][91/91]  lr: 1.1980e-05  eta: 0:27:42  time: 0.6021  data_time: 0.0007  memory: 2942  grad_norm: 0.6513  loss: 0.1570  loss_bce: 0.0083  loss_dice: 0.1486
2024/07/04 21:48:03 - mmengine - INFO - Saving checkpoint at 94 epochs
2024/07/04 21:48:17 - mmengine - INFO - Epoch(val) [94][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9989243891015611, 'occupancy': 0.8797408894266022, 'mIoU': 0.9393326392640817}, '1~2m': {'no_occupancy': 0.9982051592026411, 'occupancy': 0.6997228333401656, 'mIoU': 0.8489639962714033}, '2~3m': {'no_occupancy': 0.9994116175823532, 'occupancy': 0.47239114570664076, 'mIoU': 0.735901381644497}, '3~4m': {'no_occupancy': 0.9998860685353346, 'occupancy': 0.24695340501792115, 'mIoU': 0.6234197367766279}, 'patch_mean': {'no_occupancy': 0.9991068086054724, 'occupancy': 0.5747020683728323, 'mIoU': 0.7869044384891524}}  total_overall: {'no_occupancy': 0.9991080600136671, 'occupancy': 0.778992325771424, 'mIoU': 0.8890501928925456}  data_time: 0.0169  time: 0.1326
2024/07/04 21:49:21 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:49:21 - mmengine - INFO - Epoch(train)  [95][91/91]  lr: 1.1143e-05  eta: 0:26:38  time: 0.5994  data_time: 0.0007  memory: 2942  grad_norm: 0.6142  loss: 0.1405  loss_bce: 0.0082  loss_dice: 0.1323
2024/07/04 21:49:21 - mmengine - INFO - Saving checkpoint at 95 epochs
2024/07/04 21:49:36 - mmengine - INFO - Epoch(val) [95][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9989386412986808, 'occupancy': 0.8812958050378664, 'mIoU': 0.9401172231682736}, '1~2m': {'no_occupancy': 0.9982359519467721, 'occupancy': 0.7043060377629733, 'mIoU': 0.8512709948548727}, '2~3m': {'no_occupancy': 0.9994170438791535, 'occupancy': 0.47395476353666893, 'mIoU': 0.7366859037079112}, '3~4m': {'no_occupancy': 0.9998867734344017, 'occupancy': 0.2505384063173008, 'mIoU': 0.6252125898758513}, 'patch_mean': {'no_occupancy': 0.999119602639752, 'occupancy': 0.5775237531637024, 'mIoU': 0.7883216779017272}}  total_overall: {'no_occupancy': 0.9991208382602892, 'occupancy': 0.7818653201578595, 'mIoU': 0.8904930792090744}  data_time: 0.0160  time: 0.1307
2024/07/04 21:50:40 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:50:40 - mmengine - INFO - Epoch(train)  [96][91/91]  lr: 1.0332e-05  eta: 0:25:34  time: 0.5963  data_time: 0.0007  memory: 2942  grad_norm: 0.6038  loss: 0.1449  loss_bce: 0.0083  loss_dice: 0.1366
2024/07/04 21:50:40 - mmengine - INFO - Saving checkpoint at 96 epochs
2024/07/04 21:50:54 - mmengine - INFO - Epoch(val) [96][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9989487384102615, 'occupancy': 0.882377670698448, 'mIoU': 0.9406632045543548}, '1~2m': {'no_occupancy': 0.9982595110764578, 'occupancy': 0.7076391675842177, 'mIoU': 0.8529493393303378}, '2~3m': {'no_occupancy': 0.9994201344845286, 'occupancy': 0.47679655374975516, 'mIoU': 0.7381083441171419}, '3~4m': {'no_occupancy': 0.9998870445095608, 'occupancy': 0.2536725188104622, 'mIoU': 0.6267797816600115}, 'patch_mean': {'no_occupancy': 0.9991288571202022, 'occupancy': 0.5801214777107208, 'mIoU': 0.7896251674154615}}  total_overall: {'no_occupancy': 0.9991300793095818, 'occupancy': 0.7839464501706679, 'mIoU': 0.8915382647401249}  data_time: 0.0160  time: 0.1322
2024/07/04 21:51:58 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:51:58 - mmengine - INFO - Epoch(train)  [97][91/91]  lr: 9.5492e-06  eta: 0:24:30  time: 0.6017  data_time: 0.0007  memory: 2942  grad_norm: 0.6129  loss: 0.1405  loss_bce: 0.0077  loss_dice: 0.1328
2024/07/04 21:51:58 - mmengine - INFO - Saving checkpoint at 97 epochs
2024/07/04 21:52:13 - mmengine - INFO - Epoch(val) [97][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9989586266452856, 'occupancy': 0.8834047103475126, 'mIoU': 0.9411816684963991}, '1~2m': {'no_occupancy': 0.9982635108634154, 'occupancy': 0.7087654287162485, 'mIoU': 0.8535144697898319}, '2~3m': {'no_occupancy': 0.9994215433908106, 'occupancy': 0.478783730934689, 'mIoU': 0.7391026371627498}, '3~4m': {'no_occupancy': 0.9998864481261259, 'occupancy': 0.24757455982752427, 'mIoU': 0.6237305039768251}, 'patch_mean': {'no_occupancy': 0.9991325322564094, 'occupancy': 0.5796321074564935, 'mIoU': 0.7893823198564515}}  total_overall: {'no_occupancy': 0.9991337452768835, 'occupancy': 0.7849140636310262, 'mIoU': 0.8920239044539549}  data_time: 0.0163  time: 0.1323
2024/07/04 21:53:17 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:53:17 - mmengine - INFO - Epoch(train)  [98][91/91]  lr: 8.7937e-06  eta: 0:23:26  time: 0.5980  data_time: 0.0007  memory: 2942  grad_norm: 0.6561  loss: 0.1344  loss_bce: 0.0081  loss_dice: 0.1263
2024/07/04 21:53:17 - mmengine - INFO - Saving checkpoint at 98 epochs
2024/07/04 21:53:32 - mmengine - INFO - Epoch(val) [98][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9989644781300633, 'occupancy': 0.8840246492667042, 'mIoU': 0.9414945636983838}, '1~2m': {'no_occupancy': 0.9982759280129596, 'occupancy': 0.7103935914894687, 'mIoU': 0.8543347597512141}, '2~3m': {'no_occupancy': 0.9994252852048621, 'occupancy': 0.48180217199882597, 'mIoU': 0.740613728601844}, '3~4m': {'no_occupancy': 0.9998877494280917, 'occupancy': 0.25646551724137934, 'mIoU': 0.6281766333347355}, 'patch_mean': {'no_occupancy': 0.9991383601939942, 'occupancy': 0.5831714824990946, 'mIoU': 0.7911549213465443}}  total_overall: {'no_occupancy': 0.9991395668597044, 'occupancy': 0.7861902507042624, 'mIoU': 0.8926649087819833}  data_time: 0.0166  time: 0.1321
2024/07/04 21:54:32 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:54:37 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:54:37 - mmengine - INFO - Epoch(train)  [99][91/91]  lr: 8.0665e-06  eta: 0:22:23  time: 0.6039  data_time: 0.0007  memory: 2942  grad_norm: 0.6112  loss: 0.1415  loss_bce: 0.0080  loss_dice: 0.1335
2024/07/04 21:54:37 - mmengine - INFO - Saving checkpoint at 99 epochs
2024/07/04 21:54:52 - mmengine - INFO - Epoch(val) [99][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9989755103027648, 'occupancy': 0.8852168282213473, 'mIoU': 0.942096169262056}, '1~2m': {'no_occupancy': 0.9983067193227009, 'occupancy': 0.7149424918553522, 'mIoU': 0.8566246055890265}, '2~3m': {'no_occupancy': 0.9994268070281437, 'occupancy': 0.4802479460815665, 'mIoU': 0.7398373765548552}, '3~4m': {'no_occupancy': 0.9998881289769324, 'occupancy': 0.25897988505747127, 'mIoU': 0.6294340070172018}, 'patch_mean': {'no_occupancy': 0.9991492914076354, 'occupancy': 0.5848467878039344, 'mIoU': 0.7919980396057849}}  total_overall: {'no_occupancy': 0.9991504825792316, 'occupancy': 0.7886044909114521, 'mIoU': 0.8938774867453418}  data_time: 0.0185  time: 0.1343
2024/07/04 21:55:55 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:55:55 - mmengine - INFO - Epoch(train) [100][91/91]  lr: 7.3680e-06  eta: 0:21:19  time: 0.6010  data_time: 0.0007  memory: 2942  grad_norm: 0.6335  loss: 0.1502  loss_bce: 0.0085  loss_dice: 0.1418
2024/07/04 21:55:55 - mmengine - INFO - Saving checkpoint at 100 epochs
2024/07/04 21:56:10 - mmengine - INFO - Epoch(val) [100][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.998981732305294, 'occupancy': 0.885942129686314, 'mIoU': 0.942461930995804}, '1~2m': {'no_occupancy': 0.9983145908674064, 'occupancy': 0.7165887355703467, 'mIoU': 0.8574516632188766}, '2~3m': {'no_occupancy': 0.9994309266233349, 'occupancy': 0.48510136959403066, 'mIoU': 0.7422661481086827}, '3~4m': {'no_occupancy': 0.9998881289466001, 'occupancy': 0.2603083542488347, 'mIoU': 0.6300982415977174}, 'patch_mean': {'no_occupancy': 0.9991538446856588, 'occupancy': 0.5869851472748815, 'mIoU': 0.7930694959802702}}  total_overall: {'no_occupancy': 0.9991550320716993, 'occupancy': 0.7898874708912674, 'mIoU': 0.8945212514814833}  data_time: 0.0157  time: 0.1324
2024/07/04 21:57:14 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:57:14 - mmengine - INFO - Epoch(train) [101][91/91]  lr: 6.6987e-06  eta: 0:20:15  time: 0.6025  data_time: 0.0007  memory: 2942  grad_norm: 0.5878  loss: 0.1362  loss_bce: 0.0081  loss_dice: 0.1280
2024/07/04 21:57:14 - mmengine - INFO - Saving checkpoint at 101 epochs
2024/07/04 21:57:29 - mmengine - INFO - Epoch(val) [101][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9989898162009296, 'occupancy': 0.8868072864675515, 'mIoU': 0.9428985513342405}, '1~2m': {'no_occupancy': 0.9983362311363736, 'occupancy': 0.7201564011701461, 'mIoU': 0.8592463161532599}, '2~3m': {'no_occupancy': 0.9994305465342659, 'occupancy': 0.48518736511673544, 'mIoU': 0.7423089558255007}, '3~4m': {'no_occupancy': 0.999888183143647, 'occupancy': 0.26172574292875045, 'mIoU': 0.6308069630361988}, 'patch_mean': {'no_occupancy': 0.9991611942538041, 'occupancy': 0.5884691989207959, 'mIoU': 0.7938151965872999}}  total_overall: {'no_occupancy': 0.999162370354593, 'occupancy': 0.7916656625041486, 'mIoU': 0.8954140164293708}  data_time: 0.0178  time: 0.1329
2024/07/04 21:58:33 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:58:33 - mmengine - INFO - Epoch(train) [102][91/91]  lr: 6.0591e-06  eta: 0:19:11  time: 0.6045  data_time: 0.0007  memory: 2942  grad_norm: 0.6217  loss: 0.1304  loss_bce: 0.0076  loss_dice: 0.1228
2024/07/04 21:58:33 - mmengine - INFO - Saving checkpoint at 102 epochs
2024/07/04 21:58:48 - mmengine - INFO - Epoch(val) [102][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9989954980607795, 'occupancy': 0.8874000612651248, 'mIoU': 0.9431977796629522}, '1~2m': {'no_occupancy': 0.9983516355637768, 'occupancy': 0.7224214259676602, 'mIoU': 0.8603865307657186}, '2~3m': {'no_occupancy': 0.9994332032437029, 'occupancy': 0.487791723867425, 'mIoU': 0.7436124635555639}, '3~4m': {'no_occupancy': 0.9998885627171741, 'occupancy': 0.2631767658659018, 'mIoU': 0.631532664291538}, 'patch_mean': {'no_occupancy': 0.9991672248963583, 'occupancy': 0.590197494241528, 'mIoU': 0.7946823595689432}}  total_overall: {'no_occupancy': 0.9991683937413207, 'occupancy': 0.7930321089234325, 'mIoU': 0.8961002513323766}  data_time: 0.0158  time: 0.1314
2024/07/04 21:59:51 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 21:59:51 - mmengine - INFO - Epoch(train) [103][91/91]  lr: 5.4497e-06  eta: 0:18:07  time: 0.6026  data_time: 0.0007  memory: 2942  grad_norm: 0.5844  loss: 0.1322  loss_bce: 0.0075  loss_dice: 0.1246
2024/07/04 21:59:51 - mmengine - INFO - Saving checkpoint at 103 epochs
2024/07/04 22:00:06 - mmengine - INFO - Epoch(val) [103][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990034722261838, 'occupancy': 0.8882623576707646, 'mIoU': 0.9436329149484741}, '1~2m': {'no_occupancy': 0.9983638769094175, 'occupancy': 0.7242470829843625, 'mIoU': 0.86130547994689}, '2~3m': {'no_occupancy': 0.9994362410980511, 'occupancy': 0.48946101311845924, 'mIoU': 0.7444486271082551}, '3~4m': {'no_occupancy': 0.9998885085079235, 'occupancy': 0.2622891998564765, 'mIoU': 0.6310888541822}, 'patch_mean': {'no_occupancy': 0.999173024685394, 'occupancy': 0.5910649134075157, 'mIoU': 0.7951189690464548}}  total_overall: {'no_occupancy': 0.9991741844345841, 'occupancy': 0.7943444838905507, 'mIoU': 0.8967593341625674}  data_time: 0.0158  time: 0.1327
2024/07/04 22:01:10 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:01:10 - mmengine - INFO - Epoch(train) [104][91/91]  lr: 4.8707e-06  eta: 0:17:03  time: 0.6037  data_time: 0.0007  memory: 2942  grad_norm: 0.5389  loss: 0.1331  loss_bce: 0.0080  loss_dice: 0.1251
2024/07/04 22:01:10 - mmengine - INFO - Saving checkpoint at 104 epochs
2024/07/04 22:01:25 - mmengine - INFO - Epoch(val) [104][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990099716560981, 'occupancy': 0.8889671267975139, 'mIoU': 0.943988549226806}, '1~2m': {'no_occupancy': 0.9983757785868929, 'occupancy': 0.7264078248804596, 'mIoU': 0.8623918017336762}, '2~3m': {'no_occupancy': 0.999436401384864, 'occupancy': 0.49155694777543923, 'mIoU': 0.7454966745801517}, '3~4m': {'no_occupancy': 0.9998886169385224, 'occupancy': 0.2635353173180351, 'mIoU': 0.6317119671282787}, 'patch_mean': {'no_occupancy': 0.9991776921415944, 'occupancy': 0.5926168041928619, 'mIoU': 0.7958972481672282}}  total_overall: {'no_occupancy': 0.9991788440878137, 'occupancy': 0.7955781371535845, 'mIoU': 0.8973784906206991}  data_time: 0.0162  time: 0.1325
2024/07/04 22:02:29 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:02:29 - mmengine - INFO - Epoch(train) [105][91/91]  lr: 4.3227e-06  eta: 0:15:59  time: 0.5945  data_time: 0.0012  memory: 2942  grad_norm: 0.5559  loss: 0.1219  loss_bce: 0.0076  loss_dice: 0.1144
2024/07/04 22:02:29 - mmengine - INFO - Saving checkpoint at 105 epochs
2024/07/04 22:02:43 - mmengine - INFO - Epoch(val) [105][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990121564542009, 'occupancy': 0.8892028039331077, 'mIoU': 0.9441074801936543}, '1~2m': {'no_occupancy': 0.9983855851763094, 'occupancy': 0.727496581599237, 'mIoU': 0.8629410833877732}, '2~3m': {'no_occupancy': 0.9994399291000048, 'occupancy': 0.49217827626918537, 'mIoU': 0.7458091026845951}, '3~4m': {'no_occupancy': 0.9998887254174412, 'occupancy': 0.26266618756737337, 'mIoU': 0.6312774564924073}, 'patch_mean': {'no_occupancy': 0.999181599036989, 'occupancy': 0.5928859623422259, 'mIoU': 0.7960337806896074}}  total_overall: {'no_occupancy': 0.9991827478598501, 'occupancy': 0.7963098073808936, 'mIoU': 0.8977462776203718}  data_time: 0.0164  time: 0.1336
2024/07/04 22:03:48 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:03:48 - mmengine - INFO - Epoch(train) [106][91/91]  lr: 3.8060e-06  eta: 0:14:55  time: 0.5933  data_time: 0.0007  memory: 2942  grad_norm: 0.5181  loss: 0.1256  loss_bce: 0.0076  loss_dice: 0.1180
2024/07/04 22:03:48 - mmengine - INFO - Saving checkpoint at 106 epochs
2024/07/04 22:04:03 - mmengine - INFO - Epoch(val) [106][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990183281373047, 'occupancy': 0.8898739086182369, 'mIoU': 0.9444461183777708}, '1~2m': {'no_occupancy': 0.9983927099314641, 'occupancy': 0.7286409117133934, 'mIoU': 0.8635168108224287}, '2~3m': {'no_occupancy': 0.9994441043868444, 'occupancy': 0.4959661550570641, 'mIoU': 0.7477051297219542}, '3~4m': {'no_occupancy': 0.9998889964821052, 'occupancy': 0.2663082437275986, 'mIoU': 0.6330986201048519}, 'patch_mean': {'no_occupancy': 0.9991860347344296, 'occupancy': 0.5951973047790733, 'mIoU': 0.7971916697567514}}  total_overall: {'no_occupancy': 0.9991871782738623, 'occupancy': 0.7973811450921895, 'mIoU': 0.8982841616830259}  data_time: 0.0170  time: 0.1318
2024/07/04 22:05:07 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:05:07 - mmengine - INFO - Epoch(train) [107][91/91]  lr: 3.3210e-06  eta: 0:13:51  time: 0.6067  data_time: 0.0007  memory: 2942  grad_norm: 0.5820  loss: 0.1353  loss_bce: 0.0077  loss_dice: 0.1276
2024/07/04 22:05:07 - mmengine - INFO - Saving checkpoint at 107 epochs
2024/07/04 22:05:22 - mmengine - INFO - Epoch(val) [107][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990208942103294, 'occupancy': 0.8901647762109136, 'mIoU': 0.9445928352106214}, '1~2m': {'no_occupancy': 0.9984016739478438, 'occupancy': 0.730368704767461, 'mIoU': 0.8643851893576524}, '2~3m': {'no_occupancy': 0.9994424229389928, 'occupancy': 0.49483877310263474, 'mIoU': 0.7471405980208138}, '3~4m': {'no_occupancy': 0.9998887796026023, 'occupancy': 0.2646109716744353, 'mIoU': 0.6322498756385188}, 'patch_mean': {'no_occupancy': 0.9991884426749421, 'occupancy': 0.5949958064388612, 'mIoU': 0.7970921245569016}}  total_overall: {'no_occupancy': 0.9991895823185816, 'occupancy': 0.7980523942215719, 'mIoU': 0.8986209882700767}  data_time: 0.0172  time: 0.1314
2024/07/04 22:06:25 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:06:25 - mmengine - INFO - Epoch(train) [108][91/91]  lr: 2.8679e-06  eta: 0:12:47  time: 0.6007  data_time: 0.0007  memory: 2942  grad_norm: 0.5470  loss: 0.1315  loss_bce: 0.0079  loss_dice: 0.1235
2024/07/04 22:06:25 - mmengine - INFO - Saving checkpoint at 108 epochs
2024/07/04 22:06:40 - mmengine - INFO - Epoch(val) [108][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990264644440215, 'occupancy': 0.8907801146641322, 'mIoU': 0.9449032895540769}, '1~2m': {'no_occupancy': 0.998416039483033, 'occupancy': 0.7324844499120459, 'mIoU': 0.8654502446975394}, '2~3m': {'no_occupancy': 0.9994447006483467, 'occupancy': 0.4966805999508238, 'mIoU': 0.7480626502995853}, '3~4m': {'no_occupancy': 0.9998890507396023, 'occupancy': 0.26508620689655177, 'mIoU': 0.6324876288180771}, 'patch_mean': {'no_occupancy': 0.9991940638287509, 'occupancy': 0.5962578428558883, 'mIoU': 0.7977259533423195}}  total_overall: {'no_occupancy': 0.9991951961802599, 'occupancy': 0.7993466140996746, 'mIoU': 0.8992709051399672}  data_time: 0.0172  time: 0.1356
2024/07/04 22:07:45 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:07:45 - mmengine - INFO - Epoch(train) [109][91/91]  lr: 2.4472e-06  eta: 0:11:43  time: 0.6085  data_time: 0.0007  memory: 2942  grad_norm: 0.5460  loss: 0.1257  loss_bce: 0.0077  loss_dice: 0.1180
2024/07/04 22:07:45 - mmengine - INFO - Saving checkpoint at 109 epochs
2024/07/04 22:08:00 - mmengine - INFO - Epoch(val) [109][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990278285536844, 'occupancy': 0.8909454455160236, 'mIoU': 0.9449866370348541}, '1~2m': {'no_occupancy': 0.9984166307004952, 'occupancy': 0.7327822520421412, 'mIoU': 0.8655994413713182}, '2~3m': {'no_occupancy': 0.9994453515572124, 'occupancy': 0.49709759937032655, 'mIoU': 0.7482714754637695}, '3~4m': {'no_occupancy': 0.9998889965061829, 'occupancy': 0.26525484565685575, 'mIoU': 0.6325719210815193}, 'patch_mean': {'no_occupancy': 0.9991947018293937, 'occupancy': 0.5965200356463367, 'mIoU': 0.7978573687378652}}  total_overall: {'no_occupancy': 0.9991958338383676, 'occupancy': 0.7995691791948463, 'mIoU': 0.899382506516607}  data_time: 0.0169  time: 0.1340
2024/07/04 22:08:59 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:09:04 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:09:04 - mmengine - INFO - Epoch(train) [110][91/91]  lr: 2.0590e-06  eta: 0:10:39  time: 0.6028  data_time: 0.0007  memory: 2942  grad_norm: 0.5445  loss: 0.1290  loss_bce: 0.0077  loss_dice: 0.1213
2024/07/04 22:09:04 - mmengine - INFO - Saving checkpoint at 110 epochs
2024/07/04 22:09:19 - mmengine - INFO - Epoch(val) [110][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990322534606269, 'occupancy': 0.8914161004268264, 'mIoU': 0.9452241769437266}, '1~2m': {'no_occupancy': 0.9984257168026869, 'occupancy': 0.7341607866669425, 'mIoU': 0.8662932517348148}, '2~3m': {'no_occupancy': 0.9994469781858314, 'occupancy': 0.4986720440684635, 'mIoU': 0.7490595111271474}, '3~4m': {'no_occupancy': 0.9998891591643492, 'occupancy': 0.26659490491567994, 'mIoU': 0.6332420320400145}, 'patch_mean': {'no_occupancy': 0.9991985269033736, 'occupancy': 0.597710959019478, 'mIoU': 0.7984547429614258}}  total_overall: {'no_occupancy': 0.9991996533892863, 'occupancy': 0.8004553773435362, 'mIoU': 0.8998275153664113}  data_time: 0.0188  time: 0.1316
2024/07/04 22:10:23 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:10:23 - mmengine - INFO - Epoch(train) [111][91/91]  lr: 1.7037e-06  eta: 0:09:35  time: 0.6024  data_time: 0.0007  memory: 2942  grad_norm: 0.5301  loss: 0.1324  loss_bce: 0.0076  loss_dice: 0.1249
2024/07/04 22:10:23 - mmengine - INFO - Saving checkpoint at 111 epochs
2024/07/04 22:10:38 - mmengine - INFO - Epoch(val) [111][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990328567913905, 'occupancy': 0.8914496083134473, 'mIoU': 0.9452412325524189}, '1~2m': {'no_occupancy': 0.9984270287492032, 'occupancy': 0.7341834915156744, 'mIoU': 0.8663052601324388}, '2~3m': {'no_occupancy': 0.9994498531069818, 'occupancy': 0.5004680033499188, 'mIoU': 0.7499589282284502}, '3~4m': {'no_occupancy': 0.9998893218345898, 'occupancy': 0.2674084709260589, 'mIoU': 0.6336488963803244}, 'patch_mean': {'no_occupancy': 0.9991997651205413, 'occupancy': 0.5983773935262748, 'mIoU': 0.7987885793234081}}  total_overall: {'no_occupancy': 0.9992008917889199, 'occupancy': 0.8006517293643771, 'mIoU': 0.8999263105766485}  data_time: 0.0164  time: 0.1315
2024/07/04 22:11:41 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:11:41 - mmengine - INFO - Epoch(train) [112][91/91]  lr: 1.3815e-06  eta: 0:08:31  time: 0.6079  data_time: 0.0007  memory: 2942  grad_norm: 0.5390  loss: 0.1330  loss_bce: 0.0081  loss_dice: 0.1249
2024/07/04 22:11:41 - mmengine - INFO - Saving checkpoint at 112 epochs
2024/07/04 22:11:56 - mmengine - INFO - Epoch(val) [112][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990349317644487, 'occupancy': 0.8916820540819081, 'mIoU': 0.9453584929231784}, '1~2m': {'no_occupancy': 0.9984332812477972, 'occupancy': 0.7352613670089292, 'mIoU': 0.8668473241283632}, '2~3m': {'no_occupancy': 0.9994486601524796, 'occupancy': 0.49938420611852796, 'mIoU': 0.7494164331355038}, '3~4m': {'no_occupancy': 0.9998891591643492, 'occupancy': 0.26659490491567994, 'mIoU': 0.6332420320400145}, 'patch_mean': {'no_occupancy': 0.9992015080822687, 'occupancy': 0.5982306330312612, 'mIoU': 0.798716070556765}}  total_overall: {'no_occupancy': 0.9992026310906237, 'occupancy': 0.8010913880593599, 'mIoU': 0.9001470095749917}  data_time: 0.0178  time: 0.1356
2024/07/04 22:13:00 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:13:00 - mmengine - INFO - Epoch(train) [113][91/91]  lr: 1.0926e-06  eta: 0:07:27  time: 0.6083  data_time: 0.0007  memory: 2942  grad_norm: 0.5203  loss: 0.1146  loss_bce: 0.0070  loss_dice: 0.1076
2024/07/04 22:13:00 - mmengine - INFO - Saving checkpoint at 113 epochs
2024/07/04 22:13:15 - mmengine - INFO - Epoch(val) [113][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990381532104392, 'occupancy': 0.8920459773640462, 'mIoU': 0.9455420652872427}, '1~2m': {'no_occupancy': 0.9984354527931523, 'occupancy': 0.7357291851129543, 'mIoU': 0.8670823189530532}, '2~3m': {'no_occupancy': 0.9994496897146287, 'occupancy': 0.5009102986763765, 'mIoU': 0.7501799941955025}, '3~4m': {'no_occupancy': 0.9998891049369345, 'occupancy': 0.2664992826398852, 'mIoU': 0.6331941937884098}, 'patch_mean': {'no_occupancy': 0.9992031001637887, 'occupancy': 0.5987961859483155, 'mIoU': 0.7989996430560521}}  total_overall: {'no_occupancy': 0.9992042202591028, 'occupancy': 0.8015347762491257, 'mIoU': 0.9003694982541143}  data_time: 0.0162  time: 0.1302
2024/07/04 22:14:19 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:14:19 - mmengine - INFO - Epoch(train) [114][91/91]  lr: 8.3725e-07  eta: 0:06:23  time: 0.6028  data_time: 0.0007  memory: 2942  grad_norm: 0.5144  loss: 0.1240  loss_bce: 0.0073  loss_dice: 0.1167
2024/07/04 22:14:19 - mmengine - INFO - Saving checkpoint at 114 epochs
2024/07/04 22:14:34 - mmengine - INFO - Epoch(val) [114][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990380987645261, 'occupancy': 0.8920378561087529, 'mIoU': 0.9455379774366395}, '1~2m': {'no_occupancy': 0.9984378518180228, 'occupancy': 0.7359493414526933, 'mIoU': 0.8671935966353581}, '2~3m': {'no_occupancy': 0.9994490929973231, 'occupancy': 0.5005656386798485, 'mIoU': 0.7500073658385857}, '3~4m': {'no_occupancy': 0.9998892133857562, 'occupancy': 0.26695371367061355, 'mIoU': 0.6334214635281848}, 'patch_mean': {'no_occupancy': 0.999203564241407, 'occupancy': 0.5988766374779771, 'mIoU': 0.7990401008596921}}  total_overall: {'no_occupancy': 0.9992046830659009, 'occupancy': 0.801601646089139, 'mIoU': 0.9004031645775199}  data_time: 0.0160  time: 0.1316
2024/07/04 22:15:38 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:15:38 - mmengine - INFO - Epoch(train) [115][91/91]  lr: 6.1558e-07  eta: 0:05:19  time: 0.5997  data_time: 0.0007  memory: 2942  grad_norm: 0.5069  loss: 0.1253  loss_bce: 0.0078  loss_dice: 0.1176
2024/07/04 22:15:38 - mmengine - INFO - Saving checkpoint at 115 epochs
2024/07/04 22:15:52 - mmengine - INFO - Epoch(val) [115][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990378806655977, 'occupancy': 0.8920093432143604, 'mIoU': 0.945523611939979}, '1~2m': {'no_occupancy': 0.9984410642719974, 'occupancy': 0.7363711298429487, 'mIoU': 0.8674060970574731}, '2~3m': {'no_occupancy': 0.9994499614978475, 'occupancy': 0.5006157332151125, 'mIoU': 0.75003284735648}, '3~4m': {'no_occupancy': 0.9998893218405915, 'occupancy': 0.26714542190305207, 'mIoU': 0.6335173718718218}, 'patch_mean': {'no_occupancy': 0.9992045570690086, 'occupancy': 0.5990354070438685, 'mIoU': 0.7991199820564385}}  total_overall: {'no_occupancy': 0.9992056760183722, 'occupancy': 0.8017901305268161, 'mIoU': 0.9004979032725942}  data_time: 0.0177  time: 0.1329
2024/07/04 22:16:56 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:16:56 - mmengine - INFO - Epoch(train) [116][91/91]  lr: 4.2776e-07  eta: 0:04:15  time: 0.5987  data_time: 0.0007  memory: 2942  grad_norm: 0.4870  loss: 0.1325  loss_bce: 0.0078  loss_dice: 0.1248
2024/07/04 22:16:56 - mmengine - INFO - Saving checkpoint at 116 epochs
2024/07/04 22:17:11 - mmengine - INFO - Epoch(val) [116][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.999039955710928, 'occupancy': 0.8922411413216854, 'mIoU': 0.9456405485163066}, '1~2m': {'no_occupancy': 0.9984403531316466, 'occupancy': 0.7363728001436075, 'mIoU': 0.8674065766376271}, '2~3m': {'no_occupancy': 0.9994508289244206, 'occupancy': 0.501551189245088, 'mIoU': 0.7505010090847544}, '3~4m': {'no_occupancy': 0.9998892676191786, 'occupancy': 0.266786355475763, 'mIoU': 0.6333378115474708}, 'patch_mean': {'no_occupancy': 0.9992051013465435, 'occupancy': 0.5992378715465361, 'mIoU': 0.7992214864465398}}  total_overall: {'no_occupancy': 0.9992062189982581, 'occupancy': 0.801962166458422, 'mIoU': 0.90058419272834}  data_time: 0.0160  time: 0.1341
2024/07/04 22:18:14 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:18:14 - mmengine - INFO - Epoch(train) [117][91/91]  lr: 2.7391e-07  eta: 0:03:11  time: 0.5975  data_time: 0.0007  memory: 2942  grad_norm: 0.5121  loss: 0.1228  loss_bce: 0.0076  loss_dice: 0.1152
2024/07/04 22:18:14 - mmengine - INFO - Saving checkpoint at 117 epochs
2024/07/04 22:18:29 - mmengine - INFO - Epoch(val) [117][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990397371907997, 'occupancy': 0.8922179244935771, 'mIoU': 0.9456288308421884}, '1~2m': {'no_occupancy': 0.9984409528020625, 'occupancy': 0.7364303881964345, 'mIoU': 0.8674356704992485}, '2~3m': {'no_occupancy': 0.9994496360865112, 'occupancy': 0.5003694035364233, 'mIoU': 0.7499095198114673}, '3~4m': {'no_occupancy': 0.9998893218405915, 'occupancy': 0.26714542190305207, 'mIoU': 0.6335173718718218}, 'patch_mean': {'no_occupancy': 0.9992049119799913, 'occupancy': 0.5990407845323718, 'mIoU': 0.7991228482561815}}  total_overall: {'no_occupancy': 0.9992060289670178, 'occupancy': 0.8019012216443382, 'mIoU': 0.9005536253056781}  data_time: 0.0159  time: 0.1342
2024/07/04 22:19:33 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:19:33 - mmengine - INFO - Epoch(train) [118][91/91]  lr: 1.5413e-07  eta: 0:02:07  time: 0.6018  data_time: 0.0007  memory: 2942  grad_norm: 0.5178  loss: 0.1280  loss_bce: 0.0076  loss_dice: 0.1204
2024/07/04 22:19:33 - mmengine - INFO - Saving checkpoint at 118 epochs
2024/07/04 22:19:48 - mmengine - INFO - Epoch(val) [118][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990400651808757, 'occupancy': 0.8922501058288701, 'mIoU': 0.9456450855048729}, '1~2m': {'no_occupancy': 0.998443671042709, 'occupancy': 0.7369077019745759, 'mIoU': 0.8676756865086425}, '2~3m': {'no_occupancy': 0.9994511539791417, 'occupancy': 0.5020918442683467, 'mIoU': 0.7507714991237442}, '3~4m': {'no_occupancy': 0.9998893218345898, 'occupancy': 0.2674084709260589, 'mIoU': 0.6336488963803244}, 'patch_mean': {'no_occupancy': 0.999206053009329, 'occupancy': 0.599664530749463, 'mIoU': 0.799435291879396}}  total_overall: {'no_occupancy': 0.9992071702892493, 'occupancy': 0.8021963200696252, 'mIoU': 0.9007017451794372}  data_time: 0.0163  time: 0.1315
2024/07/04 22:20:51 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:20:51 - mmengine - INFO - Epoch(train) [119][91/91]  lr: 6.8523e-08  eta: 0:01:03  time: 0.6065  data_time: 0.0007  memory: 2942  grad_norm: 0.4942  loss: 0.1345  loss_bce: 0.0080  loss_dice: 0.1265
2024/07/04 22:20:51 - mmengine - INFO - Saving checkpoint at 119 epochs
2024/07/04 22:21:06 - mmengine - INFO - Epoch(val) [119][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990394646444483, 'occupancy': 0.8921812978152973, 'mIoU': 0.9456103812298728}, '1~2m': {'no_occupancy': 0.9984398094517194, 'occupancy': 0.7362783161838655, 'mIoU': 0.8673590628177925}, '2~3m': {'no_occupancy': 0.9994511544555857, 'occupancy': 0.5016994236737108, 'mIoU': 0.7505752890646482}, '3~4m': {'no_occupancy': 0.9998892676131739, 'occupancy': 0.2670495333811917, 'mIoU': 0.6334694004971828}, 'patch_mean': {'no_occupancy': 0.9992049240412318, 'occupancy': 0.5993021427635163, 'mIoU': 0.799253533402374}}  total_overall: {'no_occupancy': 0.9992060424805133, 'occupancy': 0.8019093200231752, 'mIoU': 0.9005576812518442}  data_time: 0.0177  time: 0.1340
2024/07/04 22:22:11 - mmengine - INFO - Exp name: indoor_oms_dk_occ_20240704_194436
2024/07/04 22:22:11 - mmengine - INFO - Epoch(train) [120][91/91]  lr: 1.7134e-08  eta: 0:00:00  time: 0.6081  data_time: 0.0007  memory: 2942  grad_norm: 0.5724  loss: 0.1271  loss_bce: 0.0076  loss_dice: 0.1194
2024/07/04 22:22:11 - mmengine - INFO - Saving checkpoint at 120 epochs
2024/07/04 22:22:26 - mmengine - INFO - Epoch(val) [120][91/91]    by_distance: {'0-1m': {'no_occupancy': 0.9990404471453859, 'occupancy': 0.8922963546906825, 'mIoU': 0.9456684009180342}, '1~2m': {'no_occupancy': 0.9984400233043768, 'occupancy': 0.7364194761724004, 'mIoU': 0.8674297497383886}, '2~3m': {'no_occupancy': 0.9994501781007359, 'occupancy': 0.5010585397075477, 'mIoU': 0.7502543589041417}, '3~4m': {'no_occupancy': 0.9998892676191786, 'occupancy': 0.266786355475763, 'mIoU': 0.6333378115474708}, 'patch_mean': {'no_occupancy': 0.9992049790424193, 'occupancy': 0.5991401815115984, 'mIoU': 0.7991725802770089}}  total_overall: {'no_occupancy': 0.9992060961998174, 'occupancy': 0.8019625387482667, 'mIoU': 0.900584317474042}  data_time: 0.0169  time: 0.1311
