2024/07/08 17:53:45 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: linux
    Python: 3.8.10 (default, May 26 2023, 14:05:08) [GCC 9.4.0]
    CUDA available: True
    numpy_random_seed: 1038335013
    GPU 0,1,2,3: NVIDIA TITAN Xp
    CUDA_HOME: /usr/local/cuda
    NVCC: Cuda compilation tools, release 11.6, V11.6.124
    GCC: x86_64-linux-gnu-gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0
    PyTorch: 1.13.0+cu116
    PyTorch compiling details: PyTorch built with:
  - GCC 9.3
  - C++ Version: 201402
  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v2.6.0 (Git Hash 52b5f107dd9cf10910aaa19cb47f3abf9b349815)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX2
  - CUDA Runtime 11.6
  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86
  - CuDNN 8.4
    - Built with CuDNN 8.3.2
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.6, CUDNN_VERSION=8.3.2, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -fabi-version=11 -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -DEDGE_PROFILER_USE_KINETO -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wunused-local-typedefs -Wno-unused-parameter -Wno-unused-function -Wno-unused-result -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Werror=cast-function-type -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.13.0, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, 

    TorchVision: 0.14.0+cu116
    OpenCV: 4.8.1
    MMEngine: 0.10.1

Runtime environment:
    cudnn_benchmark: False
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 1}
    dist_cfg: {'backend': 'nccl'}
    seed: 1038335013
    Distributed launcher: pytorch
    Distributed training: True
    GPU number: 4
------------------------------------------------------------

2024/07/08 17:53:46 - mmengine - INFO - Config:
auto_scale_lr = dict(base_batch_size=4, enable=False)
bn_kwargs = dict(eps=2e-05, momentum=0.1)
class_names = [
    'no_occupancy',
    'occupancy',
]
custom_imports = dict(
    allow_failed_imports=False,
    imports=[
        'projects.SurroundOcc.surroundocc.models',
        'projects.SurroundOcc.surroundocc.datasets',
    ])
data_root = '/home/<USER>/indoor/zed_lidar'
data_shape = (
    3,
    384,
    512,
)
dataset_type = 'EcoAi.KujialeStereoDataset'
default_hooks = dict(
    checkpoint=dict(interval=1, type='CheckpointHook'),
    logger=dict(interval=100, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    timer=dict(type='IterTimerHook'),
    visualization=dict(type='mmdet3d.Det3DVisualizationHook'))
default_scope = 'mmdet'
depoly_model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=64, num_channels=[
                128,
                256,
                512,
            ]),
        fpn_lss_params=dict(in_channels=640, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(type='EfficientNetB0'),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    type='EcoOccVisionDepoly',
    use_grid_mask=False,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depoly=True,
        depth=120,
        feat_channels=64,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=8,
        num_views=2,
        type='LSSTransformer',
        z_range=(
            -0.05,
            0.15,
        )))
depth_quant_scale = 0.03125
depthview_shape = [
    720,
    768.0,
]
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=1))
featview_shape = [
    144.0,
    32.0,
]
find_unused_parameters = True
grid_quant_scale = 0.0078125
img_norm_cfg = dict(
    mean=[
        103.53,
        116.28,
        123.675,
    ], std=[
        1.0,
        1.0,
        1.0,
    ], to_rgb=False)
keys = ('img', )
launcher = 'pytorch'
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=50)
lss_bev_size = [
    -2,
    2,
    0,
    4,
    0.05,
]
lss_depth = 120
lss_grid_size = (
    80,
    80,
)
lss_num_points = 8
lss_z_range = (
    -0.05,
    0.15,
)
meta_keys = (
    'lidar2img',
    'img_shape',
    'pc_range',
    'occ_size',
    'gt_occ',
    'occ_path',
    'filename',
)
model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=64, num_channels=[
                128,
                256,
                512,
            ]),
        fpn_lss_params=dict(in_channels=640, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(type='EfficientNetB0'),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    is_vis=False,
    lambda_dice=1,
    type='EcoOccVision',
    use_grid_mask=True,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depth=120,
        feat_channels=64,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=8,
        num_views=2,
        type='LSSTransformer',
        z_range=(
            -0.05,
            0.15,
        )))
numC_Trans = 64
occ_size = [
    80,
    80,
    4,
]
optim_wrapper = dict(
    clip_grad=dict(max_norm=35, norm_type=2),
    optimizer=dict(lr=0.0001, type='AdamW', weight_decay=0.01),
    type='AmpOptimWrapper')
param_scheduler = [
    dict(
        begin=0,
        by_epoch=False,
        end=500,
        start_factor=0.3333333333333333,
        type='LinearLR'),
    dict(by_epoch=True, type='CosineAnnealingLR'),
]
point_cloud_range = [
    -2,
    0,
    -0.05,
    2,
    4,
    0.15,
]
resume = None
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0708_val.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor/zed_lidar',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
test_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
train_cfg = dict(max_epochs=24, type='EpochBasedTrainLoop', val_interval=1)
train_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0708_train.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor/zed_lidar',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(type='PhotoMetricDistortionMultiViewImage'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        test_mode=False,
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    prefetch_factor=8,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(type='PhotoMetricDistortionMultiViewImage'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
use_semantic = True
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0708_val.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor/zed_lidar',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
val_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
view_shape = [
    24.0,
    32.0,
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='mmdet3d.Det3DLocalVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = 'work_dirs/zed_lidar_0704/zed_lidar_0708'

2024/07/08 17:53:49 - mmengine - INFO - Autoplay mode, press [SPACE] to pause.
2024/07/08 17:53:49 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2024/07/08 17:53:50 - mmengine - WARNING - The prefix is not set in metric class BEVMetric.
Name of parameter - Initialization information

img_backbone.stem.stem.conv.weight - torch.Size([32, 3, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stem.stem.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stem.stem.norm.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stem.stem.norm.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.depth.conv.weight - torch.Size([32, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.0.conv.depth.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.depth.norm.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.depth.norm.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.project.conv.weight - torch.Size([16, 32, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.0.conv.project.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.project.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.project.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.expand_conv.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.expand_conv.conv.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.expand_conv.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.expand_conv.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.depth.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.depth.conv.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.depth.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.depth.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.project.conv.weight - torch.Size([24, 96, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.project.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.project.norm.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.project.norm.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.expand_conv.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.depth.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.depth.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.depth.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.depth.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.project.conv.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.project.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.project.norm.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.project.norm.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.expand_conv.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.expand_conv.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.expand_conv.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.expand_conv.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.depth.conv.weight - torch.Size([144, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.depth.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.depth.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.depth.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.project.conv.weight - torch.Size([40, 144, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.project.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.project.norm.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.project.norm.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.conv.weight - torch.Size([240, 40, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.expand_conv.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.depth.conv.weight - torch.Size([240, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.depth.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.depth.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.depth.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.project.conv.weight - torch.Size([40, 240, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.project.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.project.norm.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.project.norm.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.expand_conv.conv.weight - torch.Size([240, 40, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.expand_conv.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.expand_conv.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.expand_conv.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.depth.conv.weight - torch.Size([240, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.depth.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.depth.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.depth.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.project.conv.weight - torch.Size([80, 240, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.depth.conv.weight - torch.Size([480, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.project.conv.weight - torch.Size([80, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.depth.conv.weight - torch.Size([480, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.project.conv.weight - torch.Size([80, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.depth.conv.weight - torch.Size([480, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.project.conv.weight - torch.Size([112, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.project.conv.weight - torch.Size([112, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.project.conv.weight - torch.Size([112, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.project.conv.weight - torch.Size([192, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.depth.conv.weight - torch.Size([1152, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.project.conv.weight - torch.Size([320, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.project.conv.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.project.norm.weight - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.project.norm.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_4.conv.conv.weight - torch.Size([112, 320, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_4.conv.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_3.conv.conv.weight - torch.Size([40, 224, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_3.conv.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_2.conv.conv.weight - torch.Size([24, 80, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_2.conv.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_1.conv.conv.weight - torch.Size([16, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_1.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_0.conv.conv.weight - torch.Size([16, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_0.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_4.conv.conv.weight - torch.Size([224, 224, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_4.conv.conv.bias - torch.Size([224]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_3.conv.conv.weight - torch.Size([80, 80, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_3.conv.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_2.conv.conv.weight - torch.Size([48, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_2.conv.conv.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_1.conv.conv.weight - torch.Size([32, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_1.conv.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_0.conv.conv.weight - torch.Size([16, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_0.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_dispconv2.conv.weight - torch.Size([2, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv2.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_dispconv1.conv.weight - torch.Size([2, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv1.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_dispconv0.conv.weight - torch.Size([2, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv0.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_conf_mask_conv21.conv.weight - torch.Size([2, 66, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv21.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_conf_mask_conv11.conv.weight - torch.Size([2, 44, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv11.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_conf_mask_conv01.conv.weight - torch.Size([2, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv01.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.0.weight - torch.Size([16, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.0.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.1.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.1.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.0.weight - torch.Size([64, 384, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.0.weight - torch.Size([64, 112, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.0.weight - torch.Size([64, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.0.weight - torch.Size([64, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.0.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

view_transformer.depth_net.0.weight - torch.Size([120, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

view_transformer.feat_net.0.weight - torch.Size([64, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.conv1.weight - torch.Size([128, 64, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.conv2.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn2.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn2.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.downsample.weight - torch.Size([128, 64, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.downsample.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.conv1.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.conv2.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn2.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn2.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.conv1.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.downsample.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.downsample.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.conv1.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.conv1.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.downsample.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.downsample.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.conv1.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.0.weight - torch.Size([512, 640, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.3.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.4.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.4.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.1.weight - torch.Size([256, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.4.weight - torch.Size([256, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.4.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.final_conv.conv.weight - torch.Size([256, 256, 3, 3]): 
Initialized by user-defined `init_weights` in ConvModule  

bev_decoder.final_conv.conv.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.0.weight - torch.Size([512, 256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.0.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.2.weight - torch.Size([4, 512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.2.bias - torch.Size([4]): 
The value is the same before and after calling `init_weights` of EcoOccVision  
2024/07/08 17:53:51 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2024/07/08 17:53:51 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2024/07/08 17:53:51 - mmengine - INFO - Checkpoints will be saved to /home/<USER>/ecoaitoolkit/work_dirs/zed_lidar_0704/zed_lidar_0708.
2024/07/08 17:55:19 - mmengine - INFO - Epoch(train)  [1][100/195]  lr: 4.6560e-05  eta: 1:07:05  time: 0.6635  data_time: 0.0008  memory: 2924  grad_norm: 6.2305  loss: 1.0153  loss_bce: 0.0508  loss_dice: 0.9645
2024/07/08 17:56:22 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 17:56:22 - mmengine - INFO - Saving checkpoint at 1 epochs
2024/07/08 17:56:28 - mmengine - INFO - Epoch(val) [1][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9519560140463074, 'occupancy': 0.07904019904364333, 'mIoU': 0.5154981065449753}, '1~2m': {'no_occupancy': 0.9875897591293672, 'occupancy': 0.015282669830799844, 'mIoU': 0.5014362144800835}, '2~3m': {'no_occupancy': 0.9941161356628981, 'occupancy': 0.0021960799972049892, 'mIoU': 0.49815610783005154}, '3~4m': {'no_occupancy': 0.9987473806272367, 'occupancy': 0.0, 'mIoU': 0.49937369031361833}, 'patch_mean': {'no_occupancy': 0.9831023223664523, 'occupancy': 0.02412973721791204, 'mIoU': 0.5036160297921822}}  total_overall: {'no_occupancy': 0.9831340795680784, 'occupancy': 0.0601356734478763, 'mIoU': 0.5216348765079774}  data_time: 0.1087  time: 0.2231
2024/07/08 17:57:54 - mmengine - INFO - Epoch(train)  [2][100/195]  lr: 7.2301e-05  eta: 0:58:41  time: 0.6948  data_time: 0.0009  memory: 2924  grad_norm: 11.3345  loss: 0.8589  loss_bce: 0.0407  loss_dice: 0.8182
2024/07/08 17:59:01 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 17:59:01 - mmengine - INFO - Saving checkpoint at 2 epochs
2024/07/08 17:59:07 - mmengine - INFO - Epoch(val) [2][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9680877974403512, 'occupancy': 0.1046895952712861, 'mIoU': 0.5363886963558187}, '1~2m': {'no_occupancy': 0.9911077177895266, 'occupancy': 0.003489595530151359, 'mIoU': 0.49729865665983897}, '2~3m': {'no_occupancy': 0.9980585150216614, 'occupancy': 0.0, 'mIoU': 0.4990292575108307}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9891911843263995, 'occupancy': 0.027044797700359367, 'mIoU': 0.5081179910133794}}  total_overall: {'no_occupancy': 0.9892108015711094, 'occupancy': 0.07999108244654964, 'mIoU': 0.5346009420088296}  data_time: 0.0352  time: 0.1483
2024/07/08 18:00:32 - mmengine - INFO - Epoch(train)  [3][100/195]  lr: 9.6983e-05  eta: 0:55:28  time: 0.7018  data_time: 0.0009  memory: 2924  grad_norm: inf  loss: 0.8161  loss_bce: 0.0407  loss_dice: 0.7754
2024/07/08 18:01:39 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:01:39 - mmengine - INFO - Saving checkpoint at 3 epochs
2024/07/08 18:01:45 - mmengine - INFO - Epoch(val) [3][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9746344565278121, 'occupancy': 0.14928451107513205, 'mIoU': 0.5619594838014721}, '1~2m': {'no_occupancy': 0.9911437042014685, 'occupancy': 0.001751598167456766, 'mIoU': 0.4964476511844626}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9908395754408434, 'occupancy': 0.0377590273106472, 'mIoU': 0.5142993013757453}}  total_overall: {'no_occupancy': 0.9908575472787862, 'occupancy': 0.1085262854717221, 'mIoU': 0.5496919163752542}  data_time: 0.0291  time: 0.1421
2024/07/08 18:03:13 - mmengine - INFO - Epoch(train)  [4][100/195]  lr: 9.6194e-05  eta: 0:52:52  time: 0.7074  data_time: 0.0009  memory: 2924  grad_norm: 7.6217  loss: 0.7868  loss_bce: 0.0384  loss_dice: 0.7483
2024/07/08 18:04:20 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:04:20 - mmengine - INFO - Saving checkpoint at 4 epochs
2024/07/08 18:04:27 - mmengine - INFO - Epoch(val) [4][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9747765204922061, 'occupancy': 0.16898138672685914, 'mIoU': 0.5718789536095326}, '1~2m': {'no_occupancy': 0.9905479001442704, 'occupancy': 0.0077369917191941655, 'mIoU': 0.4991424459317323}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9907261404176425, 'occupancy': 0.04417959461151333, 'mIoU': 0.517452867514578}}  total_overall: {'no_occupancy': 0.9907465171312289, 'occupancy': 0.12283373943626069, 'mIoU': 0.5567901282837447}  data_time: 0.0299  time: 0.1436
2024/07/08 18:05:54 - mmengine - INFO - Epoch(train)  [5][100/195]  lr: 9.3301e-05  eta: 0:50:16  time: 0.7131  data_time: 0.0009  memory: 2924  grad_norm: 6.0144  loss: 0.7789  loss_bce: 0.0395  loss_dice: 0.7394
2024/07/08 18:07:01 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:07:01 - mmengine - INFO - Saving checkpoint at 5 epochs
2024/07/08 18:07:06 - mmengine - INFO - Epoch(val) [5][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.974282409170592, 'occupancy': 0.14300769934628935, 'mIoU': 0.5586450542584407}, '1~2m': {'no_occupancy': 0.989202250490527, 'occupancy': 0.01605145908943377, 'mIoU': 0.5026268547899804}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9902662001738031, 'occupancy': 0.039764789608930776, 'mIoU': 0.5150154948913669}}  total_overall: {'no_occupancy': 0.9902833415597853, 'occupancy': 0.10282030302597492, 'mIoU': 0.5465518222928801}  data_time: 0.0361  time: 0.1497
2024/07/08 18:07:37 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:08:32 - mmengine - INFO - Epoch(train)  [6][100/195]  lr: 8.9668e-05  eta: 0:47:35  time: 0.7085  data_time: 0.0009  memory: 2924  grad_norm: 6.0098  loss: 0.7604  loss_bce: 0.0376  loss_dice: 0.7228
2024/07/08 18:09:39 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:09:39 - mmengine - INFO - Saving checkpoint at 6 epochs
2024/07/08 18:09:45 - mmengine - INFO - Epoch(val) [6][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9784730353093156, 'occupancy': 0.13702488261910342, 'mIoU': 0.5577489589642095}, '1~2m': {'no_occupancy': 0.9896910391235897, 'occupancy': 0.03054868021063473, 'mIoU': 0.5101198596671123}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9914360538667496, 'occupancy': 0.04189339070743454, 'mIoU': 0.516664722287092}}  total_overall: {'no_occupancy': 0.9914472454457929, 'occupancy': 0.09842162998087883, 'mIoU': 0.5449344377133358}  data_time: 0.0295  time: 0.1424
2024/07/08 18:11:12 - mmengine - INFO - Epoch(train)  [7][100/195]  lr: 8.5355e-05  eta: 0:44:59  time: 0.7170  data_time: 0.0009  memory: 2924  grad_norm: 7.0414  loss: 0.7405  loss_bce: 0.0367  loss_dice: 0.7038
2024/07/08 18:12:19 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:12:19 - mmengine - INFO - Saving checkpoint at 7 epochs
2024/07/08 18:12:24 - mmengine - INFO - Epoch(val) [7][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9766837845450181, 'occupancy': 0.1189078949130002, 'mIoU': 0.5477958397290091}, '1~2m': {'no_occupancy': 0.9904711142276307, 'occupancy': 0.007294748945956305, 'mIoU': 0.4988829315867935}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9911837599516855, 'occupancy': 0.03155066096473913, 'mIoU': 0.5113672104582123}}  total_overall: {'no_occupancy': 0.9911951523400996, 'occupancy': 0.08351460522584254, 'mIoU': 0.5373548787829711}  data_time: 0.0371  time: 0.1500
2024/07/08 18:13:51 - mmengine - INFO - Epoch(train)  [8][100/195]  lr: 8.0438e-05  eta: 0:42:21  time: 0.7063  data_time: 0.0009  memory: 2924  grad_norm: 6.3785  loss: 0.7369  loss_bce: 0.0376  loss_dice: 0.6992
2024/07/08 18:14:58 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:14:58 - mmengine - INFO - Saving checkpoint at 8 epochs
2024/07/08 18:15:04 - mmengine - INFO - Epoch(val) [8][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.977734587182184, 'occupancy': 0.14632698670526975, 'mIoU': 0.5620307869437269}, '1~2m': {'no_occupancy': 0.9897923700493253, 'occupancy': 0.017968853986423533, 'mIoU': 0.5038806120178744}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9912767745664006, 'occupancy': 0.04107396017292332, 'mIoU': 0.516175367369662}}  total_overall: {'no_occupancy': 0.9912897285494058, 'occupancy': 0.1028150539804258, 'mIoU': 0.5470523912649158}  data_time: 0.0309  time: 0.1440
2024/07/08 18:16:30 - mmengine - INFO - Epoch(train)  [9][100/195]  lr: 7.5000e-05  eta: 0:39:47  time: 0.7116  data_time: 0.0009  memory: 2924  grad_norm: 5.8058  loss: 0.7117  loss_bce: 0.0349  loss_dice: 0.6768
2024/07/08 18:17:38 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:17:38 - mmengine - INFO - Saving checkpoint at 9 epochs
2024/07/08 18:17:43 - mmengine - INFO - Epoch(val) [9][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9764817631523804, 'occupancy': 0.11903347854601516, 'mIoU': 0.5477576208491978}, '1~2m': {'no_occupancy': 0.9899507327167031, 'occupancy': 0.0026114770619422496, 'mIoU': 0.49628110488932264}, '2~3m': {'no_occupancy': 0.9980454181578449, 'occupancy': 0.0, 'mIoU': 0.49902270907892243}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9909971552702469, 'occupancy': 0.03041123890198935, 'mIoU': 0.5107041970861181}}  total_overall: {'no_occupancy': 0.9910086662498784, 'occupancy': 0.08162305960680313, 'mIoU': 0.5363158629283408}  data_time: 0.0307  time: 0.1442
2024/07/08 18:19:10 - mmengine - INFO - Epoch(train) [10][100/195]  lr: 6.9134e-05  eta: 0:37:12  time: 0.7107  data_time: 0.0009  memory: 2924  grad_norm: 6.0922  loss: 0.6874  loss_bce: 0.0337  loss_dice: 0.6538
2024/07/08 18:20:18 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:20:18 - mmengine - INFO - Saving checkpoint at 10 epochs
2024/07/08 18:20:24 - mmengine - INFO - Epoch(val) [10][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9747171892739206, 'occupancy': 0.11642088316942031, 'mIoU': 0.5455690362216704}, '1~2m': {'no_occupancy': 0.989118680117714, 'occupancy': 0.018097540243319346, 'mIoU': 0.5036081101805167}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.990354002606432, 'occupancy': 0.03362960585318492, 'mIoU': 0.5119918042298085}}  total_overall: {'no_occupancy': 0.9903670554230736, 'occupancy': 0.08378983967819366, 'mIoU': 0.5370784475506336}  data_time: 0.0313  time: 0.1440
2024/07/08 18:21:15 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:21:51 - mmengine - INFO - Epoch(train) [11][100/195]  lr: 6.2941e-05  eta: 0:34:39  time: 0.7092  data_time: 0.0009  memory: 2924  grad_norm: 5.7665  loss: 0.6760  loss_bce: 0.0334  loss_dice: 0.6426
2024/07/08 18:22:59 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:22:59 - mmengine - INFO - Saving checkpoint at 11 epochs
2024/07/08 18:23:04 - mmengine - INFO - Epoch(val) [11][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9790904226827688, 'occupancy': 0.1360799838958599, 'mIoU': 0.5575852032893144}, '1~2m': {'no_occupancy': 0.990071900674384, 'occupancy': 0.011508670333889179, 'mIoU': 0.5007902855041366}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9916856160978115, 'occupancy': 0.036897163557437274, 'mIoU': 0.5142913898276243}}  total_overall: {'no_occupancy': 0.9916960082744768, 'occupancy': 0.09288580950795063, 'mIoU': 0.5422909088912137}  data_time: 0.0408  time: 0.1537
2024/07/08 18:24:31 - mmengine - INFO - Epoch(train) [12][100/195]  lr: 5.6526e-05  eta: 0:32:05  time: 0.7126  data_time: 0.0009  memory: 2924  grad_norm: 6.0195  loss: 0.6805  loss_bce: 0.0352  loss_dice: 0.6452
2024/07/08 18:25:39 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:25:39 - mmengine - INFO - Saving checkpoint at 12 epochs
2024/07/08 18:25:44 - mmengine - INFO - Epoch(val) [12][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9788469131044827, 'occupancy': 0.12071152859880692, 'mIoU': 0.5497792208516448}, '1~2m': {'no_occupancy': 0.9903499539511037, 'occupancy': 0.015069715459977828, 'mIoU': 0.5027098347055408}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9916942520224199, 'occupancy': 0.03394531101469619, 'mIoU': 0.512819781518558}}  total_overall: {'no_occupancy': 0.9917036087977793, 'occupancy': 0.08405765997782308, 'mIoU': 0.5378806343878012}  data_time: 0.0380  time: 0.1510
2024/07/08 18:27:11 - mmengine - INFO - Epoch(train) [13][100/195]  lr: 5.0000e-05  eta: 0:29:31  time: 0.7112  data_time: 0.0009  memory: 2924  grad_norm: 6.2583  loss: 0.6647  loss_bce: 0.0344  loss_dice: 0.6304
2024/07/08 18:28:18 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:28:18 - mmengine - INFO - Saving checkpoint at 13 epochs
2024/07/08 18:28:24 - mmengine - INFO - Epoch(val) [13][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9805267639900883, 'occupancy': 0.13768142467558614, 'mIoU': 0.5591040943328373}, '1~2m': {'no_occupancy': 0.9896268015787072, 'occupancy': 0.014659788337597339, 'mIoU': 0.5021432949581522}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9919334266507221, 'occupancy': 0.03808530325329587, 'mIoU': 0.515009364952009}}  total_overall: {'no_occupancy': 0.9919423617591544, 'occupancy': 0.09176582690341172, 'mIoU': 0.5418540943312831}  data_time: 0.0301  time: 0.1433
2024/07/08 18:29:51 - mmengine - INFO - Epoch(train) [14][100/195]  lr: 4.3474e-05  eta: 0:26:57  time: 0.7147  data_time: 0.0009  memory: 2924  grad_norm: 6.2880  loss: 0.6433  loss_bce: 0.0331  loss_dice: 0.6102
2024/07/08 18:30:59 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:30:59 - mmengine - INFO - Saving checkpoint at 14 epochs
2024/07/08 18:31:05 - mmengine - INFO - Epoch(val) [14][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.981113421912467, 'occupancy': 0.13004218502954853, 'mIoU': 0.5555778034710077}, '1~2m': {'no_occupancy': 0.9895289764323227, 'occupancy': 0.014355498785005555, 'mIoU': 0.5019422376086642}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9920556348447208, 'occupancy': 0.03609942095363852, 'mIoU': 0.5140775278991797}}  total_overall: {'no_occupancy': 0.99206343818319, 'occupancy': 0.08554870335633329, 'mIoU': 0.5388060707697616}  data_time: 0.0395  time: 0.1529
2024/07/08 18:32:31 - mmengine - INFO - Epoch(train) [15][100/195]  lr: 3.7059e-05  eta: 0:24:23  time: 0.7136  data_time: 0.0009  memory: 2924  grad_norm: 6.0143  loss: 0.6572  loss_bce: 0.0343  loss_dice: 0.6229
2024/07/08 18:33:39 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:33:39 - mmengine - INFO - Saving checkpoint at 15 epochs
2024/07/08 18:33:45 - mmengine - INFO - Epoch(val) [15][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9813942636678653, 'occupancy': 0.13197427847543508, 'mIoU': 0.5566842710716502}, '1~2m': {'no_occupancy': 0.9902481095655685, 'occupancy': 0.021751712733286084, 'mIoU': 0.5059999111494273}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9923056285668818, 'occupancy': 0.03843149780218029, 'mIoU': 0.5153685631845311}}  total_overall: {'no_occupancy': 0.9923134407855245, 'occupancy': 0.08997551218244948, 'mIoU': 0.541144476483987}  data_time: 0.0287  time: 0.1421
2024/07/08 18:34:54 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:35:11 - mmengine - INFO - Epoch(train) [16][100/195]  lr: 3.0866e-05  eta: 0:21:49  time: 0.7062  data_time: 0.0010  memory: 2924  grad_norm: 6.1880  loss: 0.6260  loss_bce: 0.0320  loss_dice: 0.5940
2024/07/08 18:36:19 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:36:19 - mmengine - INFO - Saving checkpoint at 16 epochs
2024/07/08 18:36:25 - mmengine - INFO - Epoch(val) [16][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9825542366956069, 'occupancy': 0.14589243520171108, 'mIoU': 0.564223335948659}, '1~2m': {'no_occupancy': 0.9897314956871398, 'occupancy': 0.02693232604462497, 'mIoU': 0.5083319108658824}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.99246646835421, 'occupancy': 0.043206190311584014, 'mIoU': 0.517836329332897}}  total_overall: {'no_occupancy': 0.9924740313488694, 'occupancy': 0.09765324951661178, 'mIoU': 0.5450636404327406}  data_time: 0.0294  time: 0.1435
2024/07/08 18:37:51 - mmengine - INFO - Epoch(train) [17][100/195]  lr: 2.5000e-05  eta: 0:19:14  time: 0.7142  data_time: 0.0009  memory: 2924  grad_norm: 6.3102  loss: 0.6282  loss_bce: 0.0324  loss_dice: 0.5958
2024/07/08 18:38:58 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:38:58 - mmengine - INFO - Saving checkpoint at 17 epochs
2024/07/08 18:39:04 - mmengine - INFO - Epoch(val) [17][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9836470093440809, 'occupancy': 0.15623481602544287, 'mIoU': 0.5699409126847619}, '1~2m': {'no_occupancy': 0.9898583415426362, 'occupancy': 0.022716699383857755, 'mIoU': 0.506287520463247}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9927713729802026, 'occupancy': 0.04473787885232516, 'mIoU': 0.5187546259162639}}  total_overall: {'no_occupancy': 0.9927784366192717, 'occupancy': 0.10133257824842294, 'mIoU': 0.5470555074338473}  data_time: 0.0353  time: 0.1477
2024/07/08 18:40:31 - mmengine - INFO - Epoch(train) [18][100/195]  lr: 1.9562e-05  eta: 0:16:40  time: 0.7058  data_time: 0.0009  memory: 2924  grad_norm: 6.3379  loss: 0.6193  loss_bce: 0.0324  loss_dice: 0.5869
2024/07/08 18:41:39 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:41:39 - mmengine - INFO - Saving checkpoint at 18 epochs
2024/07/08 18:41:44 - mmengine - INFO - Epoch(val) [18][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9821300592635492, 'occupancy': 0.12340936722051757, 'mIoU': 0.5527697132420334}, '1~2m': {'no_occupancy': 0.9900507072113967, 'occupancy': 0.02665768093152121, 'mIoU': 0.5083541940714589}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9924402268772597, 'occupancy': 0.037516762038009695, 'mIoU': 0.5149784944576348}}  total_overall: {'no_occupancy': 0.9924468625089625, 'occupancy': 0.08436767533234912, 'mIoU': 0.5384072689206558}  data_time: 0.0416  time: 0.1543
2024/07/08 18:43:11 - mmengine - INFO - Epoch(train) [19][100/195]  lr: 1.4645e-05  eta: 0:14:06  time: 0.7134  data_time: 0.0009  memory: 2924  grad_norm: 6.7664  loss: 0.5797  loss_bce: 0.0306  loss_dice: 0.5490
2024/07/08 18:44:18 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:44:18 - mmengine - INFO - Saving checkpoint at 19 epochs
2024/07/08 18:44:24 - mmengine - INFO - Epoch(val) [19][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9828053250869471, 'occupancy': 0.12784015229292445, 'mIoU': 0.5553227386899358}, '1~2m': {'no_occupancy': 0.9898764472979039, 'occupancy': 0.024660999883714663, 'mIoU': 0.5072687235908093}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9925654783547361, 'occupancy': 0.03812528804415978, 'mIoU': 0.5153453831994479}}  total_overall: {'no_occupancy': 0.9925717891088819, 'occupancy': 0.08532821753273834, 'mIoU': 0.5389500033208101}  data_time: 0.0301  time: 0.1440
2024/07/08 18:45:50 - mmengine - INFO - Epoch(train) [20][100/195]  lr: 1.0332e-05  eta: 0:11:31  time: 0.7055  data_time: 0.0009  memory: 2924  grad_norm: 7.0326  loss: 0.5945  loss_bce: 0.0305  loss_dice: 0.5639
2024/07/08 18:46:58 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:46:58 - mmengine - INFO - Saving checkpoint at 20 epochs
2024/07/08 18:47:04 - mmengine - INFO - Epoch(val) [20][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9826646697788723, 'occupancy': 0.12446039198973609, 'mIoU': 0.5535625308843042}, '1~2m': {'no_occupancy': 0.9895669150170975, 'occupancy': 0.02458371177717227, 'mIoU': 0.5070753133971349}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9924529314575158, 'occupancy': 0.03726102594172709, 'mIoU': 0.5148569786996214}}  total_overall: {'no_occupancy': 0.9924591407450554, 'occupancy': 0.08279839518555666, 'mIoU': 0.537628767965306}  data_time: 0.0301  time: 0.1439
2024/07/08 18:48:30 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:48:30 - mmengine - INFO - Epoch(train) [21][100/195]  lr: 6.6987e-06  eta: 0:08:57  time: 0.7155  data_time: 0.0010  memory: 2924  grad_norm: 7.1879  loss: 0.5738  loss_bce: 0.0298  loss_dice: 0.5440
2024/07/08 18:49:38 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:49:38 - mmengine - INFO - Saving checkpoint at 21 epochs
2024/07/08 18:49:44 - mmengine - INFO - Epoch(val) [21][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9832291925764561, 'occupancy': 0.13138027996821322, 'mIoU': 0.5573047362723347}, '1~2m': {'no_occupancy': 0.9896392233175606, 'occupancy': 0.030659688748106317, 'mIoU': 0.5101494560328335}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9926121392320274, 'occupancy': 0.040509992179079884, 'mIoU': 0.5165610657055536}}  total_overall: {'no_occupancy': 0.992618322304357, 'occupancy': 0.0883032580929421, 'mIoU': 0.5404607901986496}  data_time: 0.0346  time: 0.1478
2024/07/08 18:51:10 - mmengine - INFO - Epoch(train) [22][100/195]  lr: 3.8060e-06  eta: 0:06:23  time: 0.7074  data_time: 0.0009  memory: 2924  grad_norm: 7.1600  loss: 0.5650  loss_bce: 0.0299  loss_dice: 0.5351
2024/07/08 18:52:18 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:52:18 - mmengine - INFO - Saving checkpoint at 22 epochs
2024/07/08 18:52:24 - mmengine - INFO - Epoch(val) [22][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9831808367106164, 'occupancy': 0.1267565721148102, 'mIoU': 0.5549687044127133}, '1~2m': {'no_occupancy': 0.9895855489544908, 'occupancy': 0.02618162799020013, 'mIoU': 0.5078835884723455}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9925866316748001, 'occupancy': 0.03823455002625258, 'mIoU': 0.5154105908505263}}  total_overall: {'no_occupancy': 0.9925925725767203, 'occupancy': 0.08400245828875788, 'mIoU': 0.5382975154327391}  data_time: 0.0292  time: 0.1428
2024/07/08 18:53:51 - mmengine - INFO - Epoch(train) [23][100/195]  lr: 1.7037e-06  eta: 0:03:49  time: 0.7121  data_time: 0.0010  memory: 2924  grad_norm: 6.9639  loss: 0.5657  loss_bce: 0.0301  loss_dice: 0.5356
2024/07/08 18:54:59 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:54:59 - mmengine - INFO - Saving checkpoint at 23 epochs
2024/07/08 18:55:05 - mmengine - INFO - Epoch(val) [23][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9833195535052475, 'occupancy': 0.1275317232152364, 'mIoU': 0.555425638360242}, '1~2m': {'no_occupancy': 0.9897998575767183, 'occupancy': 0.027613207414787472, 'mIoU': 0.5087065324957529}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9926748880290147, 'occupancy': 0.03878623265750597, 'mIoU': 0.5157305603432604}}  total_overall: {'no_occupancy': 0.99268078894186, 'occupancy': 0.08511517509799248, 'mIoU': 0.5388979820199262}  data_time: 0.0313  time: 0.1452
2024/07/08 18:56:31 - mmengine - INFO - Epoch(train) [24][100/195]  lr: 4.2776e-07  eta: 0:01:15  time: 0.7118  data_time: 0.0010  memory: 2924  grad_norm: 6.3374  loss: 0.5679  loss_bce: 0.0308  loss_dice: 0.5371
2024/07/08 18:57:39 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240708_175342
2024/07/08 18:57:39 - mmengine - INFO - Saving checkpoint at 24 epochs
2024/07/08 18:57:45 - mmengine - INFO - Epoch(val) [24][21/21]    by_distance: {'0-1m': {'no_occupancy': 0.9833022575653436, 'occupancy': 0.12937030125792068, 'mIoU': 0.5563362794116321}, '1~2m': {'no_occupancy': 0.9897327116454661, 'occupancy': 0.029230259623494438, 'mIoU': 0.5094814856344803}, '2~3m': {'no_occupancy': 0.998069433980034, 'occupancy': 0.0, 'mIoU': 0.499034716990017}, '3~4m': {'no_occupancy': 0.9995107070540592, 'occupancy': 0.0, 'mIoU': 0.4997553535270296}, 'patch_mean': {'no_occupancy': 0.9926537775612257, 'occupancy': 0.03965014022035378, 'mIoU': 0.5161519588907898}}  total_overall: {'no_occupancy': 0.992659793794394, 'occupancy': 0.08666593984679981, 'mIoU': 0.5396628668205968}  data_time: 0.0296  time: 0.1433
