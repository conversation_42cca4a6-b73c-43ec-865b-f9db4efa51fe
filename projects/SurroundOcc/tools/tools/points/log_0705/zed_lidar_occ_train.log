2024/07/05 18:05:34 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: linux
    Python: 3.8.10 (default, May 26 2023, 14:05:08) [GCC 9.4.0]
    CUDA available: True
    numpy_random_seed: 596596955
    GPU 0,1,2,3: NVIDIA TITAN Xp
    CUDA_HOME: /usr/local/cuda
    NVCC: Cuda compilation tools, release 11.6, V11.6.124
    GCC: x86_64-linux-gnu-gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0
    PyTorch: 1.13.0+cu116
    PyTorch compiling details: PyTorch built with:
  - GCC 9.3
  - C++ Version: 201402
  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v2.6.0 (Git Hash 52b5f107dd9cf10910aaa19cb47f3abf9b349815)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX2
  - CUDA Runtime 11.6
  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86
  - CuDNN 8.4
    - Built with CuDNN 8.3.2
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.6, CUDNN_VERSION=8.3.2, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -fabi-version=11 -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -DEDGE_PROFILER_USE_KINETO -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wunused-local-typedefs -Wno-unused-parameter -Wno-unused-function -Wno-unused-result -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Werror=cast-function-type -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.13.0, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, 

    TorchVision: 0.14.0+cu116
    OpenCV: 4.8.1
    MMEngine: 0.10.1

Runtime environment:
    cudnn_benchmark: False
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 1}
    dist_cfg: {'backend': 'nccl'}
    seed: 596596955
    Distributed launcher: pytorch
    Distributed training: True
    GPU number: 4
------------------------------------------------------------

2024/07/05 18:05:34 - mmengine - INFO - Config:
auto_scale_lr = dict(base_batch_size=4, enable=False)
bn_kwargs = dict(eps=2e-05, momentum=0.1)
class_names = [
    'no_occupancy',
    'occupancy',
]
custom_imports = dict(
    allow_failed_imports=False,
    imports=[
        'projects.SurroundOcc.surroundocc.models',
        'projects.SurroundOcc.surroundocc.datasets',
    ])
data_root = '/home/<USER>/indoor'
data_shape = (
    3,
    384,
    512,
)
dataset_type = 'EcoAi.KujialeStereoDataset'
default_hooks = dict(
    checkpoint=dict(interval=1, type='CheckpointHook'),
    logger=dict(interval=100, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    timer=dict(type='IterTimerHook'),
    visualization=dict(type='mmdet3d.Det3DVisualizationHook'))
default_scope = 'mmdet'
depoly_model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=64, num_channels=[
                128,
                256,
                512,
            ]),
        fpn_lss_params=dict(in_channels=640, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(type='EfficientNetB0'),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            64,
            64,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    type='EcoOccVisionDepoly',
    use_grid_mask=False,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depoly=True,
        depth=80,
        feat_channels=64,
        grid_size=(
            80,
            80,
        ),
        in_channels=64,
        num_points=8,
        num_views=2,
        type='LSSTransformer',
        z_range=(
            -0.05,
            0.15,
        )))
depth_quant_scale = 0.03125
depthview_shape = [
    480,
    768.0,
]
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=1))
featview_shape = [
    144.0,
    32.0,
]
find_unused_parameters = True
grid_quant_scale = 0.0078125
img_norm_cfg = dict(
    mean=[
        103.53,
        116.28,
        123.675,
    ], std=[
        1.0,
        1.0,
        1.0,
    ], to_rgb=False)
keys = ('img', )
launcher = 'pytorch'
load_from = None
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=50)
lss_bev_size = [
    -2,
    2,
    0,
    4,
    0.05,
]
lss_depth = 80
lss_grid_size = (
    80,
    80,
)
lss_num_points = 8
lss_z_range = (
    -0.05,
    0.15,
)
meta_keys = (
    'lidar2img',
    'img_shape',
    'pc_range',
    'occ_size',
    'gt_occ',
    'occ_path',
    'filename',
)
model = dict(
    bev_decoder=dict(
        Dz=4, in_dim=256, num_classes=1, out_dim=256, type='FlashOcc'),
    bev_encoder=dict(
        custom_resnet_params=dict(
            numC_input=128, num_channels=[
                128,
                256,
                512,
            ]),
        fpn_lss_params=dict(in_channels=640, out_channels=256),
        type='BEVEncoder2D'),
    img_backbone=dict(type='EfficientNetB0'),
    img_neck=dict(
        bn_kwargs=dict(eps=2e-05, momentum=0.1),
        feat_channels=[
            128,
            128,
        ],
        in_channels=[
            112,
            320,
        ],
        indexes=[
            -2,
            -1,
        ],
        scale_factor=2,
        type='FastSCNNNeck'),
    is_vis=False,
    lambda_dice=1,
    type='EcoOccVision',
    use_grid_mask=True,
    view_transformer=dict(
        bev_size=[
            -2,
            2,
            0,
            4,
            0.05,
        ],
        depth=80,
        feat_channels=128,
        grid_size=(
            80,
            80,
        ),
        in_channels=128,
        num_points=8,
        num_views=2,
        type='LSSTransformer',
        z_range=(
            -0.05,
            0.15,
        )))
numC_Trans = 64
occ_size = [
    80,
    80,
    4,
]
optim_wrapper = dict(
    clip_grad=dict(max_norm=35, norm_type=2),
    optimizer=dict(lr=0.0001, type='AdamW', weight_decay=0.01),
    type='AmpOptimWrapper')
param_scheduler = [
    dict(
        begin=0,
        by_epoch=False,
        end=500,
        start_factor=0.3333333333333333,
        type='LinearLR'),
    dict(by_epoch=True, type='CosineAnnealingLR'),
]
point_cloud_range = [
    -2,
    0,
    -0.05,
    2,
    4,
    0.15,
]
resume = False
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0704_train.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
test_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
train_cfg = dict(max_epochs=120, type='EpochBasedTrainLoop', val_interval=1)
train_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0704_train.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(type='PhotoMetricDistortionMultiViewImage'),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        test_mode=False,
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    pin_memory=True,
    prefetch_factor=8,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(type='PhotoMetricDistortionMultiViewImage'),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
use_semantic = True
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=8,
    dataset=dict(
        ann_file='pkl/indoor_zed_lidar_0704_train.pkl',
        classes=[
            'no_occupancy',
            'occupancy',
        ],
        data_root='/home/<USER>/indoor',
        occ_size=[
            80,
            80,
            4,
        ],
        pc_range=[
            -2,
            0,
            -0.05,
            2,
            4,
            0.15,
        ],
        pipeline=[
            dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
            dict(type='LoadKJLOccupancy', use_semantic=True),
            dict(scales=[
                384,
                512,
            ], type='ScaleImageMultiViewImage'),
            dict(
                mean=[
                    103.53,
                    116.28,
                    123.675,
                ],
                std=[
                    1.0,
                    1.0,
                    1.0,
                ],
                to_rgb=False,
                type='NormalizeMultiviewImage'),
            dict(
                keys=('img', ),
                meta_keys=(
                    'lidar2img',
                    'img_shape',
                    'pc_range',
                    'occ_size',
                    'gt_occ',
                    'occ_path',
                    'filename',
                ),
                type='mmdet3d.Pack3DDetInputs'),
        ],
        type='EcoAi.KujialeStereoDataset',
        use_semantic=True),
    drop_last=False,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    classes=[
        'no_occupancy',
        'occupancy',
    ],
    distance_ranges=[
        '0-1m',
        '1~2m',
        '2~3m',
        '3~4m',
    ],
    eval_metrics3d=False,
    eval_patch='fn',
    type='BEVMetric',
    use_semantic=True)
val_pipeline = [
    dict(to_float32=True, type='LoadMultiViewImageFromFiles_OCC'),
    dict(type='LoadKJLOccupancy', use_semantic=True),
    dict(scales=[
        384,
        512,
    ], type='ScaleImageMultiViewImage'),
    dict(
        mean=[
            103.53,
            116.28,
            123.675,
        ],
        std=[
            1.0,
            1.0,
            1.0,
        ],
        to_rgb=False,
        type='NormalizeMultiviewImage'),
    dict(
        keys=('img', ),
        meta_keys=(
            'lidar2img',
            'img_shape',
            'pc_range',
            'occ_size',
            'gt_occ',
            'occ_path',
            'filename',
        ),
        type='mmdet3d.Pack3DDetInputs'),
]
view_shape = [
    24.0,
    32.0,
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='mmdet3d.Det3DLocalVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = 'work_dirs/zed_lidar_0704/zed_lidar_train'

2024/07/05 18:05:41 - mmengine - INFO - Autoplay mode, press [SPACE] to pause.
2024/07/05 18:05:41 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) Det3DVisualizationHook             
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2024/07/05 18:05:43 - mmengine - WARNING - The prefix is not set in metric class BEVMetric.
Name of parameter - Initialization information

img_backbone.stem.stem.conv.weight - torch.Size([32, 3, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stem.stem.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stem.stem.norm.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stem.stem.norm.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.depth.conv.weight - torch.Size([32, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.0.conv.depth.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.depth.norm.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.depth.norm.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.project.conv.weight - torch.Size([16, 32, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.0.conv.project.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.project.norm.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.0.conv.project.norm.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.expand_conv.conv.weight - torch.Size([96, 16, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.expand_conv.conv.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.expand_conv.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.expand_conv.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.depth.conv.weight - torch.Size([96, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.depth.conv.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.depth.norm.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.depth.norm.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.project.conv.weight - torch.Size([24, 96, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.conv.project.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.project.norm.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.conv.project.norm.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.expand_conv.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.expand_conv.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.depth.conv.weight - torch.Size([144, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.depth.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.depth.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.depth.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.project.conv.weight - torch.Size([24, 144, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.1.blocks.0.project.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.project.norm.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.1.blocks.0.project.norm.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.expand_conv.conv.weight - torch.Size([144, 24, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.expand_conv.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.expand_conv.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.expand_conv.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.depth.conv.weight - torch.Size([144, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.depth.conv.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.depth.norm.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.depth.norm.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.project.conv.weight - torch.Size([40, 144, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.conv.project.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.project.norm.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.conv.project.norm.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.conv.weight - torch.Size([240, 40, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.expand_conv.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.expand_conv.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.depth.conv.weight - torch.Size([240, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.depth.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.depth.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.depth.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.project.conv.weight - torch.Size([40, 240, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.2.blocks.0.project.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.project.norm.weight - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.2.blocks.0.project.norm.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.expand_conv.conv.weight - torch.Size([240, 40, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.expand_conv.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.expand_conv.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.expand_conv.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.depth.conv.weight - torch.Size([240, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.depth.conv.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.depth.norm.weight - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.depth.norm.bias - torch.Size([240]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.project.conv.weight - torch.Size([80, 240, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.conv.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.conv.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.depth.conv.weight - torch.Size([480, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.project.conv.weight - torch.Size([80, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.0.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.0.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.depth.conv.weight - torch.Size([480, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.project.conv.weight - torch.Size([80, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.3.blocks.1.project.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.project.norm.weight - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.3.blocks.1.project.norm.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.expand_conv.conv.weight - torch.Size([480, 80, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.expand_conv.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.expand_conv.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.expand_conv.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.depth.conv.weight - torch.Size([480, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.depth.conv.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.depth.norm.weight - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.depth.norm.bias - torch.Size([480]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.project.conv.weight - torch.Size([112, 480, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.conv.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.conv.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.project.conv.weight - torch.Size([112, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.0.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.0.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.project.conv.weight - torch.Size([112, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.4.blocks.1.project.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.project.norm.weight - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.4.blocks.1.project.norm.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.expand_conv.conv.weight - torch.Size([672, 112, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.expand_conv.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.expand_conv.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.expand_conv.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.depth.conv.weight - torch.Size([672, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.depth.conv.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.depth.norm.weight - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.depth.norm.bias - torch.Size([672]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.project.conv.weight - torch.Size([192, 672, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.conv.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.conv.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.0.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.0.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.1.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.1.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.depth.conv.weight - torch.Size([1152, 1, 5, 5]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.project.conv.weight - torch.Size([192, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.5.blocks.2.project.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.project.norm.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.5.blocks.2.project.norm.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.expand_conv.conv.weight - torch.Size([1152, 192, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.expand_conv.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.expand_conv.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.expand_conv.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.depth.conv.weight - torch.Size([1152, 1, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.depth.conv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.depth.norm.weight - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.depth.norm.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.project.conv.weight - torch.Size([320, 1152, 1, 1]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.stage_blocks.6.conv.project.conv.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.project.norm.weight - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.stage_blocks.6.conv.project.norm.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_4.conv.conv.weight - torch.Size([112, 320, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_4.conv.conv.bias - torch.Size([112]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_3.conv.conv.weight - torch.Size([40, 224, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_3.conv.conv.bias - torch.Size([40]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_2.conv.conv.weight - torch.Size([24, 80, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_2.conv.conv.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_1.conv.conv.weight - torch.Size([16, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_1.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_0_0.conv.conv.weight - torch.Size([16, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_0_0.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_4.conv.conv.weight - torch.Size([224, 224, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_4.conv.conv.bias - torch.Size([224]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_3.conv.conv.weight - torch.Size([80, 80, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_3.conv.conv.bias - torch.Size([80]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_2.conv.conv.weight - torch.Size([48, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_2.conv.conv.bias - torch.Size([48]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_1.conv.conv.weight - torch.Size([32, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_1.conv.conv.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_upconv_1_0.conv.conv.weight - torch.Size([16, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_upconv_1_0.conv.conv.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_dispconv2.conv.weight - torch.Size([2, 48, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv2.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_dispconv1.conv.weight - torch.Size([2, 32, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv1.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_dispconv0.conv.weight - torch.Size([2, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_dispconv0.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_conf_mask_conv21.conv.weight - torch.Size([2, 66, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv21.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_conf_mask_conv11.conv.weight - torch.Size([2, 44, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv11.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_backbone.decoder.convs_conf_mask_conv01.conv.weight - torch.Size([2, 16, 3, 3]): 
Initialized by user-defined `init_weights` in EfficientNetB0  

img_backbone.decoder.convs_conf_mask_conv01.conv.bias - torch.Size([2]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.0.weight - torch.Size([32, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.0.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.1.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.0.1.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.0.weight - torch.Size([32, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.0.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.1.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.1.1.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.0.weight - torch.Size([32, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.0.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.1.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.2.1.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.0.weight - torch.Size([32, 320, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.0.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.1.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.poolings.3.1.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.0.weight - torch.Size([128, 448, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.0.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.extract.project.1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.0.weight - torch.Size([128, 112, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.0.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.project.1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.0.weight - torch.Size([128, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.0.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.0.1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.0.weight - torch.Size([128, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.0.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.upsample.1.1.1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.0.weight - torch.Size([128, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.0.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.0.1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.0.weight - torch.Size([128, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.0.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

img_neck.seg_convs.0.fusion.1.1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

view_transformer.depth_net.0.weight - torch.Size([80, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

view_transformer.feat_net.0.weight - torch.Size([128, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.conv1.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.conv2.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn2.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.bn2.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.downsample.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.0.downsample.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.conv1.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn1.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn1.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.conv2.weight - torch.Size([128, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn2.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.0.1.bn2.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.conv1.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.downsample.weight - torch.Size([256, 128, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.0.downsample.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.conv1.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn1.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn1.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.conv2.weight - torch.Size([256, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.1.1.bn2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.conv1.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.downsample.weight - torch.Size([512, 256, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.0.downsample.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.conv1.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.conv2.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn2.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.custom_resnet.layers.2.1.bn2.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.0.weight - torch.Size([512, 640, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.1.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.1.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.3.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.4.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.conv.4.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.1.weight - torch.Size([256, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.2.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.2.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.4.weight - torch.Size([256, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_encoder.fpn_lss.up2.4.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.final_conv.conv.weight - torch.Size([256, 256, 3, 3]): 
Initialized by user-defined `init_weights` in ConvModule  

bev_decoder.final_conv.conv.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.0.weight - torch.Size([512, 256]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.0.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.2.weight - torch.Size([4, 512]): 
The value is the same before and after calling `init_weights` of EcoOccVision  

bev_decoder.predicter.2.bias - torch.Size([4]): 
The value is the same before and after calling `init_weights` of EcoOccVision  
2024/07/05 18:05:43 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2024/07/05 18:05:43 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2024/07/05 18:05:43 - mmengine - INFO - Checkpoints will be saved to /home/<USER>/ecoaitoolkit/work_dirs/zed_lidar_0704/zed_lidar_train.
2024/07/05 18:07:17 - mmengine - INFO - Epoch(train)   [1][100/147]  lr: 4.6560e-05  eta: 4:35:41  time: 0.7290  data_time: 0.0008  memory: 3093  grad_norm: 3.2282  loss: 1.0069  loss_bce: 0.0477  loss_dice: 0.9592
2024/07/05 18:07:52 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:07:52 - mmengine - INFO - Saving checkpoint at 1 epochs
2024/07/05 18:08:14 - mmengine - INFO - Epoch(val)   [1][100/147]    eta: 0:00:09  time: 0.1738  data_time: 0.0322  memory: 3093  
2024/07/05 18:08:20 - mmengine - INFO - Epoch(val) [1][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9567977037555249, 'occupancy': 0.09374664435801429, 'mIoU': 0.5252721740567696}, '1~2m': {'no_occupancy': 0.9796689480296051, 'occupancy': 0.00412196181664835, 'mIoU': 0.49189545492312675}, '2~3m': {'no_occupancy': 0.9953088882402126, 'occupancy': 0.004407838440674002, 'mIoU': 0.4998583633404433}, '3~4m': {'no_occupancy': 0.9943079438833865, 'occupancy': 0.0001290382180408626, 'mIoU': 0.4972184910507137}, 'patch_mean': {'no_occupancy': 0.9815208709771823, 'occupancy': 0.025601370708344378, 'mIoU': 0.5035611208427634}}  total_overall: {'no_occupancy': 0.9815483665326565, 'occupancy': 0.058187894788899824, 'mIoU': 0.5198681306607782}  data_time: 0.0444  time: 0.1822
2024/07/05 18:09:49 - mmengine - INFO - Epoch(train)   [2][100/147]  lr: 6.6188e-05  eta: 4:15:02  time: 0.7345  data_time: 0.0008  memory: 3093  grad_norm: 12.0072  loss: 0.8322  loss_bce: 0.0396  loss_dice: 0.7926
2024/07/05 18:10:23 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:10:23 - mmengine - INFO - Saving checkpoint at 2 epochs
2024/07/05 18:10:45 - mmengine - INFO - Epoch(val)   [2][100/147]    eta: 0:00:08  time: 0.1723  data_time: 0.0324  memory: 3093  
2024/07/05 18:10:52 - mmengine - INFO - Epoch(val) [2][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9769451260721994, 'occupancy': 0.19476487482350516, 'mIoU': 0.5858550004478523}, '1~2m': {'no_occupancy': 0.9900056741753154, 'occupancy': 0.029157012619848408, 'mIoU': 0.5095813433975819}, '2~3m': {'no_occupancy': 0.9971872662677634, 'occupancy': 0.0, 'mIoU': 0.4985936331338817}, '3~4m': {'no_occupancy': 0.9990080320413507, 'occupancy': 0.0, 'mIoU': 0.49950401602067535}, 'patch_mean': {'no_occupancy': 0.9907865246391572, 'occupancy': 0.05598047186083839, 'mIoU': 0.5233834982499977}}  total_overall: {'no_occupancy': 0.9908058005293537, 'occupancy': 0.13731970443818264, 'mIoU': 0.5640627524837682}  data_time: 0.0327  time: 0.1704
2024/07/05 18:12:21 - mmengine - INFO - Epoch(train)   [3][100/147]  lr: 8.5780e-05  eta: 4:08:04  time: 0.7397  data_time: 0.0008  memory: 3093  grad_norm: 9.8821  loss: 0.8179  loss_bce: 0.0401  loss_dice: 0.7778
2024/07/05 18:12:55 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:12:55 - mmengine - INFO - Saving checkpoint at 3 epochs
2024/07/05 18:13:16 - mmengine - INFO - Epoch(val)   [3][100/147]    eta: 0:00:08  time: 0.1739  data_time: 0.0303  memory: 3093  
2024/07/05 18:13:23 - mmengine - INFO - Epoch(val) [3][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9828672392795063, 'occupancy': 0.24112641226266499, 'mIoU': 0.6119968257710856}, '1~2m': {'no_occupancy': 0.9876287206984561, 'occupancy': 0.06219195424464687, 'mIoU': 0.5249103374715515}, '2~3m': {'no_occupancy': 0.9971872662677634, 'occupancy': 0.0, 'mIoU': 0.4985936331338817}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9916730316088457, 'occupancy': 0.07582959162682797, 'mIoU': 0.5337513116178368}}  total_overall: {'no_occupancy': 0.9916857997050221, 'occupancy': 0.1580686526724913, 'mIoU': 0.5748772261887567}  data_time: 0.0331  time: 0.1727
2024/07/05 18:14:52 - mmengine - INFO - Epoch(train)   [4][100/147]  lr: 9.9846e-05  eta: 4:03:51  time: 0.7397  data_time: 0.0008  memory: 3093  grad_norm: 8.8570  loss: 0.7769  loss_bce: 0.0376  loss_dice: 0.7393
2024/07/05 18:15:26 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:15:26 - mmengine - INFO - Saving checkpoint at 4 epochs
2024/07/05 18:15:47 - mmengine - INFO - Epoch(val)   [4][100/147]    eta: 0:00:08  time: 0.1724  data_time: 0.0299  memory: 3093  
2024/07/05 18:15:54 - mmengine - INFO - Epoch(val) [4][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9792627934851307, 'occupancy': 0.22468865129007282, 'mIoU': 0.6019757223876018}, '1~2m': {'no_occupancy': 0.9873945740108396, 'occupancy': 0.08029475014038769, 'mIoU': 0.5338446620756137}, '2~3m': {'no_occupancy': 0.9971852294582754, 'occupancy': 0.0, 'mIoU': 0.4985926147291377}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9907128742859757, 'occupancy': 0.07624585035761512, 'mIoU': 0.5334793623217954}}  total_overall: {'no_occupancy': 0.9907309183665072, 'occupancy': 0.16044718464544866, 'mIoU': 0.5755890515059779}  data_time: 0.0354  time: 0.1747
2024/07/05 18:17:23 - mmengine - INFO - Epoch(train)   [5][100/147]  lr: 9.9726e-05  eta: 4:00:35  time: 0.7382  data_time: 0.0008  memory: 3093  grad_norm: 7.0238  loss: 0.7570  loss_bce: 0.0364  loss_dice: 0.7205
2024/07/05 18:17:57 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:17:57 - mmengine - INFO - Saving checkpoint at 5 epochs
2024/07/05 18:18:18 - mmengine - INFO - Epoch(val)   [5][100/147]    eta: 0:00:08  time: 0.1706  data_time: 0.0310  memory: 3093  
2024/07/05 18:18:26 - mmengine - INFO - Epoch(val) [5][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.982728216161376, 'occupancy': 0.25711848738023735, 'mIoU': 0.6199233517708067}, '1~2m': {'no_occupancy': 0.9909360318280246, 'occupancy': 0.042053385770619824, 'mIoU': 0.5164947087993222}, '2~3m': {'no_occupancy': 0.9971872662677634, 'occupancy': 0.0, 'mIoU': 0.4985936331338817}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9924651036117051, 'occupancy': 0.0747929682877143, 'mIoU': 0.5336290359497097}}  total_overall: {'no_occupancy': 0.9924797440030261, 'occupancy': 0.17430695874954863, 'mIoU': 0.5833933513762873}  data_time: 0.0352  time: 0.1722
2024/07/05 18:19:56 - mmengine - INFO - Epoch(train)   [6][100/147]  lr: 9.9572e-05  eta: 3:58:12  time: 0.7474  data_time: 0.0008  memory: 3093  grad_norm: 7.2412  loss: 0.7605  loss_bce: 0.0380  loss_dice: 0.7224
2024/07/05 18:20:30 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:20:30 - mmengine - INFO - Saving checkpoint at 6 epochs
2024/07/05 18:20:51 - mmengine - INFO - Epoch(val)   [6][100/147]    eta: 0:00:08  time: 0.1724  data_time: 0.0320  memory: 3093  
2024/07/05 18:20:58 - mmengine - INFO - Epoch(val) [6][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9856158363213381, 'occupancy': 0.28404540487686664, 'mIoU': 0.6348306205991023}, '1~2m': {'no_occupancy': 0.9910250560409033, 'occupancy': 0.07303223435196105, 'mIoU': 0.5320286451964322}, '2~3m': {'no_occupancy': 0.9971872662677634, 'occupancy': 0.0, 'mIoU': 0.4985936331338817}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9932092647049154, 'occupancy': 0.08926940980720692, 'mIoU': 0.5412393372560612}}  total_overall: {'no_occupancy': 0.9932204403024913, 'occupancy': 0.1907268943343566, 'mIoU': 0.5919736673184239}  data_time: 0.0348  time: 0.1724
2024/07/05 18:22:26 - mmengine - INFO - Epoch(train)   [7][100/147]  lr: 9.9384e-05  eta: 3:55:17  time: 0.7307  data_time: 0.0009  memory: 3093  grad_norm: 7.3239  loss: 0.7547  loss_bce: 0.0374  loss_dice: 0.7174
2024/07/05 18:22:39 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:23:00 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:23:00 - mmengine - INFO - Saving checkpoint at 7 epochs
2024/07/05 18:23:21 - mmengine - INFO - Epoch(val)   [7][100/147]    eta: 0:00:08  time: 0.1737  data_time: 0.0335  memory: 3093  
2024/07/05 18:23:28 - mmengine - INFO - Epoch(val) [7][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9856773889105557, 'occupancy': 0.3017398500557102, 'mIoU': 0.643708619483133}, '1~2m': {'no_occupancy': 0.9901345712257037, 'occupancy': 0.08868319189252816, 'mIoU': 0.5394088815591159}, '2~3m': {'no_occupancy': 0.9971872662677634, 'occupancy': 0.0, 'mIoU': 0.4985936331338817}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9930020316484199, 'occupancy': 0.09760576048705959, 'mIoU': 0.5453038960677398}}  total_overall: {'no_occupancy': 0.9930140042736252, 'occupancy': 0.20312442245318396, 'mIoU': 0.5980692133634046}  data_time: 0.0339  time: 0.1715
2024/07/05 18:24:57 - mmengine - INFO - Epoch(train)   [8][100/147]  lr: 9.9163e-05  eta: 3:52:37  time: 0.7316  data_time: 0.0009  memory: 3093  grad_norm: 5.9649  loss: 0.7247  loss_bce: 0.0357  loss_dice: 0.6890
2024/07/05 18:25:31 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:25:31 - mmengine - INFO - Saving checkpoint at 8 epochs
2024/07/05 18:25:52 - mmengine - INFO - Epoch(val)   [8][100/147]    eta: 0:00:08  time: 0.1741  data_time: 0.0317  memory: 3093  
2024/07/05 18:25:59 - mmengine - INFO - Epoch(val) [8][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9867481847273454, 'occupancy': 0.31262965841942025, 'mIoU': 0.6496889215733828}, '1~2m': {'no_occupancy': 0.9903621803699282, 'occupancy': 0.08813650165496557, 'mIoU': 0.5392493410124469}, '2~3m': {'no_occupancy': 0.9971872662677634, 'occupancy': 0.0, 'mIoU': 0.4985936331338817}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9933266328886734, 'occupancy': 0.10019154001859645, 'mIoU': 0.5467590864536349}}  total_overall: {'no_occupancy': 0.9933371939036229, 'occupancy': 0.20645410673726133, 'mIoU': 0.5998956503204421}  data_time: 0.0346  time: 0.1716
2024/07/05 18:27:27 - mmengine - INFO - Epoch(train)   [9][100/147]  lr: 9.8907e-05  eta: 3:50:10  time: 0.7342  data_time: 0.0008  memory: 3093  grad_norm: 6.7902  loss: 0.7417  loss_bce: 0.0375  loss_dice: 0.7042
2024/07/05 18:28:02 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:28:02 - mmengine - INFO - Saving checkpoint at 9 epochs
2024/07/05 18:28:23 - mmengine - INFO - Epoch(val)   [9][100/147]    eta: 0:00:08  time: 0.1753  data_time: 0.0342  memory: 3093  
2024/07/05 18:28:30 - mmengine - INFO - Epoch(val) [9][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9867071752406021, 'occupancy': 0.31102109960286994, 'mIoU': 0.6488641374217361}, '1~2m': {'no_occupancy': 0.9895375724415308, 'occupancy': 0.10091454196414656, 'mIoU': 0.5452260572028387}, '2~3m': {'no_occupancy': 0.997186297948499, 'occupancy': 0.0, 'mIoU': 0.4985931489742495}, '3~4m': {'no_occupancy': 0.9990085662864624, 'occupancy': 0.0, 'mIoU': 0.4995042831432312}, 'patch_mean': {'no_occupancy': 0.9931099029792736, 'occupancy': 0.10298391039175413, 'mIoU': 0.5480469066855138}}  total_overall: {'no_occupancy': 0.993120517354407, 'occupancy': 0.20625612681247515, 'mIoU': 0.599688322083441}  data_time: 0.0361  time: 0.1732
2024/07/05 18:29:59 - mmengine - INFO - Epoch(train)  [10][100/147]  lr: 9.8618e-05  eta: 3:47:55  time: 0.7408  data_time: 0.0009  memory: 3093  grad_norm: 6.7134  loss: 0.7104  loss_bce: 0.0356  loss_dice: 0.6748
2024/07/05 18:30:33 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:30:33 - mmengine - INFO - Saving checkpoint at 10 epochs
2024/07/05 18:30:55 - mmengine - INFO - Epoch(val)  [10][100/147]    eta: 0:00:08  time: 0.1712  data_time: 0.0293  memory: 3093  
2024/07/05 18:31:01 - mmengine - INFO - Epoch(val) [10][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9872751747688416, 'occupancy': 0.3360745993742182, 'mIoU': 0.6616748870715299}, '1~2m': {'no_occupancy': 0.990921567680457, 'occupancy': 0.09816234055675128, 'mIoU': 0.5445419541186042}, '2~3m': {'no_occupancy': 0.9971872662677634, 'occupancy': 0.0, 'mIoU': 0.4985936331338817}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9935982272266797, 'occupancy': 0.10855923498274236, 'mIoU': 0.5510787311047111}}  total_overall: {'no_occupancy': 0.9936090246369288, 'occupancy': 0.22450799472109428, 'mIoU': 0.6090585096790115}  data_time: 0.0322  time: 0.1703
2024/07/05 18:32:30 - mmengine - INFO - Epoch(train)  [11][100/147]  lr: 9.8296e-05  eta: 3:45:33  time: 0.7309  data_time: 0.0009  memory: 3093  grad_norm: 5.7449  loss: 0.6910  loss_bce: 0.0345  loss_dice: 0.6565
2024/07/05 18:33:04 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:33:04 - mmengine - INFO - Saving checkpoint at 11 epochs
2024/07/05 18:33:25 - mmengine - INFO - Epoch(val)  [11][100/147]    eta: 0:00:08  time: 0.1713  data_time: 0.0293  memory: 3093  
2024/07/05 18:33:32 - mmengine - INFO - Epoch(val) [11][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.987521981232975, 'occupancy': 0.3493859868782395, 'mIoU': 0.6684539840556072}, '1~2m': {'no_occupancy': 0.9910546968238242, 'occupancy': 0.11255390617972194, 'mIoU': 0.5518043015017731}, '2~3m': {'no_occupancy': 0.9971872662677634, 'occupancy': 0.0, 'mIoU': 0.4985936331338817}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9936932111285548, 'occupancy': 0.11548497326449036, 'mIoU': 0.5545890921965226}}  total_overall: {'no_occupancy': 0.9937042493972593, 'occupancy': 0.23659309033036202, 'mIoU': 0.6151486698638107}  data_time: 0.0351  time: 0.1738
2024/07/05 18:35:01 - mmengine - INFO - Epoch(train)  [12][100/147]  lr: 9.7941e-05  eta: 3:43:25  time: 0.7437  data_time: 0.0008  memory: 3093  grad_norm: 6.7249  loss: 0.6834  loss_bce: 0.0340  loss_dice: 0.6494
2024/07/05 18:35:35 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:35:35 - mmengine - INFO - Saving checkpoint at 12 epochs
2024/07/05 18:35:57 - mmengine - INFO - Epoch(val)  [12][100/147]    eta: 0:00:08  time: 0.1732  data_time: 0.0304  memory: 3093  
2024/07/05 18:36:04 - mmengine - INFO - Epoch(val) [12][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9884678605069447, 'occupancy': 0.3495257012030098, 'mIoU': 0.6689967808549773}, '1~2m': {'no_occupancy': 0.9913204609030084, 'occupancy': 0.09750613583545101, 'mIoU': 0.5444132983692297}, '2~3m': {'no_occupancy': 0.9971872662677634, 'occupancy': 0.0, 'mIoU': 0.4985936331338817}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9939961219668434, 'occupancy': 0.11175795925961521, 'mIoU': 0.5528770406132293}}  total_overall: {'no_occupancy': 0.9940052763588723, 'occupancy': 0.228649127689156, 'mIoU': 0.6113272020240141}  data_time: 0.0335  time: 0.1712
2024/07/05 18:37:33 - mmengine - INFO - Epoch(train)  [13][100/147]  lr: 9.7553e-05  eta: 3:41:22  time: 0.7533  data_time: 0.0008  memory: 3093  grad_norm: 6.2528  loss: 0.6888  loss_bce: 0.0342  loss_dice: 0.6546
2024/07/05 18:38:08 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:38:08 - mmengine - INFO - Saving checkpoint at 13 epochs
2024/07/05 18:38:29 - mmengine - INFO - Epoch(val)  [13][100/147]    eta: 0:00:08  time: 0.1754  data_time: 0.0325  memory: 3093  
2024/07/05 18:38:36 - mmengine - INFO - Epoch(val) [13][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9878145103917003, 'occupancy': 0.3590431534090219, 'mIoU': 0.6734288319003611}, '1~2m': {'no_occupancy': 0.9901943338246981, 'occupancy': 0.12027911720372855, 'mIoU': 0.5552367255142133}, '2~3m': {'no_occupancy': 0.9971871327064857, 'occupancy': 0.0, 'mIoU': 0.49859356635324287}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9935512192781353, 'occupancy': 0.11983056765318761, 'mIoU': 0.5566908934656615}}  total_overall: {'no_occupancy': 0.9935620881531397, 'occupancy': 0.24006561971494272, 'mIoU': 0.6168138539340412}  data_time: 0.0350  time: 0.1727
2024/07/05 18:39:56 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:40:04 - mmengine - INFO - Epoch(train)  [14][100/147]  lr: 9.7132e-05  eta: 3:39:05  time: 0.7405  data_time: 0.0009  memory: 3093  grad_norm: 6.3350  loss: 0.6345  loss_bce: 0.0320  loss_dice: 0.6025
2024/07/05 18:40:38 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:40:38 - mmengine - INFO - Saving checkpoint at 14 epochs
2024/07/05 18:41:00 - mmengine - INFO - Epoch(val)  [14][100/147]    eta: 0:00:08  time: 0.1767  data_time: 0.0366  memory: 3093  
2024/07/05 18:41:07 - mmengine - INFO - Epoch(val) [14][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9897220960058344, 'occupancy': 0.38734416533217814, 'mIoU': 0.6885331306690062}, '1~2m': {'no_occupancy': 0.9911665375955503, 'occupancy': 0.12328901777737593, 'mIoU': 0.5572277776864631}, '2~3m': {'no_occupancy': 0.9971872662677634, 'occupancy': 0.0, 'mIoU': 0.4985936331338817}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9942712000147013, 'occupancy': 0.12765829577738852, 'mIoU': 0.5609647478960449}}  total_overall: {'no_occupancy': 0.9942795213978719, 'occupancy': 0.2520676085616661, 'mIoU': 0.623173564979769}  data_time: 0.0353  time: 0.1732
2024/07/05 18:42:35 - mmengine - INFO - Epoch(train)  [15][100/147]  lr: 9.6679e-05  eta: 3:36:58  time: 0.7367  data_time: 0.0009  memory: 3093  grad_norm: 7.1233  loss: 0.6444  loss_bce: 0.0319  loss_dice: 0.6125
2024/07/05 18:43:10 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:43:10 - mmengine - INFO - Saving checkpoint at 15 epochs
2024/07/05 18:43:31 - mmengine - INFO - Epoch(val)  [15][100/147]    eta: 0:00:08  time: 0.1723  data_time: 0.0332  memory: 3093  
2024/07/05 18:43:38 - mmengine - INFO - Epoch(val) [15][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9898413543192534, 'occupancy': 0.3974413900064624, 'mIoU': 0.6936413721628578}, '1~2m': {'no_occupancy': 0.9913590578330084, 'occupancy': 0.12178501366922637, 'mIoU': 0.5565720357511174}, '2~3m': {'no_occupancy': 0.9971872662677634, 'occupancy': 0.0, 'mIoU': 0.4985936331338817}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9943491446524205, 'occupancy': 0.12980660091892218, 'mIoU': 0.5620778727856713}}  total_overall: {'no_occupancy': 0.9943575567204842, 'occupancy': 0.25849995342340704, 'mIoU': 0.6264287550719456}  data_time: 0.0352  time: 0.1723
2024/07/05 18:45:06 - mmengine - INFO - Epoch(train)  [16][100/147]  lr: 9.6194e-05  eta: 3:34:45  time: 0.7332  data_time: 0.0009  memory: 3093  grad_norm: 5.9968  loss: 0.6530  loss_bce: 0.0341  loss_dice: 0.6189
2024/07/05 18:45:40 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:45:40 - mmengine - INFO - Saving checkpoint at 16 epochs
2024/07/05 18:46:02 - mmengine - INFO - Epoch(val)  [16][100/147]    eta: 0:00:08  time: 0.1743  data_time: 0.0328  memory: 3093  
2024/07/05 18:46:09 - mmengine - INFO - Epoch(val) [16][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9904238536731458, 'occupancy': 0.4059369892695625, 'mIoU': 0.6981804214713542}, '1~2m': {'no_occupancy': 0.991098412197679, 'occupancy': 0.12241793718425889, 'mIoU': 0.556758174690969}, '2~3m': {'no_occupancy': 0.9971872662677634, 'occupancy': 0.0, 'mIoU': 0.4985936331338817}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9944296080820614, 'occupancy': 0.13208873161345536, 'mIoU': 0.5632591698477584}}  total_overall: {'no_occupancy': 0.9944371659464668, 'occupancy': 0.2584764487848223, 'mIoU': 0.6264568073656446}  data_time: 0.0372  time: 0.1755
2024/07/05 18:47:37 - mmengine - INFO - Epoch(train)  [17][100/147]  lr: 9.5677e-05  eta: 3:32:39  time: 0.7401  data_time: 0.0008  memory: 3093  grad_norm: 6.4132  loss: 0.6555  loss_bce: 0.0336  loss_dice: 0.6219
2024/07/05 18:48:12 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:48:12 - mmengine - INFO - Saving checkpoint at 17 epochs
2024/07/05 18:48:34 - mmengine - INFO - Epoch(val)  [17][100/147]    eta: 0:00:08  time: 0.1738  data_time: 0.0328  memory: 3093  
2024/07/05 18:48:40 - mmengine - INFO - Epoch(val) [17][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9896777927241249, 'occupancy': 0.3855624865906725, 'mIoU': 0.6876201396573987}, '1~2m': {'no_occupancy': 0.9911602969907194, 'occupancy': 0.12198983151731543, 'mIoU': 0.5565750642540174}, '2~3m': {'no_occupancy': 0.9971872662677634, 'occupancy': 0.0, 'mIoU': 0.4985936331338817}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9942585640430661, 'occupancy': 0.126888079526997, 'mIoU': 0.5605733217850315}}  total_overall: {'no_occupancy': 0.994266900096856, 'occupancy': 0.25080355585162295, 'mIoU': 0.6225352279742395}  data_time: 0.0338  time: 0.1702
2024/07/05 18:50:09 - mmengine - INFO - Epoch(train)  [18][100/147]  lr: 9.5129e-05  eta: 3:30:33  time: 0.7442  data_time: 0.0008  memory: 3093  grad_norm: 5.8836  loss: 0.6161  loss_bce: 0.0314  loss_dice: 0.5846
2024/07/05 18:50:43 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:50:43 - mmengine - INFO - Saving checkpoint at 18 epochs
2024/07/05 18:51:05 - mmengine - INFO - Epoch(val)  [18][100/147]    eta: 0:00:08  time: 0.1749  data_time: 0.0314  memory: 3093  
2024/07/05 18:51:12 - mmengine - INFO - Epoch(val) [18][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9912698271662431, 'occupancy': 0.43693211229968265, 'mIoU': 0.7141009697329629}, '1~2m': {'no_occupancy': 0.9912184846108409, 'occupancy': 0.1456774549836136, 'mIoU': 0.5684479697972272}, '2~3m': {'no_occupancy': 0.9971868989742492, 'occupancy': 0.0, 'mIoU': 0.4985934494871246}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9946710277352475, 'occupancy': 0.14565239182082407, 'mIoU': 0.5701617097780358}}  total_overall: {'no_occupancy': 0.9946780542969723, 'occupancy': 0.27908384227286526, 'mIoU': 0.6368809482849188}  data_time: 0.0369  time: 0.1751
2024/07/05 18:52:41 - mmengine - INFO - Epoch(train)  [19][100/147]  lr: 9.4550e-05  eta: 3:28:28  time: 0.7428  data_time: 0.0008  memory: 3093  grad_norm: 6.2142  loss: 0.6435  loss_bce: 0.0339  loss_dice: 0.6096
2024/07/05 18:53:15 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:53:15 - mmengine - INFO - Saving checkpoint at 19 epochs
2024/07/05 18:53:37 - mmengine - INFO - Epoch(val)  [19][100/147]    eta: 0:00:08  time: 0.1729  data_time: 0.0304  memory: 3093  
2024/07/05 18:53:44 - mmengine - INFO - Epoch(val) [19][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.990913432435647, 'occupancy': 0.4349314996884449, 'mIoU': 0.7129224660620459}, '1~2m': {'no_occupancy': 0.9916466414816395, 'occupancy': 0.14356175560356904, 'mIoU': 0.5676041985426042}, '2~3m': {'no_occupancy': 0.997186932364569, 'occupancy': 0.0, 'mIoU': 0.4985934661822845}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9946889766178781, 'occupancy': 0.14462331382300347, 'mIoU': 0.5696561452204408}}  total_overall: {'no_occupancy': 0.9946966116563224, 'occupancy': 0.2827105936179998, 'mIoU': 0.6387036026371611}  data_time: 0.0328  time: 0.1706
2024/07/05 18:55:13 - mmengine - INFO - Epoch(train)  [20][100/147]  lr: 9.3941e-05  eta: 3:26:24  time: 0.7438  data_time: 0.0008  memory: 3093  grad_norm: 6.1980  loss: 0.6151  loss_bce: 0.0319  loss_dice: 0.5833
2024/07/05 18:55:47 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:55:47 - mmengine - INFO - Saving checkpoint at 20 epochs
2024/07/05 18:56:08 - mmengine - INFO - Epoch(val)  [20][100/147]    eta: 0:00:08  time: 0.1708  data_time: 0.0295  memory: 3093  
2024/07/05 18:56:15 - mmengine - INFO - Epoch(val) [20][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9908823150250392, 'occupancy': 0.43322036360368205, 'mIoU': 0.7120513393143606}, '1~2m': {'no_occupancy': 0.9908701046032912, 'occupancy': 0.1374673429222201, 'mIoU': 0.5641687237627556}, '2~3m': {'no_occupancy': 0.9971872662677634, 'occupancy': 0.0, 'mIoU': 0.4985936331338817}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9944871465214378, 'occupancy': 0.14267192663147554, 'mIoU': 0.5685795365764567}}  total_overall: {'no_occupancy': 0.9944947134072339, 'occupancy': 0.27592021907463193, 'mIoU': 0.6352074662409329}  data_time: 0.0331  time: 0.1715
2024/07/05 18:57:15 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:57:44 - mmengine - INFO - Epoch(train)  [21][100/147]  lr: 9.3301e-05  eta: 3:24:20  time: 0.7429  data_time: 0.0009  memory: 3093  grad_norm: 5.3766  loss: 0.6163  loss_bce: 0.0318  loss_dice: 0.5845
2024/07/05 18:58:19 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 18:58:19 - mmengine - INFO - Saving checkpoint at 21 epochs
2024/07/05 18:58:40 - mmengine - INFO - Epoch(val)  [21][100/147]    eta: 0:00:08  time: 0.1725  data_time: 0.0314  memory: 3093  
2024/07/05 18:58:47 - mmengine - INFO - Epoch(val) [21][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9922430464779128, 'occupancy': 0.4732930432377963, 'mIoU': 0.7327680448578546}, '1~2m': {'no_occupancy': 0.9916528977470985, 'occupancy': 0.1656838979569749, 'mIoU': 0.5786683978520367}, '2~3m': {'no_occupancy': 0.9971852289883449, 'occupancy': 5.930916682482444e-05, 'mIoU': 0.4986222690775849}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9950225183507533, 'occupancy': 0.15975906259039901, 'mIoU': 0.5773907904705762}}  total_overall: {'no_occupancy': 0.9950287355904464, 'occupancy': 0.3018033656991009, 'mIoU': 0.6484160506447736}  data_time: 0.0335  time: 0.1722
2024/07/05 19:00:15 - mmengine - INFO - Epoch(train)  [22][100/147]  lr: 9.2632e-05  eta: 3:22:10  time: 0.7266  data_time: 0.0009  memory: 3093  grad_norm: 5.0921  loss: 0.6037  loss_bce: 0.0321  loss_dice: 0.5716
2024/07/05 19:00:49 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:00:49 - mmengine - INFO - Saving checkpoint at 22 epochs
2024/07/05 19:01:11 - mmengine - INFO - Epoch(val)  [22][100/147]    eta: 0:00:08  time: 0.1689  data_time: 0.0279  memory: 3093  
2024/07/05 19:01:18 - mmengine - INFO - Epoch(val) [22][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9920157607289344, 'occupancy': 0.46337118373625596, 'mIoU': 0.7276934722325952}, '1~2m': {'no_occupancy': 0.9912875847246023, 'occupancy': 0.17516740662738245, 'mIoU': 0.5832274956759924}, '2~3m': {'no_occupancy': 0.9971622540758769, 'occupancy': 0.0003528706023501182, 'mIoU': 0.4987575623391135}, '3~4m': {'no_occupancy': 0.9990087666283792, 'occupancy': 0.0, 'mIoU': 0.4995043833141896}, 'patch_mean': {'no_occupancy': 0.9948685915394482, 'occupancy': 0.15972286524149715, 'mIoU': 0.5772957283904727}}  total_overall: {'no_occupancy': 0.9948751419499033, 'occupancy': 0.2982832765511808, 'mIoU': 0.646579209250542}  data_time: 0.0338  time: 0.1708
2024/07/05 19:02:46 - mmengine - INFO - Epoch(train)  [23][100/147]  lr: 9.1934e-05  eta: 3:20:03  time: 0.7396  data_time: 0.0008  memory: 3093  grad_norm: 5.1764  loss: 0.5931  loss_bce: 0.0303  loss_dice: 0.5628
2024/07/05 19:03:20 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:03:20 - mmengine - INFO - Saving checkpoint at 23 epochs
2024/07/05 19:03:41 - mmengine - INFO - Epoch(val)  [23][100/147]    eta: 0:00:08  time: 0.1725  data_time: 0.0301  memory: 3093  
2024/07/05 19:03:48 - mmengine - INFO - Epoch(val) [23][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9918262652323417, 'occupancy': 0.46007495307451146, 'mIoU': 0.7259506091534266}, '1~2m': {'no_occupancy': 0.9914557475076117, 'occupancy': 0.17928732838114547, 'mIoU': 0.5853715379443786}, '2~3m': {'no_occupancy': 0.9971858638743455, 'occupancy': 0.0, 'mIoU': 0.49859293193717275}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.994869194200989, 'occupancy': 0.15984057036391425, 'mIoU': 0.5773548822824516}}  total_overall: {'no_occupancy': 0.9948760609072792, 'occupancy': 0.3003631543092393, 'mIoU': 0.6476196076082592}  data_time: 0.0344  time: 0.1737
2024/07/05 19:05:16 - mmengine - INFO - Epoch(train)  [24][100/147]  lr: 9.1206e-05  eta: 3:17:54  time: 0.7311  data_time: 0.0009  memory: 3093  grad_norm: 4.9077  loss: 0.5782  loss_bce: 0.0304  loss_dice: 0.5477
2024/07/05 19:05:50 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:05:50 - mmengine - INFO - Saving checkpoint at 24 epochs
2024/07/05 19:06:12 - mmengine - INFO - Epoch(val)  [24][100/147]    eta: 0:00:08  time: 0.1718  data_time: 0.0300  memory: 3093  
2024/07/05 19:06:19 - mmengine - INFO - Epoch(val) [24][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9926799137437122, 'occupancy': 0.4951235225453364, 'mIoU': 0.7439017181445243}, '1~2m': {'no_occupancy': 0.9917892019270645, 'occupancy': 0.19101065263126665, 'mIoU': 0.5913999272791656}, '2~3m': {'no_occupancy': 0.9971764348879282, 'occupancy': 0.0016174925323793668, 'mIoU': 0.4993969637101538}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9951636126870904, 'occupancy': 0.1719379169272456, 'mIoU': 0.583550764807168}}  total_overall: {'no_occupancy': 0.9951696820816333, 'occupancy': 0.3198857557183251, 'mIoU': 0.6575277188999792}  data_time: 0.0328  time: 0.1705
2024/07/05 19:07:48 - mmengine - INFO - Epoch(train)  [25][100/147]  lr: 9.0451e-05  eta: 3:15:49  time: 0.7365  data_time: 0.0009  memory: 3093  grad_norm: 4.6097  loss: 0.5889  loss_bce: 0.0308  loss_dice: 0.5581
2024/07/05 19:08:22 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:08:22 - mmengine - INFO - Saving checkpoint at 25 epochs
2024/07/05 19:08:43 - mmengine - INFO - Epoch(val)  [25][100/147]    eta: 0:00:08  time: 0.1699  data_time: 0.0277  memory: 3093  
2024/07/05 19:08:51 - mmengine - INFO - Epoch(val) [25][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9928598815608606, 'occupancy': 0.5027421099197861, 'mIoU': 0.7478009957403233}, '1~2m': {'no_occupancy': 0.9915816138918628, 'occupancy': 0.1930331588636378, 'mIoU': 0.5923073863777504}, '2~3m': {'no_occupancy': 0.9971797980030027, 'occupancy': 0.002786403296456781, 'mIoU': 0.49998310064972973}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9951575484113457, 'occupancy': 0.17464041801997016, 'mIoU': 0.5848989832156579}}  total_overall: {'no_occupancy': 0.9951634715856104, 'occupancy': 0.32242433335462733, 'mIoU': 0.6587939024701188}  data_time: 0.0336  time: 0.1723
2024/07/05 19:10:19 - mmengine - INFO - Epoch(train)  [26][100/147]  lr: 8.9668e-05  eta: 3:13:43  time: 0.7316  data_time: 0.0008  memory: 3093  grad_norm: 5.2148  loss: 0.5811  loss_bce: 0.0314  loss_dice: 0.5497
2024/07/05 19:10:53 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:10:53 - mmengine - INFO - Saving checkpoint at 26 epochs
2024/07/05 19:11:14 - mmengine - INFO - Epoch(val)  [26][100/147]    eta: 0:00:08  time: 0.1717  data_time: 0.0306  memory: 3093  
2024/07/05 19:11:21 - mmengine - INFO - Epoch(val) [26][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9930552631964833, 'occupancy': 0.5004311223147823, 'mIoU': 0.7467431927556327}, '1~2m': {'no_occupancy': 0.991721989457152, 'occupancy': 0.19952309040009125, 'mIoU': 0.5956225399286216}, '2~3m': {'no_occupancy': 0.997187614967804, 'occupancy': 0.0023452768729641696, 'mIoU': 0.49976644592038405}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.995243441952774, 'occupancy': 0.1755748723969594, 'mIoU': 0.5854091571748667}}  total_overall: {'no_occupancy': 0.9952490433995848, 'occupancy': 0.3212523662463824, 'mIoU': 0.6582507048229835}  data_time: 0.0353  time: 0.1731
2024/07/05 19:12:50 - mmengine - INFO - Epoch(train)  [27][100/147]  lr: 8.8857e-05  eta: 3:11:39  time: 0.7392  data_time: 0.0008  memory: 3093  grad_norm: 5.0572  loss: 0.5637  loss_bce: 0.0300  loss_dice: 0.5338
2024/07/05 19:13:24 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:13:24 - mmengine - INFO - Saving checkpoint at 27 epochs
2024/07/05 19:13:46 - mmengine - INFO - Epoch(val)  [27][100/147]    eta: 0:00:08  time: 0.1726  data_time: 0.0320  memory: 3093  
2024/07/05 19:13:53 - mmengine - INFO - Epoch(val) [27][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9929295340140308, 'occupancy': 0.5040400889108538, 'mIoU': 0.7484848114624423}, '1~2m': {'no_occupancy': 0.9914846575371729, 'occupancy': 0.19352214212152416, 'mIoU': 0.5925033998293485}, '2~3m': {'no_occupancy': 0.9971802540786511, 'occupancy': 0.004209707089288241, 'mIoU': 0.5006949805839697}, '3~4m': {'no_occupancy': 0.9990087666283792, 'occupancy': 0.0, 'mIoU': 0.4995043833141896}, 'patch_mean': {'no_occupancy': 0.9951508030645585, 'occupancy': 0.17544298453041657, 'mIoU': 0.5852968937974875}}  total_overall: {'no_occupancy': 0.9951566412498759, 'occupancy': 0.3221345991032504, 'mIoU': 0.6586456201765631}  data_time: 0.0339  time: 0.1706
2024/07/05 19:14:32 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:15:22 - mmengine - INFO - Epoch(train)  [28][100/147]  lr: 8.8020e-05  eta: 3:09:39  time: 0.7429  data_time: 0.0009  memory: 3093  grad_norm: 5.1673  loss: 0.5881  loss_bce: 0.0314  loss_dice: 0.5567
2024/07/05 19:15:57 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:15:57 - mmengine - INFO - Saving checkpoint at 28 epochs
2024/07/05 19:16:18 - mmengine - INFO - Epoch(val)  [28][100/147]    eta: 0:00:08  time: 0.1700  data_time: 0.0288  memory: 3093  
2024/07/05 19:16:25 - mmengine - INFO - Epoch(val) [28][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9930125962308882, 'occupancy': 0.5156330566708689, 'mIoU': 0.7543228264508786}, '1~2m': {'no_occupancy': 0.99198267597312, 'occupancy': 0.21797108119698397, 'mIoU': 0.604976878585052}, '2~3m': {'no_occupancy': 0.9971426752102366, 'occupancy': 0.005889938312480396, 'mIoU': 0.5015163067613585}, '3~4m': {'no_occupancy': 0.9990082323832676, 'occupancy': 0.0, 'mIoU': 0.4995041161916338}, 'patch_mean': {'no_occupancy': 0.9952865449493781, 'occupancy': 0.1848735190450833, 'mIoU': 0.5900800319972307}}  total_overall: {'no_occupancy': 0.9952925907020714, 'occupancy': 0.33891608148161423, 'mIoU': 0.6671043360918428}  data_time: 0.0344  time: 0.1724
2024/07/05 19:17:54 - mmengine - INFO - Epoch(train)  [29][100/147]  lr: 8.7157e-05  eta: 3:07:36  time: 0.7477  data_time: 0.0009  memory: 3093  grad_norm: 4.7909  loss: 0.5656  loss_bce: 0.0304  loss_dice: 0.5352
2024/07/05 19:18:28 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:18:28 - mmengine - INFO - Saving checkpoint at 29 epochs
2024/07/05 19:18:50 - mmengine - INFO - Epoch(val)  [29][100/147]    eta: 0:00:08  time: 0.1724  data_time: 0.0303  memory: 3093  
2024/07/05 19:18:57 - mmengine - INFO - Epoch(val) [29][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9929344929079, 'occupancy': 0.5122038944473442, 'mIoU': 0.7525691936776221}, '1~2m': {'no_occupancy': 0.9917594056563325, 'occupancy': 0.2274048303918265, 'mIoU': 0.6095821180240795}, '2~3m': {'no_occupancy': 0.9971841342778945, 'occupancy': 0.0075203897892172425, 'mIoU': 0.5023522620335559}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9952217332579459, 'occupancy': 0.18678227865709696, 'mIoU': 0.5910020059575214}}  total_overall: {'no_occupancy': 0.9952280437139136, 'occupancy': 0.3399172575925701, 'mIoU': 0.6675726506532418}  data_time: 0.0325  time: 0.1697
2024/07/05 19:20:26 - mmengine - INFO - Epoch(train)  [30][100/147]  lr: 8.6269e-05  eta: 3:05:32  time: 0.7457  data_time: 0.0009  memory: 3093  grad_norm: 4.5694  loss: 0.5432  loss_bce: 0.0289  loss_dice: 0.5143
2024/07/05 19:21:00 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:21:00 - mmengine - INFO - Saving checkpoint at 30 epochs
2024/07/05 19:21:21 - mmengine - INFO - Epoch(val)  [30][100/147]    eta: 0:00:08  time: 0.1770  data_time: 0.0331  memory: 3093  
2024/07/05 19:21:28 - mmengine - INFO - Epoch(val) [30][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9931027543006905, 'occupancy': 0.5190301110335382, 'mIoU': 0.7560664326671144}, '1~2m': {'no_occupancy': 0.9918012437224133, 'occupancy': 0.22483898962777552, 'mIoU': 0.6083201166750944}, '2~3m': {'no_occupancy': 0.9971660327557239, 'occupancy': 0.003802950808127046, 'mIoU': 0.5004844917819254}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9952697327421212, 'occupancy': 0.1869180128673602, 'mIoU': 0.5910938728047407}}  total_overall: {'no_occupancy': 0.9952758020759859, 'occupancy': 0.3413923908315267, 'mIoU': 0.6683340964537563}  data_time: 0.0349  time: 0.1747
2024/07/05 19:22:57 - mmengine - INFO - Epoch(train)  [31][100/147]  lr: 8.5355e-05  eta: 3:03:29  time: 0.7392  data_time: 0.0009  memory: 3093  grad_norm: 4.7629  loss: 0.5368  loss_bce: 0.0286  loss_dice: 0.5082
2024/07/05 19:23:32 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:23:32 - mmengine - INFO - Saving checkpoint at 31 epochs
2024/07/05 19:23:53 - mmengine - INFO - Epoch(val)  [31][100/147]    eta: 0:00:08  time: 0.1734  data_time: 0.0311  memory: 3093  
2024/07/05 19:24:00 - mmengine - INFO - Epoch(val) [31][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9937277594697879, 'occupancy': 0.5457934812266735, 'mIoU': 0.7697606203482307}, '1~2m': {'no_occupancy': 0.9920862660385256, 'occupancy': 0.24580205093928933, 'mIoU': 0.6189441584889075}, '2~3m': {'no_occupancy': 0.9971853417121512, 'occupancy': 0.00685706206701541, 'mIoU': 0.5020212018895833}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9955020668525304, 'occupancy': 0.19961314855824455, 'mIoU': 0.5975576077053875}}  total_overall: {'no_occupancy': 0.9955075876247222, 'occupancy': 0.35977386353336305, 'mIoU': 0.6776407255790426}  data_time: 0.0347  time: 0.1727
2024/07/05 19:25:29 - mmengine - INFO - Epoch(train)  [32][100/147]  lr: 8.4418e-05  eta: 3:01:27  time: 0.7353  data_time: 0.0009  memory: 3093  grad_norm: 4.8265  loss: 0.5584  loss_bce: 0.0293  loss_dice: 0.5291
2024/07/05 19:26:03 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:26:03 - mmengine - INFO - Saving checkpoint at 32 epochs
2024/07/05 19:26:25 - mmengine - INFO - Epoch(val)  [32][100/147]    eta: 0:00:08  time: 0.1719  data_time: 0.0288  memory: 3093  
2024/07/05 19:26:32 - mmengine - INFO - Epoch(val) [32][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9938980955752275, 'occupancy': 0.5494076656783302, 'mIoU': 0.7716528806267788}, '1~2m': {'no_occupancy': 0.9921962158989275, 'occupancy': 0.24369976897632217, 'mIoU': 0.6179479924376248}, '2~3m': {'no_occupancy': 0.9971934199836365, 'occupancy': 0.007169940585171099, 'mIoU': 0.5021816802844038}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9955741579118621, 'occupancy': 0.20006934380995586, 'mIoU': 0.5978217508609089}}  total_overall: {'no_occupancy': 0.9955793753183614, 'occupancy': 0.35981190903452887, 'mIoU': 0.6776956421764451}  data_time: 0.0339  time: 0.1716
2024/07/05 19:28:00 - mmengine - INFO - Epoch(train)  [33][100/147]  lr: 8.3457e-05  eta: 2:59:20  time: 0.7332  data_time: 0.0009  memory: 3093  grad_norm: 4.8562  loss: 0.5366  loss_bce: 0.0289  loss_dice: 0.5077
2024/07/05 19:28:34 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:28:34 - mmengine - INFO - Saving checkpoint at 33 epochs
2024/07/05 19:28:55 - mmengine - INFO - Epoch(val)  [33][100/147]    eta: 0:00:08  time: 0.1740  data_time: 0.0302  memory: 3093  
2024/07/05 19:29:03 - mmengine - INFO - Epoch(val) [33][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9939596126370297, 'occupancy': 0.5510729527897024, 'mIoU': 0.772516282713366}, '1~2m': {'no_occupancy': 0.9923678130556582, 'occupancy': 0.25123188301678434, 'mIoU': 0.6217998480362212}, '2~3m': {'no_occupancy': 0.9971839535002219, 'occupancy': 0.009233914075257574, 'mIoU': 0.5032089337877398}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9956300698456417, 'occupancy': 0.20288468747043606, 'mIoU': 0.5992573786580389}}  total_overall: {'no_occupancy': 0.9956352293645134, 'occupancy': 0.3633778392672205, 'mIoU': 0.6795065343158669}  data_time: 0.0349  time: 0.1741
2024/07/05 19:30:32 - mmengine - INFO - Epoch(train)  [34][100/147]  lr: 8.2472e-05  eta: 2:57:20  time: 0.7444  data_time: 0.0009  memory: 3093  grad_norm: 4.1553  loss: 0.5328  loss_bce: 0.0292  loss_dice: 0.5036
2024/07/05 19:31:07 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:31:07 - mmengine - INFO - Saving checkpoint at 34 epochs
2024/07/05 19:31:29 - mmengine - INFO - Epoch(val)  [34][100/147]    eta: 0:00:08  time: 0.1757  data_time: 0.0323  memory: 3093  
2024/07/05 19:31:36 - mmengine - INFO - Epoch(val) [34][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9938434257681484, 'occupancy': 0.553495561900515, 'mIoU': 0.7736694938343317}, '1~2m': {'no_occupancy': 0.9923863939153357, 'occupancy': 0.2596930674455682, 'mIoU': 0.6260397306804519}, '2~3m': {'no_occupancy': 0.99718638560842, 'occupancy': 0.009917044626700821, 'mIoU': 0.5035517151175604}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9956062763703902, 'occupancy': 0.205776418493196, 'mIoU': 0.6006913474317931}}  total_overall: {'no_occupancy': 0.9956117614521275, 'occupancy': 0.3696243061093931, 'mIoU': 0.6826180337807604}  data_time: 0.0339  time: 0.1715
2024/07/05 19:31:43 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:33:05 - mmengine - INFO - Epoch(train)  [35][100/147]  lr: 8.1466e-05  eta: 2:55:18  time: 0.7419  data_time: 0.0009  memory: 3093  grad_norm: 4.4218  loss: 0.5113  loss_bce: 0.0280  loss_dice: 0.4833
2024/07/05 19:33:40 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:33:40 - mmengine - INFO - Saving checkpoint at 35 epochs
2024/07/05 19:34:02 - mmengine - INFO - Epoch(val)  [35][100/147]    eta: 0:00:08  time: 0.1753  data_time: 0.0313  memory: 3093  
2024/07/05 19:34:08 - mmengine - INFO - Epoch(val) [35][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9943540971299815, 'occupancy': 0.5705780362905045, 'mIoU': 0.782466066710243}, '1~2m': {'no_occupancy': 0.992282150679533, 'occupancy': 0.2586838007169738, 'mIoU': 0.6254829756982534}, '2~3m': {'no_occupancy': 0.9971861849789868, 'occupancy': 0.009951242436703284, 'mIoU': 0.5035687137078451}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9957078332445396, 'occupancy': 0.2098032698610454, 'mIoU': 0.6027555515527925}}  total_overall: {'no_occupancy': 0.9957126552750484, 'occupancy': 0.3726306546844095, 'mIoU': 0.684171654979729}  data_time: 0.0339  time: 0.1738
2024/07/05 19:35:36 - mmengine - INFO - Epoch(train)  [36][100/147]  lr: 8.0438e-05  eta: 2:53:14  time: 0.7276  data_time: 0.0009  memory: 3093  grad_norm: 4.5212  loss: 0.4959  loss_bce: 0.0264  loss_dice: 0.4695
2024/07/05 19:36:11 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:36:11 - mmengine - INFO - Saving checkpoint at 36 epochs
2024/07/05 19:36:32 - mmengine - INFO - Epoch(val)  [36][100/147]    eta: 0:00:08  time: 0.1736  data_time: 0.0313  memory: 3093  
2024/07/05 19:36:39 - mmengine - INFO - Epoch(val) [36][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9941918712904853, 'occupancy': 0.5689946340575832, 'mIoU': 0.7815932526740342}, '1~2m': {'no_occupancy': 0.992525977578431, 'occupancy': 0.2712992955936038, 'mIoU': 0.6319126365860174}, '2~3m': {'no_occupancy': 0.9971900237545553, 'occupancy': 0.010116099890604968, 'mIoU': 0.5036530618225802}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9957291932032821, 'occupancy': 0.212602507385448, 'mIoU': 0.6041658502943651}}  total_overall: {'no_occupancy': 0.995734342754904, 'occupancy': 0.37960203175998175, 'mIoU': 0.6876681872574428}  data_time: 0.0340  time: 0.1725
2024/07/05 19:38:08 - mmengine - INFO - Epoch(train)  [37][100/147]  lr: 7.9389e-05  eta: 2:51:10  time: 0.7415  data_time: 0.0009  memory: 3093  grad_norm: 4.0860  loss: 0.4927  loss_bce: 0.0261  loss_dice: 0.4666
2024/07/05 19:38:43 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:38:43 - mmengine - INFO - Saving checkpoint at 37 epochs
2024/07/05 19:39:04 - mmengine - INFO - Epoch(val)  [37][100/147]    eta: 0:00:08  time: 0.1727  data_time: 0.0306  memory: 3093  
2024/07/05 19:39:11 - mmengine - INFO - Epoch(val) [37][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9946673306739902, 'occupancy': 0.5891089878945547, 'mIoU': 0.7918881592842724}, '1~2m': {'no_occupancy': 0.9926414684725848, 'occupancy': 0.28342873121764983, 'mIoU': 0.6380350998451173}, '2~3m': {'no_occupancy': 0.9971914575163455, 'occupancy': 0.010377447289156627, 'mIoU': 0.503784452402751}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9958772892131443, 'occupancy': 0.2207287916003403, 'mIoU': 0.6083030404067423}}  total_overall: {'no_occupancy': 0.9958819346062887, 'occupancy': 0.39034930934845313, 'mIoU': 0.6931156219773709}  data_time: 0.0358  time: 0.1745
2024/07/05 19:40:39 - mmengine - INFO - Epoch(train)  [38][100/147]  lr: 7.8320e-05  eta: 2:49:06  time: 0.7417  data_time: 0.0009  memory: 3093  grad_norm: 4.2271  loss: 0.4955  loss_bce: 0.0268  loss_dice: 0.4687
2024/07/05 19:41:14 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:41:14 - mmengine - INFO - Saving checkpoint at 38 epochs
2024/07/05 19:41:35 - mmengine - INFO - Epoch(val)  [38][100/147]    eta: 0:00:08  time: 0.1721  data_time: 0.0297  memory: 3093  
2024/07/05 19:41:42 - mmengine - INFO - Epoch(val) [38][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9948940171616049, 'occupancy': 0.5970955473532369, 'mIoU': 0.7959947822574209}, '1~2m': {'no_occupancy': 0.9927887109957212, 'occupancy': 0.28798116306578797, 'mIoU': 0.6403849370307546}, '2~3m': {'no_occupancy': 0.9971890176985402, 'occupancy': 0.01064767478757536, 'mIoU': 0.5039183462430578}, '3~4m': {'no_occupancy': 0.9990088667993374, 'occupancy': 0.0, 'mIoU': 0.4995044333996687}, 'patch_mean': {'no_occupancy': 0.9959701531638009, 'occupancy': 0.22393109630165006, 'mIoU': 0.6099506247327254}}  total_overall: {'no_occupancy': 0.995974488798327, 'occupancy': 0.3941580405942759, 'mIoU': 0.6950662646963014}  data_time: 0.0350  time: 0.1732
2024/07/05 19:43:11 - mmengine - INFO - Epoch(train)  [39][100/147]  lr: 7.7232e-05  eta: 2:47:03  time: 0.7444  data_time: 0.0009  memory: 3093  grad_norm: 4.2391  loss: 0.5209  loss_bce: 0.0284  loss_dice: 0.4925
2024/07/05 19:43:46 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:43:46 - mmengine - INFO - Saving checkpoint at 39 epochs
2024/07/05 19:44:07 - mmengine - INFO - Epoch(val)  [39][100/147]    eta: 0:00:08  time: 0.1716  data_time: 0.0300  memory: 3093  
2024/07/05 19:44:14 - mmengine - INFO - Epoch(val) [39][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9945474949064166, 'occupancy': 0.584548704300412, 'mIoU': 0.7895480996034143}, '1~2m': {'no_occupancy': 0.9925561633750708, 'occupancy': 0.28273332341081364, 'mIoU': 0.6376447433929422}, '2~3m': {'no_occupancy': 0.9971907471061333, 'occupancy': 0.011514375345137527, 'mIoU': 0.5043525612256354}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9958258263943194, 'occupancy': 0.2196991007640908, 'mIoU': 0.607762463579205}}  total_overall: {'no_occupancy': 0.995830652553153, 'occupancy': 0.388594783425155, 'mIoU': 0.692212717989154}  data_time: 0.0329  time: 0.1705
2024/07/05 19:45:42 - mmengine - INFO - Epoch(train)  [40][100/147]  lr: 7.6125e-05  eta: 2:44:59  time: 0.7377  data_time: 0.0009  memory: 3093  grad_norm: 3.9604  loss: 0.4884  loss_bce: 0.0267  loss_dice: 0.4617
2024/07/05 19:46:17 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:46:17 - mmengine - INFO - Saving checkpoint at 40 epochs
2024/07/05 19:46:38 - mmengine - INFO - Epoch(val)  [40][100/147]    eta: 0:00:08  time: 0.1751  data_time: 0.0328  memory: 3093  
2024/07/05 19:46:45 - mmengine - INFO - Epoch(val) [40][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9945621957507734, 'occupancy': 0.5894428217685788, 'mIoU': 0.792002508759676}, '1~2m': {'no_occupancy': 0.992655532089943, 'occupancy': 0.29215402176136573, 'mIoU': 0.6424047769256543}, '2~3m': {'no_occupancy': 0.9971777137120285, 'occupancy': 0.0127781346726625, 'mIoU': 0.5049779241923454}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9958510854356004, 'occupancy': 0.22359374455065176, 'mIoU': 0.6097224149931261}}  total_overall: {'no_occupancy': 0.995855997110928, 'occupancy': 0.3952707530737831, 'mIoU': 0.6955633750923556}  data_time: 0.0357  time: 0.1738
2024/07/05 19:48:15 - mmengine - INFO - Epoch(train)  [41][100/147]  lr: 7.5000e-05  eta: 2:42:57  time: 0.7387  data_time: 0.0009  memory: 3093  grad_norm: 4.1445  loss: 0.4826  loss_bce: 0.0262  loss_dice: 0.4564
2024/07/05 19:48:30 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:48:50 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:48:50 - mmengine - INFO - Saving checkpoint at 41 epochs
2024/07/05 19:49:11 - mmengine - INFO - Epoch(val)  [41][100/147]    eta: 0:00:08  time: 0.1701  data_time: 0.0289  memory: 3093  
2024/07/05 19:49:18 - mmengine - INFO - Epoch(val) [41][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9952056010063306, 'occupancy': 0.6140818102756924, 'mIoU': 0.8046437056410115}, '1~2m': {'no_occupancy': 0.9930775416104075, 'occupancy': 0.30229295514594257, 'mIoU': 0.647685248378175}, '2~3m': {'no_occupancy': 0.9971946603648756, 'occupancy': 0.010727112157786281, 'mIoU': 0.503960886261331}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9961216757928176, 'occupancy': 0.23177546939485533, 'mIoU': 0.6139485725938365}}  total_overall: {'no_occupancy': 0.9961256879207976, 'occupancy': 0.40662864746179267, 'mIoU': 0.7013771676912951}  data_time: 0.0332  time: 0.1709
2024/07/05 19:50:48 - mmengine - INFO - Epoch(train)  [42][100/147]  lr: 7.3858e-05  eta: 2:40:56  time: 0.7462  data_time: 0.0009  memory: 3093  grad_norm: 4.2709  loss: 0.4672  loss_bce: 0.0261  loss_dice: 0.4411
2024/07/05 19:51:22 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:51:22 - mmengine - INFO - Saving checkpoint at 42 epochs
2024/07/05 19:51:44 - mmengine - INFO - Epoch(val)  [42][100/147]    eta: 0:00:08  time: 0.1725  data_time: 0.0314  memory: 3093  
2024/07/05 19:51:51 - mmengine - INFO - Epoch(val) [42][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9950601410340693, 'occupancy': 0.6075896927392873, 'mIoU': 0.8013249168866783}, '1~2m': {'no_occupancy': 0.9928998280969458, 'occupancy': 0.2991131849426168, 'mIoU': 0.6460065065197813}, '2~3m': {'no_occupancy': 0.9971885691541331, 'occupancy': 0.012433142535422725, 'mIoU': 0.5048108558447779}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9960393596187013, 'occupancy': 0.22978400505433172, 'mIoU': 0.6129116823365165}}  total_overall: {'no_occupancy': 0.9960435900079831, 'occupancy': 0.4028281178937793, 'mIoU': 0.6994358539508813}  data_time: 0.0336  time: 0.1724
2024/07/05 19:53:20 - mmengine - INFO - Epoch(train)  [43][100/147]  lr: 7.2700e-05  eta: 2:38:55  time: 0.7454  data_time: 0.0009  memory: 3093  grad_norm: 3.9872  loss: 0.4963  loss_bce: 0.0275  loss_dice: 0.4688
2024/07/05 19:53:55 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:53:55 - mmengine - INFO - Saving checkpoint at 43 epochs
2024/07/05 19:54:16 - mmengine - INFO - Epoch(val)  [43][100/147]    eta: 0:00:08  time: 0.1719  data_time: 0.0313  memory: 3093  
2024/07/05 19:54:23 - mmengine - INFO - Epoch(val) [43][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9949390185422398, 'occupancy': 0.6059668352685157, 'mIoU': 0.8004529269053777}, '1~2m': {'no_occupancy': 0.9928687862233845, 'occupancy': 0.30778250364261894, 'mIoU': 0.6503256449330017}, '2~3m': {'no_occupancy': 0.9971915477497989, 'occupancy': 0.011610552911451907, 'mIoU': 0.5044010503306254}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.99600206317627, 'occupancy': 0.23133997295564662, 'mIoU': 0.6136710180659584}}  total_overall: {'no_occupancy': 0.9960065940695307, 'occupancy': 0.4066304966653804, 'mIoU': 0.7013185453674555}  data_time: 0.0339  time: 0.1725
2024/07/05 19:55:53 - mmengine - INFO - Epoch(train)  [44][100/147]  lr: 7.1526e-05  eta: 2:36:53  time: 0.7470  data_time: 0.0009  memory: 3093  grad_norm: 4.5584  loss: 0.4897  loss_bce: 0.0267  loss_dice: 0.4630
2024/07/05 19:56:28 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:56:28 - mmengine - INFO - Saving checkpoint at 44 epochs
2024/07/05 19:56:50 - mmengine - INFO - Epoch(val)  [44][100/147]    eta: 0:00:08  time: 0.1743  data_time: 0.0323  memory: 3093  
2024/07/05 19:56:57 - mmengine - INFO - Epoch(val) [44][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.995328164484973, 'occupancy': 0.6194791875759157, 'mIoU': 0.8074036760304444}, '1~2m': {'no_occupancy': 0.993094812834571, 'occupancy': 0.3062003618346472, 'mIoU': 0.6496475873346091}, '2~3m': {'no_occupancy': 0.9971950155871425, 'occupancy': 0.012229107627905885, 'mIoU': 0.5047120616075242}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9961567232740858, 'occupancy': 0.2344771642596172, 'mIoU': 0.6153169437668515}}  total_overall: {'no_occupancy': 0.9961606138528275, 'occupancy': 0.4094852146426104, 'mIoU': 0.7028229142477189}  data_time: 0.0340  time: 0.1718
2024/07/05 19:58:26 - mmengine - INFO - Epoch(train)  [45][100/147]  lr: 7.0337e-05  eta: 2:34:50  time: 0.7449  data_time: 0.0009  memory: 3093  grad_norm: 3.9292  loss: 0.4674  loss_bce: 0.0261  loss_dice: 0.4413
2024/07/05 19:59:00 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 19:59:00 - mmengine - INFO - Saving checkpoint at 45 epochs
2024/07/05 19:59:21 - mmengine - INFO - Epoch(val)  [45][100/147]    eta: 0:00:08  time: 0.1738  data_time: 0.0335  memory: 3093  
2024/07/05 19:59:28 - mmengine - INFO - Epoch(val) [45][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9952533792726214, 'occupancy': 0.6205985320623445, 'mIoU': 0.8079259556674829}, '1~2m': {'no_occupancy': 0.9930212429581335, 'occupancy': 0.30868698766348623, 'mIoU': 0.6508541153108098}, '2~3m': {'no_occupancy': 0.9971809744105553, 'occupancy': 0.014222159946754472, 'mIoU': 0.5057015671786549}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9961161242077419, 'occupancy': 0.2358769199181463, 'mIoU': 0.6159965220629441}}  total_overall: {'no_occupancy': 0.9961201896946535, 'occupancy': 0.41215656446681703, 'mIoU': 0.7041383770807352}  data_time: 0.0361  time: 0.1728
2024/07/05 20:00:58 - mmengine - INFO - Epoch(train)  [46][100/147]  lr: 6.9134e-05  eta: 2:32:48  time: 0.7357  data_time: 0.0009  memory: 3093  grad_norm: 3.6219  loss: 0.4353  loss_bce: 0.0240  loss_dice: 0.4113
2024/07/05 20:01:32 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:01:32 - mmengine - INFO - Saving checkpoint at 46 epochs
2024/07/05 20:01:54 - mmengine - INFO - Epoch(val)  [46][100/147]    eta: 0:00:08  time: 0.1772  data_time: 0.0331  memory: 3093  
2024/07/05 20:02:01 - mmengine - INFO - Epoch(val) [46][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9952979693937486, 'occupancy': 0.6227349534900437, 'mIoU': 0.8090164614418962}, '1~2m': {'no_occupancy': 0.9930091542535641, 'occupancy': 0.3132711542748652, 'mIoU': 0.6531401542642146}, '2~3m': {'no_occupancy': 0.9971797264875641, 'occupancy': 0.015732432117468824, 'mIoU': 0.5064560793025165}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9961239375811335, 'occupancy': 0.23793463497059442, 'mIoU': 0.617029286275864}}  total_overall: {'no_occupancy': 0.9961280025437105, 'occupancy': 0.4143007838311578, 'mIoU': 0.7052143931874342}  data_time: 0.0343  time: 0.1732
2024/07/05 20:03:30 - mmengine - INFO - Epoch(train)  [47][100/147]  lr: 6.7918e-05  eta: 2:30:45  time: 0.7373  data_time: 0.0009  memory: 3093  grad_norm: 3.5894  loss: 0.4767  loss_bce: 0.0262  loss_dice: 0.4506
2024/07/05 20:04:04 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:04:04 - mmengine - INFO - Saving checkpoint at 47 epochs
2024/07/05 20:04:26 - mmengine - INFO - Epoch(val)  [47][100/147]    eta: 0:00:08  time: 0.1744  data_time: 0.0315  memory: 3093  
2024/07/05 20:04:33 - mmengine - INFO - Epoch(val) [47][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.995427649535164, 'occupancy': 0.6326836343581221, 'mIoU': 0.8140556419466431}, '1~2m': {'no_occupancy': 0.9932006474633466, 'occupancy': 0.32474496256623114, 'mIoU': 0.6589728050147888}, '2~3m': {'no_occupancy': 0.9971952900748049, 'occupancy': 0.01544917479369842, 'mIoU': 0.5063222324342517}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.996208121815743, 'occupancy': 0.24321944292951292, 'mIoU': 0.619713782372628}}  total_overall: {'no_occupancy': 0.9962120972410637, 'occupancy': 0.4238931905727742, 'mIoU': 0.710052643906919}  data_time: 0.0332  time: 0.1717
2024/07/05 20:05:56 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:06:03 - mmengine - INFO - Epoch(train)  [48][100/147]  lr: 6.6690e-05  eta: 2:28:43  time: 0.7449  data_time: 0.0009  memory: 3093  grad_norm: 3.4866  loss: 0.4616  loss_bce: 0.0254  loss_dice: 0.4362
2024/07/05 20:06:38 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:06:38 - mmengine - INFO - Saving checkpoint at 48 epochs
2024/07/05 20:06:59 - mmengine - INFO - Epoch(val)  [48][100/147]    eta: 0:00:08  time: 0.1731  data_time: 0.0323  memory: 3093  
2024/07/05 20:07:06 - mmengine - INFO - Epoch(val) [48][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.995472165330551, 'occupancy': 0.6303726650066543, 'mIoU': 0.8129224151686028}, '1~2m': {'no_occupancy': 0.9931205115056592, 'occupancy': 0.3193235149843892, 'mIoU': 0.6562220132450242}, '2~3m': {'no_occupancy': 0.997195991396005, 'occupancy': 0.01544143510376363, 'mIoU': 0.5063187132498843}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.996199392105468, 'occupancy': 0.24128440377370178, 'mIoU': 0.6187418979395849}}  total_overall: {'no_occupancy': 0.9962032610154131, 'occupancy': 0.41902663467563944, 'mIoU': 0.7076149478455263}  data_time: 0.0363  time: 0.1743
2024/07/05 20:08:35 - mmengine - INFO - Epoch(train)  [49][100/147]  lr: 6.5451e-05  eta: 2:26:40  time: 0.7387  data_time: 0.0009  memory: 3093  grad_norm: 3.8493  loss: 0.4712  loss_bce: 0.0259  loss_dice: 0.4453
2024/07/05 20:09:09 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:09:09 - mmengine - INFO - Saving checkpoint at 49 epochs
2024/07/05 20:09:31 - mmengine - INFO - Epoch(val)  [49][100/147]    eta: 0:00:08  time: 0.1726  data_time: 0.0329  memory: 3093  
2024/07/05 20:09:38 - mmengine - INFO - Epoch(val) [49][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9954500936649788, 'occupancy': 0.6323824846999935, 'mIoU': 0.8139162891824862}, '1~2m': {'no_occupancy': 0.9932958305572095, 'occupancy': 0.32849256394404147, 'mIoU': 0.6608941972506255}, '2~3m': {'no_occupancy': 0.9971889008851231, 'occupancy': 0.016806035690927774, 'mIoU': 0.5069974682880255}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9962359313242422, 'occupancy': 0.24442027108374068, 'mIoU': 0.6203281012039914}}  total_overall: {'no_occupancy': 0.9962398591758885, 'occupancy': 0.42493018441619834, 'mIoU': 0.7105850217960434}  data_time: 0.0349  time: 0.1722
2024/07/05 20:11:07 - mmengine - INFO - Epoch(train)  [50][100/147]  lr: 6.4201e-05  eta: 2:24:37  time: 0.7404  data_time: 0.0009  memory: 3093  grad_norm: 3.5834  loss: 0.4713  loss_bce: 0.0253  loss_dice: 0.4461
2024/07/05 20:11:42 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:11:42 - mmengine - INFO - Saving checkpoint at 50 epochs
2024/07/05 20:12:03 - mmengine - INFO - Epoch(val)  [50][100/147]    eta: 0:00:08  time: 0.1697  data_time: 0.0288  memory: 3093  
2024/07/05 20:12:10 - mmengine - INFO - Epoch(val) [50][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9958334304965608, 'occupancy': 0.6508008914539852, 'mIoU': 0.823317160975273}, '1~2m': {'no_occupancy': 0.9934298819007095, 'occupancy': 0.3333775204163442, 'mIoU': 0.6634037011585269}, '2~3m': {'no_occupancy': 0.9971960400155087, 'occupancy': 0.01767623593271098, 'mIoU': 0.5074361379741098}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.996367063150609, 'occupancy': 0.2504636619507601, 'mIoU': 0.6234153625506845}}  total_overall: {'no_occupancy': 0.9963704949011206, 'occupancy': 0.4324311721027539, 'mIoU': 0.7144008335019373}  data_time: 0.0325  time: 0.1712
2024/07/05 20:13:40 - mmengine - INFO - Epoch(train)  [51][100/147]  lr: 6.2941e-05  eta: 2:22:35  time: 0.7378  data_time: 0.0009  memory: 3093  grad_norm: 3.8519  loss: 0.4365  loss_bce: 0.0234  loss_dice: 0.4131
2024/07/05 20:14:14 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:14:14 - mmengine - INFO - Saving checkpoint at 51 epochs
2024/07/05 20:14:36 - mmengine - INFO - Epoch(val)  [51][100/147]    eta: 0:00:08  time: 0.1754  data_time: 0.0326  memory: 3093  
2024/07/05 20:14:43 - mmengine - INFO - Epoch(val) [51][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9957719109417086, 'occupancy': 0.6509619622007771, 'mIoU': 0.8233669365712428}, '1~2m': {'no_occupancy': 0.9933719679842792, 'occupancy': 0.3390451220169199, 'mIoU': 0.6662085450005996}, '2~3m': {'no_occupancy': 0.9971973061040412, 'occupancy': 0.018028874277558084, 'mIoU': 0.5076130901907997}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9963375213049215, 'occupancy': 0.25200898962381374, 'mIoU': 0.6241732554643676}}  total_overall: {'no_occupancy': 0.996341138861488, 'occupancy': 0.43559791170591367, 'mIoU': 0.7159695252837008}  data_time: 0.0354  time: 0.1749
2024/07/05 20:16:12 - mmengine - INFO - Epoch(train)  [52][100/147]  lr: 6.1672e-05  eta: 2:20:32  time: 0.7435  data_time: 0.0009  memory: 3093  grad_norm: 3.6950  loss: 0.4372  loss_bce: 0.0237  loss_dice: 0.4136
2024/07/05 20:16:46 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:16:46 - mmengine - INFO - Saving checkpoint at 52 epochs
2024/07/05 20:17:08 - mmengine - INFO - Epoch(val)  [52][100/147]    eta: 0:00:08  time: 0.1710  data_time: 0.0279  memory: 3093  
2024/07/05 20:17:14 - mmengine - INFO - Epoch(val) [52][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9958903420992876, 'occupancy': 0.6559103391268546, 'mIoU': 0.8259003406130712}, '1~2m': {'no_occupancy': 0.9934206741259685, 'occupancy': 0.3436413812121225, 'mIoU': 0.6685310276690455}, '2~3m': {'no_occupancy': 0.9971805943628851, 'occupancy': 0.01982795648994091, 'mIoU': 0.508504275426413}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9963751276944495, 'occupancy': 0.2548449192072295, 'mIoU': 0.6256100234508395}}  total_overall: {'no_occupancy': 0.9963786035929312, 'occupancy': 0.43811338019571017, 'mIoU': 0.7172459918943207}  data_time: 0.0330  time: 0.1708
2024/07/05 20:18:43 - mmengine - INFO - Epoch(train)  [53][100/147]  lr: 6.0396e-05  eta: 2:18:28  time: 0.7360  data_time: 0.0009  memory: 3093  grad_norm: 3.8000  loss: 0.4370  loss_bce: 0.0243  loss_dice: 0.4127
2024/07/05 20:19:18 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:19:18 - mmengine - INFO - Saving checkpoint at 53 epochs
2024/07/05 20:19:39 - mmengine - INFO - Epoch(val)  [53][100/147]    eta: 0:00:08  time: 0.1733  data_time: 0.0320  memory: 3093  
2024/07/05 20:19:46 - mmengine - INFO - Epoch(val) [53][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9957432307750588, 'occupancy': 0.6497614346112777, 'mIoU': 0.8227523326931683}, '1~2m': {'no_occupancy': 0.9933650309931821, 'occupancy': 0.3462551931820386, 'mIoU': 0.6698101120876103}, '2~3m': {'no_occupancy': 0.9971825538729633, 'occupancy': 0.02112651545913336, 'mIoU': 0.5091545346660483}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9963249289577152, 'occupancy': 0.2542857858131124, 'mIoU': 0.6253053573854138}}  total_overall: {'no_occupancy': 0.9963286572999331, 'occupancy': 0.43768491681427946, 'mIoU': 0.7170067870571063}  data_time: 0.0344  time: 0.1736
2024/07/05 20:21:16 - mmengine - INFO - Epoch(train)  [54][100/147]  lr: 5.9112e-05  eta: 2:16:26  time: 0.7449  data_time: 0.0009  memory: 3093  grad_norm: 3.5270  loss: 0.4375  loss_bce: 0.0243  loss_dice: 0.4131
2024/07/05 20:21:51 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:21:51 - mmengine - INFO - Saving checkpoint at 54 epochs
2024/07/05 20:22:12 - mmengine - INFO - Epoch(val)  [54][100/147]    eta: 0:00:08  time: 0.1702  data_time: 0.0292  memory: 3093  
2024/07/05 20:22:19 - mmengine - INFO - Epoch(val) [54][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.995914202015862, 'occupancy': 0.6595890024454042, 'mIoU': 0.827751602230633}, '1~2m': {'no_occupancy': 0.9935180955991769, 'occupancy': 0.35238122339764416, 'mIoU': 0.6729496594984106}, '2~3m': {'no_occupancy': 0.9971824368546892, 'occupancy': 0.023154311911735767, 'mIoU': 0.5101683743832125}, '3~4m': {'no_occupancy': 0.9990079646644731, 'occupancy': 0.0006054846820784944, 'mIoU': 0.4998067246732758}, 'patch_mean': {'no_occupancy': 0.9964056747835502, 'occupancy': 0.2589325056092156, 'mIoU': 0.627669090196383}}  total_overall: {'no_occupancy': 0.9964091736229411, 'occupancy': 0.4439846456476018, 'mIoU': 0.7201969096352715}  data_time: 0.0335  time: 0.1702
2024/07/05 20:23:20 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:23:49 - mmengine - INFO - Epoch(train)  [55][100/147]  lr: 5.7822e-05  eta: 2:14:23  time: 0.7517  data_time: 0.0009  memory: 3093  grad_norm: 4.2246  loss: 0.4401  loss_bce: 0.0242  loss_dice: 0.4159
2024/07/05 20:24:24 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:24:24 - mmengine - INFO - Saving checkpoint at 55 epochs
2024/07/05 20:24:45 - mmengine - INFO - Epoch(val)  [55][100/147]    eta: 0:00:08  time: 0.1751  data_time: 0.0325  memory: 3093  
2024/07/05 20:24:52 - mmengine - INFO - Epoch(val) [55][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9958932269634899, 'occupancy': 0.6571710106533344, 'mIoU': 0.8265321188084122}, '1~2m': {'no_occupancy': 0.9935316197654689, 'occupancy': 0.34627656107279603, 'mIoU': 0.6699040904191325}, '2~3m': {'no_occupancy': 0.9971881041391216, 'occupancy': 0.020301792840273173, 'mIoU': 0.5087449484896974}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9964054627644343, 'occupancy': 0.2559373411416009, 'mIoU': 0.6261714019530176}}  total_overall: {'no_occupancy': 0.9964089145974218, 'occupancy': 0.44063942011722645, 'mIoU': 0.7185241673573242}  data_time: 0.0350  time: 0.1732
2024/07/05 20:26:21 - mmengine - INFO - Epoch(train)  [56][100/147]  lr: 5.6526e-05  eta: 2:12:20  time: 0.7409  data_time: 0.0009  memory: 3093  grad_norm: 3.4921  loss: 0.4413  loss_bce: 0.0243  loss_dice: 0.4170
2024/07/05 20:26:55 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:26:55 - mmengine - INFO - Saving checkpoint at 56 epochs
2024/07/05 20:27:17 - mmengine - INFO - Epoch(val)  [56][100/147]    eta: 0:00:08  time: 0.1731  data_time: 0.0321  memory: 3093  
2024/07/05 20:27:24 - mmengine - INFO - Epoch(val) [56][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9961717979986408, 'occupancy': 0.6734160616004754, 'mIoU': 0.8347939297995581}, '1~2m': {'no_occupancy': 0.9936930218190191, 'occupancy': 0.3586343313429365, 'mIoU': 0.6761636765809778}, '2~3m': {'no_occupancy': 0.99720180705622, 'occupancy': 0.023003381135595195, 'mIoU': 0.5101025940959076}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9965188817658842, 'occupancy': 0.2637634435197518, 'mIoU': 0.6301411626428179}}  total_overall: {'no_occupancy': 0.9965220418462085, 'occupancy': 0.451352399352709, 'mIoU': 0.7239372205994588}  data_time: 0.0345  time: 0.1728
2024/07/05 20:28:54 - mmengine - INFO - Epoch(train)  [57][100/147]  lr: 5.5226e-05  eta: 2:10:17  time: 0.7505  data_time: 0.0009  memory: 3093  grad_norm: 3.5423  loss: 0.4348  loss_bce: 0.0238  loss_dice: 0.4109
2024/07/05 20:29:28 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:29:28 - mmengine - INFO - Saving checkpoint at 57 epochs
2024/07/05 20:29:50 - mmengine - INFO - Epoch(val)  [57][100/147]    eta: 0:00:08  time: 0.1740  data_time: 0.0319  memory: 3093  
2024/07/05 20:29:57 - mmengine - INFO - Epoch(val) [57][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9960329024288862, 'occupancy': 0.6682629472344369, 'mIoU': 0.8321479248316616}, '1~2m': {'no_occupancy': 0.9936728618818954, 'occupancy': 0.36212189099582304, 'mIoU': 0.6778973764388592}, '2~3m': {'no_occupancy': 0.9971804826999195, 'occupancy': 0.021213456054529012, 'mIoU': 0.5091969693772243}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9964737868000895, 'occupancy': 0.26289957357119725, 'mIoU': 0.6296866801856433}}  total_overall: {'no_occupancy': 0.9964771657808456, 'occupancy': 0.4515526810891055, 'mIoU': 0.7240149234349755}  data_time: 0.0362  time: 0.1747
2024/07/05 20:31:26 - mmengine - INFO - Epoch(train)  [58][100/147]  lr: 5.3923e-05  eta: 2:08:14  time: 0.7395  data_time: 0.0009  memory: 3093  grad_norm: 3.7537  loss: 0.4227  loss_bce: 0.0236  loss_dice: 0.3990
2024/07/05 20:32:00 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:32:00 - mmengine - INFO - Saving checkpoint at 58 epochs
2024/07/05 20:32:21 - mmengine - INFO - Epoch(val)  [58][100/147]    eta: 0:00:08  time: 0.1740  data_time: 0.0311  memory: 3093  
2024/07/05 20:32:28 - mmengine - INFO - Epoch(val) [58][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9962596707466727, 'occupancy': 0.6808788706428963, 'mIoU': 0.8385692706947845}, '1~2m': {'no_occupancy': 0.9938299159950977, 'occupancy': 0.36688810148866774, 'mIoU': 0.6803590087418827}, '2~3m': {'no_occupancy': 0.9971934260961755, 'occupancy': 0.022868104400395283, 'mIoU': 0.5100307652482854}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9965729782569007, 'occupancy': 0.26765876913298986, 'mIoU': 0.6321158736949453}}  total_overall: {'no_occupancy': 0.9965760402986636, 'occupancy': 0.4581264063677667, 'mIoU': 0.7273512233332151}  data_time: 0.0345  time: 0.1733
2024/07/05 20:33:58 - mmengine - INFO - Epoch(train)  [59][100/147]  lr: 5.2617e-05  eta: 2:06:11  time: 0.7477  data_time: 0.0009  memory: 3093  grad_norm: 3.6692  loss: 0.4093  loss_bce: 0.0223  loss_dice: 0.3870
2024/07/05 20:34:32 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:34:32 - mmengine - INFO - Saving checkpoint at 59 epochs
2024/07/05 20:34:54 - mmengine - INFO - Epoch(val)  [59][100/147]    eta: 0:00:08  time: 0.1721  data_time: 0.0304  memory: 3093  
2024/07/05 20:35:01 - mmengine - INFO - Epoch(val) [59][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9962254921632152, 'occupancy': 0.6804576690337524, 'mIoU': 0.8383415805984837}, '1~2m': {'no_occupancy': 0.9938107110746356, 'occupancy': 0.3741734127258117, 'mIoU': 0.6839920619002237}, '2~3m': {'no_occupancy': 0.9972061506959579, 'occupancy': 0.022718777741438118, 'mIoU': 0.509962464218698}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9965628135308664, 'occupancy': 0.26933746487525057, 'mIoU': 0.6329501392030585}}  total_overall: {'no_occupancy': 0.9965660215563291, 'occupancy': 0.46124828138098983, 'mIoU': 0.7289071514686595}  data_time: 0.0337  time: 0.1710
2024/07/05 20:36:30 - mmengine - INFO - Epoch(train)  [60][100/147]  lr: 5.1309e-05  eta: 2:04:07  time: 0.7428  data_time: 0.0009  memory: 3093  grad_norm: 3.7240  loss: 0.4383  loss_bce: 0.0249  loss_dice: 0.4134
2024/07/05 20:37:04 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:37:04 - mmengine - INFO - Saving checkpoint at 60 epochs
2024/07/05 20:37:25 - mmengine - INFO - Epoch(val)  [60][100/147]    eta: 0:00:08  time: 0.1740  data_time: 0.0315  memory: 3093  
2024/07/05 20:37:33 - mmengine - INFO - Epoch(val) [60][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9963759198626937, 'occupancy': 0.6861280146787173, 'mIoU': 0.8412519672707055}, '1~2m': {'no_occupancy': 0.9939249684440388, 'occupancy': 0.3754052778285033, 'mIoU': 0.684665123136271}, '2~3m': {'no_occupancy': 0.9972060314857911, 'occupancy': 0.025041074820261248, 'mIoU': 0.5111235531530262}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9966289549955452, 'occupancy': 0.2716435918318704, 'mIoU': 0.6341362734137078}}  total_overall: {'no_occupancy': 0.9966319097090532, 'occupancy': 0.4627331680946988, 'mIoU': 0.729682538901876}  data_time: 0.0358  time: 0.1741
2024/07/05 20:39:02 - mmengine - INFO - Epoch(train)  [61][100/147]  lr: 5.0000e-05  eta: 2:02:05  time: 0.7428  data_time: 0.0009  memory: 3093  grad_norm: 3.3514  loss: 0.4164  loss_bce: 0.0230  loss_dice: 0.3933
2024/07/05 20:39:37 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:39:37 - mmengine - INFO - Saving checkpoint at 61 epochs
2024/07/05 20:39:58 - mmengine - INFO - Epoch(val)  [61][100/147]    eta: 0:00:08  time: 0.1720  data_time: 0.0316  memory: 3093  
2024/07/05 20:40:05 - mmengine - INFO - Epoch(val) [61][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9962838916471686, 'occupancy': 0.6843476868622467, 'mIoU': 0.8403157892547076}, '1~2m': {'no_occupancy': 0.9939691512193647, 'occupancy': 0.3817369326344316, 'mIoU': 0.6878530419268981}, '2~3m': {'no_occupancy': 0.9972044226609196, 'occupancy': 0.025753220607231384, 'mIoU': 0.5114788216340754}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9966165914292775, 'occupancy': 0.2729594600259774, 'mIoU': 0.6347880257276275}}  total_overall: {'no_occupancy': 0.9966197099040247, 'occupancy': 0.46624092672387074, 'mIoU': 0.7314303183139477}  data_time: 0.0349  time: 0.1731
2024/07/05 20:40:44 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:41:34 - mmengine - INFO - Epoch(train)  [62][100/147]  lr: 4.8691e-05  eta: 2:00:01  time: 0.7407  data_time: 0.0009  memory: 3093  grad_norm: 3.3407  loss: 0.4104  loss_bce: 0.0222  loss_dice: 0.3882
2024/07/05 20:42:08 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:42:08 - mmengine - INFO - Saving checkpoint at 62 epochs
2024/07/05 20:42:30 - mmengine - INFO - Epoch(val)  [62][100/147]    eta: 0:00:08  time: 0.1733  data_time: 0.0323  memory: 3093  
2024/07/05 20:42:37 - mmengine - INFO - Epoch(val) [62][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9963065175769552, 'occupancy': 0.6857075446028351, 'mIoU': 0.8410070310898952}, '1~2m': {'no_occupancy': 0.9938337793140014, 'occupancy': 0.37687629969315384, 'mIoU': 0.6853550395035777}, '2~3m': {'no_occupancy': 0.9972058223482719, 'occupancy': 0.030147433817052243, 'mIoU': 0.5136766280826621}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9965887548572213, 'occupancy': 0.2731828195282603, 'mIoU': 0.6348857871927408}}  total_overall: {'no_occupancy': 0.9965918737821821, 'occupancy': 0.4646291266375115, 'mIoU': 0.7306105002098467}  data_time: 0.0339  time: 0.1715
2024/07/05 20:44:06 - mmengine - INFO - Epoch(train)  [63][100/147]  lr: 4.7383e-05  eta: 1:57:58  time: 0.7473  data_time: 0.0009  memory: 3093  grad_norm: 3.6139  loss: 0.4132  loss_bce: 0.0230  loss_dice: 0.3901
2024/07/05 20:44:41 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:44:41 - mmengine - INFO - Saving checkpoint at 63 epochs
2024/07/05 20:45:02 - mmengine - INFO - Epoch(val)  [63][100/147]    eta: 0:00:08  time: 0.1736  data_time: 0.0331  memory: 3093  
2024/07/05 20:45:09 - mmengine - INFO - Epoch(val) [63][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9964857441222761, 'occupancy': 0.6970004777730336, 'mIoU': 0.8467431109476549}, '1~2m': {'no_occupancy': 0.9939561675296363, 'occupancy': 0.3870035610174821, 'mIoU': 0.6904798642735592}, '2~3m': {'no_occupancy': 0.9971959775874562, 'occupancy': 0.03328344462353212, 'mIoU': 0.5152397111054942}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9966616973572564, 'occupancy': 0.279321870853512, 'mIoU': 0.6379917841053842}}  total_overall: {'no_occupancy': 0.9966646169438755, 'occupancy': 0.47254092384863167, 'mIoU': 0.7346027703962537}  data_time: 0.0355  time: 0.1732
2024/07/05 20:46:39 - mmengine - INFO - Epoch(train)  [64][100/147]  lr: 4.6077e-05  eta: 1:55:55  time: 0.7413  data_time: 0.0009  memory: 3093  grad_norm: 3.1803  loss: 0.4016  loss_bce: 0.0220  loss_dice: 0.3796
2024/07/05 20:47:14 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:47:14 - mmengine - INFO - Saving checkpoint at 64 epochs
2024/07/05 20:47:36 - mmengine - INFO - Epoch(val)  [64][100/147]    eta: 0:00:08  time: 0.1741  data_time: 0.0312  memory: 3093  
2024/07/05 20:47:43 - mmengine - INFO - Epoch(val) [64][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9965587724925514, 'occupancy': 0.7003406539618152, 'mIoU': 0.8484497132271833}, '1~2m': {'no_occupancy': 0.9940386161730103, 'occupancy': 0.38904086531958915, 'mIoU': 0.6915397407462998}, '2~3m': {'no_occupancy': 0.9972088932457935, 'occupancy': 0.03434810236265958, 'mIoU': 0.5157784978042266}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.996703795525253, 'occupancy': 0.280932405411016, 'mIoU': 0.6388181004681345}}  total_overall: {'no_occupancy': 0.9967066002820391, 'occupancy': 0.47448999273082154, 'mIoU': 0.7355982965064303}  data_time: 0.0343  time: 0.1720
2024/07/05 20:49:12 - mmengine - INFO - Epoch(train)  [65][100/147]  lr: 4.4774e-05  eta: 1:53:52  time: 0.7473  data_time: 0.0009  memory: 3093  grad_norm: 3.3313  loss: 0.4205  loss_bce: 0.0237  loss_dice: 0.3967
2024/07/05 20:49:46 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:49:47 - mmengine - INFO - Saving checkpoint at 65 epochs
2024/07/05 20:50:08 - mmengine - INFO - Epoch(val)  [65][100/147]    eta: 0:00:08  time: 0.1736  data_time: 0.0319  memory: 3093  
2024/07/05 20:50:15 - mmengine - INFO - Epoch(val) [65][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9965918417572328, 'occupancy': 0.7031740459503413, 'mIoU': 0.849882943853787}, '1~2m': {'no_occupancy': 0.9941351623166144, 'occupancy': 0.39328662830058, 'mIoU': 0.6937108953085972}, '2~3m': {'no_occupancy': 0.997207636969297, 'occupancy': 0.03281400002313288, 'mIoU': 0.515010818496215}, '3~4m': {'no_occupancy': 0.9990086663912189, 'occupancy': 6.735991243211384e-05, 'mIoU': 0.4995380131518255}, 'patch_mean': {'no_occupancy': 0.9967358268585907, 'occupancy': 0.2823355085466216, 'mIoU': 0.6395356677026062}}  total_overall: {'no_occupancy': 0.9967385745130265, 'occupancy': 0.47759672563918, 'mIoU': 0.7371676500761033}  data_time: 0.0339  time: 0.1722
2024/07/05 20:51:45 - mmengine - INFO - Epoch(train)  [66][100/147]  lr: 4.3474e-05  eta: 1:51:49  time: 0.7459  data_time: 0.0009  memory: 3093  grad_norm: 3.4273  loss: 0.4126  loss_bce: 0.0235  loss_dice: 0.3892
2024/07/05 20:52:19 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:52:19 - mmengine - INFO - Saving checkpoint at 66 epochs
2024/07/05 20:52:40 - mmengine - INFO - Epoch(val)  [66][100/147]    eta: 0:00:08  time: 0.1748  data_time: 0.0320  memory: 3093  
2024/07/05 20:52:47 - mmengine - INFO - Epoch(val) [66][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.996620441588278, 'occupancy': 0.7046916205541444, 'mIoU': 0.8506560310712112}, '1~2m': {'no_occupancy': 0.994126375226071, 'occupancy': 0.3968277940419701, 'mIoU': 0.6954770846340206}, '2~3m': {'no_occupancy': 0.9972048418596087, 'occupancy': 0.03954192867306191, 'mIoU': 0.5183733852663353}, '3~4m': {'no_occupancy': 0.999007196421463, 'occupancy': 0.0008736779320715408, 'mIoU': 0.4999404371767672}, 'patch_mean': {'no_occupancy': 0.9967397137738552, 'occupancy': 0.28548375530031195, 'mIoU': 0.6411117345370836}}  total_overall: {'no_occupancy': 0.9967424616089123, 'occupancy': 0.47944905131135285, 'mIoU': 0.7380957564601326}  data_time: 0.0335  time: 0.1735
2024/07/05 20:54:16 - mmengine - INFO - Epoch(train)  [67][100/147]  lr: 4.2178e-05  eta: 1:49:46  time: 0.7441  data_time: 0.0009  memory: 3093  grad_norm: 3.2664  loss: 0.4156  loss_bce: 0.0239  loss_dice: 0.3917
2024/07/05 20:54:51 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:54:51 - mmengine - INFO - Saving checkpoint at 67 epochs
2024/07/05 20:55:12 - mmengine - INFO - Epoch(val)  [67][100/147]    eta: 0:00:08  time: 0.1719  data_time: 0.0309  memory: 3093  
2024/07/05 20:55:19 - mmengine - INFO - Epoch(val) [67][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.996697555254199, 'occupancy': 0.7101963096720737, 'mIoU': 0.8534469324631364}, '1~2m': {'no_occupancy': 0.9941779745004896, 'occupancy': 0.3999664898893173, 'mIoU': 0.6970722321949034}, '2~3m': {'no_occupancy': 0.9972074087337763, 'occupancy': 0.04010606182348282, 'mIoU': 0.5186567352786295}, '3~4m': {'no_occupancy': 0.9990068956762059, 'occupancy': 0.0011083030369182457, 'mIoU': 0.5000575993565621}, 'patch_mean': {'no_occupancy': 0.9967724585411677, 'occupancy': 0.287844291105448, 'mIoU': 0.6423083748233078}}  total_overall: {'no_occupancy': 0.9967751109793098, 'occupancy': 0.48292873736658415, 'mIoU': 0.739851924172947}  data_time: 0.0350  time: 0.1745
2024/07/05 20:56:48 - mmengine - INFO - Epoch(train)  [68][100/147]  lr: 4.0888e-05  eta: 1:47:42  time: 0.7394  data_time: 0.0009  memory: 3093  grad_norm: 3.2784  loss: 0.4246  loss_bce: 0.0235  loss_dice: 0.4011
2024/07/05 20:57:23 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:57:23 - mmengine - INFO - Saving checkpoint at 68 epochs
2024/07/05 20:57:44 - mmengine - INFO - Epoch(val)  [68][100/147]    eta: 0:00:08  time: 0.1724  data_time: 0.0286  memory: 3093  
2024/07/05 20:57:51 - mmengine - INFO - Epoch(val) [68][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9966906923821366, 'occupancy': 0.7105847381441887, 'mIoU': 0.8536377152631627}, '1~2m': {'no_occupancy': 0.9941392044805698, 'occupancy': 0.39797619744448776, 'mIoU': 0.6960577009625288}, '2~3m': {'no_occupancy': 0.9972004173943803, 'occupancy': 0.04531271352434747, 'mIoU': 0.5212565654593638}, '3~4m': {'no_occupancy': 0.9990084987774792, 'occupancy': 0.0007403356749253355, 'mIoU': 0.4998744172262023}, 'patch_mean': {'no_occupancy': 0.9967597032586415, 'occupancy': 0.2886534961969873, 'mIoU': 0.6427065997278144}}  total_overall: {'no_occupancy': 0.996762363667966, 'occupancy': 0.48279829685809544, 'mIoU': 0.7397803302630307}  data_time: 0.0333  time: 0.1725
2024/07/05 20:57:59 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:59:20 - mmengine - INFO - Epoch(train)  [69][100/147]  lr: 3.9604e-05  eta: 1:45:39  time: 0.7462  data_time: 0.0009  memory: 3093  grad_norm: 3.3987  loss: 0.4035  loss_bce: 0.0231  loss_dice: 0.3804
2024/07/05 20:59:54 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 20:59:54 - mmengine - INFO - Saving checkpoint at 69 epochs
2024/07/05 21:00:16 - mmengine - INFO - Epoch(val)  [69][100/147]    eta: 0:00:08  time: 0.1733  data_time: 0.0310  memory: 3093  
2024/07/05 21:00:23 - mmengine - INFO - Epoch(val) [69][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9967047115314915, 'occupancy': 0.7119511045035675, 'mIoU': 0.8543279080175294}, '1~2m': {'no_occupancy': 0.9941765707524336, 'occupancy': 0.40390310981748057, 'mIoU': 0.6990398402849571}, '2~3m': {'no_occupancy': 0.9972150142510282, 'occupancy': 0.04514592564604586, 'mIoU': 0.5211804699485371}, '3~4m': {'no_occupancy': 0.999008900189657, 'occupancy': 0.0, 'mIoU': 0.4995044500948285}, 'patch_mean': {'no_occupancy': 0.9967762991811525, 'occupancy': 0.2902500349917735, 'mIoU': 0.643513167086463}}  total_overall: {'no_occupancy': 0.9967789919491047, 'occupancy': 0.48591208262902513, 'mIoU': 0.7413455372890649}  data_time: 0.0345  time: 0.1718
2024/07/05 21:01:52 - mmengine - INFO - Epoch(train)  [70][100/147]  lr: 3.8328e-05  eta: 1:43:35  time: 0.7401  data_time: 0.0009  memory: 3093  grad_norm: 3.1626  loss: 0.4001  loss_bce: 0.0227  loss_dice: 0.3774
2024/07/05 21:02:27 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:02:27 - mmengine - INFO - Saving checkpoint at 70 epochs
2024/07/05 21:02:48 - mmengine - INFO - Epoch(val)  [70][100/147]    eta: 0:00:08  time: 0.1727  data_time: 0.0304  memory: 3093  
2024/07/05 21:02:55 - mmengine - INFO - Epoch(val) [70][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9967815493032123, 'occupancy': 0.7177520092433127, 'mIoU': 0.8572667792732624}, '1~2m': {'no_occupancy': 0.9942147015736286, 'occupancy': 0.4064229129208918, 'mIoU': 0.7003188072472601}, '2~3m': {'no_occupancy': 0.9972312596327682, 'occupancy': 0.04751565282325233, 'mIoU': 0.5223734562280102}, '3~4m': {'no_occupancy': 0.9990087989927066, 'occupancy': 0.0010432002692129729, 'mIoU': 0.5000259996309597}, 'patch_mean': {'no_occupancy': 0.9968090773755789, 'occupancy': 0.29318344381416744, 'mIoU': 0.6449962605948731}}  total_overall: {'no_occupancy': 0.9968116851277401, 'occupancy': 0.4897335715284775, 'mIoU': 0.7432726283281088}  data_time: 0.0344  time: 0.1734
2024/07/05 21:04:25 - mmengine - INFO - Epoch(train)  [71][100/147]  lr: 3.7059e-05  eta: 1:41:32  time: 0.7444  data_time: 0.0009  memory: 3093  grad_norm: 3.2323  loss: 0.3955  loss_bce: 0.0229  loss_dice: 0.3726
2024/07/05 21:04:59 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:04:59 - mmengine - INFO - Saving checkpoint at 71 epochs
2024/07/05 21:05:20 - mmengine - INFO - Epoch(val)  [71][100/147]    eta: 0:00:08  time: 0.1751  data_time: 0.0335  memory: 3093  
2024/07/05 21:05:27 - mmengine - INFO - Epoch(val) [71][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9968954140855446, 'occupancy': 0.723773066426066, 'mIoU': 0.8603342402558053}, '1~2m': {'no_occupancy': 0.9944176114673119, 'occupancy': 0.4123519762306257, 'mIoU': 0.7033847938489688}, '2~3m': {'no_occupancy': 0.9972256761938398, 'occupancy': 0.04818862995806696, 'mIoU': 0.5227071530759534}, '3~4m': {'no_occupancy': 0.9990079306116224, 'occupancy': 0.0012773431262973016, 'mIoU': 0.5001426368689599}, 'patch_mean': {'no_occupancy': 0.9968866580895797, 'occupancy': 0.296397753935264, 'mIoU': 0.6466422060124218}}  total_overall: {'no_occupancy': 0.9968890435500114, 'occupancy': 0.49401417219866545, 'mIoU': 0.7454516078743384}  data_time: 0.0331  time: 0.1720
2024/07/05 21:06:58 - mmengine - INFO - Epoch(train)  [72][100/147]  lr: 3.5799e-05  eta: 1:39:30  time: 0.7408  data_time: 0.0009  memory: 3093  grad_norm: 3.1475  loss: 0.3749  loss_bce: 0.0211  loss_dice: 0.3537
2024/07/05 21:07:33 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:07:33 - mmengine - INFO - Saving checkpoint at 72 epochs
2024/07/05 21:07:54 - mmengine - INFO - Epoch(val)  [72][100/147]    eta: 0:00:08  time: 0.1733  data_time: 0.0302  memory: 3093  
2024/07/05 21:08:01 - mmengine - INFO - Epoch(val) [72][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.996931331432145, 'occupancy': 0.7267672693661821, 'mIoU': 0.8618493003991635}, '1~2m': {'no_occupancy': 0.9943644478097597, 'occupancy': 0.41448621547337766, 'mIoU': 0.7044253316415687}, '2~3m': {'no_occupancy': 0.9972389657479205, 'occupancy': 0.0486064762606154, 'mIoU': 0.522922721004268}, '3~4m': {'no_occupancy': 0.9990091335579547, 'occupancy': 0.00037054191755442334, 'mIoU': 0.49968983773775455}, 'patch_mean': {'no_occupancy': 0.996885969636945, 'occupancy': 0.29755762575443234, 'mIoU': 0.6472217976956887}}  total_overall: {'no_occupancy': 0.996888377388568, 'occupancy': 0.49599297465178394, 'mIoU': 0.7464406760201759}  data_time: 0.0341  time: 0.1730
2024/07/05 21:09:31 - mmengine - INFO - Epoch(train)  [73][100/147]  lr: 3.4549e-05  eta: 1:37:27  time: 0.7428  data_time: 0.0009  memory: 3093  grad_norm: 2.9895  loss: 0.3894  loss_bce: 0.0218  loss_dice: 0.3676
2024/07/05 21:10:06 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:10:06 - mmengine - INFO - Saving checkpoint at 73 epochs
2024/07/05 21:10:28 - mmengine - INFO - Epoch(val)  [73][100/147]    eta: 0:00:08  time: 0.1726  data_time: 0.0308  memory: 3093  
2024/07/05 21:10:35 - mmengine - INFO - Epoch(val) [73][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9969611808113931, 'occupancy': 0.7278314717766704, 'mIoU': 0.8623963262940317}, '1~2m': {'no_occupancy': 0.9944468795071402, 'occupancy': 0.4197543892220603, 'mIoU': 0.7071006343646002}, '2~3m': {'no_occupancy': 0.9972135173279953, 'occupancy': 0.052228040482978744, 'mIoU': 0.5247207789054871}, '3~4m': {'no_occupancy': 0.9990066609818415, 'occupancy': 0.002079751771562749, 'mIoU': 0.5005432063767021}, 'patch_mean': {'no_occupancy': 0.9969070596570926, 'occupancy': 0.30047341331331806, 'mIoU': 0.6486902364852053}}  total_overall: {'no_occupancy': 0.9969094057640694, 'occupancy': 0.49781941593952184, 'mIoU': 0.7473644108517956}  data_time: 0.0349  time: 0.1726
2024/07/05 21:12:04 - mmengine - INFO - Epoch(train)  [74][100/147]  lr: 3.3310e-05  eta: 1:35:23  time: 0.7396  data_time: 0.0009  memory: 3093  grad_norm: 2.9577  loss: 0.3801  loss_bce: 0.0218  loss_dice: 0.3582
2024/07/05 21:12:38 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:12:38 - mmengine - INFO - Saving checkpoint at 74 epochs
2024/07/05 21:13:00 - mmengine - INFO - Epoch(val)  [74][100/147]    eta: 0:00:08  time: 0.1722  data_time: 0.0306  memory: 3093  
2024/07/05 21:13:07 - mmengine - INFO - Epoch(val) [74][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9969448622577112, 'occupancy': 0.7289321558461729, 'mIoU': 0.862938509051942}, '1~2m': {'no_occupancy': 0.9944332442689293, 'occupancy': 0.4248186915305085, 'mIoU': 0.7096259678997189}, '2~3m': {'no_occupancy': 0.9972340354832535, 'occupancy': 0.05494129325985007, 'mIoU': 0.5260876643715517}, '3~4m': {'no_occupancy': 0.9990083637923272, 'occupancy': 0.0021839012876617974, 'mIoU': 0.5005961325399945}, 'patch_mean': {'no_occupancy': 0.9969051264505553, 'occupancy': 0.3027190104810483, 'mIoU': 0.6498120684658018}}  total_overall: {'no_occupancy': 0.9969075691033525, 'occupancy': 0.5015143664454662, 'mIoU': 0.7492109677744094}  data_time: 0.0335  time: 0.1721
2024/07/05 21:14:36 - mmengine - INFO - Epoch(train)  [75][100/147]  lr: 3.2082e-05  eta: 1:33:20  time: 0.7400  data_time: 0.0009  memory: 3093  grad_norm: 3.2032  loss: 0.3835  loss_bce: 0.0221  loss_dice: 0.3614
2024/07/05 21:14:52 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:15:10 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:15:10 - mmengine - INFO - Saving checkpoint at 75 epochs
2024/07/05 21:15:32 - mmengine - INFO - Epoch(val)  [75][100/147]    eta: 0:00:08  time: 0.1728  data_time: 0.0324  memory: 3093  
2024/07/05 21:15:39 - mmengine - INFO - Epoch(val) [75][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9970889341564073, 'occupancy': 0.7371323005559306, 'mIoU': 0.8671106173561689}, '1~2m': {'no_occupancy': 0.9945154270988514, 'occupancy': 0.42479657466634907, 'mIoU': 0.7096560008826003}, '2~3m': {'no_occupancy': 0.997219852778568, 'occupancy': 0.05732080172120938, 'mIoU': 0.5272703272498886}, '3~4m': {'no_occupancy': 0.9990069275744055, 'occupancy': 0.00261575995372117, 'mIoU': 0.5008113437640633}, 'patch_mean': {'no_occupancy': 0.996957785402058, 'occupancy': 0.30546635922430254, 'mIoU': 0.6512120723131802}}  total_overall: {'no_occupancy': 0.9969599774353726, 'occupancy': 0.5036874677360456, 'mIoU': 0.7503237225857091}  data_time: 0.0372  time: 0.1749
2024/07/05 21:17:07 - mmengine - INFO - Epoch(train)  [76][100/147]  lr: 3.0866e-05  eta: 1:31:16  time: 0.7384  data_time: 0.0009  memory: 3093  grad_norm: 2.9436  loss: 0.3818  loss_bce: 0.0217  loss_dice: 0.3601
2024/07/05 21:17:42 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:17:42 - mmengine - INFO - Saving checkpoint at 76 epochs
2024/07/05 21:18:04 - mmengine - INFO - Epoch(val)  [76][100/147]    eta: 0:00:08  time: 0.1753  data_time: 0.0334  memory: 3093  
2024/07/05 21:18:11 - mmengine - INFO - Epoch(val) [76][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.997168498221105, 'occupancy': 0.7428369313647856, 'mIoU': 0.8700027147929452}, '1~2m': {'no_occupancy': 0.9945971506263575, 'occupancy': 0.42958810529319763, 'mIoU': 0.7120926279597776}, '2~3m': {'no_occupancy': 0.9972373712694628, 'occupancy': 0.05544644896094999, 'mIoU': 0.5263419101152064}, '3~4m': {'no_occupancy': 0.9990062257785844, 'occupancy': 0.003215191781166009, 'mIoU': 0.5011107087798752}, 'patch_mean': {'no_occupancy': 0.9970023114738774, 'occupancy': 0.3077716693500248, 'mIoU': 0.6523869904119511}}  total_overall: {'no_occupancy': 0.9970044064713444, 'occupancy': 0.5079314346076802, 'mIoU': 0.7524679205395123}  data_time: 0.0349  time: 0.1720
2024/07/05 21:19:39 - mmengine - INFO - Epoch(train)  [77][100/147]  lr: 2.9663e-05  eta: 1:29:12  time: 0.7376  data_time: 0.0009  memory: 3093  grad_norm: 3.0021  loss: 0.3657  loss_bce: 0.0207  loss_dice: 0.3450
2024/07/05 21:20:14 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:20:14 - mmengine - INFO - Saving checkpoint at 77 epochs
2024/07/05 21:20:35 - mmengine - INFO - Epoch(val)  [77][100/147]    eta: 0:00:08  time: 0.1719  data_time: 0.0313  memory: 3093  
2024/07/05 21:20:42 - mmengine - INFO - Epoch(val) [77][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9971967458463524, 'occupancy': 0.7447843618009597, 'mIoU': 0.8709905538236561}, '1~2m': {'no_occupancy': 0.9946403105274776, 'occupancy': 0.4323206712847678, 'mIoU': 0.7134804909061228}, '2~3m': {'no_occupancy': 0.9972377495099137, 'occupancy': 0.058078622511729606, 'mIoU': 0.5276581860108217}, '3~4m': {'no_occupancy': 0.9990082977400363, 'occupancy': 0.001445706168626495, 'mIoU': 0.5002270019543315}, 'patch_mean': {'no_occupancy': 0.9970207759059451, 'occupancy': 0.30915734044152093, 'mIoU': 0.653089058173733}}  total_overall: {'no_occupancy': 0.9970228345668798, 'occupancy': 0.5098912035668949, 'mIoU': 0.7534570190668873}  data_time: 0.0357  time: 0.1745
2024/07/05 21:22:11 - mmengine - INFO - Epoch(train)  [78][100/147]  lr: 2.8474e-05  eta: 1:27:09  time: 0.7331  data_time: 0.0010  memory: 3093  grad_norm: 3.2032  loss: 0.3743  loss_bce: 0.0219  loss_dice: 0.3524
2024/07/05 21:22:46 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:22:46 - mmengine - INFO - Saving checkpoint at 78 epochs
2024/07/05 21:23:07 - mmengine - INFO - Epoch(val)  [78][100/147]    eta: 0:00:08  time: 0.1725  data_time: 0.0310  memory: 3093  
2024/07/05 21:23:14 - mmengine - INFO - Epoch(val) [78][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9970849946187292, 'occupancy': 0.7389360286009117, 'mIoU': 0.8680105116098205}, '1~2m': {'no_occupancy': 0.9945644483609896, 'occupancy': 0.43348129924613393, 'mIoU': 0.7140228738035618}, '2~3m': {'no_occupancy': 0.9972325700185733, 'occupancy': 0.06218806939713223, 'mIoU': 0.5297103197078528}, '3~4m': {'no_occupancy': 0.9990081307550803, 'occupancy': 0.0014790288830810862, 'mIoU': 0.5002435798190807}, 'patch_mean': {'no_occupancy': 0.9969725359383432, 'occupancy': 0.3090211065318147, 'mIoU': 0.652996821235079}}  total_overall: {'no_occupancy': 0.9969747937531229, 'occupancy': 0.5090991237352419, 'mIoU': 0.7530369587441824}  data_time: 0.0331  time: 0.1710
2024/07/05 21:24:44 - mmengine - INFO - Epoch(train)  [79][100/147]  lr: 2.7300e-05  eta: 1:25:06  time: 0.7443  data_time: 0.0009  memory: 3093  grad_norm: 3.2276  loss: 0.3662  loss_bce: 0.0213  loss_dice: 0.3449
2024/07/05 21:25:19 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:25:19 - mmengine - INFO - Saving checkpoint at 79 epochs
2024/07/05 21:25:40 - mmengine - INFO - Epoch(val)  [79][100/147]    eta: 0:00:08  time: 0.1690  data_time: 0.0292  memory: 3093  
2024/07/05 21:25:47 - mmengine - INFO - Epoch(val) [79][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9971358545317754, 'occupancy': 0.7411502430709657, 'mIoU': 0.8691430488013705}, '1~2m': {'no_occupancy': 0.9946760793944179, 'occupancy': 0.43581193612770913, 'mIoU': 0.7152440077610636}, '2~3m': {'no_occupancy': 0.9972372424803219, 'occupancy': 0.05877601174154939, 'mIoU': 0.5280066271109356}, '3~4m': {'no_occupancy': 0.999008062152679, 'occupancy': 0.003321451037953449, 'mIoU': 0.5011647565953162}, 'patch_mean': {'no_occupancy': 0.9970143096397985, 'occupancy': 0.30976491049454447, 'mIoU': 0.6533896100671714}}  total_overall: {'no_occupancy': 0.9970164520117915, 'occupancy': 0.5104583087098388, 'mIoU': 0.7537373803608152}  data_time: 0.0334  time: 0.1708
2024/07/05 21:27:17 - mmengine - INFO - Epoch(train)  [80][100/147]  lr: 2.6142e-05  eta: 1:23:02  time: 0.7365  data_time: 0.0009  memory: 3093  grad_norm: 2.8681  loss: 0.3807  loss_bce: 0.0218  loss_dice: 0.3589
2024/07/05 21:27:51 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:27:51 - mmengine - INFO - Saving checkpoint at 80 epochs
2024/07/05 21:28:12 - mmengine - INFO - Epoch(val)  [80][100/147]    eta: 0:00:08  time: 0.1733  data_time: 0.0323  memory: 3093  
2024/07/05 21:28:19 - mmengine - INFO - Epoch(val) [80][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9971870807322435, 'occupancy': 0.7458686499478758, 'mIoU': 0.8715278653400597}, '1~2m': {'no_occupancy': 0.9947092949770995, 'occupancy': 0.44274708062644913, 'mIoU': 0.7187281878017743}, '2~3m': {'no_occupancy': 0.9972533146311324, 'occupancy': 0.06587615283267456, 'mIoU': 0.5315647337319035}, '3~4m': {'no_occupancy': 0.9990086637762369, 'occupancy': 0.0027208370772835297, 'mIoU': 0.5008647504267602}, 'patch_mean': {'no_occupancy': 0.9970395885291781, 'occupancy': 0.3143031801210708, 'mIoU': 0.6556713843251244}}  total_overall: {'no_occupancy': 0.9970417202531464, 'occupancy': 0.5158955666172202, 'mIoU': 0.7564686434351833}  data_time: 0.0346  time: 0.1727
2024/07/05 21:29:49 - mmengine - INFO - Epoch(train)  [81][100/147]  lr: 2.5000e-05  eta: 1:20:59  time: 0.7471  data_time: 0.0009  memory: 3093  grad_norm: 2.9630  loss: 0.3741  loss_bce: 0.0221  loss_dice: 0.3520
2024/07/05 21:30:24 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:30:24 - mmengine - INFO - Saving checkpoint at 81 epochs
2024/07/05 21:30:45 - mmengine - INFO - Epoch(val)  [81][100/147]    eta: 0:00:08  time: 0.1728  data_time: 0.0300  memory: 3093  
2024/07/05 21:30:52 - mmengine - INFO - Epoch(val) [81][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9972544989078612, 'occupancy': 0.7500713485352004, 'mIoU': 0.8736629237215308}, '1~2m': {'no_occupancy': 0.994743378756267, 'occupancy': 0.4435433276985806, 'mIoU': 0.7191433532274238}, '2~3m': {'no_occupancy': 0.997256486592923, 'occupancy': 0.06603226576622669, 'mIoU': 0.5316443761795748}, '3~4m': {'no_occupancy': 0.9990072604840304, 'occupancy': 0.0036193939190830872, 'mIoU': 0.5013133272015567}, 'patch_mean': {'no_occupancy': 0.9970654061852704, 'occupancy': 0.31581658397977275, 'mIoU': 0.6564409950825216}}  total_overall: {'no_occupancy': 0.9970674373689191, 'occupancy': 0.5175125480805248, 'mIoU': 0.757289992724722}  data_time: 0.0344  time: 0.1725
2024/07/05 21:32:16 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:32:21 - mmengine - INFO - Epoch(train)  [82][100/147]  lr: 2.3875e-05  eta: 1:18:56  time: 0.7401  data_time: 0.0009  memory: 3093  grad_norm: 3.0001  loss: 0.3652  loss_bce: 0.0207  loss_dice: 0.3445
2024/07/05 21:32:56 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:32:56 - mmengine - INFO - Saving checkpoint at 82 epochs
2024/07/05 21:33:18 - mmengine - INFO - Epoch(val)  [82][100/147]    eta: 0:00:08  time: 0.1770  data_time: 0.0337  memory: 3093  
2024/07/05 21:33:25 - mmengine - INFO - Epoch(val) [82][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9973353417691855, 'occupancy': 0.7556364514841826, 'mIoU': 0.876485896626684}, '1~2m': {'no_occupancy': 0.9948270932923634, 'occupancy': 0.44820388047836907, 'mIoU': 0.7215154868853663}, '2~3m': {'no_occupancy': 0.9972539498120546, 'occupancy': 0.0658160830739167, 'mIoU': 0.5315350164429856}, '3~4m': {'no_occupancy': 0.9990080620533153, 'occupancy': 0.003421756669490175, 'mIoU': 0.5012149093614027}, 'patch_mean': {'no_occupancy': 0.9971061117317297, 'occupancy': 0.3182695429264896, 'mIoU': 0.6576878273291097}}  total_overall: {'no_occupancy': 0.997108024579051, 'occupancy': 0.5212144958573336, 'mIoU': 0.7591612602181923}  data_time: 0.0359  time: 0.1744
2024/07/05 21:34:54 - mmengine - INFO - Epoch(train)  [83][100/147]  lr: 2.2768e-05  eta: 1:16:52  time: 0.7381  data_time: 0.0009  memory: 3093  grad_norm: 2.9964  loss: 0.3637  loss_bce: 0.0212  loss_dice: 0.3425
2024/07/05 21:35:28 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:35:28 - mmengine - INFO - Saving checkpoint at 83 epochs
2024/07/05 21:35:50 - mmengine - INFO - Epoch(val)  [83][100/147]    eta: 0:00:08  time: 0.1742  data_time: 0.0335  memory: 3093  
2024/07/05 21:35:57 - mmengine - INFO - Epoch(val) [83][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9973599676859836, 'occupancy': 0.7577434918369444, 'mIoU': 0.877551729761464}, '1~2m': {'no_occupancy': 0.9947926844946428, 'occupancy': 0.44853195432401544, 'mIoU': 0.7216623194093291}, '2~3m': {'no_occupancy': 0.9972582866320309, 'occupancy': 0.06646577211735272, 'mIoU': 0.5318620293746918}, '3~4m': {'no_occupancy': 0.9990076623621545, 'occupancy': 0.002416817018419838, 'mIoU': 0.5007122396902872}, 'patch_mean': {'no_occupancy': 0.997104650293703, 'occupancy': 0.3187895088241831, 'mIoU': 0.657947079558943}}  total_overall: {'no_occupancy': 0.9971065625752245, 'occupancy': 0.522114030312214, 'mIoU': 0.7596102964437192}  data_time: 0.0361  time: 0.1743
2024/07/05 21:37:26 - mmengine - INFO - Epoch(train)  [84][100/147]  lr: 2.1680e-05  eta: 1:14:49  time: 0.7365  data_time: 0.0009  memory: 3093  grad_norm: 2.9881  loss: 0.3574  loss_bce: 0.0203  loss_dice: 0.3370
2024/07/05 21:38:00 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:38:00 - mmengine - INFO - Saving checkpoint at 84 epochs
2024/07/05 21:38:22 - mmengine - INFO - Epoch(val)  [84][100/147]    eta: 0:00:08  time: 0.1750  data_time: 0.0313  memory: 3093  
2024/07/05 21:38:29 - mmengine - INFO - Epoch(val) [84][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9973823593434193, 'occupancy': 0.7597373853175572, 'mIoU': 0.8785598723304883}, '1~2m': {'no_occupancy': 0.9949005748124914, 'occupancy': 0.4547543434593324, 'mIoU': 0.7248274591359118}, '2~3m': {'no_occupancy': 0.9972658057361805, 'occupancy': 0.06995580901314369, 'mIoU': 0.5336108073746622}, '3~4m': {'no_occupancy': 0.9990075607660237, 'occupancy': 0.00385424259943527, 'mIoU': 0.5014309016827295}, 'patch_mean': {'no_occupancy': 0.9971390751645287, 'occupancy': 0.3220754450973672, 'mIoU': 0.659607260130948}}  total_overall: {'no_occupancy': 0.9971409435300657, 'occupancy': 0.526062763573973, 'mIoU': 0.7616018535520193}  data_time: 0.0328  time: 0.1713
2024/07/05 21:39:57 - mmengine - INFO - Epoch(train)  [85][100/147]  lr: 2.0611e-05  eta: 1:12:45  time: 0.7432  data_time: 0.0009  memory: 3093  grad_norm: 2.9161  loss: 0.3686  loss_bce: 0.0210  loss_dice: 0.3476
2024/07/05 21:40:32 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:40:32 - mmengine - INFO - Saving checkpoint at 85 epochs
2024/07/05 21:40:53 - mmengine - INFO - Epoch(val)  [85][100/147]    eta: 0:00:08  time: 0.1753  data_time: 0.0313  memory: 3093  
2024/07/05 21:41:00 - mmengine - INFO - Epoch(val) [85][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9973721580202849, 'occupancy': 0.7592076006907195, 'mIoU': 0.8782898793555022}, '1~2m': {'no_occupancy': 0.9948910983389945, 'occupancy': 0.4557241998945235, 'mIoU': 0.7253076491167589}, '2~3m': {'no_occupancy': 0.997273954649693, 'occupancy': 0.07015025688345124, 'mIoU': 0.5337121057665721}, '3~4m': {'no_occupancy': 0.9990083629976596, 'occupancy': 0.0029878555422202455, 'mIoU': 0.5009981092699399}, 'patch_mean': {'no_occupancy': 0.997136393501658, 'occupancy': 0.32201747825272864, 'mIoU': 0.6595769358771932}}  total_overall: {'no_occupancy': 0.997138297685292, 'occupancy': 0.5264556107130911, 'mIoU': 0.7617969541991916}  data_time: 0.0347  time: 0.1735
2024/07/05 21:42:31 - mmengine - INFO - Epoch(train)  [86][100/147]  lr: 1.9562e-05  eta: 1:10:42  time: 0.7494  data_time: 0.0009  memory: 3093  grad_norm: 3.3655  loss: 0.3617  loss_bce: 0.0209  loss_dice: 0.3408
2024/07/05 21:43:05 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:43:05 - mmengine - INFO - Saving checkpoint at 86 epochs
2024/07/05 21:43:27 - mmengine - INFO - Epoch(val)  [86][100/147]    eta: 0:00:09  time: 0.1705  data_time: 0.0286  memory: 3093  
2024/07/05 21:43:34 - mmengine - INFO - Epoch(val) [86][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9973885274107784, 'occupancy': 0.7608216865426569, 'mIoU': 0.8791051069767177}, '1~2m': {'no_occupancy': 0.9949041021706531, 'occupancy': 0.4580128032227849, 'mIoU': 0.726458452696719}, '2~3m': {'no_occupancy': 0.9972631529167723, 'occupancy': 0.0715580531133871, 'mIoU': 0.5344106030150797}, '3~4m': {'no_occupancy': 0.9990079272990561, 'occupancy': 0.004623231739490942, 'mIoU': 0.5018155795192735}, 'patch_mean': {'no_occupancy': 0.9971409274493149, 'occupancy': 0.32375394365457993, 'mIoU': 0.6604474355519474}}  total_overall: {'no_occupancy': 0.9971428127024748, 'occupancy': 0.5279611853647863, 'mIoU': 0.7625519990336306}  data_time: 0.0398  time: 0.1770
2024/07/05 21:45:02 - mmengine - INFO - Epoch(train)  [87][100/147]  lr: 1.8534e-05  eta: 1:08:38  time: 0.7345  data_time: 0.0009  memory: 3093  grad_norm: 2.7009  loss: 0.3301  loss_bce: 0.0191  loss_dice: 0.3110
2024/07/05 21:45:37 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:45:37 - mmengine - INFO - Saving checkpoint at 87 epochs
2024/07/05 21:45:58 - mmengine - INFO - Epoch(val)  [87][100/147]    eta: 0:00:08  time: 0.1745  data_time: 0.0293  memory: 3093  
2024/07/05 21:46:05 - mmengine - INFO - Epoch(val) [87][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9973406871229777, 'occupancy': 0.7580181972129366, 'mIoU': 0.8776794421679572}, '1~2m': {'no_occupancy': 0.9948545082158174, 'occupancy': 0.45880868388576507, 'mIoU': 0.7268315960507913}, '2~3m': {'no_occupancy': 0.997249362980053, 'occupancy': 0.07465872703780685, 'mIoU': 0.53595404500893}, '3~4m': {'no_occupancy': 0.9990060903584032, 'occupancy': 0.005080511059303268, 'mIoU': 0.5020433007088532}, 'patch_mean': {'no_occupancy': 0.9971126621693127, 'occupancy': 0.32414152979895294, 'mIoU': 0.6606270959841328}}  total_overall: {'no_occupancy': 0.9971146395112455, 'occupancy': 0.5272720661982516, 'mIoU': 0.7621933528547485}  data_time: 0.0317  time: 0.1713
2024/07/05 21:47:35 - mmengine - INFO - Epoch(train)  [88][100/147]  lr: 1.7528e-05  eta: 1:06:35  time: 0.7354  data_time: 0.0009  memory: 3093  grad_norm: 2.8340  loss: 0.3659  loss_bce: 0.0209  loss_dice: 0.3450
2024/07/05 21:48:10 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:48:10 - mmengine - INFO - Saving checkpoint at 88 epochs
2024/07/05 21:48:31 - mmengine - INFO - Epoch(val)  [88][100/147]    eta: 0:00:08  time: 0.1716  data_time: 0.0316  memory: 3093  
2024/07/05 21:48:38 - mmengine - INFO - Epoch(val) [88][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9974727130521612, 'occupancy': 0.7668816611575543, 'mIoU': 0.8821771871048578}, '1~2m': {'no_occupancy': 0.9949726803188437, 'occupancy': 0.4650157828902407, 'mIoU': 0.7299942316045422}, '2~3m': {'no_occupancy': 0.9972672208171747, 'occupancy': 0.07624832070806849, 'mIoU': 0.5367577707626215}, '3~4m': {'no_occupancy': 0.9990077937371625, 'occupancy': 0.00462261227839515, 'mIoU': 0.5018152030077788}, 'patch_mean': {'no_occupancy': 0.9971801019813354, 'occupancy': 0.32819209425856466, 'mIoU': 0.66268609811995}}  total_overall: {'no_occupancy': 0.9971818985677164, 'occupancy': 0.5330399272457439, 'mIoU': 0.7651109129067302}  data_time: 0.0348  time: 0.1736
2024/07/05 21:49:40 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:50:07 - mmengine - INFO - Epoch(train)  [89][100/147]  lr: 1.6543e-05  eta: 1:04:31  time: 0.7431  data_time: 0.0009  memory: 3093  grad_norm: 3.1785  loss: 0.3725  loss_bce: 0.0212  loss_dice: 0.3513
2024/07/05 21:50:42 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:50:42 - mmengine - INFO - Saving checkpoint at 89 epochs
2024/07/05 21:51:03 - mmengine - INFO - Epoch(val)  [89][100/147]    eta: 0:00:08  time: 0.1722  data_time: 0.0309  memory: 3093  
2024/07/05 21:51:10 - mmengine - INFO - Epoch(val) [89][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.997492513084117, 'occupancy': 0.7682564772955781, 'mIoU': 0.8828744951898475}, '1~2m': {'no_occupancy': 0.9949704874651538, 'occupancy': 0.46410073394888807, 'mIoU': 0.729535610707021}, '2~3m': {'no_occupancy': 0.9972783717521357, 'occupancy': 0.07313300425377038, 'mIoU': 0.5352056880029531}, '3~4m': {'no_occupancy': 0.9990079282265767, 'occupancy': 0.0036886448421846836, 'mIoU': 0.5013482865343807}, 'patch_mean': {'no_occupancy': 0.9971873251319958, 'occupancy': 0.3272947150851053, 'mIoU': 0.6622410201085506}}  total_overall: {'no_occupancy': 0.9971890987348353, 'occupancy': 0.5331489392996608, 'mIoU': 0.765169019017248}  data_time: 0.0332  time: 0.1725
2024/07/05 21:52:39 - mmengine - INFO - Epoch(train)  [90][100/147]  lr: 1.5582e-05  eta: 1:02:27  time: 0.7498  data_time: 0.0009  memory: 3093  grad_norm: 2.7475  loss: 0.3497  loss_bce: 0.0204  loss_dice: 0.3294
2024/07/05 21:53:14 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:53:14 - mmengine - INFO - Saving checkpoint at 90 epochs
2024/07/05 21:53:35 - mmengine - INFO - Epoch(val)  [90][100/147]    eta: 0:00:08  time: 0.1789  data_time: 0.0371  memory: 3093  
2024/07/05 21:53:42 - mmengine - INFO - Epoch(val) [90][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9975486261752513, 'occupancy': 0.772322824550651, 'mIoU': 0.8849357253629512}, '1~2m': {'no_occupancy': 0.9950607542556237, 'occupancy': 0.4672758476947964, 'mIoU': 0.73116830097521}, '2~3m': {'no_occupancy': 0.9972906442849558, 'occupancy': 0.07160513606921333, 'mIoU': 0.5344478901770846}, '3~4m': {'no_occupancy': 0.9990080943839035, 'occupancy': 0.004490578999840819, 'mIoU': 0.5017493366918722}, 'patch_mean': {'no_occupancy': 0.9972270297749335, 'occupancy': 0.32892359682862543, 'mIoU': 0.6630753133017795}}  total_overall: {'no_occupancy': 0.9972287029453769, 'occupancy': 0.5361881846569567, 'mIoU': 0.7667084438011669}  data_time: 0.0354  time: 0.1737
2024/07/05 21:55:11 - mmengine - INFO - Epoch(train)  [91][100/147]  lr: 1.4645e-05  eta: 1:00:24  time: 0.7366  data_time: 0.0009  memory: 3093  grad_norm: 2.5830  loss: 0.3561  loss_bce: 0.0210  loss_dice: 0.3351
2024/07/05 21:55:45 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:55:45 - mmengine - INFO - Saving checkpoint at 91 epochs
2024/07/05 21:56:07 - mmengine - INFO - Epoch(val)  [91][100/147]    eta: 0:00:08  time: 0.1749  data_time: 0.0319  memory: 3093  
2024/07/05 21:56:14 - mmengine - INFO - Epoch(val) [91][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9975128521186497, 'occupancy': 0.7703613034591561, 'mIoU': 0.8839370777889028}, '1~2m': {'no_occupancy': 0.995045432740629, 'occupancy': 0.4696134927284468, 'mIoU': 0.7323294627345379}, '2~3m': {'no_occupancy': 0.9972907822271838, 'occupancy': 0.07500741179958494, 'mIoU': 0.5361490970133844}, '3~4m': {'no_occupancy': 0.9990088624970658, 'occupancy': 0.004360477304554183, 'mIoU': 0.50168466990081}, 'patch_mean': {'no_occupancy': 0.9972144823958821, 'occupancy': 0.3298356713229355, 'mIoU': 0.6635250768594088}}  total_overall: {'no_occupancy': 0.9972162331137562, 'occupancy': 0.5368644232361778, 'mIoU': 0.7670403281749669}  data_time: 0.0331  time: 0.1712
2024/07/05 21:57:43 - mmengine - INFO - Epoch(train)  [92][100/147]  lr: 1.3731e-05  eta: 0:58:20  time: 0.7447  data_time: 0.0009  memory: 3093  grad_norm: 2.8925  loss: 0.3653  loss_bce: 0.0209  loss_dice: 0.3444
2024/07/05 21:58:18 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 21:58:18 - mmengine - INFO - Saving checkpoint at 92 epochs
2024/07/05 21:58:39 - mmengine - INFO - Epoch(val)  [92][100/147]    eta: 0:00:08  time: 0.1712  data_time: 0.0304  memory: 3093  
2024/07/05 21:58:46 - mmengine - INFO - Epoch(val) [92][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9975681428587317, 'occupancy': 0.7742221475487342, 'mIoU': 0.8858951452037329}, '1~2m': {'no_occupancy': 0.9950670685774599, 'occupancy': 0.4717633073831097, 'mIoU': 0.7334151879802848}, '2~3m': {'no_occupancy': 0.9972840821060855, 'occupancy': 0.07721985815602836, 'mIoU': 0.537251970131057}, '3~4m': {'no_occupancy': 0.9990076265528453, 'occupancy': 0.004855141008362562, 'mIoU': 0.5019313837806039}, 'patch_mean': {'no_occupancy': 0.9972317300237806, 'occupancy': 0.3320151135240587, 'mIoU': 0.6646234217739196}}  total_overall: {'no_occupancy': 0.9972334084352346, 'occupancy': 0.5388344754057455, 'mIoU': 0.76803394192049}  data_time: 0.0352  time: 0.1732
2024/07/05 22:00:15 - mmengine - INFO - Epoch(train)  [93][100/147]  lr: 1.2843e-05  eta: 0:56:17  time: 0.7437  data_time: 0.0009  memory: 3093  grad_norm: 2.6824  loss: 0.3326  loss_bce: 0.0188  loss_dice: 0.3139
2024/07/05 22:00:49 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:00:49 - mmengine - INFO - Saving checkpoint at 93 epochs
2024/07/05 22:01:11 - mmengine - INFO - Epoch(val)  [93][100/147]    eta: 0:00:08  time: 0.1718  data_time: 0.0307  memory: 3093  
2024/07/05 22:01:18 - mmengine - INFO - Epoch(val) [93][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9975925179275219, 'occupancy': 0.7759512203146333, 'mIoU': 0.8867718691210775}, '1~2m': {'no_occupancy': 0.9951487072105948, 'occupancy': 0.47592200772060883, 'mIoU': 0.7355353574656018}, '2~3m': {'no_occupancy': 0.9972951291618408, 'occupancy': 0.07450577076905497, 'mIoU': 0.535900449965448}, '3~4m': {'no_occupancy': 0.999008494672248, 'occupancy': 0.004892720402811639, 'mIoU': 0.5019506075375298}, 'patch_mean': {'no_occupancy': 0.9972612122430513, 'occupancy': 0.3328179298017772, 'mIoU': 0.6650395710224143}}  total_overall: {'no_occupancy': 0.9972628451058483, 'occupancy': 0.5412828730382842, 'mIoU': 0.7692728590720662}  data_time: 0.0331  time: 0.1706
2024/07/05 22:02:47 - mmengine - INFO - Epoch(train)  [94][100/147]  lr: 1.1980e-05  eta: 0:54:13  time: 0.7455  data_time: 0.0009  memory: 3093  grad_norm: 2.8742  loss: 0.3544  loss_bce: 0.0207  loss_dice: 0.3337
2024/07/05 22:03:22 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:03:22 - mmengine - INFO - Saving checkpoint at 94 epochs
2024/07/05 22:03:44 - mmengine - INFO - Epoch(val)  [94][100/147]    eta: 0:00:08  time: 0.1776  data_time: 0.0322  memory: 3093  
2024/07/05 22:03:51 - mmengine - INFO - Epoch(val) [94][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9976291399234367, 'occupancy': 0.7787998800021363, 'mIoU': 0.8882145099627865}, '1~2m': {'no_occupancy': 0.9951647271334296, 'occupancy': 0.4779089742458089, 'mIoU': 0.7365368506896193}, '2~3m': {'no_occupancy': 0.9972899719946404, 'occupancy': 0.07599808695256098, 'mIoU': 0.5366440294736007}, '3~4m': {'no_occupancy': 0.9990087283394258, 'occupancy': 0.004960575158579198, 'mIoU': 0.5019846517490025}, 'patch_mean': {'no_occupancy': 0.9972731418477332, 'occupancy': 0.33441687908977136, 'mIoU': 0.6658450104687523}}  total_overall: {'no_occupancy': 0.9972747298618971, 'occupancy': 0.5430365977876785, 'mIoU': 0.7701556638247878}  data_time: 0.0349  time: 0.1744
2024/07/05 22:05:20 - mmengine - INFO - Epoch(train)  [95][100/147]  lr: 1.1143e-05  eta: 0:52:09  time: 0.7468  data_time: 0.0009  memory: 3093  grad_norm: 2.7333  loss: 0.3371  loss_bce: 0.0196  loss_dice: 0.3175
2024/07/05 22:05:54 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:05:54 - mmengine - INFO - Saving checkpoint at 95 epochs
2024/07/05 22:06:16 - mmengine - INFO - Epoch(val)  [95][100/147]    eta: 0:00:08  time: 0.1728  data_time: 0.0291  memory: 3093  
2024/07/05 22:06:23 - mmengine - INFO - Epoch(val) [95][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9976616948658663, 'occupancy': 0.7812735223625821, 'mIoU': 0.8894676086142241}, '1~2m': {'no_occupancy': 0.9951994417441138, 'occupancy': 0.4797745004104659, 'mIoU': 0.7374869710772898}, '2~3m': {'no_occupancy': 0.9972890493290154, 'occupancy': 0.08224413188005969, 'mIoU': 0.5397665906045376}, '3~4m': {'no_occupancy': 0.9990083610109849, 'occupancy': 0.004992084697920244, 'mIoU': 0.5020002228544526}, 'patch_mean': {'no_occupancy': 0.9972896367374952, 'occupancy': 0.33707105983775704, 'mIoU': 0.6671803482876261}}  total_overall: {'no_occupancy': 0.9972911724320763, 'occupancy': 0.5450403446219977, 'mIoU': 0.7711657585270371}  data_time: 0.0340  time: 0.1734
2024/07/05 22:07:04 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:07:52 - mmengine - INFO - Epoch(train)  [96][100/147]  lr: 1.0332e-05  eta: 0:50:06  time: 0.7435  data_time: 0.0009  memory: 3093  grad_norm: 2.5876  loss: 0.3262  loss_bce: 0.0187  loss_dice: 0.3076
2024/07/05 22:08:26 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:08:26 - mmengine - INFO - Saving checkpoint at 96 epochs
2024/07/05 22:08:48 - mmengine - INFO - Epoch(val)  [96][100/147]    eta: 0:00:08  time: 0.1741  data_time: 0.0308  memory: 3093  
2024/07/05 22:08:55 - mmengine - INFO - Epoch(val) [96][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9976952339920403, 'occupancy': 0.7839528090632354, 'mIoU': 0.8908240215276378}, '1~2m': {'no_occupancy': 0.9952473423953461, 'occupancy': 0.48307868525351416, 'mIoU': 0.7391630138244301}, '2~3m': {'no_occupancy': 0.9973044562689286, 'occupancy': 0.08148493780514175, 'mIoU': 0.5393946970370351}, '3~4m': {'no_occupancy': 0.99900872853802, 'occupancy': 0.00476042810328788, 'mIoU': 0.5018845783206539}, 'patch_mean': {'no_occupancy': 0.9973139402985838, 'occupancy': 0.33831921505629475, 'mIoU': 0.6678165776774393}}  total_overall: {'no_occupancy': 0.9973154376720567, 'occupancy': 0.5476690486933188, 'mIoU': 0.7724922431826877}  data_time: 0.0328  time: 0.1710
2024/07/05 22:10:24 - mmengine - INFO - Epoch(train)  [97][100/147]  lr: 9.5492e-06  eta: 0:48:02  time: 0.7420  data_time: 0.0009  memory: 3093  grad_norm: 3.0746  loss: 0.3526  loss_bce: 0.0205  loss_dice: 0.3321
2024/07/05 22:10:58 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:10:58 - mmengine - INFO - Saving checkpoint at 97 epochs
2024/07/05 22:11:19 - mmengine - INFO - Epoch(val)  [97][100/147]    eta: 0:00:08  time: 0.1725  data_time: 0.0291  memory: 3093  
2024/07/05 22:11:26 - mmengine - INFO - Epoch(val) [97][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9977116485798473, 'occupancy': 0.7852297298258115, 'mIoU': 0.8914706892028295}, '1~2m': {'no_occupancy': 0.995258991346918, 'occupancy': 0.48360982113910234, 'mIoU': 0.7394344062430102}, '2~3m': {'no_occupancy': 0.9973014771505273, 'occupancy': 0.08217466375863323, 'mIoU': 0.5397380704545802}, '3~4m': {'no_occupancy': 0.9990086946510397, 'occupancy': 0.005260468583777988, 'mIoU': 0.5021345816174089}, 'patch_mean': {'no_occupancy': 0.9973202029320831, 'occupancy': 0.3390686708268313, 'mIoU': 0.6681944368794572}}  total_overall: {'no_occupancy': 0.9973216725029956, 'occupancy': 0.5483386864488914, 'mIoU': 0.7728301794759436}  data_time: 0.0362  time: 0.1751
2024/07/05 22:12:55 - mmengine - INFO - Epoch(train)  [98][100/147]  lr: 8.7937e-06  eta: 0:45:58  time: 0.7421  data_time: 0.0009  memory: 3093  grad_norm: 2.9294  loss: 0.3553  loss_bce: 0.0202  loss_dice: 0.3350
2024/07/05 22:13:30 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:13:30 - mmengine - INFO - Saving checkpoint at 98 epochs
2024/07/05 22:13:52 - mmengine - INFO - Epoch(val)  [98][100/147]    eta: 0:00:08  time: 0.1742  data_time: 0.0307  memory: 3093  
2024/07/05 22:13:58 - mmengine - INFO - Epoch(val) [98][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9977076590070298, 'occupancy': 0.7850079832952753, 'mIoU': 0.8913578211511526}, '1~2m': {'no_occupancy': 0.9952545538525275, 'occupancy': 0.48439638067091684, 'mIoU': 0.7398254672617222}, '2~3m': {'no_occupancy': 0.9972908803615158, 'occupancy': 0.08296967915526714, 'mIoU': 0.5401302797583915}, '3~4m': {'no_occupancy': 0.9990083605143153, 'occupancy': 0.005491883565370995, 'mIoU': 0.5022501220398431}, 'patch_mean': {'no_occupancy': 0.9973153634338471, 'occupancy': 0.33946648167170757, 'mIoU': 0.6683909225527773}}  total_overall: {'no_occupancy': 0.997316840029731, 'occupancy': 0.5483761210261576, 'mIoU': 0.7728464805279442}  data_time: 0.0336  time: 0.1720
2024/07/05 22:15:28 - mmengine - INFO - Epoch(train)  [99][100/147]  lr: 8.0665e-06  eta: 0:43:55  time: 0.7396  data_time: 0.0009  memory: 3093  grad_norm: 2.6719  loss: 0.3487  loss_bce: 0.0206  loss_dice: 0.3281
2024/07/05 22:16:03 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:16:03 - mmengine - INFO - Saving checkpoint at 99 epochs
2024/07/05 22:16:24 - mmengine - INFO - Epoch(val)  [99][100/147]    eta: 0:00:08  time: 0.1726  data_time: 0.0311  memory: 3093  
2024/07/05 22:16:31 - mmengine - INFO - Epoch(val) [99][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9977436809677082, 'occupancy': 0.7875984637242248, 'mIoU': 0.8926710723459665}, '1~2m': {'no_occupancy': 0.995289769024761, 'occupancy': 0.4874180651536864, 'mIoU': 0.7413539170892237}, '2~3m': {'no_occupancy': 0.9973010372486919, 'occupancy': 0.08666561179050158, 'mIoU': 0.5419833245195967}, '3~4m': {'no_occupancy': 0.9990083606467604, 'occupancy': 0.005358652968610016, 'mIoU': 0.5021835068076852}, 'patch_mean': {'no_occupancy': 0.9973357119719803, 'occupancy': 0.3417601984092557, 'mIoU': 0.669547955190618}}  total_overall: {'no_occupancy': 0.9973371511218367, 'occupancy': 0.5507911719616022, 'mIoU': 0.7740641615417194}  data_time: 0.0338  time: 0.1715
2024/07/05 22:18:01 - mmengine - INFO - Epoch(train) [100][100/147]  lr: 7.3680e-06  eta: 0:41:51  time: 0.7397  data_time: 0.0009  memory: 3093  grad_norm: 2.7890  loss: 0.3231  loss_bce: 0.0187  loss_dice: 0.3044
2024/07/05 22:18:35 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:18:35 - mmengine - INFO - Saving checkpoint at 100 epochs
2024/07/05 22:18:56 - mmengine - INFO - Epoch(val) [100][100/147]    eta: 0:00:08  time: 0.1747  data_time: 0.0332  memory: 3093  
2024/07/05 22:19:03 - mmengine - INFO - Epoch(val) [100][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.997739508161111, 'occupancy': 0.7876444579050189, 'mIoU': 0.892691983033065}, '1~2m': {'no_occupancy': 0.9952891946062437, 'occupancy': 0.4892761412381177, 'mIoU': 0.7422826679221807}, '2~3m': {'no_occupancy': 0.9973065172963038, 'occupancy': 0.09035023405335288, 'mIoU': 0.5438283756748283}, '3~4m': {'no_occupancy': 0.9990090950391926, 'occupancy': 0.005562583250257606, 'mIoU': 0.5022858391447251}, 'patch_mean': {'no_occupancy': 0.9973360787757128, 'occupancy': 0.34320835411168676, 'mIoU': 0.6702722164436998}}  total_overall: {'no_occupancy': 0.9973375436412711, 'occupancy': 0.5520216389275231, 'mIoU': 0.7746795912843971}  data_time: 0.0346  time: 0.1733
2024/07/05 22:20:33 - mmengine - INFO - Epoch(train) [101][100/147]  lr: 6.6987e-06  eta: 0:39:48  time: 0.7477  data_time: 0.0009  memory: 3093  grad_norm: 2.7789  loss: 0.3497  loss_bce: 0.0204  loss_dice: 0.3293
2024/07/05 22:21:07 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:21:07 - mmengine - INFO - Saving checkpoint at 101 epochs
2024/07/05 22:21:28 - mmengine - INFO - Epoch(val) [101][100/147]    eta: 0:00:08  time: 0.1734  data_time: 0.0306  memory: 3093  
2024/07/05 22:21:35 - mmengine - INFO - Epoch(val) [101][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9977653627052604, 'occupancy': 0.7896375877642535, 'mIoU': 0.893701475234757}, '1~2m': {'no_occupancy': 0.9953294911505319, 'occupancy': 0.4909587325958116, 'mIoU': 0.7431441118731718}, '2~3m': {'no_occupancy': 0.997310784137364, 'occupancy': 0.09142508942777508, 'mIoU': 0.5443679367825696}, '3~4m': {'no_occupancy': 0.9990092950514186, 'occupancy': 0.005896888218787955, 'mIoU': 0.5024530916351032}, 'patch_mean': {'no_occupancy': 0.9973537332611437, 'occupancy': 0.34447957450165706, 'mIoU': 0.6709166538814004}}  total_overall: {'no_occupancy': 0.9973551517261297, 'occupancy': 0.5536401334185854, 'mIoU': 0.7754976425723575}  data_time: 0.0335  time: 0.1726
2024/07/05 22:23:05 - mmengine - INFO - Epoch(train) [102][100/147]  lr: 6.0591e-06  eta: 0:37:44  time: 0.7503  data_time: 0.0009  memory: 3093  grad_norm: 2.5542  loss: 0.3195  loss_bce: 0.0188  loss_dice: 0.3007
2024/07/05 22:23:40 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:23:40 - mmengine - INFO - Saving checkpoint at 102 epochs
2024/07/05 22:24:01 - mmengine - INFO - Epoch(val) [102][100/147]    eta: 0:00:08  time: 0.1749  data_time: 0.0325  memory: 3093  
2024/07/05 22:24:08 - mmengine - INFO - Epoch(val) [102][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9977952188244045, 'occupancy': 0.7917596359064197, 'mIoU': 0.8947774273654121}, '1~2m': {'no_occupancy': 0.9953628460334738, 'occupancy': 0.4925649159091347, 'mIoU': 0.7439638809713043}, '2~3m': {'no_occupancy': 0.9973157965508251, 'occupancy': 0.09129250008480229, 'mIoU': 0.5443041483178137}, '3~4m': {'no_occupancy': 0.9990088946630723, 'occupancy': 0.005594780438369473, 'mIoU': 0.5023018375507209}, 'patch_mean': {'no_occupancy': 0.997370689017944, 'occupancy': 0.3453029580846816, 'mIoU': 0.6713368235513129}}  total_overall: {'no_occupancy': 0.9973720619520436, 'occupancy': 0.5550404539133811, 'mIoU': 0.7762062579327124}  data_time: 0.0364  time: 0.1750
2024/07/05 22:24:19 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:25:37 - mmengine - INFO - Epoch(train) [103][100/147]  lr: 5.4497e-06  eta: 0:35:41  time: 0.7407  data_time: 0.0010  memory: 3093  grad_norm: 2.7126  loss: 0.3307  loss_bce: 0.0195  loss_dice: 0.3111
2024/07/05 22:26:12 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:26:12 - mmengine - INFO - Saving checkpoint at 103 epochs
2024/07/05 22:26:34 - mmengine - INFO - Epoch(val) [103][100/147]    eta: 0:00:08  time: 0.1754  data_time: 0.0345  memory: 3093  
2024/07/05 22:26:41 - mmengine - INFO - Epoch(val) [103][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9977856213313693, 'occupancy': 0.7912068854673558, 'mIoU': 0.8944962533993626}, '1~2m': {'no_occupancy': 0.9953541955964329, 'occupancy': 0.49331803029818055, 'mIoU': 0.7443361129473067}, '2~3m': {'no_occupancy': 0.9973139411969484, 'occupancy': 0.09333603905254731, 'mIoU': 0.5453249901247479}, '3~4m': {'no_occupancy': 0.9990085939830031, 'occupancy': 0.005759587452805706, 'mIoU': 0.5023840907179045}, 'patch_mean': {'no_occupancy': 0.9973655880269384, 'occupancy': 0.3459051355677224, 'mIoU': 0.6716353617973304}}  total_overall: {'no_occupancy': 0.9973669842708329, 'occupancy': 0.5552645994855802, 'mIoU': 0.7763157918782065}  data_time: 0.0331  time: 0.1713
2024/07/05 22:28:09 - mmengine - INFO - Epoch(train) [104][100/147]  lr: 4.8707e-06  eta: 0:33:37  time: 0.7357  data_time: 0.0009  memory: 3093  grad_norm: 2.8131  loss: 0.3362  loss_bce: 0.0195  loss_dice: 0.3167
2024/07/05 22:28:44 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:28:44 - mmengine - INFO - Saving checkpoint at 104 epochs
2024/07/05 22:29:05 - mmengine - INFO - Epoch(val) [104][100/147]    eta: 0:00:08  time: 0.1699  data_time: 0.0283  memory: 3093  
2024/07/05 22:29:12 - mmengine - INFO - Epoch(val) [104][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.997817848989699, 'occupancy': 0.7935355260150319, 'mIoU': 0.8956766875023654}, '1~2m': {'no_occupancy': 0.9953898147173273, 'occupancy': 0.4952130343187911, 'mIoU': 0.7453014245180593}, '2~3m': {'no_occupancy': 0.997318975216678, 'occupancy': 0.0945586211564058, 'mIoU': 0.5459387981865419}, '3~4m': {'no_occupancy': 0.9990093285742505, 'occupancy': 0.005763833619570225, 'mIoU': 0.5023865810969104}, 'patch_mean': {'no_occupancy': 0.9973839918744887, 'occupancy': 0.34726775377744973, 'mIoU': 0.6723258728259692}}  total_overall: {'no_occupancy': 0.9973853410265476, 'occupancy': 0.5569685009689621, 'mIoU': 0.7771769209977548}  data_time: 0.0334  time: 0.1728
2024/07/05 22:30:40 - mmengine - INFO - Epoch(train) [105][100/147]  lr: 4.3227e-06  eta: 0:31:33  time: 0.7396  data_time: 0.0009  memory: 3093  grad_norm: 2.9639  loss: 0.3371  loss_bce: 0.0200  loss_dice: 0.3170
2024/07/05 22:31:15 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:31:15 - mmengine - INFO - Saving checkpoint at 105 epochs
2024/07/05 22:31:37 - mmengine - INFO - Epoch(val) [105][100/147]    eta: 0:00:08  time: 0.1742  data_time: 0.0329  memory: 3093  
2024/07/05 22:31:44 - mmengine - INFO - Epoch(val) [105][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9978352855629298, 'occupancy': 0.7950034157660922, 'mIoU': 0.8964193506645111}, '1~2m': {'no_occupancy': 0.9954044769867573, 'occupancy': 0.4965597541762931, 'mIoU': 0.7459821155815252}, '2~3m': {'no_occupancy': 0.9973201966836825, 'occupancy': 0.09622987936064521, 'mIoU': 0.5467750380221639}, '3~4m': {'no_occupancy': 0.9990095288181008, 'occupancy': 0.005864955216877665, 'mIoU': 0.5024372420174892}, 'patch_mean': {'no_occupancy': 0.9973923720128676, 'occupancy': 0.34841450112997707, 'mIoU': 0.6729034365714224}}  total_overall: {'no_occupancy': 0.9973936998011381, 'occupancy': 0.5581970404500691, 'mIoU': 0.7777953701256036}  data_time: 0.0343  time: 0.1718
2024/07/05 22:33:12 - mmengine - INFO - Epoch(train) [106][100/147]  lr: 3.8060e-06  eta: 0:29:30  time: 0.7271  data_time: 0.0009  memory: 3093  grad_norm: 2.9227  loss: 0.3254  loss_bce: 0.0188  loss_dice: 0.3065
2024/07/05 22:33:46 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:33:46 - mmengine - INFO - Saving checkpoint at 106 epochs
2024/07/05 22:34:07 - mmengine - INFO - Epoch(val) [106][100/147]    eta: 0:00:08  time: 0.1740  data_time: 0.0318  memory: 3093  
2024/07/05 22:34:14 - mmengine - INFO - Epoch(val) [106][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9978252012044321, 'occupancy': 0.794130935220843, 'mIoU': 0.8959780682126375}, '1~2m': {'no_occupancy': 0.9954154645572733, 'occupancy': 0.49758151780606663, 'mIoU': 0.7464984911816699}, '2~3m': {'no_occupancy': 0.997324086948253, 'occupancy': 0.09835013955163409, 'mIoU': 0.5478371132499436}, '3~4m': {'no_occupancy': 0.9990090612516329, 'occupancy': 0.005962100468091877, 'mIoU': 0.5024855808598624}, 'patch_mean': {'no_occupancy': 0.9973934534903979, 'occupancy': 0.34900617326165895, 'mIoU': 0.6731998133760284}}  total_overall: {'no_occupancy': 0.9973947976607385, 'occupancy': 0.5584611079893307, 'mIoU': 0.7779279528250346}  data_time: 0.0351  time: 0.1750
2024/07/05 22:35:43 - mmengine - INFO - Epoch(train) [107][100/147]  lr: 3.3210e-06  eta: 0:27:26  time: 0.7396  data_time: 0.0009  memory: 3093  grad_norm: 2.8308  loss: 0.3511  loss_bce: 0.0210  loss_dice: 0.3301
2024/07/05 22:36:18 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:36:18 - mmengine - INFO - Saving checkpoint at 107 epochs
2024/07/05 22:36:39 - mmengine - INFO - Epoch(val) [107][100/147]    eta: 0:00:08  time: 0.1748  data_time: 0.0319  memory: 3093  
2024/07/05 22:36:46 - mmengine - INFO - Epoch(val) [107][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9978357101636003, 'occupancy': 0.7951159222401208, 'mIoU': 0.8964758162018606}, '1~2m': {'no_occupancy': 0.9954129274882403, 'occupancy': 0.49785287759382757, 'mIoU': 0.7466329025410339}, '2~3m': {'no_occupancy': 0.9973252577372785, 'occupancy': 0.09818583124063918, 'mIoU': 0.5477555444889588}, '3~4m': {'no_occupancy': 0.9990093284419347, 'occupancy': 0.005897085800923096, 'mIoU': 0.5024532071214289}, 'patch_mean': {'no_occupancy': 0.9973958059577634, 'occupancy': 0.3492629292188777, 'mIoU': 0.6733293675883205}}  total_overall: {'no_occupancy': 0.9973971413461153, 'occupancy': 0.5589931690579665, 'mIoU': 0.778195155202041}  data_time: 0.0336  time: 0.1733
2024/07/05 22:38:15 - mmengine - INFO - Epoch(train) [108][100/147]  lr: 2.8679e-06  eta: 0:25:22  time: 0.7498  data_time: 0.0010  memory: 3093  grad_norm: 2.8003  loss: 0.3369  loss_bce: 0.0199  loss_dice: 0.3169
2024/07/05 22:38:49 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:38:49 - mmengine - INFO - Saving checkpoint at 108 epochs
2024/07/05 22:39:11 - mmengine - INFO - Epoch(val) [108][100/147]    eta: 0:00:08  time: 0.1741  data_time: 0.0306  memory: 3093  
2024/07/05 22:39:18 - mmengine - INFO - Epoch(val) [108][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9978256088296962, 'occupancy': 0.7944331395001827, 'mIoU': 0.8961293741649394}, '1~2m': {'no_occupancy': 0.9954204294283083, 'occupancy': 0.4985018172869073, 'mIoU': 0.7469611233576078}, '2~3m': {'no_occupancy': 0.9973259484205742, 'occupancy': 0.09942632170978627, 'mIoU': 0.5483761350651802}, '3~4m': {'no_occupancy': 0.9990093952560426, 'occupancy': 0.005864169089126993, 'mIoU': 0.5024367821725848}, 'patch_mean': {'no_occupancy': 0.9973953454836553, 'occupancy': 0.34955636189650086, 'mIoU': 0.673475853690078}}  total_overall: {'no_occupancy': 0.9973966936969235, 'occupancy': 0.5591797510684146, 'mIoU': 0.778288222382669}  data_time: 0.0321  time: 0.1708
2024/07/05 22:40:47 - mmengine - INFO - Epoch(train) [109][100/147]  lr: 2.4472e-06  eta: 0:23:19  time: 0.7256  data_time: 0.0009  memory: 3093  grad_norm: 2.6459  loss: 0.3228  loss_bce: 0.0189  loss_dice: 0.3040
2024/07/05 22:41:04 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:41:21 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:41:21 - mmengine - INFO - Saving checkpoint at 109 epochs
2024/07/05 22:41:42 - mmengine - INFO - Epoch(val) [109][100/147]    eta: 0:00:08  time: 0.1738  data_time: 0.0316  memory: 3093  
2024/07/05 22:41:49 - mmengine - INFO - Epoch(val) [109][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9978686070754673, 'occupancy': 0.7976711684031953, 'mIoU': 0.8977698877393313}, '1~2m': {'no_occupancy': 0.9954506137914443, 'occupancy': 0.49999677433030487, 'mIoU': 0.7477236940608746}, '2~3m': {'no_occupancy': 0.9973237655206304, 'occupancy': 0.1006981077016319, 'mIoU': 0.5490109366111311}, '3~4m': {'no_occupancy': 0.9990094953283659, 'occupancy': 0.005964697702083456, 'mIoU': 0.5024870965152247}, 'patch_mean': {'no_occupancy': 0.9974131204289769, 'occupancy': 0.35108268703430395, 'mIoU': 0.6742479037316405}}  total_overall: {'no_occupancy': 0.9974144020011512, 'occupancy': 0.560888790114677, 'mIoU': 0.7791515960579141}  data_time: 0.0338  time: 0.1727
2024/07/05 22:43:18 - mmengine - INFO - Epoch(train) [110][100/147]  lr: 2.0590e-06  eta: 0:21:15  time: 0.7316  data_time: 0.0009  memory: 3093  grad_norm: 2.8371  loss: 0.3351  loss_bce: 0.0201  loss_dice: 0.3151
2024/07/05 22:43:53 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:43:53 - mmengine - INFO - Saving checkpoint at 110 epochs
2024/07/05 22:44:14 - mmengine - INFO - Epoch(val) [110][100/147]    eta: 0:00:08  time: 0.1748  data_time: 0.0331  memory: 3093  
2024/07/05 22:44:21 - mmengine - INFO - Epoch(val) [110][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9978761537464431, 'occupancy': 0.7981647376359579, 'mIoU': 0.8980204456912004}, '1~2m': {'no_occupancy': 0.995464643128907, 'occupancy': 0.5011731687453828, 'mIoU': 0.7483189059371449}, '2~3m': {'no_occupancy': 0.9973271044818713, 'occupancy': 0.10093247949668575, 'mIoU': 0.5491297919892786}, '3~4m': {'no_occupancy': 0.9990095285865946, 'occupancy': 0.006098122816863656, 'mIoU': 0.5025538257017291}, 'patch_mean': {'no_occupancy': 0.997419357485954, 'occupancy': 0.3515921271737225, 'mIoU': 0.6745057423298383}}  total_overall: {'no_occupancy': 0.9974206316908407, 'occupancy': 0.5615543910706028, 'mIoU': 0.7794875113807218}  data_time: 0.0346  time: 0.1720
2024/07/05 22:45:51 - mmengine - INFO - Epoch(train) [111][100/147]  lr: 1.7037e-06  eta: 0:19:11  time: 0.7355  data_time: 0.0009  memory: 3093  grad_norm: 2.6670  loss: 0.3427  loss_bce: 0.0199  loss_dice: 0.3228
2024/07/05 22:46:26 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:46:26 - mmengine - INFO - Saving checkpoint at 111 epochs
2024/07/05 22:46:48 - mmengine - INFO - Epoch(val) [111][100/147]    eta: 0:00:08  time: 0.1750  data_time: 0.0323  memory: 3093  
2024/07/05 22:46:54 - mmengine - INFO - Epoch(val) [111][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9978599413965654, 'occupancy': 0.7971266842037854, 'mIoU': 0.8974933128001754}, '1~2m': {'no_occupancy': 0.9954620788116676, 'occupancy': 0.5016218142769387, 'mIoU': 0.7485419465443032}, '2~3m': {'no_occupancy': 0.9973293039089157, 'occupancy': 0.10157303370786516, 'mIoU': 0.5494511688083904}, '3~4m': {'no_occupancy': 0.9990094951629986, 'occupancy': 0.0061312181189221784, 'mIoU': 0.5025703566409604}, 'patch_mean': {'no_occupancy': 0.9974152048200369, 'occupancy': 0.3516131875768778, 'mIoU': 0.6745141961984573}}  total_overall: {'no_occupancy': 0.9974165061063318, 'occupancy': 0.5615781767907215, 'mIoU': 0.7794973414485267}  data_time: 0.0349  time: 0.1730
2024/07/05 22:48:23 - mmengine - INFO - Epoch(train) [112][100/147]  lr: 1.3815e-06  eta: 0:17:08  time: 0.7425  data_time: 0.0009  memory: 3093  grad_norm: 2.7393  loss: 0.3319  loss_bce: 0.0195  loss_dice: 0.3124
2024/07/05 22:48:58 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:48:58 - mmengine - INFO - Saving checkpoint at 112 epochs
2024/07/05 22:49:18 - mmengine - INFO - Epoch(val) [112][100/147]    eta: 0:00:08  time: 0.1732  data_time: 0.0310  memory: 3093  
2024/07/05 22:49:26 - mmengine - INFO - Epoch(val) [112][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9978766069770199, 'occupancy': 0.7983643831685421, 'mIoU': 0.898120495072781}, '1~2m': {'no_occupancy': 0.9954808661060861, 'occupancy': 0.5025676503288079, 'mIoU': 0.749024258217447}, '2~3m': {'no_occupancy': 0.9973317423164553, 'occupancy': 0.10163621028956984, 'mIoU': 0.5494839763030126}, '3~4m': {'no_occupancy': 0.9990093616339827, 'occupancy': 0.0060971013643101825, 'mIoU': 0.5025532314991464}, 'patch_mean': {'no_occupancy': 0.9974246442583861, 'occupancy': 0.35216633628780747, 'mIoU': 0.6747954902730968}}  total_overall: {'no_occupancy': 0.9974259196576526, 'occupancy': 0.5624211829033593, 'mIoU': 0.7799235512805059}  data_time: 0.0348  time: 0.1739
2024/07/05 22:50:54 - mmengine - INFO - Epoch(train) [113][100/147]  lr: 1.0926e-06  eta: 0:15:04  time: 0.7321  data_time: 0.0010  memory: 3093  grad_norm: 2.7745  loss: 0.3399  loss_bce: 0.0208  loss_dice: 0.3191
2024/07/05 22:51:29 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:51:29 - mmengine - INFO - Saving checkpoint at 113 epochs
2024/07/05 22:51:51 - mmengine - INFO - Epoch(val) [113][100/147]    eta: 0:00:08  time: 0.1788  data_time: 0.0373  memory: 3093  
2024/07/05 22:51:58 - mmengine - INFO - Epoch(val) [113][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9978910035481275, 'occupancy': 0.7993604648602028, 'mIoU': 0.8986257342041652}, '1~2m': {'no_occupancy': 0.9954955854518597, 'occupancy': 0.502789378977885, 'mIoU': 0.7491424822148723}, '2~3m': {'no_occupancy': 0.9973326782403473, 'occupancy': 0.10158737301578372, 'mIoU': 0.5494600256280655}, '3~4m': {'no_occupancy': 0.9990095286527391, 'occupancy': 0.006031514664120028, 'mIoU': 0.5025205216584295}, 'patch_mean': {'no_occupancy': 0.9974321989732683, 'occupancy': 0.3524421828794979, 'mIoU': 0.674937190926383}}  total_overall: {'no_occupancy': 0.9974334477836815, 'occupancy': 0.5628411091098009, 'mIoU': 0.7801372784467412}  data_time: 0.0355  time: 0.1732
2024/07/05 22:53:27 - mmengine - INFO - Epoch(train) [114][100/147]  lr: 8.3725e-07  eta: 0:13:01  time: 0.7485  data_time: 0.0009  memory: 3093  grad_norm: 2.6617  loss: 0.3472  loss_bce: 0.0208  loss_dice: 0.3264
2024/07/05 22:54:02 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:54:02 - mmengine - INFO - Saving checkpoint at 114 epochs
2024/07/05 22:54:23 - mmengine - INFO - Epoch(val) [114][100/147]    eta: 0:00:08  time: 0.1747  data_time: 0.0319  memory: 3093  
2024/07/05 22:54:31 - mmengine - INFO - Epoch(val) [114][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9978691421865231, 'occupancy': 0.797744612613966, 'mIoU': 0.8978068774002446}, '1~2m': {'no_occupancy': 0.9954645750808307, 'occupancy': 0.5017409292725316, 'mIoU': 0.7486027521766812}, '2~3m': {'no_occupancy': 0.9973296965975142, 'occupancy': 0.10250451846114121, 'mIoU': 0.5499171075293278}, '3~4m': {'no_occupancy': 0.9990093281442227, 'occupancy': 0.0061967726538097595, 'mIoU': 0.5026030503990162}, 'patch_mean': {'no_occupancy': 0.9974181855022728, 'occupancy': 0.35204670825036216, 'mIoU': 0.6747324468763175}}  total_overall: {'no_occupancy': 0.9974194748607471, 'occupancy': 0.5618383477658807, 'mIoU': 0.7796289113133139}  data_time: 0.0331  time: 0.1722
2024/07/05 22:56:01 - mmengine - INFO - Epoch(train) [115][100/147]  lr: 6.1558e-07  eta: 0:10:57  time: 0.7417  data_time: 0.0009  memory: 3093  grad_norm: 2.8781  loss: 0.3417  loss_bce: 0.0203  loss_dice: 0.3214
2024/07/05 22:56:35 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:56:35 - mmengine - INFO - Saving checkpoint at 115 epochs
2024/07/05 22:56:57 - mmengine - INFO - Epoch(val) [115][100/147]    eta: 0:00:08  time: 0.1733  data_time: 0.0329  memory: 3093  
2024/07/05 22:57:04 - mmengine - INFO - Epoch(val) [115][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.997891730128291, 'occupancy': 0.7993145891000116, 'mIoU': 0.8986031596141513}, '1~2m': {'no_occupancy': 0.9954924390421973, 'occupancy': 0.5025343575031234, 'mIoU': 0.7490133982726603}, '2~3m': {'no_occupancy': 0.9973351073899682, 'occupancy': 0.10270136530286329, 'mIoU': 0.5500182363464158}, '3~4m': {'no_occupancy': 0.9990096285928164, 'occupancy': 0.006265233815510642, 'mIoU': 0.5026374312041635}, 'patch_mean': {'no_occupancy': 0.9974322262883183, 'occupancy': 0.35270388643037726, 'mIoU': 0.6750680563593477}}  total_overall: {'no_occupancy': 0.9974334769482661, 'occupancy': 0.5627872735317516, 'mIoU': 0.7801103752400089}  data_time: 0.0345  time: 0.1717
2024/07/05 22:58:30 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:58:34 - mmengine - INFO - Epoch(train) [116][100/147]  lr: 4.2776e-07  eta: 0:08:53  time: 0.7380  data_time: 0.0009  memory: 3093  grad_norm: 2.5635  loss: 0.3271  loss_bce: 0.0194  loss_dice: 0.3076
2024/07/05 22:59:08 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 22:59:08 - mmengine - INFO - Saving checkpoint at 116 epochs
2024/07/05 22:59:29 - mmengine - INFO - Epoch(val) [116][100/147]    eta: 0:00:08  time: 0.1737  data_time: 0.0302  memory: 3093  
2024/07/05 22:59:36 - mmengine - INFO - Epoch(val) [116][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9978815606340466, 'occupancy': 0.7987801879690681, 'mIoU': 0.8983308743015574}, '1~2m': {'no_occupancy': 0.9954816684531903, 'occupancy': 0.503052600463274, 'mIoU': 0.7492671344582322}, '2~3m': {'no_occupancy': 0.9973323959794923, 'occupancy': 0.10328295870478073, 'mIoU': 0.5503076773421365}, '3~4m': {'no_occupancy': 0.9990090943112824, 'occupancy': 0.0062951522305097225, 'mIoU': 0.502652123270896}, 'patch_mean': {'no_occupancy': 0.9974261798445029, 'occupancy': 0.35285272484190816, 'mIoU': 0.6751394523432055}}  total_overall: {'no_occupancy': 0.9974274527098606, 'occupancy': 0.5628599485171553, 'mIoU': 0.7801437006135079}  data_time: 0.0335  time: 0.1737
2024/07/05 23:01:06 - mmengine - INFO - Epoch(train) [117][100/147]  lr: 2.7391e-07  eta: 0:06:50  time: 0.7454  data_time: 0.0009  memory: 3093  grad_norm: 2.7341  loss: 0.3279  loss_bce: 0.0198  loss_dice: 0.3081
2024/07/05 23:01:40 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 23:01:40 - mmengine - INFO - Saving checkpoint at 117 epochs
2024/07/05 23:02:02 - mmengine - INFO - Epoch(val) [117][100/147]    eta: 0:00:08  time: 0.1834  data_time: 0.0305  memory: 3093  
2024/07/05 23:02:09 - mmengine - INFO - Epoch(val) [117][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.997884128677984, 'occupancy': 0.7988927675051786, 'mIoU': 0.8983884480915814}, '1~2m': {'no_occupancy': 0.9954820718019822, 'occupancy': 0.5026057582410277, 'mIoU': 0.7490439150215049}, '2~3m': {'no_occupancy': 0.9973330997966976, 'occupancy': 0.10303534116695501, 'mIoU': 0.5501842204818264}, '3~4m': {'no_occupancy': 0.999009428448103, 'occupancy': 0.006064210270627948, 'mIoU': 0.5025368193593654}, 'patch_mean': {'no_occupancy': 0.9974271821811918, 'occupancy': 0.35264951929594723, 'mIoU': 0.6750383507385695}}  total_overall: {'no_occupancy': 0.997428448886216, 'occupancy': 0.5626958332383839, 'mIoU': 0.7800621410622999}  data_time: 0.0333  time: 0.1757
2024/07/05 23:03:38 - mmengine - INFO - Epoch(train) [118][100/147]  lr: 1.5413e-07  eta: 0:04:46  time: 0.7363  data_time: 0.0010  memory: 3093  grad_norm: 3.0545  loss: 0.3546  loss_bce: 0.0209  loss_dice: 0.3337
2024/07/05 23:04:13 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 23:04:13 - mmengine - INFO - Saving checkpoint at 118 epochs
2024/07/05 23:04:34 - mmengine - INFO - Epoch(val) [118][100/147]    eta: 0:00:08  time: 0.1748  data_time: 0.0323  memory: 3093  
2024/07/05 23:04:41 - mmengine - INFO - Epoch(val) [118][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9978896418784509, 'occupancy': 0.799430537677077, 'mIoU': 0.8986600897777639}, '1~2m': {'no_occupancy': 0.9954892498730642, 'occupancy': 0.5033618888067684, 'mIoU': 0.7494255693399163}, '2~3m': {'no_occupancy': 0.9973370050814641, 'occupancy': 0.1034633981783425, 'mIoU': 0.5504002016299033}, '3~4m': {'no_occupancy': 0.9990094617724753, 'occupancy': 0.006131012705937533, 'mIoU': 0.5025702372392065}, 'patch_mean': {'no_occupancy': 0.9974313396513637, 'occupancy': 0.35309670934203136, 'mIoU': 0.6752640244966975}}  total_overall: {'no_occupancy': 0.997432602564053, 'occupancy': 0.5633490322223391, 'mIoU': 0.780390817393196}  data_time: 0.0344  time: 0.1723
2024/07/05 23:06:10 - mmengine - INFO - Epoch(train) [119][100/147]  lr: 6.8523e-08  eta: 0:02:43  time: 0.7413  data_time: 0.0010  memory: 3093  grad_norm: 2.9129  loss: 0.3400  loss_bce: 0.0201  loss_dice: 0.3199
2024/07/05 23:06:44 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 23:06:44 - mmengine - INFO - Saving checkpoint at 119 epochs
2024/07/05 23:07:06 - mmengine - INFO - Epoch(val) [119][100/147]    eta: 0:00:08  time: 0.1725  data_time: 0.0315  memory: 3093  
2024/07/05 23:07:13 - mmengine - INFO - Epoch(val) [119][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9979054915676534, 'occupancy': 0.8004149365944316, 'mIoU': 0.8991602140810425}, '1~2m': {'no_occupancy': 0.9955066791585535, 'occupancy': 0.5032569160506887, 'mIoU': 0.7493817976046211}, '2~3m': {'no_occupancy': 0.997338250860204, 'occupancy': 0.10237550827316655, 'mIoU': 0.5498568795666853}, '3~4m': {'no_occupancy': 0.9990094953614395, 'occupancy': 0.005931386922464708, 'mIoU': 0.502470441141952}, 'patch_mean': {'no_occupancy': 0.9974399792369626, 'occupancy': 0.3529946869601879, 'mIoU': 0.6752173330985752}}  total_overall: {'no_occupancy': 0.9974412095187111, 'occupancy': 0.563523566997275, 'mIoU': 0.780482388257993}  data_time: 0.0340  time: 0.1722
2024/07/05 23:08:42 - mmengine - INFO - Epoch(train) [120][100/147]  lr: 1.7134e-08  eta: 0:00:39  time: 0.7341  data_time: 0.0010  memory: 3093  grad_norm: 2.7755  loss: 0.3231  loss_bce: 0.0191  loss_dice: 0.3040
2024/07/05 23:09:16 - mmengine - INFO - Exp name: indoor_zed_lidar_occ_20240705_180526
2024/07/05 23:09:16 - mmengine - INFO - Saving checkpoint at 120 epochs
2024/07/05 23:09:38 - mmengine - INFO - Epoch(val) [120][100/147]    eta: 0:00:08  time: 0.1765  data_time: 0.0358  memory: 3093  
2024/07/05 23:09:45 - mmengine - INFO - Epoch(val) [120][147/147]    by_distance: {'0-1m': {'no_occupancy': 0.9978845808756016, 'occupancy': 0.7991018430803547, 'mIoU': 0.8984932119779782}, '1~2m': {'no_occupancy': 0.9954859413588519, 'occupancy': 0.5033867156976255, 'mIoU': 0.7494363285282387}, '2~3m': {'no_occupancy': 0.9973345653255948, 'occupancy': 0.10354976409795552, 'mIoU': 0.5504421647117752}, '3~4m': {'no_occupancy': 0.9990094617063258, 'occupancy': 0.006197603035150459, 'mIoU': 0.5026035323707381}, 'patch_mean': {'no_occupancy': 0.9974286373165935, 'occupancy': 0.35305898147777154, 'mIoU': 0.6752438093971825}}  total_overall: {'no_occupancy': 0.9974299073316378, 'occupancy': 0.5632268530560788, 'mIoU': 0.7803283801938583}  data_time: 0.0345  time: 0.1718
