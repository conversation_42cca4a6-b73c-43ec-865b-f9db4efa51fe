import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
import os
import time


class PointCloudVisualizer:
    def __init__(self, folder_path):
        self.folder_path = folder_path
        self.files = sorted([file for file in os.listdir(folder_path) if file.endswith('.pcd')])
        self.index = 0  # 当前显示的文件索引
        self.vis = o3d.visualization.VisualizerWithKeyCallback()
        self.vis.create_window()

        self.origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.4, origin=[0, 0, 0])
        self.vis.register_key_callback(262, self.next_callback)  # Right key
        self.vis.register_key_callback(263, self.previous_callback)  # Left key

    def set_custom_viewpoint(self, vis):
        ctr = vis.get_view_control()
        ctr.set_lookat([0.22167814190143573, 0.48231197996297359, 0.15774473304656123])
        ctr.set_front([-0.41347856489619761, -0.91050284897665223, 0.0044764245532130115])
        ctr.set_up([0.015139844789555171, -0.0019594649272651758, 0.99988346600838784])
        ctr.set_zoom(0.69999999999999996)

    def load_pcd(self, index):
        # 载入点云
        pcd_path = os.path.join(self.folder_path, self.files[index])
        pcd = o3d.io.read_point_cloud(pcd_path)
        return pcd

    def process_pcd(self, pcd):
        # 变换、裁剪和 FOV 过滤

        T_oms_tof = np.array([[0.9999, -0.00124883, -0.00460191, 0.019164],
                              [0.00122781, 0.9999, -0.00456, 0.0038442],
                              [0.00460, 0.00456, 0.9999, -0.01337119],
                              [0, 0, 0, 1]], dtype=np.float64)
        T_plidar_oms = np.linalg.inv(
            np.array([
            [1, 0, 0, 0],
            [0, 0, -1, 0],
            [0, 1, 0, 0],
            [0, 0, 0, 1]
        ], dtype=np.float32))

        pcd.transform(T_oms_tof)
        pcd.transform(T_plidar_oms)

        occupancy_range = np.array([[-2, 0, -0.05], [2, 4, 0.15]])
        bounding_box = o3d.geometry.AxisAlignedBoundingBox(occupancy_range[0], occupancy_range[1])
        pcd = pcd.crop(bounding_box)

        # 首先为所有点应用颜色映射
        points = np.asarray(pcd.points)
        y_coords = points[:, 2]
        y_normalized = ((y_coords - y_coords.min()) / (y_coords.max() - y_coords.min()))
        cmap = plt.get_cmap('jet')
        colors = cmap(y_normalized)[:, :3]  # 取 RGB 分量

        # # 然后将 FOV 之外的点置为灰色
        # gray_color = np.array([0.9, 0.9, 0.9])
        # gray_color_fov = np.array([0.97, 0.97, 0.97])
        # # colors[~fov_mask] = gray_color_fov  # FOV之外的点设置为灰色
        # # colors[points[:, 2] > 0.5] = gray_color

        pcd.colors = o3d.utility.Vector3dVector(colors)
        # pcd = pcd.select_by_index(np.where(fov_mask)[0])

        return pcd

    def visualize(self):
        pcd = self.load_pcd(self.index)
        pcd = self.process_pcd(pcd)

        self.vis.add_geometry(pcd)
        self.vis.add_geometry(self.origin)
        self.set_custom_viewpoint(self.vis)  # 设置自定义视角

        # 键盘回调函数
    def next_callback(self, vis):
        self.index = (self.index + 1) % len(self.files)
        print(f"Loading next point cloud: {os.path.join(self.folder_path, self.files[self.index])}")
        vis.clear_geometries()
        new_pcd = self.load_pcd(self.index)
        new_pcd = self.process_pcd(new_pcd)
        self.vis.add_geometry(new_pcd)
        self.vis.add_geometry(self.origin)
        self.set_custom_viewpoint(vis)  # 重新设置视角

    def previous_callback(self, vis):
        self.index = (self.index - 1 + len(self.files)) % len(self.files)
        print(f"Loading previous point cloud: {os.path.join(self.folder_path, self.files[self.index])}")
        vis.clear_geometries()
        new_pcd = self.load_pcd(self.index)
        new_pcd = self.process_pcd(new_pcd)
        self.vis.add_geometry(new_pcd)
        self.vis.add_geometry(self.origin)
        self.set_custom_viewpoint(vis)  # 重新设置视角

    def run(self):
        self.visualize()
        self.vis.run()
        self.vis.destroy_window()


if __name__ == "__main__":
    # folder_path = '/home/<USER>/Datasets/indoor/scenes1_3_withoutSlam/dk_oms/dk_points'
    folder_path = '/home/<USER>/Datasets/indoor/scenes/scenes1_3/dk_oms/oms_points'
    visualizer = PointCloudVisualizer(folder_path)
    visualizer.run()
