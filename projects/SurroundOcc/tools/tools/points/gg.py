import gi
gi.require_version('Gtk', '3.0')
from gi.repository import Gtk, GdkPixbuf
import os
import subprocess

class ImageViewer(Gtk.Window):

    def __init__(self):
        Gtk.Window.__init__(self, title="Image Viewer")
        self.folder_path = None
        self.image_list = []
        self.current_image_index = 0

        self.grid = Gtk.Grid()
        self.add(self.grid)

        # Create buttons
        self.browse_button = Gtk.Button(label="Browse Folder")
        self.browse_button.connect("clicked", self.on_browse_clicked)
        self.grid.attach(self.browse_button, 0, 0, 1, 1)

        self.previous_button = Gtk.Button(label="Previous")
        self.previous_button.connect("clicked", self.on_previous_clicked)
        self.grid.attach(self.previous_button, 1, 0, 1, 1)

        self.next_button = Gtk.Button(label="Next")
        self.next_button.connect("clicked", self.on_next_clicked)
        self.grid.attach(self.next_button, 2, 0, 1, 1)

        self.delete_button = Gtk.Button(label="Delete")
        self.delete_button.connect("clicked", self.on_delete_clicked)
        self.grid.attach(self.delete_button, 3, 0, 1, 1)

        # Create image region
        self.image_view = Gtk.Image()
        self.image_name_label = Gtk.Label()
        self.grid.attach(self.image_name_label, 0, 1, 4, 1)
        self.grid.attach_next_to(self.image_view, self.image_name_label, Gtk.PositionType.BOTTOM, 4, 1)

        # Create folder path entry and open button
        self.folder_entry = Gtk.Entry()
        self.grid.attach(self.folder_entry, 0, 2, 3, 1)
        self.open_folder_button = Gtk.Button(label="Open Folder")
        self.open_folder_button.connect("clicked", self.on_open_folder_clicked)
        self.grid.attach(self.open_folder_button, 3, 2, 1, 1)

        self.show_all()

        # Open folder selection dialog at startup
        self.open_folder_dialog()

    def open_folder_dialog(self):
        dialog = Gtk.FileChooserDialog("Please choose a folder", self,
                                       Gtk.FileChooserAction.SELECT_FOLDER,
                                       (Gtk.STOCK_CANCEL, Gtk.ResponseType.CANCEL,
                                        Gtk.STOCK_OPEN, Gtk.ResponseType.OK))

        response = dialog.run()
        if response == Gtk.ResponseType.OK:
            self.folder_path = dialog.get_filename()
            self.folder_entry.set_text(self.folder_path)
            dialog.destroy()
            self.load_images_from_folder()
        elif response == Gtk.ResponseType.CANCEL:
            dialog.destroy()

    def load_images_from_folder(self):
        if self.folder_path:
            self.image_list = []
            images = os.listdir(self.folder_path)
            images.sort()
            for file_name in images:
                if file_name.lower().endswith(('.png', '.jpg', '.jpeg')):
                    self.image_list.append(os.path.join(self.folder_path, file_name))

            self.current_image_index = 0
            self.show_current_image()

    def on_browse_clicked(self, button):
        self.open_folder_dialog()

    def on_open_folder_clicked(self, button):
        self.folder_path = self.folder_entry.get_text()
        if self.is_valid_path(self.folder_path):
            self.load_images_from_folder()
        else:
            # Show error dialog
            error_dialog = Gtk.MessageDialog(self, 0, Gtk.MessageType.ERROR, Gtk.ButtonsType.OK, "Invalid Folder")
            error_dialog.format_secondary_text("The folder path you entered is not valid.")
            error_dialog.run()
            error_dialog.destroy()

    def is_valid_path(self, path):
        # Check if the path is a directory or a valid remote path
        if os.path.isdir(path):
            return True
        try:
            subprocess.run(['ls', path], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return True
        except subprocess.CalledProcessError:
            return False

    def show_current_image(self):
        if self.image_list:
            pixbuf = GdkPixbuf.Pixbuf.new_from_file(self.image_list[self.current_image_index])
            self.image_view.set_from_pixbuf(pixbuf)
            self.image_name_label.set_text(self.image_list[self.current_image_index])

    def on_previous_clicked(self, button):
        if self.image_list:
            self.current_image_index = (self.current_image_index - 1) % len(self.image_list)
            self.show_current_image()

    def on_next_clicked(self, button):
        if self.image_list:
            self.current_image_index = (self.current_image_index + 1) % len(self.image_list)
            self.show_current_image()

    def on_delete_clicked(self, button):
        if self.image_list:
            image_path = self.image_list[self.current_image_index]
            os.remove(image_path)
            del self.image_list[self.current_image_index]

            if self.image_list:
                self.current_image_index %= len(self.image_list)
                self.show_current_image()
            else:
                self.image_view.set_from_pixbuf(None)

win = ImageViewer()
win.connect("destroy", Gtk.main_quit)
win.show_all()
Gtk.main()
