# 主要进行室内数据下tof点云模拟真实6度tof的点云数据
# 这个代码输出的点云是在tof坐标系下的，需要通过外参转到相机坐标系，之后转到假想雷达坐标系
import os

import numpy as np
import open3d as o3d
import math
import matplotlib.pyplot as plt


def get_pcd_from_depth_indoor(depth_array, intrin):
    H = depth_array.shape[0]

    angle_camera = 77.02
    angle_tof = 6

    H_tof = np.tan(angle_tof * np.pi / 360) * 2 * intrin[1, 1]  # 反向计算对应的H

    # all_line = angle_tof / angle_camera * H

    all_line = H_tof

    start = int(H / 2 - all_line / 2)
    end = int(H / 2 + all_line / 2)

    depth_array[0:start, :] = 0
    depth_array[end:, :] = 0

    fx = intrin[0][0]  # 焦距x
    fy = intrin[1][1]  # 焦距y
    cx = intrin[0][2]  # 中心点x
    cy = intrin[1][2]  # 中心点y

    points = []

    tof_h = 6
    tof_w = 224

    step_h = int(all_line / (tof_h - 1))
    step_w = int(depth_array.shape[1] / tof_w)

    for i in range(start, end, step_h):
        for j in range(0, depth_array.shape[1], step_w):

            if depth_array[i][j]:
                z3 = depth_array[i][j]
                x3 = (j - cx) * z3 / fx
                y3 = (i - cy) * z3 / fy

                point = np.stack((x3, y3, z3), axis=-1)
                points.append(point)

    ans_points = np.array(points)

    return ans_points


def draw_point2uv(points_, img_, intrinsic_, distortion_coeffs_):
    height, width = 180, 240
    points_np = np.asarray(points_.points)

    pt = points_np
    angle = np.arctan2(pt[:, 0], pt[:, 2]) * 180 / math.pi

    pt = pt[angle < 60]
    angle = angle[angle < 60]
    pt = pt[angle > -60]

    pt_1 = pt / np.expand_dims(pt[:, 2], 1)
    r = np.expand_dims((pt_1[:, 0] ** 2 + pt_1[:, 1] ** 2) ** (1 / 2), 1)
    r = (pt_1[:, 0] ** 2 + pt_1[:, 1] ** 2) ** (1 / 2)

    r2 = r * r
    r4 = r2 * r2

    u = np.round(intrinsic_[0][0] * pt_1[:, 0] + intrinsic_[0][2])
    v = np.round(intrinsic_[1][1] * pt_1[:, 1] + intrinsic_[1][2])

    v = v[u >= 1]
    pt = pt[u >= 1]
    u = u[u >= 1]

    v = v[u < width - 1]
    pt = pt[u < width - 1]
    u = u[u < width - 1]

    u = u[v >= 1]
    pt = pt[v >= 1]
    v = v[v >= 1]

    u = u[v < height - 1]
    pt = pt[v < height - 1]
    v = v[v < height - 1]

    # 这里写成1与height-1是为了后面好多上色，正常应该是0，heght

    pic_img = img_ * 1.0

    v = v.astype("int32")
    u = u.astype("int32")
    pic_img[v, u] = pt[:, 2]

    return pic_img


def main():
    tof_dir = '/home/<USER>/Datasets/indoor/scenes/scenes1_3/dk_oms/oms_points'
    tof_list = os.listdir(tof_dir)
    tof_list.sort()
    count = 0
    for i, tof_path in enumerate(tof_list):
        tof_pcd_path = os.path.join(tof_dir, tof_path)
        print(tof_pcd_path)
        origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.2, origin=[0, 0, 0])

        T_oms_tof = np.array([[0.9999, -0.00124883, -0.00460191, 0.019164],
                              [0.00122781, 0.9999, -0.00456, 0.0038442],
                              [0.00460, 0.00456, 0.9999, -0.01337119],
                              [0, 0, 0, 1]], dtype=np.float64)
        lidar2left_rt = np.array([
            [1, 0, 0, 0],
            [0, 0, -1, 0],
            [0, 1, 0, 0],
            [0, 0, 0, 1]
        ], dtype=np.float32)

        mesh = o3d.io.read_point_cloud(tof_pcd_path)
        # mesh_trans = mesh.copy().transform(T_oms_tof)

        # 去除000
        temp_points = np.array(mesh.points)
        mask_p = abs(temp_points - np.array([0, 0, 0])).max(1) > 1e-2
        temp_points = temp_points[mask_p]
        mesh.points = o3d.utility.Vector3dVector(temp_points)

        H = 180
        W = 240

        img = np.zeros((H, W))
        intric = np.zeros((3, 3))

        intric[0, 0] = 113.105000
        intric[0, 2] = 123.835000
        intric[1, 1] = 113.105000
        intric[1, 2] = 85.830500
        intric[2, 2] = 1

        dist = np.array([-0.053488, -0.000228, 0.000440, -0.000024, -0.020353])  # k1 k2 p1 p2 k3

        depth_ = draw_point2uv(mesh, img, intric, dist)

        # fov_x = 2 * np.arctan(W / (2*intric[0,0])) * 180 / np.pi
        # fov_y = 2 * np.arctan(H / (2*intric[1,1])) * 180 / np.pi

        # print('Horizontal FOV: {:.2f} degrees'.format(fov_x))
        # print('Vertical FOV: {:.2f} degrees'.format(fov_y))

        # 竖直fov 77.02
        tof_points = get_pcd_from_depth_indoor(depth_, intric)
        if len(tof_points) > 0:
            tof_pcd = o3d.geometry.PointCloud()
            tof_pcd.points = o3d.utility.Vector3dVector(tof_points.astype(float))
        else:
            count += 1

        point_cloud_range2 = np.array([[-1.975, -0.125, 0.025], [2, 0.015, 4]])
        bounding_box = o3d.geometry.AxisAlignedBoundingBox(point_cloud_range2[0], point_cloud_range2[1])
        filter = tof_pcd.crop(bounding_box)

        o3d.visualization.draw_geometries([mesh, origin])
        o3d.visualization.draw_geometries([tof_pcd, origin])
        o3d.visualization.draw_geometries([filter, origin])
    print(count)


if __name__ == '__main__':
    main()
