import tkinter as tk
from tkinter import filedialog, messagebox
from PIL import Image, ImageTk
import os

class ImageViewer(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Image Viewer")  # 设置窗口标题
        self.folder_path = None  # 保存选择的文件夹路径
        self.image_list = []  # 保存图像文件路径列表
        self.current_image_index = 0  # 当前显示的图像索引

        # 创建按钮
        self.browse_button = tk.But<PERSON>(self, text="Browse Folder", command=self.on_browse_clicked)
        self.browse_button.grid(row=0, column=0)  # 布局按钮

        self.previous_button = tk.But<PERSON>(self, text="Previous", command=self.on_previous_clicked)
        self.previous_button.grid(row=0, column=1)

        self.next_button = tk.Button(self, text="Next", command=self.on_next_clicked)
        self.next_button.grid(row=0, column=2)

        self.delete_button = tk.But<PERSON>(self, text="Delete", command=self.on_delete_clicked)
        self.delete_button.grid(row=0, column=3)

        # 创建文件名和路径显示区域
        self.image_name_label = tk.Label(self, text="File Path and Name will be displayed here", anchor='w', justify='left')
        self.image_name_label.grid(row=1, column=0, columnspan=4, sticky='w', padx=5, pady=5)

        # 创建图像显示区域
        self.image_view = tk.Label(self)  # 用于显示图像
        self.image_view.grid(row=2, column=0, columnspan=4)  # 跨四列布局

    def on_browse_clicked(self):
        # 打开文件夹选择对话框
        self.folder_path = filedialog.askdirectory()
        if self.folder_path:
            self.image_list = []  # 清空图像列表
            images = os.listdir(self.folder_path)  # 获取文件夹中所有文件
            images.sort()  # 对文件排序
            for file_name in images:
                # 仅添加图像文件（.png, .jpg, .jpeg）
                if file_name.lower().endswith(('.png', '.jpg', '.jpeg')):
                    self.image_list.append(os.path.join(self.folder_path, file_name))

            self.current_image_index = 0  # 重置当前图像索引
            self.show_current_image()  # 显示当前图像

    def show_current_image(self):
        if self.image_list:
            image_path = self.image_list[self.current_image_index]  # 获取当前图像路径
            image = Image.open(image_path)  # 打开图像文件
            image = ImageTk.PhotoImage(image)  # 将图像转换为Tkinter可用的格式
            self.image_view.config(image=image)  # 在Label中显示图像
            self.image_view.image = image  # 保存图像引用防止被垃圾回收
            # 显示文件路径和文件名
            self.image_name_label.config(text=f"Path: {image_path}\nFile: {os.path.basename(image_path)}")

    def on_previous_clicked(self):
        if self.image_list:
            # 显示上一张图像（使用模运算循环）
            self.current_image_index = (self.current_image_index - 1) % len(self.image_list)
            self.show_current_image()

    def on_next_clicked(self):
        if self.image_list:
            # 显示下一张图像（使用模运算循环）
            self.current_image_index = (self.current_image_index + 1) % len(self.image_list)
            self.show_current_image()

    def on_delete_clicked(self):
        if self.image_list:
            image_path = self.image_list[self.current_image_index]  # 获取当前图像路径
            os.remove(image_path)  # 删除图像文件
            del self.image_list[self.current_image_index]  # 从列表中移除图像路径

            if self.image_list:
                # 确保索引在范围内
                self.current_image_index %= len(self.image_list)
                self.show_current_image()  # 显示当前图像
            else:
                # 清除图像和标签
                self.image_view.config(image='')
                self.image_name_label.config(text='')

if __name__ == "__main__":
    app = ImageViewer()
    app.mainloop()  # 运行主循环
