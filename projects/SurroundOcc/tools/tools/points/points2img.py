import numpy as np
import open3d as o3d
import cv2


class PointCloudProjector:
    def __init__(self, intrinsic_matrix, extrinsic_matrix):
        self.intrinsic_matrix = intrinsic_matrix
        self.extrinsic_matrix = extrinsic_matrix

    def read_pcd(self, file_path):
        pcd = o3d.io.read_point_cloud(file_path)
        points = np.asarray(pcd.points)
        return points

    def read_rgb(self, file_path):
        image = cv2.imread(file_path)
        return image

    def transform_point_cloud(self, points):
        # Add a column of ones to the point cloud matrix to make it homogeneous
        ones = np.ones((points.shape[0], 1))
        points_homogeneous = np.hstack((points, ones))

        # Transform the points using the extrinsic matrix
        transformed_points = self.extrinsic_matrix @ points_homogeneous.T
        return transformed_points.T

    def project_to_image(self, points):
        # Only take the first three columns to ignore the homogeneous coordinate
        points = points[:, :3]

        # Project points onto the image plane
        points_image_plane = self.intrinsic_matrix @ points.T
        points_image_plane = points_image_plane.T

        # Normalize by the third coordinate
        points_image_plane /= points_image_plane[:, 2].reshape(-1, 1)
        return points_image_plane[:, :2]

    def visualize_projection(self, image, transformed_points, projected_points):
        # Compute distances for coloring
        distances = np.linalg.norm(transformed_points[:, :3], axis=1)
        max_distance = np.max(distances)
        min_distance = np.min(distances)

        for point, distance in zip(projected_points, distances):
            x, y = int(point[0]), int(point[1])
            if 0 <= x < image.shape[1] and 0 <= y < image.shape[0]:
                color_intensity = int(255 * (distance - min_distance) / (max_distance - min_distance))
                color = (0, color_intensity, 255 - color_intensity)  # Color from blue to red based on distance
                cv2.circle(image, (x, y), 1, color, -1)
        cv2.imshow('Projected Points', image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()


if __name__ == '__main__':
    # File paths
    pcd_file = '/home/<USER>/Datasets/indoor/scenes1-3_withoutSlam/dk_oms/dk_points/frame000001.pcd'
    rgb_file = '/home/<USER>/Datasets/indoor/scenes1-3_withoutSlam/dk_oms/oms_rgb/frame000001.jpg'

    # Camera intrinsic parameters (example values)
    intrinsic_oms = np.array([
        [627.361, 0, 630.733],
        [0, 627.189, 531.027],
        [0, 0, 1]], dtype=np.float64)

    # Extrinsic parameters from point cloud to camera (example values)
    extrinsic_oms_dk = np.array([
        [1.00008513, 0.01026008, 0.00913286, 0.01722355],
        [-0.01236991, 0.96158858, 0.27421972, -0.1017069],
        [-0.00596806, -0.27430616, 0.96163204, -0.05610114],
        [0., 0., 0., 1.]], dtype=np.float64)

    # Create an instance of the PointCloudProjector class
    projector = PointCloudProjector(intrinsic_oms, extrinsic_oms_dk)

    # Read the point cloud and RGB image
    points = projector.read_pcd(pcd_file)
    image = projector.read_rgb(rgb_file)

    # Transform point cloud to RGB camera coordinate system
    transformed_points = projector.transform_point_cloud(points)

    # Project transformed points to the image plane
    projected_points = projector.project_to_image(transformed_points)

    # Visualize the projected points on the RGB image
    projector.visualize_projection(image, transformed_points, projected_points)

