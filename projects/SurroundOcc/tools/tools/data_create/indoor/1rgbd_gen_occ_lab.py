# here put the import lib
import numpy as np
import cv2 as cv
import os
import matplotlib.pyplot as plt
import logging
from scipy.spatial.transform import Rotation as R
import open3d as o3d
import numpy as np
from glob import glob
import os
import cv2
from sklearn.utils import assert_all_finite
import json
import os.path as osp
import copy
import math
from tqdm import tqdm

# https://blog.csdn.net/yuegooxi/article/details/122015346
# k1，k2，p1，p2，k3～k6,这里只用前4个参数
# autoware 给的时k1，k2，k3，p1，p2


plt.figure()


def read_pose(path):
    pose = open(path, 'r').read().split('\n')[1].split(' ')
    T = [pose[1], pose[2], pose[3], 1]
    Rq = [pose[4], pose[5], pose[6], pose[7]]
    r = R.from_quat(Rq)
    Rm = r.as_matrix()
    Tlc = np.zeros((4, 4), dtype=np.float64)
    Tlc[:3, :3] = Rm
    Tlc[:, 3] = T
    return Tlc


# def get_rays_pytorch(H, W, K):
#     i, j = torch.meshgrid(torch.linspace(0, W-1, W), torch.linspace(0, H-1, H))  # pytorch's meshgrid has indexing='ij'
#     i = i.t()
#     j = j.t()
#     rays = torch.stack([(i-K[0][2])/K[0][0], -(j-K[1][2])/K[1][1], -torch.ones_like(i)], -1)
#     return rays


def get_rays_nerf(H, W, instrinsics):
    i, j = np.meshgrid(np.linspace(0, W - 1, W), np.linspace(0, H - 1, H))  # pytorch's meshgrid has indexing='ij'
    i = i[:, :, np.newaxis]
    j = j[:, :, np.newaxis]
    rays = np.concatenate(
        [(i - instrinsics[0][2]) / instrinsics[0][0], -(j - instrinsics[1][2]) / instrinsics[1][1], -np.ones_like(i)],
        -1)
    show = False
    if show == True:
        import matplotlib.pyplot as plt
        fig = plt.figure()
        # ax = fig.gca(projection="3d")
        ax = fig.add_subplot(projection='3d')
        ax.scatter(rays[:, :, 0], rays[:, :, 1], rays[:, :, 2])
        plt.show()

    return rays


def get_rays_camxyz(occupancy_range, instrinsics, img_size, gridsize):
    fx = instrinsics[0][0]
    fy = instrinsics[1][1]
    cx = instrinsics[0][2]
    cy = instrinsics[1][2]
    fov_x = (math.atan(img_size[1] / (2 * fx)) * 2) * (180 / math.pi)  # 将弧度转换为度数
    fov_y = (math.atan(img_size[0] / (2 * fy)) * 2) * (180 / math.pi)  # 将弧度转换为度数

    # occupancy_range=np.array([[-1.974, -0.049999, 0.0251], [2.024,0.05, 4.02499]] )   
    # 定义左、右、前、上、下边界
    bound_left = occupancy_range[0][0]
    bound_right = occupancy_range[1][0]
    bound_far = occupancy_range[1][2]
    bound_near = occupancy_range[0][2]
    bound_up = occupancy_range[1][1]
    bound_down = occupancy_range[0][1]

    # num_x=math.ceil((-occupancy_range[0][0]-gridsize/2)/gridsize)+math.ceil((occupancy_range[1][0]-gridsize/2)/gridsize)+1
    # num_y=math.ceil((-occupancy_range[0][1]-gridsize/2)/gridsize)+math.ceil((occupancy_range[1][1]-gridsize/2)/gridsize)+1
    # num_z=math.ceil((-occupancy_range[0][2]-gridsize/2)/gridsize)+math.ceil((occupancy_range[1][2]-gridsize/2)/gridsize)+1

    num_x = math.ceil((occupancy_range[1][0] - occupancy_range[0][0]) / gridsize)
    num_y = math.ceil((occupancy_range[1][1] - occupancy_range[0][1]) / gridsize)
    num_z = math.ceil((occupancy_range[1][2] - occupancy_range[0][2]) / gridsize)

    # 几个平面的每个grid所处的中心
    grid_centers = []

    # 左面
    # x = bound_left  # + gridsize / 2 从左侧格子的中心开始
    x = bound_left - gridsize / 2 + gridsize / 6
    for i in range(num_y * 3 - 1):  # 分成更小的格子
        for j in range(num_z * 3 - 1):
            y = bound_down + i * gridsize / 3 + gridsize / 6  # 中心点仍为 gridsize/2, 是否应为gridsize/6？
            z = bound_near + j * gridsize / 3 + gridsize / 6
            if abs(math.atan(x / z) * 2 * (180 / math.pi)) < fov_x and abs(   # 计算每个点是否在fov之内
                    math.atan(y / z) * 2 * (180 / math.pi)) < fov_y:
                grid_centers.append((x, y, z))

    # 右面
    # x = bound_right - gridsize / 2  # 从最右侧格子的中心开始
    x = bound_right - gridsize / 6  # 从最右侧格子的中心开始
    for i in range(num_y * 3 - 1):
        for j in range(num_z * 3 - 1):
            y = bound_down + i * gridsize / 3 + gridsize / 6
            z = bound_near + j * gridsize / 3 + gridsize / 6
            if abs(math.atan(x / z) * 2 * (180 / math.pi)) < fov_x and abs(
                    math.atan(y / z) * 2 * (180 / math.pi)) < fov_y:
                grid_centers.append((x, y, z))

    # 视锥深度方向平面
    # z = bound_far - gridsize / 2
    z = bound_far - gridsize / 6
    for i in range(num_x * 3 - 1):
        for j in range(num_y * 3 - 1):
            x = bound_left + i * gridsize / 3 + gridsize / 6
            y = bound_down + j * gridsize / 3 + gridsize / 6
            if abs(math.atan(x / z) * 2 * (180 / math.pi)) < fov_x and abs(
                    math.atan(y / z) * 2 * (180 / math.pi)) < fov_y:
                grid_centers.append((x, y, z))

    # 上平面
    # y = bound_down #+ gridsize / 2   # 这里已经是格子的中心了，还要 + gridsize / 2？
    y = bound_down - gridsize / 2 + gridsize / 6
    for i in range(num_x * 3 - 1):
        for j in range(num_z * 3 - 1):
            x = bound_left + i * gridsize / 3 + gridsize / 6
            z = bound_near + j * gridsize / 3 + gridsize / 6
            if abs(math.atan(x / z) * 2 * (180 / math.pi)) < fov_x and abs(
                    math.atan(y / z) * 2 * (180 / math.pi)) < fov_y:
                grid_centers.append((x, y, z))

    # up方向通过down进行定位，仍然是0.05（相机以下5cm）
    y = bound_up - gridsize / 6
    # y = bound_down + math.ceil((bound_up - bound_down) / gridsize) * gridsize - gridsize / 2
    for i in range(num_x * 3 - 1):
        for j in range(num_z * 3 - 1):
            x = bound_left + i * gridsize / 3 + gridsize / 6
            z = bound_near + j * gridsize / 3 + gridsize / 6
            if abs(math.atan(x / z) * 2 * (180 / math.pi)) < fov_x and abs(
                    math.atan(y / z) * 2 * (180 / math.pi)) < fov_y:
                grid_centers.append((x, y, z))
    return grid_centers


def get_rays(H, W, instrinsics):
    i, j = np.meshgrid(np.linspace(0, W - 1, W), np.linspace(0, H - 1, H))  # pytorch's meshgrid has indexing='ij'
    i = i[:, :, np.newaxis]
    j = j[:, :, np.newaxis]
    rays = np.concatenate(
        [(i - instrinsics[0][2]) / instrinsics[0][0], (j - instrinsics[1][2]) / instrinsics[1][1], np.ones_like(i)], -1)
    show = False
    if show == True:
        import matplotlib.pyplot as plt
        fig = plt.figure()
        # ax = fig.gca(projection="3d")
        ax = fig.add_subplot(projection='3d')
        ax.scatter(rays[:, :, 0], rays[:, :, 1], rays[:, :, 2])
        plt.show()

    return rays


def hit(rays, pcd):
    pass


def get_points(occupancy_range, voxel_size, min_distance=0.1):
    """
    occupancy_range: 感知范围#以房顶为中心4x4x2.9的范围，2.9表示向下0.1m到3米的范围
    voxel_size：单个格子尺寸
    """
    box_min_bound = occupancy_range[0]
    box_max_bound = occupancy_range[1]

    # num_x=math.ceil((-occupancy_range[0][0]-voxel_size/2)/voxel_size)+math.ceil((occupancy_range[1][0]-voxel_size/2)/voxel_size)+1
    # num_y=math.ceil((-occupancy_range[0][1]-voxel_size/2)/voxel_size)+math.ceil((occupancy_range[1][1]-voxel_size/2)/voxel_size)+1
    # num_z=math.ceil((-occupancy_range[0][2]-voxel_size/2)/voxel_size)+math.ceil((occupancy_range[1][2]-voxel_size/2)/voxel_size)+1

    # cube_resultion=(num_x,num_y,num_z)
    cube_resultion = (box_max_bound - box_min_bound + voxel_size / 2) / voxel_size

    x_grid = np.linspace(box_min_bound[0], box_max_bound[0] - voxel_size / 2., math.ceil(cube_resultion[0]))
    y_grid = np.linspace(box_min_bound[1], box_max_bound[1] - voxel_size / 2., math.ceil(cube_resultion[1]))
    z_grid = np.linspace(box_min_bound[2], box_max_bound[2] - voxel_size / 2., math.ceil(cube_resultion[2]))
    cube_grid = np.asarray(np.meshgrid(x_grid, y_grid, z_grid)).transpose(2, 1, 3, 0).reshape(-1, 3)
    return cube_grid.astype(np.float32)

    # x_grid=np.linspace(box_min_bound[0]+voxel_size/2.,box_max_bound[0]-voxel_size/2.,round(cube_resultion[0]))
    # y_grid=np.linspace(box_min_bound[1]+voxel_size/2.,box_max_bound[1]-voxel_size/2.,round(cube_resultion[1]))
    # z_grid=np.linspace(box_min_bound[2]+voxel_size/2.,box_max_bound[2]-voxel_size/2.,round(cube_resultion[2]))
    # cube_grid=np.asarray(np.meshgrid(x_grid,y_grid,z_grid)).transpose(1,2,3,0).reshape(-1,3)
    # return cube_grid.astype(np.float32)


def calCrossPoint(line_point, line_vector, plane_point, plane_vector):
    """calculate cross point between line and plane

    Args:
        line_point (np.shape(3, 1)): 直线上一点
        line_vector (np.shape(3, 1)): 直线方向
        plane_point (np.shape(3, 1)): 平面上一点
        plane_vector (np.shape(3, 1)): 平面法向量
    """
    dot_product = np.dot(line_vector, plane_vector)
    if (dot_product == 0):
        return [0, 0, 0]
    else:
        m = (plane_point[0] - line_point[0]) * plane_vector[0] + \
            (plane_point[1] - line_point[1]) * plane_vector[1] + \
            (plane_point[2] - line_point[2]) * plane_vector[2] / dot_product
    return line_point + line_vector * m


def crop_point_cloud(pcd, crop_range):
    bounding_box = o3d.geometry.AxisAlignedBoundingBox(crop_range[0], crop_range[1])
    return pcd.crop(bounding_box)


def grid_rays_to_labels(voxel_grid, camera_rays, sample_distance, total_points, voxel_size, H, W):
    # voxel_grid是lidar扫到的占用的格子，occupancy_voxel_gird是camera看得到的格子
    camera_rays = camera_rays.reshape(1, -1)  # 8472(Nx3)
    sample_distance = sample_distance.reshape(-1, 1)
    sample_rays = sample_distance @ camera_rays  # camera_rays表示图像平面每个点的世界坐标，与sample_rays相乘表示到摄像头之间的所有点
    camera_rays = camera_rays.reshape(-1, 3)  # 8472(Nx3)
    sample_rays_points = sample_rays.reshape(-1, 3)  # Nx3 #等角度扇形展开，密集采样
    # voxel_grid空间是一个预置的立方体空间，比如左右2m，上下？m，前后2m；sample_rays_points是相机的射线空间
    valid_mask = voxel_grid.check_if_included(o3d.utility.Vector3dVector(sample_rays_points))  # bev空间的mask
    valid_mask_temp = np.asarray(valid_mask).reshape(sample_distance.shape[0], camera_rays.shape[0])
    surface_index = valid_mask_temp.argmax(axis=0)

    occupancy_mask = np.zeros((sample_distance.shape[0], camera_rays.shape[0]), dtype=bool)  # 视锥体空间的mask
    visiable_mask = np.zeros((sample_distance.shape[0], camera_rays.shape[0]), dtype=bool)  # 视锥体空间的mask

    # empty_mask=np.ones((sample_distance.shape[0], camera_rays.shape[0]), dtype=bool)

    # visiable_mask_tensor[:surface_index_tensor+1]=torch.where(surface_index_tensor>0,        True       ,       False   )
    for i, val in enumerate(surface_index):
        if val == 0:  # 射线方向上没有点 即mesh对应处为空
            # empty_mask[:, i] = False
            continue
        else:
            visiable_mask[:val + 1, i] = True  # 表面和表面到相机之间的设置为true
            occupancy_mask[val, i] = True
    sample_rays_points = sample_rays_points.reshape(sample_distance.shape[0], camera_rays.shape[0], 3)  # 视锥体空间
    occupancy_points = sample_rays_points[occupancy_mask]  # surface,视锥体空间
    visiable_points = sample_rays_points[visiable_mask]  # surface前面的点
    # ray_empty_points = sample_rays_points[empty_mask]#射线打过去完全空洞，但是这个解决不了某些情况

    show_lable = False
    if show_lable == True:
        vis_pcd = o3d.geometry.PointCloud()
        origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0, 0, 0])
        vis_pcd.points = o3d.utility.Vector3dVector(occupancy_points)
        # points_num=np.array(camera_coord_mesh.points).shape[0]
        # camera_coord_mesh.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(points_num, 3)))
        # voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd, voxel_size=0.05)
        o3d.visualization.draw_geometries([vis_pcd, origin])

    occupancy_pcd = o3d.geometry.PointCloud()
    occupancy_pcd.points = o3d.utility.Vector3dVector(occupancy_points)  # 视锥体空间

    # occupancy_range = np.array([[-1.975, -1.425, 0.025], [2, 0.03, 4]])  # 1.5m
    occupancy_range = np.array([[-1.975, -0.125, 0.025], [2, 0.03, 4]])  # 1.5m
    occupancy_pcd = crop_point_cloud(occupancy_pcd, occupancy_range)

    new_robust_point = np.array([[occupancy_range[0][0], occupancy_range[0][1], occupancy_range[0][2]]])
    for point in new_robust_point:
        occupancy_pcd.points.append(point)


    # colors = np.zeros(occupancy_points.shape)
    # colors[:,2] = 255
    # occupancy_pcd.colors = o3d.utility.Vector3dVector(colors)
    # occupancy_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(occupancy_points.shape[0], 3)))

    # camera可以看见的格子数量
    occupancy_voxel_gird = o3d.geometry.VoxelGrid.create_from_point_cloud(occupancy_pcd, voxel_size=voxel_size)

    # 这一步骤有点问题
    occupancy_labels = occupancy_voxel_gird.check_if_included(o3d.utility.Vector3dVector(total_points))

    visiable_pcd = o3d.geometry.PointCloud()
    visiable_pcd.points = o3d.utility.Vector3dVector(visiable_points)
    # visiable_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(points_num, 3)))
    visiable_voxel_gird = o3d.geometry.VoxelGrid.create_from_point_cloud(visiable_pcd, voxel_size=voxel_size)
    visiable_labels = visiable_voxel_gird.check_if_included(o3d.utility.Vector3dVector(total_points))

    # ray_empty_pcd = o3d.geometry.PointCloud()
    # ray_empty_pcd.points = o3d.utility.Vector3dVector(ray_empty_points)
    # # ray_empty_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(points_num, 3)))
    # ray_empty_voxel_gird = o3d.geometry.VoxelGrid.create_from_point_cloud(ray_empty_pcd, voxel_size=voxel_size)
    # ray_empty_labels = ray_empty_voxel_gird.check_if_included(o3d.utility.Vector3dVector(total_points))

    return occupancy_labels, visiable_labels  # ,ray_empty_labels


def rays_to_labels(voxel_grid, camera_rays, sample_distance, total_points, voxel_size, H, W):
    # voxel_grid是lidar扫到的占用的格子，occupancy_voxel_gird是camera看得到的格子
    camera_rays = camera_rays.reshape(1, -1)
    sample_distance = sample_distance.reshape(-1, 1)
    sample_rays = sample_distance @ camera_rays  # camera_rays表示图像平面每个点的世界坐标，与sample_rays相乘表示到摄像头之间的所有点
    sample_rays_points = sample_rays.reshape(-1, 3)  # Nx3 #等角度扇形展开，密集采样
    # voxel_grid空间是一个预置的立方体空间，比如左右2m，上下？m，前后2m；sample_rays_points是相机的射线空间
    valid_mask = voxel_grid.check_if_included(o3d.utility.Vector3dVector(sample_rays_points))  # bev空间的mask
    valid_mask_temp = np.asarray(valid_mask).reshape(sample_distance.shape[0], H * W)
    surface_index = valid_mask_temp.argmax(axis=0)

    occupancy_mask = np.zeros((sample_distance.shape[0], H * W), dtype=bool)  # 视锥体空间的mask
    visiable_mask = np.zeros((sample_distance.shape[0], H * W), dtype=bool)  # 视锥体空间的mask

    # visiable_mask_tensor[:surface_index_tensor+1]=torch.where(surface_index_tensor>0,        True       ,       False   )
    for i, val in enumerate(surface_index):
        if val == 0:  # 射线方向上没有点 即mesh对应处为空
            continue
        else:
            visiable_mask[:val + 1, i] = True  # 表面和表面到相机之间的设置为true
            occupancy_mask[val, i] = True
    sample_rays_points = sample_rays_points.reshape(sample_distance.shape[0], H * W, 3)  # 视锥体空间
    occupancy_points = sample_rays_points[occupancy_mask]  # surface,视锥体空间
    visiable_points = sample_rays_points[visiable_mask]  # surface前面的点

    show_lable = True
    if show_lable == True:
        vis_pcd = o3d.geometry.PointCloud()
        origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0, 0, 0])
        vis_pcd.points = o3d.utility.Vector3dVector(occupancy_points)
        # points_num=np.array(camera_coord_mesh.points).shape[0]
        # camera_coord_mesh.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(points_num, 3)))
        # voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd, voxel_size=0.05)
        o3d.visualization.draw_geometries([vis_pcd, origin])

    occupancy_pcd = o3d.geometry.PointCloud()
    occupancy_pcd.points = o3d.utility.Vector3dVector(occupancy_points)  # 视锥体空间
    # colors = np.zeros(occupancy_points.shape)
    # colors[:,2] = 255
    # occupancy_pcd.colors = o3d.utility.Vector3dVector(colors)
    # occupancy_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(occupancy_points.shape[0], 3)))

    # camera可以看见的格子数量
    occupancy_voxel_gird = o3d.geometry.VoxelGrid.create_from_point_cloud(occupancy_pcd, voxel_size=voxel_size)

    occupancy_labels = occupancy_voxel_gird.check_if_included(o3d.utility.Vector3dVector(total_points))

    visiable_pcd = o3d.geometry.PointCloud()
    visiable_pcd.points = o3d.utility.Vector3dVector(visiable_points)
    # visiable_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(points_num, 3)))
    visiable_voxel_gird = o3d.geometry.VoxelGrid.create_from_point_cloud(visiable_pcd, voxel_size=voxel_size)
    visiable_labels = visiable_voxel_gird.check_if_included(o3d.utility.Vector3dVector(total_points))

    return occupancy_labels, visiable_labels


# def gen_occupancy_mesh(mesh, pose_path, extrinsics, instrinsic,stereo_extrinsics,save_path,\
#         occupancy_range=np.array([[-2, -0.31, -0], [2,0.09, 2]]), \
#         voxel_size = 0.05, min_distance=0.1):
#     """ 注意：3D坐标系是open3d所在的坐标系
#     """
#
#     #np.array([[-2., -2., -3.], [2., 2., -0.1]])
#     #[-2, -0.12, 0.1], [2,0.08, 3] 备份
#
#     # 从lidar坐标系转到opencv坐标系的转换矩阵
#     # transform_mat = np.zeros((4,4), dtype=np.float32)
#     # rotation_x = R.from_euler('x', [180], degrees=True).as_matrix().squeeze()
#
#
#
#     # rotation_y = R.from_euler('z', [-90], degrees=True).as_matrix().squeeze()#lidar坐标系转opencv？
#     # rotation_x = R.from_euler('y', [0], degrees=True).as_matrix().squeeze()
#     # transform_mat[:3,:3] = rotation_y@rotation_x
#     # transform_mat[:4, 3] = [0,0,0,1]
#
#     # # 从opencv坐标系转到mesh坐标系的转换矩阵
#     # transform_mat1 = np.zeros((4,4), dtype=np.float32)
#     # rotation_x1 = R.from_euler('y', [?], degrees=True).as_matrix().squeeze()
#     # rotation_y1 = R.from_euler('z', [?], degrees=True).as_matrix().squeeze()#
#     # transform_mat1[:3,:3] = rotation_x1@rotation_y1
#     # transform_mat1[:4, 3] = [0,0,0,1]
#
#     H, W = 376, 672
#     # instrinsic = np.loadtxt(osp.join(src_dir, 'instrinsics.txt'))
#
#     # instrinsics=np.zeros((3, 3),dtype=np.float64)
#     # instrinsics[2,2]=1.0
#     # instrinsics[0, 2] =instrinsic[2]
#     # instrinsics[1, 2] =instrinsic[3]
#     # instrinsics[0, 0] =instrinsic[0]
#     # instrinsics[1, 1] =instrinsic[1]
#
#     # K = instrinsic
#     # K_extend=np.zeros((4, 4),dtype=np.float64)
#     # K_extend[3,3]=1.0
#     # K_extend[:3,:3]=K
#
#     # print(K_extend)
#
#     # from colmap
#     # instrinsics = np.array([
#     #     [instrinsics[0], 0, instrinsics[2]],
#     #     [0, instrinsics[1], instrinsics[3]],
#     #     [0,0,1]]).astype(np.float64)
#     # from gongzhuang
#     # instrinsics =  np.array([
#     #             [321.4475202058989, 0, 649.3810871940957],
#     #             [0, 321.5622469758229, 485.7818157326032],
#     #             [0, 0, 1]]).astype(np.float64) / 2.0
#     # stereo_extrinsics = stereo_extrinsics @ transform_mat
#
#     camera_rays = get_rays(H, W, instrinsic)
#     max_distance = 6.0
#     sample_distance = np.linspace(min_distance, max_distance, round((max_distance - min_distance) / 0.01))
#
#     total_points = get_points(occupancy_range, voxel_size, min_distance=min_distance)
#     # num_of_sample_poinst = total_points.shape[0] * 2
#
#     vis_pcd = o3d.geometry.PointCloud()
#     origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0,0,0])
#
#     dst_dir = osp.join(src_path, 'occupancy_label')
#     os.makedirs(dst_dir, exist_ok=True)
#
#     Twl=read_pose(pose_path)
#     Tlc=extrinsics#明明是cam到lidar的外参
#
#
#
#     pose = Twl@ Tlc#@ transform_mat #这里考虑了camera外参Tlc,#这里是pose转换到了opencv坐标系？
#     # right_pose = pose @ stereo_extrinsics#Tw_lelf@Tleft_right=Tw_right
#
#     # translation = pose[:3, 3:4]
#     # position = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1, origin=translation)
#
#     # right_translation = right_pose[:3, 3:4]
#     # right_position = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1, origin=right_translation)
#
#
#
#     # 更改坐标系为当前帧坐标系（open3d表示）
#     # camera_coord_mesh = copy.deepcopy(mesh)
#     # o3d.visualization.draw_geometries([pcd])
#     pcd_copy = copy.deepcopy(pcd)#Twl
#     pcd_copy.transform(np.linalg.inv(pose))#根据外参转换,表示对点云进行旋转和平移 ,即Twc的逆*points,即Tcw*points
#     # o3d.visualization.draw_geometries([pcd_copy,origin])
#
#
#
#     cropped_mesh = crop_point_cloud(pcd_copy, occupancy_range)#面片#以cam坐标系去筛选范围内的点
#     # src_min_bound = cropped_mesh.get_min_bound()
#     # src_max_bound = cropped_mesh.get_max_bound()
#     # print('cloud min bound: ', src_min_bound)
#     # print('cloud max bound: ', src_max_bound)
#     # o3d.visualization.draw_geometries([cropped_mesh])
#
#     # bounding_box = mesh.get_axis_aligned_bounding_box()
#     # sample_voxel_size = 0.01
#     # num_of_points = int((occupancy_range[1][0] - occupancy_range[0][0]) / sample_voxel_size * \
#     #                     (occupancy_range[1][1] - occupancy_range[0][1]) / sample_voxel_size * \
#     #                     (occupancy_range[1][2] - occupancy_range[0][2]) / sample_voxel_size)
#     # cropped_pcd = cropped_mesh.sample_points_uniformly(number_of_points=int(num_of_points))
#     # #点云如果已经是points，则不需要通过sample_points_uniformly生成points
#
#     # o3d.visualization.draw_geometries([cropped_pcd])
#
#     voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(cropped_mesh, voxel_size=voxel_size)#整个局部空间所有的体素占用情况
#     # o3d.visualization.draw_geometries([voxel_grid])
#
#
#     # current left img
#     left_camera_rays = camera_rays.reshape(-1, 3)
#     left_occupancy_labels, left_visiable_labels = rays_to_labels(voxel_grid, left_camera_rays, sample_distance, total_points, voxel_size, H, W)
#    #此时left_occupancy_labels已经是我们可以使用的gt了，但是需要与右边相机计算出并集
#
#
#     # current right img
#     temp_pcd = o3d.geometry.PointCloud()
#     temp_pcd.points = o3d.utility.Vector3dVector(left_camera_rays)
#     temp_pcd.transform(stereo_extrinsics)#                Trl为外参  ，即左相机到右相机的外参  Trl@ points_L=Tr
#     right_camera_rays = np.asarray(temp_pcd.points)
#     right_occupancy_labels, right_visiable_labels = rays_to_labels(voxel_grid, right_camera_rays, sample_distance, total_points, voxel_size, H, W)
#
#
#     occupancy_labels = np.bitwise_or(left_occupancy_labels, right_occupancy_labels)#bev空间，仅遮挡物为True
#     # visiable_labels = np.bitwise_or(left_visiable_labels, right_visiable_labels)#bev空间包含空气和遮挡物
#     # non_occupancy_lables = np.bitwise_xor(occupancy_labels, visiable_labels)#前方空气类别
#
#     # -1：视野范围外(或无穷远)，或被障碍物遮挡对应的区域； 1：障碍物表面所在区域；2：障碍物到相机之间的空间
#     labels = np.zeros_like(occupancy_labels, dtype=np.int8)
#     labels[occupancy_labels] = 1
#     # labels[non_occupancy_lables] = 2
#
#     # test = np.bitwise_and(occupancy_labels, non_occupancy_lables)
#
#     # save
#     filename = save_path+'/'+pose_path.split('/')[-1].replace('txt','npy')
#     dst_path = osp.join(dst_dir, filename)
#     # np.save(dst_path, labels)
#     # aa=np.load('/raid/wqx/grids/eco_occ/data/stereo_nanjing/scenes/scnen_001/无人场景/12cm/occupancy/data/0.npy')
#
#
#     show_lable = True
#     if show_lable == True:
#
#
#         vis_pcd.points = o3d.utility.Vector3dVector(total_points[occupancy_labels])#这里显示了遮挡物
#         # o3d.visualization.draw_geometries([vis_pcd, origin])
#
#         voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd, voxel_size=0.05)
#         o3d.visualization.draw_geometries([voxel_grid,origin])
#
#         # points_num=np.array(vis_pcd.points ).shape[0]
#         # vis_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(points_num, 3)))
#         # voxel_grid_color = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd, voxel_size=0.05)
#         # o3d.visualization.draw_geometries([voxel_grid_color, origin])
#
#
#
#     print(1)

def gen_occupancy_pcd(pcd, pose, instrinsic, pose_path, stereo_extrinsics, save_path, img_size, \
                      occupancy_range, voxel_size, camera_rays, min_distance=0.05):
    """ 注意：3D坐标系是open3d所在的坐标系,但这里我修改成了相机坐标系了，生成的标签以相机为准
    """

    # 从lidar坐标系转到opencv坐标系的转换矩阵
    # transform_mat = np.zeros((4,4), dtype=np.float32)
    # rotation_x = R.from_euler('x', [90], degrees=True).as_matrix().squeeze()
    # rotation_y = R.from_euler('y', [-90], degrees=True).as_matrix().squeeze()#lidar坐标系转opencv？
    # # rotation_x = R.from_euler('z', [180], degrees=True).as_matrix().squeeze()
    # transform_mat[:3,:3] = rotation_y@rotation_x
    # transform_mat[:4, 3] = [0,0,0,1]

    H, W = 925, 1240
    # instrinsic = np.loadtxt(osp.join(src_dir, 'instrinsics.txt'))

    # instrinsics=np.zeros((3, 3),dtype=np.float64)
    # instrinsics[2,2]=1.0
    # instrinsics[0, 2] =instrinsic[2]
    # instrinsics[1, 2] =instrinsic[3]
    # instrinsics[0, 0] =instrinsic[0]
    # instrinsics[1, 1] =instrinsic[1]

    # K = instrinsic
    # K_extend=np.zeros((4, 4),dtype=np.float64)
    # K_extend[3,3]=1.0
    # K_extend[:3,:3]=K

    # print(K_extend)

    # from colmap
    # instrinsics = np.array([
    #     [instrinsics[0], 0, instrinsics[2]],
    #     [0, instrinsics[1], instrinsics[3]],
    #     [0,0,1]]).astype(np.float64)
    # from gongzhuang
    # instrinsics =  np.array([
    #             [321.4475202058989, 0, 649.3810871940957],
    #             [0, 321.5622469758229, 485.7818157326032],
    #             [0, 0, 1]]).astype(np.float64) / 2.0
    # stereo_extrinsics = stereo_extrinsics @ transform_mat

    # camera_rays = get_rays(H, W, instrinsic)#像素坐标系转相机坐标系
    camera_rays = np.array(camera_rays)

    max_distance = 1.0
    sample_distance = np.linspace(min_distance, max_distance, round((max_distance - min_distance) / 0.01))  # 100份儿切段

    total_points = get_points(occupancy_range, voxel_size, min_distance=min_distance)

    ##只用于生成相机fov内的点
    # total_points_fov=total_points.copy()
    # mask = np.zeros((total_points_fov.shape[0]), dtype=bool)#视锥体空间的mask
    # for i in range(total_points_fov.shape[0]):
    #     x,y,z=total_points_fov[i,:]
    #     if abs(math.atan(x/z)*2* (180/math.pi))<118.66 and abs(math.atan(y/z)*2* (180/math.pi))<103.03 :
    #         mask[i]=1
    # total_points_fov=total_points_fov[mask] 
    ##

    vis_pcd = o3d.geometry.PointCloud()
    origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0, 0, 0])
    # os.makedirs(src_path, exist_ok=True)

    # 更改坐标系为当前帧坐标系（open3d表示）
    # camera_coord_mesh = copy.deepcopy(mesh)
    # o3d.visualization.draw_geometries([pcd])
    pcd_copy = copy.deepcopy(pcd)  # Twl
    # o3d.visualization.draw_geometries([pcd_copy,origin])
    pcd_copy.transform(pose)  # 根据外参转换,表示对点云进行旋转和平移 ,即Twc的逆*points,即Tcw*points
    # o3d.visualization.draw_geometries([pcd_copy,origin])

    # y_threshold1=-0.5
    # points=np.asarray(pcd_copy.points)
    # indices_ground=np.where(points[:,1]>y_threshold1)[0]
    # pcd_copy=pcd_copy.select_by_index(indices_ground)

    # occupancy_range_ = np.array([[-1.975, -1.425, 0.025], [2, 0.03, 4]])
    occupancy_range_ = np.array([[-1.975, -0.125, 0.025], [2, 0.03, 4]])

    cropped_pcd = crop_point_cloud(pcd_copy, occupancy_range_)  # 面片#以cam坐标系去筛选范围内的点
    # o3d.io.write_point_cloud("点云彩色.pcd", cropped_pcd)
    # points=np.array(cropped_pcd.points )
    # points_num=np.array(cropped_pcd.points ).shape[0]
    # cropped_pcd.colors=o3d.utility.Vector3dVector(np.random.uniform(0.5, 0.5, size=(points_num, 3)))
    # o3d.io.write_point_cloud("点云灰色.pcd", cropped_pcd)
    # o3d.visualization.draw_geometries([cropped_pcd,origin])
    new_robust_point = np.array([[occupancy_range_[0][0], occupancy_range_[0][1], occupancy_range_[0][2]]])
    for point in new_robust_point:
        cropped_pcd.points.append(point)

    voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(cropped_pcd, voxel_size)  # 整个局部空间所有的体素占用情况
    # bbox = voxel_grid.get_axis_aligned_bounding_box()
    # min_bound = np.array(bbox.min_bound)#栅格的边
    # max_bound = np.array(bbox.max_bound)#栅格的外边
    # o3d.io.write_voxel_grid("栅格.ply", voxel_grid)
    # o3d.visualization.draw_geometries([voxel_grid,origin])

    ##只生成fov内的栅格状态，必须使用total_points_fov
    occupancy_labels = voxel_grid.check_if_included(o3d.utility.Vector3dVector(total_points))
    # vis_pcd.points = o3d.utility.Vector3dVector(total_points_fov[occupancy_labels])
    # points_num=np.array(vis_pcd.points ).shape[0]
    # vis_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0.5, 0.5, size=(points_num, 3)))
    # voxel_grid_color = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd, voxel_size=0.05)
    # o3d.visualization.draw_geometries([voxel_grid_color, origin])
    # o3d.io.write_voxel_grid("fov内栅格.ply", voxel_grid_color)
    ##

    # current left img
    left_camera_rays = camera_rays
    left_occupancy_labels, left_visiable_labels = grid_rays_to_labels(voxel_grid, left_camera_rays, sample_distance,
                                                                      total_points, voxel_size, img_size[1],
                                                                      img_size[0])

    # temp_pcd = o3d.geometry.PointCloud()
    # temp_pcd.points = o3d.utility.Vector3dVector(left_camera_rays)
    # temp_pcd.transform(stereo_extrinsics)#                Trl为外参  ，即左相机到右相机的外参  Trl@ points_L=Tr
    # right_camera_rays = np.asarray(temp_pcd.points)
    # right_occupancy_labels, right_visiable_labels = grid_rays_to_labels(voxel_grid, right_camera_rays, sample_distance, total_points, voxel_size, img_size[1], img_size[0])

    # occupancy_labels = np.bitwise_or(left_occupancy_labels, right_occupancy_labels)#表面遮挡物

    occupancy_labels = np.bitwise_or(left_occupancy_labels, left_occupancy_labels)  # 表面遮挡物
    occupancy_labels[0] = 0#这句加上


    # visiable_labels = np.bitwise_or(left_visiable_labels, right_visiable_labels)#包含空气和遮挡物
    # non_occupancy_lables = np.bitwise_xor(occupancy_labels, visiable_labels)#只包含空气类别
    # # ray_empty_lables = np.bitwise_or(left_ray_empty_labels, right_ray_empty_labels)#包含无点云区域解决不了，不清楚是感知区域内是扫到了没点云还是没扫到导致没点云

    labels = np.zeros_like(occupancy_labels, dtype=np.int8)
    labels[occupancy_labels] = 1

    # labels[non_occupancy_lables] = 2
    # labels[ray_empty_lables] = 3

    # save
    # camera_path = pose_path.split('/')[-2]
    img_path_name = pose_path.split('/')[-1].split('.')[-2][:-5]
    save_path = os.path.join(save_path, img_path_name) + '_occ.npy'
    np.save(save_path, labels)
    # aa=np.load('/raid/wqx/grids/eco_occ/data/stereo_nanjing/scenes/scnen_001/无人场景/12cm/occupancy/data/0.npy')

    show_lable = False
    if show_lable == True:
        # 默认显示右图fusion图
        vis_pcd.points = o3d.utility.Vector3dVector(total_points[occupancy_labels])  # 这里显示了遮挡物
        # o3d.visualization.draw_geometries([vis_pcd, origin])
        voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd, voxel_size)
        # o3d.visualization.draw_geometries([voxel_grid,origin])
        points_num = np.array(vis_pcd.points).shape[0]
        vis_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0.5, 0.5, size=(points_num, 3)))
        voxel_grid_color = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd, voxel_size=0.05)
        o3d.visualization.draw_geometries([voxel_grid_color, origin])
        o3d.io.write_voxel_grid("left.ply", voxel_grid_color)

        # 显示右图
        # vis_pcd.points = o3d.utility.Vector3dVector(total_points[occupancy_labels1])
        # o3d.visualization.draw_geometries([vis_pcd, origin])
        # voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd, voxel_size)
        # o3d.visualization.draw_geometries([voxel_grid,origin])
        # points_num=np.array(vis_pcd.points ).shape[0]
        # vis_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0.5, 0.5, size=(points_num, 3)))
        # voxel_grid_color = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd, voxel_size=0.05)
        # o3d.visualization.draw_geometries([voxel_grid_color, origin])
        # o3d.io.write_voxel_grid("right.ply", voxel_grid_color)

        # 显示fusion图：左+右
        # vis_pcd.points = o3d.utility.Vector3dVector(total_points[occupancy_labels2])
        # voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd, voxel_size)
        # points_num=np.array(vis_pcd.points ).shape[0]
        # vis_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0.5, 0.5, size=(points_num, 3)))
        # voxel_grid_color = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd, voxel_size=0.05)
        # o3d.visualization.draw_geometries([voxel_grid_color, origin])
        # o3d.io.write_voxel_grid("fusion.ply", voxel_grid_color)


class Calibrate():
    def __init__(self, cam_mats, dist_corfs, rot_mat, trans_vec):
        self.cam_mats = cam_mats
        self.rot_mat = rot_mat
        self.dist_corfs = dist_corfs
        self.rot_mat_inv = np.linalg.inv(self.rot_mat)
        self.trans_vec = trans_vec
        self.trans_vec_inv = -self.trans_vec


class Stereo():
    """双目工装数据处理，如yuv2rgb，矫正等
       注意：
           0：左目；
           1：右目；
           module1：模组1，基线为12cm；
           module2：模组2，基线为16cm；
           module3：模组3，基线为20cm；
    """

    def __init__(self, img_size):
        """
        args:
            img_size: (width, height)
        """
        self.img_size = img_size

        # 模组1
        self.cam_mats_module1 = {   # 内参
            '0': np.array([
                [321.4475202058989, 0, 649.3810871940957],
                [0, 321.5622469758229, 485.7818157326032],
                [0, 0, 1]], dtype=np.float64),
            '1': np.array([
                [320.0634980848295, 0, 639.8470471118967],
                [0, 320.4112267666799, 471.8594892100028],
                [0, 0, 1]], dtype=np.float64)}
        self.dist_corfs_module1 = { # 畸变参数
            '0': np.array([[
                0.7825840518623307, 0.7857393546334689, 4.710335937870699e-05, \
                0.0003712693693960966, 0.03567583162711111, 0.8190525500510427, \
                0.8333147294194869, 0.1129123410465364]], dtype=np.float64).reshape(8, 1),
            '1': np.array([[
                0.6232572014846256, 0.6784064946488555, 2.382494241423335e-05, \
                -1.035138953403252e-05, 0.02699721217089060, 0.6600461860713133, \
                0.7213044525030704, 0.09141727985805929]], dtype=np.float64).reshape(8, 1)}
        self.rot_mat_module1 = np.array([   # 外参
            [0.9999757522946446, -0.0058246186402728, 0.003816888844214655],
            [0.00583457540184895, 0.9999795916735226, -0.002602684446733787],
            [-0.0038016513035578113, 0.002624891263370244, 0.9999893286396689]], dtype=np.float64)
        self.trans_vec_module1 = np.array([0.11965661932361803, 0.00021152199765414763, -0.0006454811417098998],
                                          dtype=np.float64)

        # 模组2
        # 内参
        self.cam_mats_module2 = {
            '0': np.array([
                [321.4865556576209, 0, 646.1025610235099],
                [0, 321.0097496624281, 466.6922027997995],
                [0, 0, 1]], dtype=np.float64),
            '1': np.array([
                [320.8912131565299, 0, 644.2261177625821],
                [0, 321.0097496624281, 473.6775290921570],
                [0, 0, 1]], dtype=np.float64)}
        # 畸变参数
        self.dist_corfs_module2 = {
            '0': np.array([[
                0.7825840518623307, 0.7857393546334689, 4.710335937870699e-05, \
                0.0003712693693960966, 0.03567583162711111, 0.8190525500510427, \
                0.8333147294194869, 0.1129123410465364]], dtype=np.float64).reshape(8, 1),
            '1': np.array([[
                0.6232572014846256, 0.6784064946488555, 2.382494241423335e-05, \
                -1.035138953403252e-05, 0.02699721217089060, 0.6600461860713133, \
                0.7213044525030704, 0.09141727985805929]], dtype=np.float64).reshape(8, 1)}
        # 外参 第一次标注
        self.rot_mat_module2 = np.array([
            [0.999493017790356, 0.00492971851390951, -0.03145481304505169],
            [-0.005324917470504143, 0.9999077852467023, -0.012492648116618496],
            [0.03139032720851848, 0.012653808849787075, 0.9994271001325378]], dtype=np.float64)
        self.trans_vec_module2 = np.array([0.15927528810552818, -9.548906403178666e-05, 0.0006838184440500877],
                                          dtype=np.float64)

        # 外参 0524更新
        # self.rot_mat_module1 = np.array([
        #     [0.9997485408519743, 0.005319338649918189, -0.021784391216652727],
        #     [-0.005480530844647172, 0.999957995993991, -0.007346429700653558],
        #     [0.02174439803750752, 0.007463972401694711, 0.9997357002077987]], dtype=np.float64)
        # self.trans_vec_module1 = np.array([0.16542854941848475, 0.0009154233474289518, -0.008772485900331202], dtype=np.float64)

        # 模组3
        self.cam_mats_module3 = {
            '0': np.array([
                [321.1236583095948, 0, 647.8429636689347],
                [0, 321.2383027147355, 466.6727495667608],
                [0, 0, 1]], dtype=np.float64),
            '1': np.array([
                [321.6200316569539, 0, 656.1731739940485],
                [0, 321.8193576337943, 483.7313501751819],
                [0, 0, 1]], dtype=np.float64)}
        self.dist_corfs_module3 = {
            '0': np.array([[
                0.7825840518623307, 0.7857393546334689, 4.710335937870699e-05, \
                0.0003712693693960966, 0.03567583162711111, 0.8190525500510427, \
                0.8333147294194869, 0.1129123410465364]], dtype=np.float64).reshape(8, 1),
            '1': np.array([[
                0.6232572014846256, 0.6784064946488555, 2.382494241423335e-05, \
                -1.035138953403252e-05, 0.02699721217089060, 0.6600461860713133, \
                0.7213044525030704, 0.09141727985805929]], dtype=np.float64).reshape(8, 1)}
        self.rot_mat_module3 = np.array([
            [0.9999541317625686, 0.0005857210487854145, -0.009559879801184503],
            [-0.0006550899245874188, 0.9999734695072935, -0.0072547321610909685],
            [0.009555376923533148, 0.007260661980251753, 0.9999279861867305]], dtype=np.float64)
        self.trans_vec_module3 = np.array([0.1997465801714262, -0.0002328099129024403, 0.0007567377143897466],
                                          dtype=np.float64)

        # zed 内外参 畸变参数 对应 module_id = 4
        self.cam_mats_module4 = {
            '0': np.array([
                [1061.6700, 0, 1110.6600],
                [0, 1061.1400, 614.6030],
                [0, 0, 1]], dtype=np.float64),
            '1': np.array([
                [1063.1801, 0, 1074.2500],
                [0, 1062.8101, 626.4870],
                [0, 0, 1]], dtype=np.float64)}
        self.dist_corfs_module4 = {
            '0': np.array([[
                -0.0443, 0.0139, 0.0001, -0.0004]], dtype=np.float64).reshape(4, 1),
            '1': np.array([[
                -0.0452, 0.0126, 0.0001, -0.0004]], dtype=np.float64).reshape(4, 1)}
        self.rot_mat_module4 = R.from_rotvec(np.array([0.0026, -0.0001, 0.0002])).as_matrix()
        self.trans_vec_module4 = np.array([120.1240, -0.2858, 0.1092], dtype=np.float64) / 1000.0

        # zed 内外参 畸变参数 对应 module_id = 5,分辨率672x376
        self.cam_mats_module5 = {
            '0': np.array([
                [265.4175, 0, 336.415],
                [0, 265.285, 185.15075],
                [0, 0, 1]], dtype=np.float64),
            '1': np.array([
                [265.795, 0, 327.3125],
                [0, 265.7025, 188.12175],
                [0, 0, 1]], dtype=np.float64)}

        self.dist_corfs_module5 = {
            '0': np.array([[
                -0.0443, 0.0139, 0.0001, -0.0004]], dtype=np.float64).reshape(4, 1),

            '1': np.array([[
                -0.0452, 0.0126, 0.0001, -0.0004]], dtype=np.float64).reshape(4, 1)}

        self.rot_mat_module5 = R.from_rotvec(np.array([0.0026, -0.0001, 0.0002])).as_matrix()
        self.trans_vec_module5 = np.array([120.1240, -0.2858, 0.1092], dtype=np.float64) / 1000.0

        # zed 内外参 畸变参数 对应 module_id = 5,分辨率672x376
        self.cam_mats_module6 = {
            '0': np.array([
                [367.6901181887478, 0, 620.0],
                [0, 367.6901181887478, 462.5],
                [0, 0, 1]], dtype=np.float64),
            '1': np.array([
                [367.6901181887478, 0, 620.0],
                [0, 367.6901181887478, 462.5],
                [0, 0, 1]], dtype=np.float64)}

        self.dist_corfs_module6 = {
            '0': np.array([[
                0, 0, 0, 0]], dtype=np.float64).reshape(4, 1),

            '1': np.array([[
                0, 0, 0, 0]], dtype=np.float64).reshape(4, 1)}

        self.rot_mat_module6 = R.from_rotvec(np.array([0.0, 0.0, 0.0])).as_matrix()
        self.trans_vec_module6 = np.array([120, 0, 0], dtype=np.float64) / 1000.0

        dist_corfs_zero = {
            '0': np.zeros((4, 1), dtype=np.float64),
            '1': np.zeros((4, 1), dtype=np.float64)
        }

        self.calib_module1 = Calibrate(self.cam_mats_module1, dist_corfs_zero, self.rot_mat_module1,
                                       self.trans_vec_module1)
        self.calib_module2 = Calibrate(self.cam_mats_module2, dist_corfs_zero, self.rot_mat_module2,
                                       self.trans_vec_module2)
        self.calib_module3 = Calibrate(self.cam_mats_module3, dist_corfs_zero, self.rot_mat_module3,
                                       self.trans_vec_module3)
        self.calib_module4 = Calibrate(self.cam_mats_module4, dist_corfs_zero, self.rot_mat_module4,
                                       self.trans_vec_module4)
        self.calib_module5 = Calibrate(self.cam_mats_module5, dist_corfs_zero, self.rot_mat_module5,
                                       self.trans_vec_module5)
        self.calib_module6 = Calibrate(self.cam_mats_module6, dist_corfs_zero, self.rot_mat_module6,
                                       self.trans_vec_module6)

    def yuv2bgr(self, filepath, save_dir=None, camera_id=None):
        """
        :param filepath: 待处理 YUV 视频的名字
        :param save_dir: 转换为bgr图像的保存路径
        :camera_id: 摄像机id, 支持 ‘0’：左目；‘1’：右目
        :return: None
        """
        bgr_img = np.zeros((960, 1280, 3))
        with open(filepath, 'rb') as f:
            yuvdata = np.fromfile(f, dtype=np.uint8)
            try:
                bgr_img = cv.cvtColor(yuvdata.reshape((960 * 3 // 2, 1280)), cv.COLOR_YUV2BGR_NV12)
                filename = filepath.split("/")[-1]
                if save_dir is not None:
                    if camera_id is not None:
                        camera_data = 'image_02/data' if camera_id == 0 else 'image_03/data'
                    else:
                        camera_data = ''
                    save_dir = os.path.join(save_dir, camera_data)
                    os.makedirs(save_dir, exist_ok=True)
                    savepath = os.path.join(save_dir, filename.replace("yuv", "jpg"))
                    cv.imwrite(savepath, bgr_img)
            except:
                logging.info("file {} can not open".format(filepath))
        return bgr_img

    def stereo_rectify(self, img0, img1, cali_params, show_results=False):
        """
        args:
            img0: 左目图像
            img1: 右目图像
            cali_params: 矫正参数
        """
        R0, R1, P0, P1, Q, _, _ = cv.stereoRectify(cali_params.cam_mats['0'],
                                                   cali_params.dist_corfs['0'],
                                                   cali_params.cam_mats['1'],
                                                   cali_params.dist_corfs['1'],
                                                   self.img_size,
                                                   cali_params.rot_mat,
                                                   cali_params.trans_vec,
                                                   )

        map00, map01 = cv.initUndistortRectifyMap(cali_params.cam_mats['0'], cali_params.dist_corfs['0'], R0, P0,
                                                  self.img_size, cv.CV_32FC1)
        map10, map11 = cv.initUndistortRectifyMap(cali_params.cam_mats['1'], cali_params.dist_corfs['1'], R1, P1,
                                                  self.img_size, cv.CV_32FC1)

        undistorted_rectify0 = cv.remap(img0, map00, map01, cv.INTER_LINEAR)
        undistorted_rectify1 = cv.remap(img1, map10, map11, cv.INTER_LINEAR)

        undistorted_rectify_cat = np.concatenate((undistorted_rectify0, undistorted_rectify1), axis=1)
        undistorted_rectify_sub = np.subtract(undistorted_rectify1.astype(np.float32),
                                              undistorted_rectify0.astype(np.float32))
        # undistorted_rectify_sub[undistorted_rectify_sub < 0] = 255
        # undistorted_rectify_sub = np.abs(undistorted_rectify_sub).astype(np.uint8)
        undistorted_rectify_abs = np.abs(
            np.subtract(undistorted_rectify1.astype(np.float32), undistorted_rectify0.astype(np.float32))).astype(
            np.uint8)
        undistorted_rectify_abs_gray = cv.cvtColor(undistorted_rectify_abs, cv.COLOR_BGR2GRAY)
        undistorted_rectify_abs_mean = np.mean(undistorted_rectify_sub, axis=2)
        undistorted_rectify_abs_1 = undistorted_rectify_abs_mean.copy()
        undistorted_rectify_abs_1[undistorted_rectify_abs_1 > 0] = 255
        undistorted_rectify_abs_1[undistorted_rectify_abs_1 < 0] = 0

        if show_results == True:
            undistorted_rectify_cat[100, :] = [0, 0, 255]
            undistorted_rectify_cat[200, :] = [0, 0, 255]
            undistorted_rectify_cat[300, :] = [0, 0, 255]
            # undistorted_rectify_cat[400, :] = [0, 0, 255]
            # undistorted_rectify_cat[500, :] = [0, 0, 255]
            # undistorted_rectify_cat[600, :] = [0, 0, 255]
            # undistorted_rectify_cat[700, :] = [0, 0, 255]
            # undistorted_rectify_cat[800, :] = [0, 0, 255]
            # undistorted_rectify_cat[900, :] = [0, 0, 255]

            cv.namedWindow('src img0', cv.WINDOW_NORMAL)
            cv.namedWindow('src img1', cv.WINDOW_NORMAL)
            cv.namedWindow('undistorted img0', cv.WINDOW_NORMAL)
            cv.namedWindow('undistorted img1', cv.WINDOW_NORMAL)
            cv.namedWindow('undistorted_rectify_cat', cv.WINDOW_NORMAL)
            cv.namedWindow('undistorted_rectify_abs', cv.WINDOW_NORMAL)
            cv.namedWindow('undistorted_rectify_abs_gray', cv.WINDOW_NORMAL)

            cv.imshow('src img0', img0)
            cv.imshow('src img1', img1)
            cv.imshow('undistorted img0', undistorted_rectify0)
            cv.imshow('undistorted img1', undistorted_rectify1)
            cv.imshow('undistorted_rectify_abs', undistorted_rectify_abs)
            cv.imshow('undistorted_rectify_cat', undistorted_rectify_cat)

            cv.imshow('undistorted_rectify_abs_gray', undistorted_rectify_abs_gray)
            cv.waitKey(0)
            print(1)

        if show_results == False:
            undistorted_rectify1 = cv.cvtColor(undistorted_rectify1, cv.COLOR_BGRA2RGB)
            undistorted_rectify0 = cv.cvtColor(undistorted_rectify0, cv.COLOR_BGRA2RGB)

            plt.subplot(231)
            plt.imshow(undistorted_rectify0.astype(np.uint8))
            plt.subplot(232)
            plt.imshow(undistorted_rectify1.astype(np.uint8))
            plt.subplot(233)
            plt.imshow(undistorted_rectify_abs_1)  # 基线矫正的两图相减
            plt.subplot(234)
            plt.imshow(undistorted_rectify_abs)
            plt.subplot(235)
            plt.imshow(undistorted_rectify_abs_mean)
            # plt.subplot(236)
            # plt.imshow(undistorted_rectify_abs_1)
            plt.pause(0.1)
            plt.clf()
            plt.show()

        return undistorted_rectify0, undistorted_rectify1

    def process(self, img_0_path, img_1_path, module_id):
        """
        module_id: 双目模组对应的id
            4:zed模组对应内参
        """
        if img_0_path.endswith('jpg') or img_0_path.endswith('png'):
            img0 = cv.imread(img_0_path)
            img1 = cv.imread(img_1_path)
        elif img_0_path.endswith('yuv'):
            img0 = self.yuv2bgr(img_0_path)
            img1 = self.yuv2bgr(img_1_path)
        else:
            raise Exception
            print('image format not supported !')
        # cv.namedWindow('src img00', cv.WINDOW_NORMAL)
        # cv.namedWindow('src img01', cv.WINDOW_NORMAL)
        # cv.imshow('src img00', img0)
        # cv.imshow('src img01', img1)

        if module_id == 1:
            img0 = cv.undistort(img0, self.cam_mats_module1['0'], self.dist_corfs_module1['0'], None,
                                self.cam_mats_module1['0'])
            img1 = cv.undistort(img1, self.cam_mats_module1['1'], self.dist_corfs_module1['1'], None,
                                self.cam_mats_module1['1'])
            rectified_img0, rectified_img1 = self.stereo_rectify(img0, img1, self.calib_module1, show_results=True)
        elif module_id == 2:
            img0 = cv.undistort(img0, self.cam_mats_module2['0'], self.dist_corfs_module2['0'], None,
                                self.cam_mats_module2['0'])
            img1 = cv.undistort(img1, self.cam_mats_module2['1'], self.dist_corfs_module2['1'], None,
                                self.cam_mats_module2['1'])
            rectified_img0, rectified_img1 = self.stereo_rectify(img0, img1, self.calib_module2, show_results=True)
        elif module_id == 3:
            img0 = cv.undistort(img0, self.cam_mats_module3['0'], self.dist_corfs_module3['0'], None,
                                self.cam_mats_module3['0'])
            img1 = cv.undistort(img1, self.cam_mats_module3['1'], self.dist_corfs_module3['1'], None,
                                self.cam_mats_module3['1'])
            cv.namedWindow('img0', cv.WINDOW_NORMAL)
            cv.namedWindow('img1', cv.WINDOW_NORMAL)
            cv.imshow('img0', img0)
            cv.imshow('img1', img1)
            rectified_img0, rectified_img1 = self.stereo_rectify(img0, img1, self.calib_module3, show_results=True)
        elif module_id == 4:
            img0 = cv.undistort(img0, self.cam_mats_module4['0'], self.dist_corfs_module4['0'], None,
                                self.cam_mats_module4['0'])
            img1 = cv.undistort(img1, self.cam_mats_module4['1'], self.dist_corfs_module4['1'], None,
                                self.cam_mats_module4['1'])
            cv.namedWindow('img0', cv.WINDOW_NORMAL)
            cv.namedWindow('img1', cv.WINDOW_NORMAL)
            cv.imshow('img0', img0)
            cv.imshow('img1', img1)
            rectified_img0, rectified_img1 = self.stereo_rectify(img0, img1, self.calib_module4, show_results=True)
        elif module_id == 5:
            img0 = cv.undistort(img0, self.cam_mats_module5['0'], self.dist_corfs_module5['0'], None,
                                self.cam_mats_module5['0'])
            img1 = cv.undistort(img1, self.cam_mats_module5['1'], self.dist_corfs_module5['1'], None,
                                self.cam_mats_module5['1'])
        elif module_id == 6:
            # 去畸变
            img0 = cv.undistort(img0, self.cam_mats_module6['0'], self.dist_corfs_module6['0'], None,
                                self.cam_mats_module6['0'])
            img1 = cv.undistort(img1, self.cam_mats_module6['1'], self.dist_corfs_module6['1'], None,
                                self.cam_mats_module6['1'])
            # cv.namedWindow('img0', cv.WINDOW_NORMAL)
            # cv.namedWindow('img1', cv.WINDOW_NORMAL)
            # cv.imshow('img0', img0)
            # cv.imshow('img1', img1)
            # cv.waitKey(200)
            # rectified_img0, rectified_img1 = self.stereo_rectify(img0, img1, self.calib_module5, show_results=True)    
        return img0, img1, self.cam_mats_module6['0'], self.cam_mats_module6[
            '1'], self.rot_mat_module6, self.trans_vec_module6  # 返回左目的内参,和右目外参Tlr
    # return rectified_img0, rectified_img1


def img_pcts_show_mesh(undistort_img0, whole_pcd, pose_path):  # undistort_img0,pcd_file_path, Intrinsic,Extrinsics
    """ mesh: x右，y上，z后
        pose: colmap/opencv坐标系，x向，y下，z前
    """

    # 1.显示为mesh
    # whole_pcd = o3d.io.read_triangle_mesh(pcd_file_path)
    # whole_pcd.compute_vertex_normals()

    # 2.显示为point
    # whole_pcd = o3d.io.read_point_cloud(pcd_file_path)

    # origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=2, origin=[0,0,0])
    # o3d.visualization.draw_geometries([whole_pcd])
    vis = o3d.visualization.Visualizer()
    vis.create_window()
    vis.add_geometry(whole_pcd)
    # 从colmap坐标系转换为mesh所在的坐标系
    rotation_z = R.from_euler('y', [180], degrees=True).as_matrix().squeeze()
    rotation_y = R.from_euler('z', [180], degrees=True).as_matrix().squeeze()
    print(rotation_z)
    print(rotation_y)
    translations = []

    view_control = vis.get_view_control()
    view_control.change_field_of_view(step=30.0)  # 默认显示60度，且可显示范围是5～90度。
    # step=0时，显示60+0度fov；step=-90时，显示60+ (-90)度

    img = undistort_img0
    cv2.imshow("img", img)
    cv2.waitKey(200)

    # Tlc是pose
    Tlc = read_pose(pose_path)
    extrinsic = np.array([-0.01245, 0.0288124, 0.999507, 0.0873019,
                          -0.999916, -0.00400288, -0.0123406, 0.0707602,
                          0.00364534, -0.999577, 0.0288598, -0.289288,
                          0, 0, 0, 1], dtype=np.float64)
    RT = Tlc
    # RT = np.linalg.inv(RT)
    # RT = RT
    # print('RT: ', RT)
    rotation = RT[:3, :3]
    rotation = rotation @ rotation_z @ rotation_y
    translation = RT[:3, 3]
    translations.append(list(translation))
    front_vector = (rotation[:, 2]).tolist()
    up_vector = (rotation[:, 1]).tolist()
    view_control.set_front(front_vector)
    view_control.set_up(up_vector)
    view_control.set_lookat(translation)
    view_control.set_zoom(0.001)
    vis.update_geometry(whole_pcd)
    vis.poll_events()
    vis.update_renderer()

    # vis.capture_screen_image(path)
    # cv2.destroyAllWindows()
    # vis.destroy_window()


def img_pcts_show_pcd(undistort_img0, whole_pcd, pose_path,
                      extrinsic_Tlc):  # undistort_img0,pcd_file_path, Intrinsic,Extrinsics
    """ mesh: x右，y上，z后
        pose: colmap/opencv坐标系，x向，y下，z前
    """

    # 1.显示为mesh
    # whole_pcd = o3d.io.read_triangle_mesh(pcd_file_path)
    # whole_pcd.compute_vertex_normals()

    # 2.显示为point
    # whole_pcd = o3d.io.read_point_cloud(pcd_file_path)

    # origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=2, origin=[0,0,0])
    # o3d.visualization.draw_geometries([whole_pcd])

    # # 从lidar坐标系转换为mesh所在的坐标系   [x：前；y：左；z：上]->[x:右；y：上；z：后]
    transform_mat = np.zeros((4, 4), dtype=np.float32)
    rotation_z = R.from_euler('z', [-90], degrees=True).as_matrix().squeeze()
    rotation_y = R.from_euler('x', [90], degrees=True).as_matrix().squeeze()
    transform_mat[:3, :3] = rotation_z @ rotation_y
    transform_mat[:4, 3] = [0, 0, 0, 1]
    translations = []

    view_control = vis.get_view_control()
    view_control.change_field_of_view(step=30.0)  # 默认显示60度，且可显示范围是5～90度。
    # step=0时，显示60+0度fov；step=-90时，显示60+ (-90)度

    # img=undistort_img0
    # cv2.imshow("img",img)
    # cv2.waitKey(200)

    # Twl是pose
    Twl = read_pose(pose_path)

    # 这里先乘transform_mat还是后乘没区别
    pose_cam = Twl @ extrinsic_Tlc  # 这里考虑了camera外参Tlc,#这里是pose转换到了mesh坐标系

    whole_pcd.transform(np.linalg.inv(pose_cam))  # Points_mesh=Tmw@Points_w,其中m为mesh
    points_num = np.array(whole_pcd.points).shape[0]
    whole_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(points_num, 3)))
    voxel_grid_color = o3d.geometry.VoxelGrid.create_from_point_cloud(whole_pcd, voxel_size=0.05)

    # pose_cam=np.linalg.inv(pose_cam)#
    rotation = pose_cam[:3, :3]
    translation = pose_cam[:3, 3]
    translations.append(list(translation))
    front_vector = (rotation[:, 2]).tolist()
    up_vector = (rotation[:, 1]).tolist()
    view_control.set_front(front_vector)
    view_control.set_up(up_vector)
    view_control.set_lookat(translation)
    view_control.set_zoom(0.001)
    vis.update_geometry(voxel_grid_color)
    vis.poll_events()
    vis.update_renderer()

    # vis.capture_screen_image(path)


def find_files_with_string(files, search_str):
    list_file = []
    for file in files:
        if search_str in file:
            list_file.append(file)
    return list_file


def find_folders_with_string(root_path, search_str):
    list_mid_camera_path = []
    for dirpath, dirnames, filenames in os.walk(root_path):
        if search_str in dirpath:
            list_mid_camera_path.append(dirpath)
    return list_mid_camera_path


if __name__ == '__main__':
    """注意：需要修改src_dir, dst_dir, module_id
    """
    # 右相机外参=Trl, 类似于orbslam3中Tcw为外参
    img_size = (1240, 925)
    module_id = 6
    image_format = '.jpg'
    cloud_format = 'pcd'

    # save_path='/raid/hbw/bev_related/data/data_only_for_all'

    input_path = '/home/<USER>/yp/Datasets/kws_deliver_0509/scenes/'
    # input_path = '/home/<USER>/Datasets/0509/scenes/0509_yp/'
    # cameras = ['camera2']
    cameras = ['camera0', 'camera2', 'camera5']
    # cameras = ['camera0/','camera1/','camera2/','camera3/','camera4/','camera5/','camera6/']
    # home_name_list = ['3FO3NNTEBW98']
    home_name_list = os.listdir(input_path)
    home_name_list.sort()
    home_name_list = home_name_list[42:]
    # home_name_list = home_name_list[5:10]
    # home_name_list = home_name_list[10:15]
    # home_name_list = home_name_list[15:20]
    # home_name_list = home_name_list[20:25]
    # home_name_list = home_name_list[25:30]
    # home_name_list = home_name_list[30:35]
    # home_name_list = home_name_list[35:40]
    # home_name_list = home_name_list[40:45]
    # home_name_list = home_name_list[45:]
    # home_name_list = ['3FO3NTO3MURV']


    print(home_name_list)

    for home in home_name_list:
        input_path_1 = input_path + '/' + str(home)

        # AI_folders= find_folders_with_string(input_path,folder_type)
        # for i in AI_folders:

        for camera_name in (cameras):
            # scene_name=i.split('/')[-3]
            # scene_save_path=os.path.join(save_path, scene_name)
            # os.makedirs(scene_save_path, exist_ok=True)
            # path_1=os.path.join(scene_save_path, '无人场景')
            # os.makedirs(path_1, exist_ok=True)
            # baseline=os.path.join(path_1, '12cm')
            # os.makedirs(baseline, exist_ok=True)
            # left_path=os.path.join(baseline, 'image_02')
            # os.makedirs(left_path, exist_ok=True)
            # right_path=os.path.join(baseline, 'image_03')
            # os.makedirs(right_path, exist_ok=True)
            # occupancy_path=os.path.join(baseline, 'occupancy')
            # os.makedirs(occupancy_path, exist_ok=True)
            # bev_path=os.path.join(baseline, 'bev')
            # os.makedirs(bev_path, exist_ok=True)
            # os.makedirs(os.path.join(left_path, 'data'), exist_ok=True)
            # os.makedirs(os.path.join(right_path, 'data'), exist_ok=True)
            # os.makedirs(os.path.join(occupancy_path, 'data'), exist_ok=True)
            # os.makedirs(os.path.join(bev_path, 'data'), exist_ok=True)

            # 图像目录

            # src_path=os.path.join(os.path.join(i[:-27], 'pointcloud'),'pointcloud.ply')
            # src_file = "/raid/kujiale_data/kws_deliver_1/AI/3FO3OXSKLRC6/cameras_sampling_a/camera2"
            # src_path='/raid/kujiale_data/kws_deliver_1/AI/3FO3OXSKLRC6/cameras_sampling_a/occupancy_label'

            # pcd或者ply目录
            pcd_file_path = os.path.join(input_path_1, 'pointcloud', 'pointcloud.ply')
            show = False

            if cloud_format == 'mesh':  # 如果是mesh
                whole_pcd = o3d.io.read_triangle_mesh(pcd_file_path)
                whole_pcd.compute_vertex_normals()
            else:
                whole_pcd = o3d.io.read_point_cloud(pcd_file_path)  # 如果是pcds
            points = np.asarray(whole_pcd.points) / 1000
            whole_pcd.points = o3d.utility.Vector3dVector(points)

            # 相机外参
            # extrinsic_Tlc=np.array([[-0.01245, 0.0288124, 0.999507,0.0873019],
            #                     [-0.999916,-0.00400288,-0.0123406,0.0707602],
            #                     [0.00364534,-0.999577,0.0288598,-0.289288],
            #                     [0,0,0,1]], dtype=np.float64)#Tlc,即相机外参，即相机-to-lidar,外参并不准确
            pic_path = input_path_1 + '/' + camera_name

            # occupancy_path = input_path_1 + '/occ_rgbd_0.75m/' + camera_name
            # occupancy_path = input_path_1 + '/occ_rgbd_1m/' + camera_name
            occupancy_path = input_path_1 + '/occ_stereo_new/' + camera_name
            os.makedirs(occupancy_path, exist_ok=True)

            src_dirs = os.listdir(pic_path)

            src_dirs = [os.path.join(pic_path, dirname) for dirname in src_dirs]

            print("***************************************************************************")
            print("处理文件夹: ", pic_path)

            camera_img_type = 'jpg'
            list_cam_path = find_files_with_string(src_dirs, camera_img_type)
            # find_folders_with_string(src_file,camera0_img_type)[0]

            # 按图像id排序
            camera_left_imgs = sorted(list_cam_path, key=lambda x: int(x.split('/')[-1].split('_')[2]), reverse=False)
            # if  'camera0' in camera_left_imgs[0]:
            #     camera_right_imgs =  [dirname.replace('camera0','camera1') for dirname in camera_left_imgs]
            # elif  'camera2' in camera_left_imgs[0]:
            #     camera_right_imgs =  [dirname.replace('camera2','camera4') for dirname in camera_left_imgs]
            # else:
            #     camera_right_imgs =  [dirname.replace('camera5','camera6') for dirname in camera_left_imgs]

            camera_right_imgs = camera_left_imgs

            stereo = Stereo(img_size)
            if show:
                vis = o3d.visualization.Visualizer()
                vis.create_window()

            # 这里背景介绍：
            # 相机距离地面5cm，camere_rays的每个点处于的位置有点讲究，中央的grid中心处于相机坐标系原点，所以感知相机上下xn的区域，否则代码无法运行
            occupancy_range = np.array(
                [[-1.975, -0.125, 0.025], [2, 0.05, 4]])  # 设置技巧：最小值设为边界+voxel/2，最大值设为边界-1e-6，高度设置注意不穿模即可
                # [[-1.974, -0.6749, 0.025], [2, 0.049, 4]])  # 0.75m
                # [[-1.974, -0.9249, 0.025], [2, 0.049, 4]])  # 1m
                # [[-1.975, -1.425, 0.025], [2, 0.05, 4]])  # 1.5m
                
            # occupancy_range1=np.array([[-1.974, -0.049999, 0.0251], [2.024,0.05, 2.02499]])  
            voxel_size = 0.05
            instrinsic = stereo.cam_mats_module6['0']
            camera_rays = get_rays_camxyz(occupancy_range, instrinsic, img_size, voxel_size)  # 先计算fov再根据格子与相机中心相连接形成射线

            # vis.add_geometry(whole_pcd)
            img_index = 0
            # camera_left_imgs = camera_left_imgs[:200]
            # camera_left_imgs = camera_left_imgs[200:400]
            # camera_left_imgs = camera_left_imgs[400:600]
            # camera_left_imgs = camera_left_imgs[600:800]
            # camera_left_imgs = camera_left_imgs[800:1000]
            # camera_left_imgs = camera_left_imgs[1000:1200]
            # camera_left_imgs = camera_left_imgs[1200:1400]
            # camera_left_imgs = camera_left_imgs[1400:1600]
            # camera_left_imgs = camera_left_imgs[1600:]
            print(camera_left_imgs[0])
            
            for img2_num in tqdm(camera_left_imgs):

                img_left_path = camera_left_imgs[img_index]
                img_right_path = camera_right_imgs[img_index]
                img_index += 1
                # if img_index<=170:
                #     continue

                pose_path = img_left_path.replace('rgb.jpg', 'pose.json')
                with open(pose_path, 'r') as f:
                    data = json.load(f)
                undistort_img0, undistort_img1, Intrinsic_l, Intrinsic_r, stereo_extrinsics_rot, stereo_extrinsics_trans = stereo.process(
                    img_left_path, img_right_path, module_id=module_id)
                stereo_extrinsics = np.zeros((4, 4), dtype=np.float32)
                stereo_extrinsics[:3, :3] = stereo_extrinsics_rot
                stereo_extrinsics[:3, 3] = stereo_extrinsics_trans
                stereo_extrinsics[3, :] = [0, 0, 0, 1]

                Tcl = np.asarray(data['extrinsic'])
                Tcl[:3, 3] = Tcl[:3, 3] / 1000
                # Tlc=  np.linalg.inv(Tcl)
                # print(Tcl)
                # np.save('right2left.npy',stereo_extrinsics)#Tlr 即right->left
                # np.save('left_intrinsic.npy',Intrinsic_l)
                # np.save('right_intrinsic.npy',Intrinsic_r)
                # saveimg_path=img0_path.replace('zed_lidar','/processed/undistorted_imgs/left').replace('_l','')
                # cv2.imwrite(saveimg_path,undistort_img0)

                # saveimg_path1=img0_path.replace('zed_lidar','/processed/undistorted_imgs/right').replace('_l','')
                # cv2.imwrite(saveimg_path1,undistort_img1)

                # 目前1和2用不了，因为不是mesh，显示不好看

                # 1.mesh显示整个场景，由于open3d坐标系不一样，为了显示，需要做一个转换
                # img_pcts_show_mesh(undistort_img0,whole_pcd,pose_path)

                # 2.pcd显示整个场景，由于open3d坐标系不一样，为了显示，需要做一个转换
                # img_pcts_show_pcd(undistort_img0,whole_pcd,pose_path,extrinsic_Tlc)

                # 3.输入mesh，制作标签
                # 此函数使用时需要微调transform_mat，比如lidar坐标系转换成opencv坐标系
                # gen_occupancy_mesh(whole_pcd, pose_path, extrinsic_Tlc,Intrinsic_l,src_path,stereo_extrinsics,save_path)

                # 4.输入pcd，制作标签
                gen_occupancy_pcd(whole_pcd, Tcl, Intrinsic_l, pose_path, stereo_extrinsics, occupancy_path, img_size,
                                  occupancy_range, voxel_size, camera_rays)

    if show:
        cv2.destroyAllWindows()
        vis.destroy_window()
