
import open3d as o3d
import numpy as np
import os

import math

import PIL.Image as Image

import matplotlib.pyplot as plt
import numpy as np

# def crop_point_cloud(pcd, crop_range):
#     bounding_box = o3d.geometry.AxisAlignedBoundingBox(crop_range[0], crop_range[1])
#     return pcd.crop(bounding_box)

# # def get_fov(radius, focal_length):
# #     # 求出鱼眼图像上一个像素点的角度
# #     pixel_angle = 2 * math.atan(radius / (2 * focal_length))
# #     # 将角度转换为FOV（度）
# #     fov = pixel_angle * 180 / math.pi
# #     return fov

# # radius = 100
# # focal_length = 8
# # fov = get_fov(radius, focal_length)
# # print(fov)

# import cv2
# import numpy as np

# 相机的内参矩阵
# K = np.array([[  508.35,    0,  642.015421132869],
#               [    0,  508.02,  350.41],
#               [    0,    0,    1]])#最初按照针孔相机标的内参

K = np.array([[  113.391609,    0,  104.253815],
              [    0,  113.391609,  78.633530],
              [    0,    0,    1]])#初次按照鱼眼相机标的内参（bai，当前测试数据使用的）

# K = np.array([[  507.8251219765492,    0,  639.60],
#               [    0,  508.1991320183231,  352.4046791817908],
#               [    0,    0,    1]])#初次按照鱼眼相机标的内参（bai，当前测试数据使用的）

# 图像的尺寸
width = 208
height = 156

# # fov = 4*np.arcsin(width/(508.35*4))* 180 / np.pi   鱼眼

# 计算相机的水平和垂直视场角
fov_x = 2 * np.arctan(width / (2*K[0,0])) * 180 / np.pi

fov_y = 2 * np.arctan(height / (2*K[1,1])) * 180 / np.pi

print('Horizontal FOV: {:.2f} degrees'.format(fov_x))
print('Vertical FOV: {:.2f} degrees'.format(fov_y))