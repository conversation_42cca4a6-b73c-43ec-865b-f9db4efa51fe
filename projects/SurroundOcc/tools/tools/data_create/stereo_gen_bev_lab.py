import os
import open3d as o3d
import numpy as np
import os.path as osp
import json
import cv2
# from rich import print
from PIL import Image
from PIL import Image, ImageDraw
import math
import matplotlib.pyplot as plt
import time
from PIL import Image

color_map = np.array(
    [[0.04313725, 0.12156863, 0.71764706],
     [0.40784314, 0.25882353, 0.27058824],
     [0.04313725, 0.12156863, 0.71764706],
     [0.40784314, 0.25882353, 0.27058824],
     [0.04313725, 0.12156863, 0.71764706],
     [0.36862745, 0.3372549, 0.94901961],
     [0.40784314, 0.25882353, 0.27058824],

     ]
)


def mk_mask():
    new_pic = np.zeros((80, 80), dtype=np.uint8)

    h, w = new_pic.shape

    angle_deg = 60  # 这个相当于是fov的一半

    # angle_deg = 30

    angle_rad = math.radians(angle_deg)
    tan_angle = math.tan(angle_rad)

    # Traverse all pixels
    for i in range(h):
        for j in range(w):
            dx = i - h  # 高
            dy = j - w / 2  # 宽

            # Check pixel side
            is_right = dy > 0

            # Calculate tangent value for current pixel
            if dx == 0:
                continue
            tan_val = float(dy) / dx  # 正在左半区，负在右半区

            # Check if pixel is below the angle lines
            if (is_right and tan_val > -tan_angle) or (not is_right and tan_val < tan_angle):
                new_pic[i, j] = 1

    return new_pic


def get_pcd_from_depth(depth_array, intrin):
    H = depth_array.shape[0]

    angle_camera = 103

    angle_tof = 56

    all_line = angle_tof / angle_camera * H

    start = int(H / 2 - all_line / 2)

    end = int(H / 2 + all_line / 2)

    depth_array[0:start, :] = 0

    depth_array[end:, :] = 0

    fx = intrin[0][0]  # 焦距x
    fy = intrin[1][1]  # 焦距y
    cx = intrin[0][2]  # 中心点x
    cy = intrin[1][2]  # 中心点y

    points = []

    tof_h = 109

    tof_w = 224

    step_h = int(depth_array.shape[0] / tof_h)

    step_w = int(depth_array.shape[1] / tof_w)

    # mask_d = np.zeros_like(depth_array)

    for i in range(0, depth_array.shape[0], step_h):
        for j in range(0, depth_array.shape[1], step_w):

            if depth_array[i][j]:
                # mask_d[i][j] = 1

                z3 = depth_array[i][j]

                x3 = (j - cx) * z3 / fx
                y3 = (i - cy) * z3 / fy

                point = np.stack((x3, y3, z3), axis=-1)

                points.append(point)

    ans_points = np.array(points)

    return ans_points  # ,mask_d


def main():
    main_path = "/home/<USER>/Datasets/nuscenes_data/kws_deliver_0325/scenes/"

    home_names = os.listdir(main_path)

    home_names.sort()

    origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.05, origin=[0, 0, 0])

    camera_name_s = ["camera2/"]
    # camera_name_s = ["camera0/", "camera2/", "camera3/", "camera5/"]

    mask = mk_mask()

    for home_name in home_names:

        # if home_name != "3FO3P2KP17FC":

        #     print("处理过了")

        #     continue

        home_n = os.path.join(main_path, home_name)

        src_pl_path = os.path.join(home_n, 'pointcloud', 'pointcloud.ply')

        mesh = o3d.io.read_point_cloud(src_pl_path)

        mesh_1 = o3d.io.read_point_cloud(src_pl_path)

        points_ori = mesh_1.points  # *1.0

        for camera_name in camera_name_s:

            pic_path = os.path.join(home_n, camera_name)

            floder_names = os.listdir(pic_path)
            floder_names.sort()  # 这里是所有当前户型当前相机下的所有rgb等数据的名字

            from tqdm import tqdm
            for floder_name in tqdm(floder_names):

                if floder_name.endswith("_pose.json"):

                    mesh.points = points_ori

                    cam_json = osp.join(pic_path, floder_name)  # , "camera.json")

                    with open(cam_json) as f:
                        # 加载JSON数据
                        data = json.load(f)

                    extrinsic_matrix = np.array(data['extrinsic'])

                    intrinsic_matrix = np.array(data['intrinsic'])

                    depth_path = cam_json.replace("pose.json", "depth.png")

                    depth = Image.open(depth_path)
                    depth = np.array(depth) / 1000.

                    mesh = mesh.transform(extrinsic_matrix)  # ~ 转换到相机坐标系

                    cam2lidar = np.array([
                        [1, 0, 0, 0],
                        [0, 0, 1, 0],
                        [0, -1, 0, 0],
                        [0, 0, 0, 1]], dtype=np.float64)

                    mesh = mesh.transform(cam2lidar)  # 转到雷达

                    # 下面通过depth intri pose 计算当前雷达坐标系下的点云

                    pcd = get_pcd_from_depth(depth, intrinsic_matrix)

                    if pcd.size == 0:

                        points_to_save = pcd

                    else:

                        depth_pcd_o = o3d.geometry.PointCloud()
                        depth_pcd_o.points = o3d.utility.Vector3dVector(pcd)

                        depth_pcd_o.transform(cam2lidar)

                        # depth_pcd_filter_range= np.array([[-1.975, 0.025, -0.02], [2, 4, 0.075]])#这个range设成occ 的范围，做bev的时候可以再进行裁剪

                        depth_pcd_filter_range = np.array([[-1.975, 0.025, -0.045], [2, 4, 0.15]])

                        depth_pcd_croped = crop_point_cloud(depth_pcd_o, depth_pcd_filter_range)

                        points_to_save = np.array(depth_pcd_croped.points)

                    vis_pcd = o3d.geometry.PointCloud()
                    # origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0,0,0])

                    vis_pcd.points = o3d.utility.Vector3dVector(np.array(mesh.points) / 1000.)
                    vis_pcd.colors = o3d.utility.Vector3dVector(mesh.colors)  # wqx

                    # o3d.visualization.draw_geometries([vis_pcd, origin])
                    # occupancy_range= np.array([[-1.975, 0.025, -0.02], [2, 4, 0.075]])

                    occupancy_range= np.array([[-1.975, 0.025, -0.02], [2, 4, 0.055]])

                    # occupancy_range = np.array([[-1.975, 0.025, -0.02], [2, 4, 0.08]])

                    # occupancy_range= np.array([[-2.025, -0.1, 0], [2.05, 0.1, 4]])  #for test

                    vis_pcd_cropped = crop_point_cloud(vis_pcd, occupancy_range)
                    # o3d.visualization.draw_geometries([vis_pcd_cropped, origin])

                    new_robust_point = np.array(
                        [[occupancy_range[0][0], occupancy_range[0][1], occupancy_range[0][2]]])  # 加点
                    for point in new_robust_point:
                        vis_pcd_cropped.points.append(point)
                        vis_pcd_cropped.colors.append([0, 0, 1])  # 颜色也需要加上

                    points = np.asarray(vis_pcd_cropped.points)

                    # points_semantic=np.asarray(vis_pcd_cropped.colors)
                    # 计算每个维度的最小值和最大值
                    x_min, y_min, z_min = np.min(points, axis=0)
                    x_max, y_max, z_max = np.max(points, axis=0)

                    # 打印范围信息
                    print("X范围: [{}, {}]".format(x_min, x_max))
                    print("Y范围: [{}, {}]".format(y_min, y_max))
                    print("Z范围: [{}, {}]".format(z_min, z_max))

                    vis_pcd_cropped_voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd_cropped,
                                                                                                voxel_size=0.05)

                    # vis_pcd_cropped_voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd_cropped, voxel_size=0.025)

                    # o3d.visualization.draw_geometries([vis_pcd_cropped_voxel_grid, origin])

                    img = create_2d_voxel_fast(vis_pcd_cropped_voxel_grid, occupancy_range, mask)

                    final_image = Image.fromarray(img.astype('uint8'), mode='L')

                    path_img = pic_path[:-8] + "bev_rgbd/" + camera_name + floder_name[:-9] + "bev.png"

                    img_wjj_path = pic_path[:-8] + "bev_rgbd/" + camera_name
                    os.makedirs(img_wjj_path, exist_ok=True)

                    points_path = pic_path[:-8] + "points_tof_56/" + camera_name + floder_name[:-9] + "pcd.npy"

                    points_wjj_path = pic_path[:-8] + "points_tof_56/" + camera_name
                    os.makedirs(points_wjj_path, exist_ok=True)

                    # path = floder_name[:-9]+"bev.png"

                    final_image.save(path_img)

                    np.save(points_path, points_to_save)

                    # plus = 10

                    # # img = cv2.copyMakeBorder(img,plus,plus,plus,plus,cv2.BORDER_CONSTANT,value=[0])

                    # # img = cv2.bitwise_not(np.array(img))

                    # _, binary = cv2.threshold(img, 128, 255, cv2.THRESH_BINARY)

                    # # 寻找轮廓
                    # contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                    # new_img = np.zeros_like(img)#*255

                    # for contour in contours:
                    #     cv2.drawContours(new_img, [contour], -1, 255, thickness=1)

                    # new_img = new_img[1:,1:-1]

                    # img = img[1:,1:-1]

                    # print(1)

                    # plt.figure()
                    # plt.subplot(1,2,1)
                    # plt.imshow(img)
                    # plt.subplot(1,2,2)
                    # plt.imshow(new_img)
                    # plt.show()
                    # img = np.uint8(visiable_voxel_gird_projected_2d_voxel)

                    # visiable_voxel_gird_projected_2d_voxel, _ = create_2d_voxel_fast(vis_pcd_cropped_voxel_grid,occupancy_range)
                    # img = np.uint8(visiable_voxel_gird_projected_2d_voxel)

                    # cv2.imshow("img", img)
                    # cv2.waitKey(0)

                    # cv2.destroyWindow("img")


def crop_point_cloud(pcd, crop_range):
    bounding_box = o3d.geometry.AxisAlignedBoundingBox(crop_range[0], crop_range[1])
    return pcd.crop(bounding_box)


def points_to_voxel_grid(points, voxel_size):
    # Compute the extent of the point cloud
    min_coords = np.min(points[:, :3], axis=0)
    max_coords = np.max(points[:, :3], axis=0)

    epsilon = 1e-6  # A small offset to avoid index out of bound issues
    max_coords = max_coords  # + epsilon

    # Compute the dimensions of the voxel grid
    grid_dims = np.ceil((max_coords - min_coords) / voxel_size).astype(int)

    # Create an empty voxel grid with the same dimensions as the point cloud extent
    voxel_grid = np.zeros(grid_dims, dtype=int)

    for row in points:
        coords = row[:3]
        label = row[3]

        # Convert the point coordinates to voxel coordinates and store the label
        voxel_coords = np.floor((coords - min_coords) / voxel_size).astype(int)
        voxel_grid[tuple(voxel_coords)] = max(voxel_grid[tuple(voxel_coords)], label)

    return voxel_grid


def points_to_voxel_grid_(points, voxel_size, grid_shape):
    # Compute the extent of the point cloud
    min_coords = np.min(points[:, :3], axis=0)
    max_coords = np.max(points[:, :3], axis=0)

    epsilon = 1e-6  # A small offset to avoid index out of bound issues
    max_coords = max_coords  # + epsilon

    # Compute the dimensions of the voxel grid
    grid_dims = np.array(grid_shape, dtype=int)

    # Create an empty voxel grid with the same dimensions as the point cloud extent
    voxel_grid = np.zeros(grid_dims, dtype=int)

    for row in points:
        coords = row[:3]
        label = row[3]

        # Convert the point coordinates to voxel coordinates and store the label
        voxel_coords = np.floor((coords - min_coords) / voxel_size).astype(int)
        voxel_grid[tuple(voxel_coords)] = max(voxel_grid[tuple(voxel_coords)], label)

    return voxel_grid


def get_points(occupancy_range, voxel_size, min_distance=0.1):
    """
    occupancy_range: 感知范围
    voxel_size：单个格子尺寸
    """
    box_min_bound = occupancy_range[0]
    box_max_bound = occupancy_range[1]

    cube_resultion = (box_max_bound - box_min_bound) / voxel_size

    x_grid = np.linspace(box_min_bound[0], box_max_bound[0], round(cube_resultion[0]))
    y_grid = np.linspace(box_min_bound[1], box_max_bound[1], round(cube_resultion[1]))
    z_grid = np.linspace(box_min_bound[2], box_max_bound[2], round(cube_resultion[2]))
    cube_grid = np.asarray(np.meshgrid(x_grid, y_grid, z_grid)).transpose(1, 2, 3, 0).reshape(-1, 3)
    return cube_grid.astype(np.float32)


def create_2d_voxel_fast(voxel_grid, occupancy_range, mask_):
    bbox = voxel_grid.get_axis_aligned_bounding_box()
    min_bound = np.array(bbox.min_bound)  # 栅格的边
    max_bound = np.array(bbox.max_bound)  # 栅格的外边
    size_extend = max_bound - min_bound
    voxel_size = voxel_grid.voxel_size
    # dim = np.round(size_extend / voxel_size).astype(np.int)#

    # **************************************新逻辑*************************************

    num_x = (occupancy_range[1][0] - (occupancy_range[0][0] - voxel_size / 2)) / voxel_size

    num_y = (occupancy_range[1][1] - (occupancy_range[0][1] - voxel_size / 2)) / voxel_size

    f_x = math.floor(num_x)

    f_y = math.floor(num_y)

    if (f_x != num_x):
        num_x = f_x + 1

    if (f_y != num_y):
        num_y = f_y + 1

    num_x = int(num_x)
    num_y = int(num_y)

    # if dim[0]<num1:
    dx_l = math.floor((-occupancy_range[0][0] + min_bound[0] + voxel_size / 2) / voxel_size)
    # if  dim[1]<num2:
    dy_l = math.floor((-occupancy_range[0][1] + min_bound[1] + voxel_size / 2) / voxel_size)

    # 获取voxel_grid的所有voxel坐标
    voxel_coordinates = voxel_grid.get_voxels()
    voxel_colors = [voxel.color for voxel in voxel_coordinates]  # wqx
    # voxel_colors = voxel_grid.get_voxel_colors()

    # 使用grid_index计算voxel坐标
    voxel_coordinates = np.array([i.grid_index for i in voxel_coordinates])
    voxel_colors = np.array([i for i in voxel_colors])  # wqx
    # voxel_all=np.concatenate((voxel_coordinates,voxel_colors),axis=1)
    # voxel_coordinates = (voxel_coordinates - min_bound).astype(int)
    # voxel_coordinates = (voxel_coordinates * voxel_size).astype(int)

    # point_000 = np.array([0, 0, 0])
    # voxel_index_000 = np.floor((point_000 - min_bound) / voxel_size).astype(int)

    # assert dim[2] > 399

    # 创建空的二维体素数组
    voxel_2d = np.zeros((num_y, num_x), dtype=np.uint8)

    nonzero_indices = np.array(voxel_coordinates)

    voxel_2d[voxel_coordinates[:, 1] + dy_l, voxel_coordinates[:, 0] + dx_l] = 255

    voxel_2d = cv2.flip(voxel_2d, 0)

    result = generate_visible(voxel_2d, mask_)

    # plt.figure()
    # plt.imshow(result)
    # plt.sh

    return result

    # origin = np.asarray(voxel_grid.origin)
    # # 计算体素中心坐标
    # voxel_centers = (tuple(voxel_size * (coord + 0.5) + origin for coord in voxel_coordinates))
    # voxel_centers = np.array(voxel_centers)

    # return  nonzero_indices, voxel_centers
    # return voxel_2d_400, nonzero_indices, voxel_centers


def imshow_img(img):
    cv2.imshow


def generate_visible(img, mask_0):
    new_pic = img

    temp_pic = np.zeros_like(new_pic)

    image_bigger = Image.fromarray(new_pic.astype('uint8'), mode='L')
    # image_bigger.show()
    # image_bigger.save("plus10_60_angle.png")
    new_image = Image.fromarray(temp_pic.astype('uint8'), mode='L')

    # 检查每个像素点
    # time1=time.time()

    mid_y = image_bigger.height - 1

    mid_x = image_bigger.width // 2

    for x in range(image_bigger.width):
        for y in range(image_bigger.height):

            points = is_line_intersect_boundary_2(x, y, image_bigger.width // 2, image_bigger.height - 1, new_pic,
                                                  image_bigger.height, image_bigger.width)

            # 这块代码有进一步优化空间
            # 1是可能不一定要遍历所有像素，只需要考虑最左、最右、最上方三个边
            # 2是距离最近的点位置肯定是在行数最大处取得，直接对那一点赋值就行

            if points is not None:
                if len(points) > 0:

                    max_d = 80 * 80 * 2

                    xx = -1
                    yy = -1

                    for point in points:

                        x_ = point[0][0]
                        y_ = point[0][1]

                        d = (mid_x - x_) ** 2 + (mid_y - y_) ** 2

                        if d < max_d:
                            max_d = d
                            xx = x_
                            yy = y_

                new_image.putpixel((xx, yy), 255)  # 障碍物

            else:

                # new_image.putpixel((x, y), 128)  #可通行
                new_image.putpixel((x, y), 128)  # 可通行

    ans_image = np.array(new_image)

    ans_image = ans_image * mask_0  # fov

    return ans_image


# def is_line_intersect_boundary(x1, y1, x2, y2, pic,h,w):

#     # pic = cv2.resize(pic,(400,400),interpolation=cv2.INTER_NEAREST)
#     # 为绘制轮廓和线段的图像创建一个空画布（全0），尺寸为原图像尺寸的一样
#     canvas2 = np.zeros((h, w), dtype=np.uint8)#canvas2 = torch.zeros(h, w, dtype=torch.uint8)

#     # 在第二个画布上绘制线段 - line_img
#     cv2.line(canvas2, (x1, y1), (x2, y2), color=255, thickness=1)

#     # 计算两个画布的逐像素位与（bitwise_and）运算。
#     # 若存在与操作结果值为255的像素点，说明线段与边界存在相交。

#     intersection_img = cv2.bitwise_and(pic, canvas2)#torch.bitwise_and()

#     intersection_pixels = np.any(intersection_img == 255)#torch.any()

#     return intersection_pixels


def is_line_intersect_boundary_2(x1, y1, x2, y2, pic, h, w):
    # pic = cv2.resize(pic,(400,400),interpolation=cv2.INTER_NEAREST)
    # 为绘制轮廓和线段的图像创建一个空画布（全0），尺寸为原图像尺寸的一样
    canvas2 = np.zeros((h, w), dtype=np.uint8)  # canvas2 = torch.zeros(h, w, dtype=torch.uint8)

    # 在第二个画布上绘制线段 - line_img
    cv2.line(canvas2, (x1, y1), (x2, y2), color=255, thickness=1)

    # 计算两个画布的逐像素位与（bitwise_and）运算。
    # 若存在与操作结果值为255的像素点，说明线段与边界存在相交。

    intersection_img = cv2.bitwise_and(pic, canvas2)  # torch.bitwise_and()

    intersection_pixels = cv2.findNonZero(intersection_img)

    # intersection_pixels = np.any(intersection_img == 255)#torch.any()

    return intersection_pixels


import numpy as np
from scipy.interpolate import griddata
from sklearn.neighbors import KDTree


def interpolate_ground_points(points, voxel_size, K):
    # Project ground points onto X-Y plane
    xy_points = points[:, :2]
    z_values = points[:, 2]
    z_min = np.min(z_values)

    # Compute the grid spacing for interpolation
    x_min, y_min = np.min(xy_points, axis=0)
    x_max, y_max = np.max(xy_points, axis=0)
    x_range = np.arange(x_min, x_max + voxel_size, voxel_size)
    y_range = np.arange(y_min, y_max + voxel_size, voxel_size)

    # Create a 2D grid for interpolation
    x_mesh, y_mesh = np.meshgrid(x_range, y_range)
    s_grid = np.zeros_like(x_mesh)

    # Convert points to a KDTree for efficient searching
    point_tree = KDTree(xy_points)

    # Interpolate missing values using nearest neighbors
    missing_indices = np.where(np.isnan(s_grid))
    for i, j in zip(missing_indices[0], missing_indices[1]):
        query_point = np.array([x_mesh[i, j], y_mesh[i, j]])
        _, indices = point_tree.query(query_point, k=K)
        s_grid[i, j] = np.mean(z_values[indices])

    # Interpolate the remaining missing values using griddata
    missing_indices = np.where(np.isnan(s_grid))
    s_values = griddata(xy_points, z_values, (x_mesh, y_mesh), method="linear")
    s_grid[missing_indices] = s_values[missing_indices]

    # Project the dense point cloud back to 3D space
    dense_points = np.zeros((s_grid.size, 3))
    dense_points[:, :2] = np.vstack([x_mesh.ravel(), y_mesh.ravel()]).T
    dense_points[:, 2] = s_grid.ravel() + z_min

    return dense_points


if __name__ == "__main__":
    main()
