import lmdb
import pickle
from pathlib import Path
from tqdm import tqdm
import os


os.chdir('/home/<USER>/yp/Datasets/kws_deliver_0509')


def create_lmdb_from_pkl(data_path, map_size):
    pkl_path = data_path['pkl_file_path']
    lmdb_path = data_path['lmdb_database_path']

    with open(pkl_path, 'rb') as file:
        pkl_data = pickle.load(file)

    Path(lmdb_path).mkdir(parents=True, exist_ok=True)

    env = lmdb.open(lmdb_path, map_size=int(map_size))

    with env.begin(write=True) as txn:
        for info in tqdm(pkl_data['infos'], desc='Converting PKL to LMDB', unit='frame'):
            token = info["token"]

            # # Lidar data      .pcd.bin
            # if 'lidar_path' in info and Path(info['lidar_path']).is_file():
            #     lidar_key = f'data_lidar_{token}'
            #     with open(info['lidar_path'], 'rb') as lidar_file:
            #         txn.put(lidar_key.encode(), lidar_file.read())
            #     info['lidar_path'] = lidar_key

            # # Camera data   .jpg
            # for cam_name in ['CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_FRONT_LEFT', 'CAM_BACK', 'CAM_BACK_LEFT',
            #                  'CAM_BACK_RIGHT']:
            #     if 'cams' in info and cam_name in info['cams'] and 'data_path' in info['cams'][cam_name]:
            #         cam_data_path = info['cams'][cam_name]['data_path']
            #         cam_key = f'data_cams_{cam_name}_{info["token"]}'
            #         with open(cam_data_path, 'rb') as cam_file:
            #             txn.put(cam_key.encode(), cam_file.read())
            #         info['cams'][cam_name]['data_path'] = cam_key

            # # Sweeps data     .pcd.bin
            # if 'sweeps' in info and info['sweeps']:
            #     for i, sweep in enumerate(info['sweeps']):
            #         if 'data_path' in sweep and Path(sweep['data_path']).is_file():
            #             sweep_key = f'data_sweep_{info["token"]}_{i}'
            #             with open(sweep['data_path'], 'rb') as sweep_file:
            #                 txn.put(sweep_key.encode(), sweep_file.read())
            #             sweep['data_path'] = sweep_key

            # # Occupancy data    .npz
            # for occ_key in ['occ_path']:
            # # for occ_key in ['occ_gt_path', 'occ_path']:
            #     if occ_key in info:
            #         # full_path = os.path.join('./data/nuscenes/', info[occ_key])
            #         if Path(info[occ_key]).is_file():
            #             occ_data_key = f'data_{occ_key}_{token}'
            #             with open(info[occ_key], 'rb') as occ_file:
            #                 txn.put(occ_data_key.encode(), occ_file.read())
            #             info[occ_key] = occ_data_key
            #         else:
            #             print(f"Missing file for key: {occ_key}, Value: {info[occ_key]}")
            #             raise FileNotFoundError(f"File for key '{occ_key}' not found: {info[occ_key]}")

            # kw data   .npy
            # for kw_key in ['kd_logits', 'kd_sigmoid']:
            for kw_key in ['transformer_occfeature_path']:
                if kw_key in info:
                    # full_path = os.path.join('./data/nuscenes/', info[occ_key])
                    if Path(info[kw_key]).is_file():
                        kw_data_key = f'data_{kw_key}_{token}'
                        with open(info[kw_key], 'rb') as occfeature_file:
                            txn.put(kw_data_key.encode(), occfeature_file.read())
                        info[kw_key] = kw_data_key
                    else:
                        print(f"Missing file for key: {kw_key}, Value: {info[kw_key]}")
                        raise FileNotFoundError(f"File for key '{kw_key}' not found: {info[kw_key]}")

        txn.put('pkl'.encode(), pickle.dumps(pkl_data))

    env.close()


def create_lmdb_from_pkl_kjl_stereo(data_path, map_size, batch_size=1000):
    pkl_path = data_path['pkl_file_path']
    lmdb_path = data_path['lmdb_database_path']

    with open(pkl_path, 'rb') as file:
        pkl_data = pickle.load(file)

    Path(lmdb_path).mkdir(parents=True, exist_ok=True)

    env = lmdb.open(lmdb_path, map_size=int(map_size))

    items_processed = 0
    txn = env.begin(write=True)

    # right2left = pkl_data['right2left']
    # print(right2left.shape)

    for info in tqdm(pkl_data['infos'], desc='Converting PKL to LMDB', unit='frame'):
        token = info["token"]

        # info path
        # for info_name in ['left_image_path', 'right_image_path']:
        for info_name in ['left_image_path', 'right_image_path', 'occ_path', 'bev_path']:
        # for info_name in ['left_image_path', 'right_image_path', 'occ_path']:
            if info_name in info:
                cam_data_path = info[info_name]
                full_path = os.path.abspath(cam_data_path)
                if not os.path.exists(full_path):
                    raise FileNotFoundError(f"The camera data file does not exist: {full_path}")

                if info_name == 'left_image_path' or info_name == 'right_image_path':
                    print(cam_data_path)
                    camera_id = cam_data_path.split("/")[-2]  # 使用"_"分割字符串
                elif info_name == 'occ_path' or info_name == 'bev_path':
                    camera_id = cam_data_path.split("_")[-2]

                cam_key = f'data_{token}_{info_name}_{camera_id}'

                # cam_key = f'data_{token}_{info_name}'

                with open(full_path, 'rb') as cam_file:
                    txn.put(cam_key.encode(), cam_file.read())
                info[info_name] = cam_key

        items_processed += 1
        if items_processed % batch_size == 0:
            txn.commit()
            txn = env.begin(write=True)

    # 处理剩余的数据
    if items_processed % batch_size != 0:
        txn.commit()

    txn = env.begin(write=True)
    txn.put('pkl'.encode(), pickle.dumps(pkl_data))
    txn.commit()

    env.close()

def create_lmdb_from_pkl_kjl_fusion(data_path, map_size, batch_size=1000):
    pkl_path = data_path['pkl_file_path']
    lmdb_path = data_path['lmdb_database_path']

    with open(pkl_path, 'rb') as file:
        pkl_data = pickle.load(file)

    Path(lmdb_path).mkdir(parents=True, exist_ok=True)

    env = lmdb.open(lmdb_path, map_size=int(map_size))

    items_processed = 0
    txn = env.begin(write=True)

    for info in tqdm(pkl_data['infos'], desc='Converting PKL to LMDB', unit='frame'):
        token = info["token"]

        # info path
        for info_name in ['cams', 'lidar_path', 'bev_path', 'occ_path']:
            if info_name in info:

                # 检查cam_data_path文件是否存在
                cam_data_path = info[info_name]
                # print("Camera data path:", repr(cam_data_path))

                # 检查并打印当前工作目录
                # print("Current Working Directory:", os.getcwd())

                # 打印绝对路径以验证
                full_path = os.path.abspath(cam_data_path)
                if '89' in full_path:
                    print(1)
                # print("Full path resolved:", full_path)

                if not os.path.exists(full_path):
                    raise FileNotFoundError(f"The camera data file does not exist: {full_path}")

                # cam_data_path = info[info_name]
                cam_key = f'data_{token}_{info_name}'

                with open(full_path, 'rb') as cam_file:
                    txn.put(cam_key.encode(), cam_file.read())
                info[info_name] = cam_key

        items_processed += 1
        if items_processed % batch_size == 0:
            txn.commit()
            txn = env.begin(write=True)

    # 处理剩余的数据
    if items_processed % batch_size != 0:
        txn.commit()

    txn = env.begin(write=True)
    txn.put('pkl'.encode(), pickle.dumps(pkl_data))
    txn.commit()

    env.close()

    pkl_path = data_path['pkl_file_path']
    lmdb_path = data_path['lmdb_database_path']

    with open(pkl_path, 'rb') as file:
        pkl_data = pickle.load(file)

    Path(lmdb_path).mkdir(parents=True, exist_ok=True)

    env = lmdb.open(lmdb_path, map_size=int(map_size))

    items_processed = 0
    txn = env.begin(write=True)

    # right2left = pkl_data['right2left']
    # print(right2left.shape)

    for info in tqdm(pkl_data['infos'], desc='Converting PKL to LMDB', unit='frame'):
        token = info["token"]

        # info path
        for info_name in ['left_image_path', 'right_image_path']:
        # for info_name in ['left_image_path', 'right_image_path', 'occ_path', 'bev_path']:
        # for info_name in ['left_image_path', 'right_image_path', 'occ_path']:
            if info_name in info:
                cam_data_path = info[info_name]
                full_path = os.path.abspath(cam_data_path)
                if not os.path.exists(full_path):
                    raise FileNotFoundError(f"The camera data file does not exist: {full_path}")

                if info_name == 'left_image_path' or info_name == 'right_image_path':
                    print(cam_data_path)
                    camera_id = cam_data_path.split("/")[-2]  # 使用"_"分割字符串
                elif info_name == 'occ_path' or info_name == 'bev_path':
                    camera_id = cam_data_path.split("_")[-2]

                cam_key = f'data_{token}_{info_name}_{camera_id}'

                # cam_key = f'data_{token}_{info_name}'

                with open(full_path, 'rb') as cam_file:
                    txn.put(cam_key.encode(), cam_file.read())
                info[info_name] = cam_key

        items_processed += 1
        if items_processed % batch_size == 0:
            txn.commit()
            txn = env.begin(write=True)

    # 处理剩余的数据
    if items_processed % batch_size != 0:
        txn.commit()

    txn = env.begin(write=True)
    txn.put('pkl'.encode(), pickle.dumps(pkl_data))
    txn.commit()

    env.close()


def create_lmdb_from_pkl_indoor_fusion(data_path, map_size, batch_size=1000):
    pkl_path = data_path['pkl_file_path']
    lmdb_path = data_path['lmdb_database_path']

    with open(pkl_path, 'rb') as file:
        pkl_data = pickle.load(file)

    Path(lmdb_path).mkdir(parents=True, exist_ok=True)

    env = lmdb.open(lmdb_path, map_size=int(map_size))

    items_processed = 0
    txn = env.begin(write=True)

    for info in tqdm(pkl_data['infos'], desc='Converting PKL to LMDB', unit='frame'):
        token = info["token"]

        # info path
        for info_name in ['cams', 'lidar_path', 'lidar1_path', 'occ_path', 'bev_path', 'occ_fill_path']:
            if info_name in info:

                # 检查cam_data_path文件是否存在
                cam_data_path = info[info_name]
                # print("Camera data path:", repr(cam_data_path))

                # 检查并打印当前工作目录
                # print("Current Working Directory:", os.getcwd())

                # 打印绝对路径以验证
                full_path = os.path.abspath(cam_data_path)
                # print("Full path resolved:", full_path)

                if not os.path.exists(full_path):
                    raise FileNotFoundError(f"The camera data file does not exist: {full_path}")

                # cam_data_path = info[info_name]
                cam_key = f'data_{token}_{info_name}'

                with open(full_path, 'rb') as cam_file:
                    txn.put(cam_key.encode(), cam_file.read())
                info[info_name] = cam_key

        items_processed += 1
        if items_processed % batch_size == 0:
            txn.commit()
            txn = env.begin(write=True)

    # 处理剩余的数据
    if items_processed % batch_size != 0:
        txn.commit()

    txn = env.begin(write=True)
    txn.put('pkl'.encode(), pickle.dumps(pkl_data))
    txn.commit()

    env.close()

def create_lmdb_from_pkl_indoor_zed(data_path, map_size, batch_size=1000):
    pkl_path = data_path['pkl_file_path']
    lmdb_path = data_path['lmdb_database_path']

    with open(pkl_path, 'rb') as file:
        pkl_data = pickle.load(file)

    Path(lmdb_path).mkdir(parents=True, exist_ok=True)

    env = lmdb.open(lmdb_path, map_size=int(map_size))

    items_processed = 0
    txn = env.begin(write=True)

    for info in tqdm(pkl_data['infos'], desc='Converting PKL to LMDB', unit='frame'):
        token = info["token"]

        # info path
        for info_name in ['left_image_path', 'right_image_path', 'bev_path', 'occ_path', 'occ_fill_path']:
            if info_name in info:

                # 检查cam_data_path文件是否存在
                cam_data_path = info[info_name]
                # print("Camera data path:", repr(cam_data_path))

                # 检查并打印当前工作目录
                # print("Current Working Directory:", os.getcwd())

                # 打印绝对路径以验证
                full_path = os.path.abspath(cam_data_path)
                # print("Full path resolved:", full_path)

                if not os.path.exists(full_path):
                    raise FileNotFoundError(f"The camera data file does not exist: {full_path}")

                # cam_data_path = info[info_name]
                cam_key = f'data_{token}_{info_name}'

                with open(full_path, 'rb') as cam_file:
                    txn.put(cam_key.encode(), cam_file.read())
                info[info_name] = cam_key

        items_processed += 1
        if items_processed % batch_size == 0:
            txn.commit()
            txn = env.begin(write=True)

    # 处理剩余的数据
    if items_processed % batch_size != 0:
        txn.commit()

    txn = env.begin(write=True)
    txn.put('pkl'.encode(), pickle.dumps(pkl_data))
    txn.commit()

    env.close()



if __name__ == "__main__":
    lmdb_file_size = 1e12  # 1TB

    # mini_train_paths = {
    #     'pkl_file_path': '/home/<USER>/nuscenes_infos_train_mini.pkl',
    #     'lmdb_database_path': '/home/<USER>/LMDB_data/lmdb_surroundocc_infos_train_mini'
    # }
    #
    # mini_val_paths = {
    #     'pkl_file_path': '/home/<USER>/nuscenes_infos_val_mini.pkl',
    #     'lmdb_database_path': '/home/<USER>/LMDB_data/lmdb_surroundocc_infos_val_mini'
    # }
    #
    # train_paths = {
    #     'pkl_file_path': '/home/<USER>/nuscenes_infos_train.pkl',
    #     'lmdb_database_path': '/home/<USER>/LMDB_data/lmdb_surroundocc_infos_train'
    # }
    #
    # val_paths = {
    #     'pkl_file_path': '/home/<USER>/nuscenes_infos_val.pkl',
    #     'lmdb_database_path': '/home/<USER>/LMDB_data/lmdb_surroundocc_infos_val'
    # }
    #
    # kd_train_paths = {
    #     'pkl_file_path': './data/nuscenes_infos_train_addKd.pkl',
    #     'lmdb_database_path': './data/LMDB_data/nuscenes_infos_train_addKd'
    # }

    # kjl_train_paths = {
    #     'pkl_file_path': '/home/<USER>/kujiale_3FO3P2INJDB4_train.pkl',
    #     'lmdb_database_path': '/home/<USER>/kujiale_3FO3P2INJDB4_train',
    # }
    #
    # kjl_val_paths_ = {
    #     'pkl_file_path': '/home/<USER>/kujiale_3FO3P2INJDB4_val.pkl',
    #     'lmdb_database_path': '/home/<USER>/kujiale_3FO3P2INJDB4_val',
    # }

    stereo_train_paths = {
        'pkl_file_path': '/home/<USER>/yp/Datasets/kws_deliver_0509/pkl/kujiale_fusion_0509_150cm_new_train.pkl',
        'lmdb_database_path': '/home/<USER>/yp/Datasets/kws_deliver_0509/lmdb/kujiale_fusion_0509_150cm_new_train',
    }
    stereo_val_paths = {
        'pkl_file_path': '/home/<USER>/yp/Datasets/kws_deliver_0509/pkl/kujiale_fusion_0509_150cm_new_val.pkl',
        'lmdb_database_path': '/home/<USER>/yp/Datasets/kws_deliver_0509/lmdb/kujiale_fusion_0509_150cm_new_val',
    }

    fusion_train_paths = {
        'pkl_file_path': '/home/<USER>/kws_deliver_0509/pkl/kujiale_fusion-B_0509_train_mini.pkl',
        'lmdb_database_path': '/home/<USER>/kws_deliver_0509/lmdb/kujiale_fusion-B_0509_train_mini',
    }
    fusion_val_paths = {
        'pkl_file_path': '/home/<USER>/kws_deliver_0509/pkl/kujiale_fusion-B_0509_val_mini.pkl',
        'lmdb_database_path': '/home/<USER>/kws_deliver_0509/lmdb/kujiale_fusion-B_0509_val_mini',
    }

    # indoor
    indoor_zed_train_paths = {
        'pkl_file_path': '/home/<USER>/indoor/zed_lidar/pkl/indoor_zed_lidar_0718_train.pkl',
        'lmdb_database_path': '/home/<USER>/indoor/zed_lidar/lmdb/indoor_zed_lidar_0717_train111',
    }
    indoor_zed_val_paths = {
        'pkl_file_path': '/home/<USER>/indoor/zed_lidar/pkl/indoor_zed_lidar_0717_val.pkl',
        'lmdb_database_path': '/home/<USER>/indoor/zed_lidar/lmdb/indoor_zed_lidar_0717_val++',
    }

    indoor_fusion_train_paths = {
        'pkl_file_path': '/home/<USER>/indoor/oms_lidar/pkl/indoor_oms_lidar_0717_train.pkl',
        'lmdb_database_path': '/home/<USER>/indoor/oms_lidar/lmdb/indoor_oms_lidar_0717_train++',
    }
    indoor_fusion_val_paths = {
        'pkl_file_path': '/home/<USER>/indoor/oms_lidar/pkl/indoor_oms_lidar_0717_val.pkl',
        'lmdb_database_path': '/home/<USER>/indoor/oms_lidar/lmdb/indoor_oms_lidar_0717_val++',
    }

    create_lmdb_from_pkl_kjl_fusion(
        data_path=stereo_train_paths,
        map_size=lmdb_file_size
    )

    create_lmdb_from_pkl_kjl_fusion(
        data_path=stereo_val_paths,
        map_size=lmdb_file_size
    )

    # create_lmdb_from_pkl_indoor_zed(
    #     data_path=indoor_zed_train_paths,
    #     map_size=lmdb_file_size
    # )

    # create_lmdb_from_pkl_indoor_zed(
    #     data_path=indoor_zed_val_paths,
    #     map_size=lmdb_file_size
    # )

    
    # create_lmdb_from_pkl_indoor_fusion(
    #     data_path=indoor_fusion_train_paths,
    #     map_size=lmdb_file_size
    # )

    # create_lmdb_from_pkl_indoor_fusion(
    #     data_path=indoor_fusion_val_paths,
    #     map_size=lmdb_file_size
    # )


