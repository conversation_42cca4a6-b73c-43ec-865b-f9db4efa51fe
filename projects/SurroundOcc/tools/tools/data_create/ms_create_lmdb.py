import lmdb
import pickle
from pathlib import Path
from tqdm import tqdm
import os
from multiprocessing import Pool
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor

os.chdir('/home/<USER>/new_grass_scene')


def process_info(info):
    token = info["token"]
    for info_name in ['left_image_path', 'right_image_path', 'occ_path', 'bev_path']:
        if info_name in info:
            cam_data_path = info[info_name]
            full_path = os.path.abspath(cam_data_path)
            if not os.path.exists(full_path):
                raise FileNotFoundError(f"The camera data file does not exist: {full_path}")
            cam_key = f'data_{token}_{info_name}'
            with open(full_path, 'rb') as cam_file:
                return cam_key, cam_file.read()

def create_lmdb_from_pkl_kjl_stereo(data_path, map_size, batch_size=1000):
    pkl_path = data_path['pkl_file_path']
    lmdb_path = data_path['lmdb_database_path']

    with open(pkl_path, 'rb') as file:
        pkl_data = pickle.load(file)

    Path(lmdb_path).mkdir(parents=True, exist_ok=True)

    env = lmdb.open(lmdb_path, map_size=int(map_size))

    items_processed = 0

    with ThreadPoolExecutor() as executor:
        futures = []
        for info in pkl_data['infos']:
            futures.append(executor.submit(process_info, info))
        with env.begin(write=True) as txn:
            for future in tqdm(futures, desc='Converting PKL to LMDB', unit='frame'):
                cam_key, cam_data = future.result()
                txn.put(cam_key.encode(), cam_data)
                items_processed += 1
                if items_processed % batch_size == 0:
                    txn.commit()
                    txn = env.begin(write=True)

    # 处理剩余的数据
    if items_processed % batch_size != 0:
        with env.begin(write=True) as txn:
            txn.commit()

if __name__ == "__main__":
    lmdb_file_size = 1e12  # 1TB

    # mini_train_paths = {
    #     'pkl_file_path': '/home/<USER>/nuscenes_infos_train_mini.pkl',
    #     'lmdb_database_path': '/home/<USER>/LMDB_data/lmdb_surroundocc_infos_train_mini'
    # }
    #
    # mini_val_paths = {
    #     'pkl_file_path': '/home/<USER>/nuscenes_infos_val_mini.pkl',
    #     'lmdb_database_path': '/home/<USER>/LMDB_data/lmdb_surroundocc_infos_val_mini'
    # }
    #
    # train_paths = {
    #     'pkl_file_path': '/home/<USER>/nuscenes_infos_train.pkl',
    #     'lmdb_database_path': '/home/<USER>/LMDB_data/lmdb_surroundocc_infos_train'
    # }
    #
    # val_paths = {
    #     'pkl_file_path': '/home/<USER>/nuscenes_infos_val.pkl',
    #     'lmdb_database_path': '/home/<USER>/LMDB_data/lmdb_surroundocc_infos_val'
    # }
    #
    # kd_train_paths = {
    #     'pkl_file_path': './data/nuscenes_infos_train_addKd.pkl',
    #     'lmdb_database_path': './data/LMDB_data/nuscenes_infos_train_addKd'
    # }

    # kjl_train_paths = {
    #     'pkl_file_path': '/home/<USER>/kujiale_3FO3P2INJDB4_train.pkl',
    #     'lmdb_database_path': '/home/<USER>/kujiale_3FO3P2INJDB4_train',
    # }
    #
    # kjl_val_paths_ = {
    #     'pkl_file_path': '/home/<USER>/kujiale_3FO3P2INJDB4_val.pkl',
    #     'lmdb_database_path': '/home/<USER>/kujiale_3FO3P2INJDB4_val',
    # }

    stereo_train_paths = {
        'pkl_file_path': '/home/<USER>/new_grass_scene/pkl/gecaoji_d5_train.pkl',
        'lmdb_database_path': '/home/<USER>/new_grass_scene/lmdb/gecaoji_d5_train_ms',
    }

    stereo_val_paths = {
        'pkl_file_path': '/home/<USER>/new_grass_scene/pkl/gecaoji_d1_val.pkl',
        'lmdb_database_path': '/home/<USER>/new_grass_scene/lmdb/gecaoji_d1_val_ms',
    }

    fusion_train_paths = {
        'pkl_file_path': '/home/<USER>/kws_deliver_0410/pkl/kujiale_fusion_horizon_d17_train.pkl',
        'lmdb_database_path': '/home/<USER>/kws_deliver_0410/lmdb/kujiale_fusion_horizon_d17_train',
    }

    fusion_val_paths = {
        'pkl_file_path': '/home/<USER>/kws_deliver_0410/pkl/kujiale_fusion_horizon_d4_val.pkl',
        'lmdb_database_path': '/home/<USER>/kws_deliver_0410/lmdb/kujiale_fusion_horizon_d4_val',
    }

    # create_lmdb_from_pkl_kjl_stereo(
    #     data_path=stereo_train_paths,
    #     map_size=lmdb_file_size
    # )

    create_lmdb_from_pkl_kjl_stereo(
        data_path=stereo_val_paths,
        map_size=lmdb_file_size
    )

    # create_lmdb_from_pkl_kjl_fusion(
    #     data_path=fusion_train_paths,
    #     map_size=lmdb_file_size
    # )
    #
    # create_lmdb_from_pkl_kjl_fusion(
    #     data_path=fusion_val_paths,
    #     map_size=lmdb_file_size
    # )
