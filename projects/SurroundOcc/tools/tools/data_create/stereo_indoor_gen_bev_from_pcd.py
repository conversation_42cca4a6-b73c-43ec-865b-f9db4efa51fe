import numpy as np
import cv2
import os
import open3d as o3d
import copy

import math
from tqdm import tqdm
import random
from PIL import Image
import matplotlib.pyplot as plt


def crop_point_cloud(pcd, crop_range):
    bounding_box = o3d.geometry.AxisAlignedBoundingBox(crop_range[0], crop_range[1])
    return pcd.crop(bounding_box)

def get_points(occupancy_range, voxel_size, min_distance=0.1):
    """
    occupancy_range: 感知范围#以房顶为中心4x4x2.9的范围，2.9表示向下0.1m到3米的范围
    voxel_size：单个格子尺寸
    """
    box_min_bound = occupancy_range[0]
    box_max_bound = occupancy_range[1]

    # num_x=math.ceil((-occupancy_range[0][0]-voxel_size/2)/voxel_size)+math.ceil((occupancy_range[1][0]-voxel_size/2)/voxel_size)+1
    # num_y=math.ceil((-occupancy_range[0][1]-voxel_size/2)/voxel_size)+math.ceil((occupancy_range[1][1]-voxel_size/2)/voxel_size)+1
    # num_z=math.ceil((-occupancy_range[0][2]-voxel_size/2)/voxel_size)+math.ceil((occupancy_range[1][2]-voxel_size/2)/voxel_size)+1

    # cube_resultion=(num_x,num_y,num_z)
    cube_resultion = (box_max_bound - box_min_bound + voxel_size / 2) / voxel_size

    x_grid = np.linspace(box_min_bound[0], box_max_bound[0] - voxel_size / 2., math.ceil(cube_resultion[0]))
    y_grid = np.linspace(box_min_bound[1], box_max_bound[1] - voxel_size / 2., math.ceil(cube_resultion[1]))
    z_grid = np.linspace(box_min_bound[2], box_max_bound[2] - voxel_size / 2., math.ceil(cube_resultion[2]))
    cube_grid = np.asarray(np.meshgrid(x_grid, y_grid, z_grid)).transpose(2, 1, 3, 0).reshape(-1, 3)
    return cube_grid.astype(np.float32)

def mk_mask():
    new_pic = np.zeros((80, 80), dtype=np.uint8)

    h, w = new_pic.shape

    angle_deg = 82.47/2  # 这个相当于是fov的一半

    # angle_deg = 30

    angle_rad = math.radians(angle_deg)
    tan_angle = math.tan(angle_rad)

    # Traverse all pixels
    for i in range(h):
        for j in range(w):
            dx = i - h  # 高
            dy = j - w / 2  # 宽

            # Check pixel side
            is_right = dy > 0

            # Calculate tangent value for current pixel
            if dx == 0:
                continue
            tan_val = float(dy) / dx  # 正在左半区，负在右半区

            # Check if pixel is below the angle lines
            if (is_right and tan_val > -tan_angle) or (not is_right and tan_val < tan_angle):
                new_pic[i, j] = 1

    return new_pic

def is_line_intersect_boundary_2(x1, y1, x2, y2, pic, h, w):
    # pic = cv2.resize(pic,(400,400),interpolation=cv2.INTER_NEAREST)
    # 为绘制轮廓和线段的图像创建一个空画布（全0），尺寸为原图像尺寸的一样
    canvas2 = np.zeros((h, w), dtype=np.uint8)  # canvas2 = torch.zeros(h, w, dtype=torch.uint8)

    # 在第二个画布上绘制线段 - line_img
    cv2.line(canvas2, (x1, y1), (x2, y2), color=255, thickness=1)

    # 计算两个画布的逐像素位与（bitwise_and）运算。
    # 若存在与操作结果值为255的像素点，说明线段与边界存在相交。

    intersection_img = cv2.bitwise_and(pic, canvas2)  # torch.bitwise_and()

    intersection_pixels = cv2.findNonZero(intersection_img)

    # intersection_pixels = np.any(intersection_img == 255)#torch.any()

    return intersection_pixels

def generate_visible(img, mask_0):
    new_pic = img

    temp_pic = np.zeros_like(new_pic)

    image_bigger = Image.fromarray(new_pic.astype('uint8'), mode='L')
    # image_bigger.show()
    # image_bigger.save("plus10_60_angle.png")
    new_image = Image.fromarray(temp_pic.astype('uint8'), mode='L')

    # 检查每个像素点
    # time1=time.time()

    mid_y = image_bigger.height - 1

    mid_x = image_bigger.width // 2

    for x in range(image_bigger.width):
        for y in range(image_bigger.height):

            points = is_line_intersect_boundary_2(x, y, image_bigger.width // 2, image_bigger.height - 1, new_pic,
                                                  image_bigger.height, image_bigger.width)

            # 这块代码有进一步优化空间
            # 1是可能不一定要遍历所有像素，只需要考虑最左、最右、最上方三个边
            # 2是距离最近的点位置肯定是在行数最大处取得，直接对那一点赋值就行

            if points is not None:
                if len(points) > 0:

                    max_d = 80 * 80 * 2

                    xx = -1
                    yy = -1

                    for point in points:

                        x_ = point[0][0]
                        y_ = point[0][1]

                        d = (mid_x - x_) ** 2 + (mid_y - y_) ** 2

                        if d < max_d:
                            max_d = d
                            xx = x_
                            yy = y_

                new_image.putpixel((xx, yy), 255)  # 障碍物

            else:

                # new_image.putpixel((x, y), 128)  #可通行
                new_image.putpixel((x, y), 128)  # 可通行

    ans_image = np.array(new_image)

    ans_image = ans_image * mask_0  # fov

    return ans_image
def create_2d_voxel_fast(voxel_grid, occupancy_range, mask_):
    bbox = voxel_grid.get_axis_aligned_bounding_box()
    min_bound = np.array(bbox.min_bound)  # 栅格的边
    max_bound = np.array(bbox.max_bound)  # 栅格的外边
    size_extend = max_bound - min_bound
    voxel_size = voxel_grid.voxel_size
    # dim = np.round(size_extend / voxel_size).astype(np.int)#

    # **************************************新逻辑*************************************

    num_x = (occupancy_range[1][0] - (occupancy_range[0][0] - voxel_size / 2)) / voxel_size

    num_y = (occupancy_range[1][1] - (occupancy_range[0][1] - voxel_size / 2)) / voxel_size

    f_x = math.floor(num_x)

    f_y = math.floor(num_y)

    if (f_x != num_x):
        num_x = f_x + 1

    if (f_y != num_y):
        num_y = f_y + 1

    num_x = int(num_x)
    num_y = int(num_y)

    # if dim[0]<num1:
    dx_l = math.floor((-occupancy_range[0][0] + min_bound[0] + voxel_size / 2) / voxel_size)
    # if  dim[1]<num2:
    dy_l = math.floor((-occupancy_range[0][1] + min_bound[1] + voxel_size / 2) / voxel_size)

    # 获取voxel_grid的所有voxel坐标
    voxel_coordinates = voxel_grid.get_voxels()
    voxel_colors = [voxel.color for voxel in voxel_coordinates]  # wqx
    # voxel_colors = voxel_grid.get_voxel_colors()

    # 使用grid_index计算voxel坐标
    voxel_coordinates = np.array([i.grid_index for i in voxel_coordinates])
    voxel_colors = np.array([i for i in voxel_colors])  # wqx
    # voxel_all=np.concatenate((voxel_coordinates,voxel_colors),axis=1)
    # voxel_coordinates = (voxel_coordinates - min_bound).astype(int)
    # voxel_coordinates = (voxel_coordinates * voxel_size).astype(int)

    # point_000 = np.array([0, 0, 0])
    # voxel_index_000 = np.floor((point_000 - min_bound) / voxel_size).astype(int)

    # assert dim[2] > 399

    # 创建空的二维体素数组
    voxel_2d = np.zeros((num_y, num_x), dtype=np.uint8)

    nonzero_indices = np.array(voxel_coordinates)

    voxel_2d[voxel_coordinates[:, 1] + dy_l, voxel_coordinates[:, 0] + dx_l] = 255

    voxel_2d = cv2.flip(voxel_2d, 0)

    result = generate_visible(voxel_2d, mask_)


    # plt.figure()
    # plt.imshow(result)
    # plt.show()

    return result

    # origin = np.asarray(voxel_grid.origin)
    # # 计算体素中心坐标
    # voxel_centers = (tuple(voxel_size * (coord + 0.5) + origin for coord in voxel_coordinates))
    # voxel_centers = np.array(voxel_centers)

    # return  nonzero_indices, voxel_centers
    # return voxel_2d_400, nonzero_indices, voxel_centers


# 参数部分

if __name__ == '__main__':





    voxel_size = 0.05    


    # max_distance = 1.05
    # min_distance = 0.05
    # sample_distance = np.linspace(min_distance, max_distance, round((max_distance - min_distance) / 0.01))  # 100份儿切段
    occupancy_range = np.array([[-1.975, 0.025, -0.00], [2, 4, 0.03]])

    root_pcd_dir = '/home/<USER>/proj/temp/indoor/lidar_zed/ouster'
    save_dir = '/home/<USER>/proj/temp/indoor/lidar_zed/ouster_bev'
    # os.makedirs(save_dir, exist_ok=True)
    pcd_files = os.listdir(root_pcd_dir)
    pcd_files.sort()
    for pcd_file in pcd_files:
        pcd_file_path = os.path.join(root_pcd_dir, pcd_file)
        print(pcd_file_path)
        whole_pcd = o3d.io.read_point_cloud(pcd_file_path)  
        #TODO：此处points 是否要除以1000
        points = np.asarray(whole_pcd.points)
        # TODO: 滤除50cm以内的点
        # 滤除50cm以内的点
        points = points[np.linalg.norm(points, axis=1) > 0.2]

        whole_pcd.points = o3d.utility.Vector3dVector(points)

        # TODO： transform 外参
        whole_pcd.transform(
            np.linalg.inv(
                np.array([[0.474472, 0.268451, 0.838338, 0.181437],
                        [-0.870536, 0.00185493, 0.492101, 0.14509],
                        [0.13055, -0.963292, 0.234576, -0.187475],
                        [0,0,0,1]], dtype=np.float64)
            )
        )

        whole_pcd.transform( np.array([
                            [1, 0, 0, 0],
                            [0, 0, 1, 0],
                            [0, -1, 0, 0],
                            [0, 0, 0, 1]], dtype=np.float64)
        )



        # 可视化点云
        # o3d.visualization.draw_geometries([pcd])

        pcd_copy = copy.deepcopy(whole_pcd)

        cropped_pcd = crop_point_cloud(pcd_copy, occupancy_range)  # 面片#以cam坐标系去筛选范围内的点

        # 这个地方append 一个最原始的点
        new_robust_point = np.array([[occupancy_range[0][0], occupancy_range[0][1], occupancy_range[0][2]]])

        for point in new_robust_point:
                cropped_pcd.points.append(point)
                        # points_semantic=np.asarray(vis_pcd_cropped.colors)
                        # 计算每个维度的最小值和最大值

        points = np.asarray(cropped_pcd.points)
        x_min, y_min, z_min = np.min(points, axis=0)
        x_max, y_max, z_max = np.max(points, axis=0)

        # 打印范围信息
        print("X范围: [{}, {}]".format(x_min, x_max))
        print("Y范围: [{}, {}]".format(y_min, y_max))
        print("Z范围: [{}, {}]".format(z_min, z_max))

        voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(cropped_pcd, voxel_size)  # 整个局部空间所有的体素占用情况
        origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0, 0, 0])
        # o3d.visualization.draw_geometries([voxel_grid, origin])
        mask = mk_mask()
        img = create_2d_voxel_fast(voxel_grid, occupancy_range, mask)
        # 保存图片
        img_path = os.path.join(save_dir, pcd_file.split('/')[-1].replace('.pcd', '.png'))
        cv2.imwrite(img_path, img)