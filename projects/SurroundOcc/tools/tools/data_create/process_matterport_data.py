import numpy as np
import cv2 as cv
import os
import matplotlib.pyplot as plt
import logging
import tqdm
from scipy.spatial.transform import Rotation as R
import open3d as o3d
import open3d.visualization.gui as gui
import open3d.visualization.rendering as rendering
import numpy as np
from glob import glob
import os
import cv2
from sklearn.utils import assert_all_finite
import json
import os.path as osp
import copy
import math
from PIL import Image, ImageDraw
import time
import shutil

from mpl_toolkits.mplot3d import Axes3D
from sklearn.preprocessing import MinMaxScaler

import zipfile

def unzip_and_delete_files(directory):
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.zip'):
                file_path = os.path.join(root, file)
                destination_folder = root  
                with zipfile.ZipFile(file_path, 'r') as zip_ref:
                    zip_ref.extractall(destination_folder)
                os.remove(file_path)
                
def unzip_and_delete(zip_dir):
    # 获取指定目录下所有文件
    for item in os.listdir(zip_dir):
        # 如果文件是 .zip 文件
        if item.endswith('.zip'):
            zip_path = os.path.join(zip_dir, item)
            # 创建解压文件的目录
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(zip_dir)
            # 解压后删除 .zip 文件
            os.remove(zip_path)
            print(f'{item} 解压完毕并已删除.')
            
FILETYPES = [
    'undistorted_camera_parameters',
    'undistorted_color_images',
    'undistorted_depth_images',
]

def clear_files(path):
    if not os.path.exists(path):
        print("文件夹不存在！")
        exit()
    
    for filename in os.listdir(path):
        file_path = os.path.join(path, filename)
        try:
            if os.path.isfile(file_path):
                os.unlink(file_path)
        except Exception as e:
            print(f"删除 {file_path} 时出错：{e}")
            
def make_dir(path):
    if not os.path.exists(path):
        os.makedirs(path)
        return True
    else:
        return False

def clear_files(path):
    if not os.path.exists(path):
        print("文件夹不存在！")
        exit()
    
    for filename in os.listdir(path):
        file_path = os.path.join(path, filename)
        try:
            if os.path.isfile(file_path):
                os.unlink(file_path)
        except Exception as e:
            print(f"删除 {file_path} 时出错：{e}")

def write_to_txt(matrix, file_path):
    with open(file_path, 'w') as f:
        for row in matrix:
            f.write(' '.join([str(num) for num in row]) + '\n')

def crop_point_cloud(pcd, crop_range):
    bounding_box = o3d.geometry.AxisAlignedBoundingBox(crop_range[0], crop_range[1])
    return pcd.crop(bounding_box)

def get_pcd_from_depth(depth_array, intrin):

    h = depth_array.shape[0]
    w = depth_array.shape[1]
    fx = intrin[0][0]  # 焦距x
    fy = intrin[1][1]  # 焦距y
    cx = intrin[0][2]  # 中心点x
    cy = intrin[1][2]  # 中心点y
    

    # fov_w = (math.atan(w / (2 * fx)) * 2) * (180 / math.pi)  # 将弧度转换为度数
    # fov_h = (math.atan(h / (2 * fy)) * 2) * (180 / math.pi)  # 将弧度转换为度数
    
    angle_tof_h = 20
    angle_tof_w = 30
    
    H_tof = np.tan(angle_tof_h * np.pi / 360) * 2 * fy  # 反向计算对应的H
    W_tof = np.tan(angle_tof_w * np.pi / 360) * 2 * fx

    all_line = H_tof 

    all_line_2 = W_tof

    start = int(h / 2 - all_line / 2)
    end = int(h / 2 + all_line / 2)

    depth_array[:start, :] = 0
    depth_array[end:, :] = 0

    start_2 = int(w / 2 - all_line_2 / 2)
    end_2 = int(w / 2 + all_line_2 / 2)
    
    # print(start,end)#up 
    # print(start_2,end_2)#left
    
    depth_array[:, :start_2] = 0
    depth_array[:, end_2:] = 0

    points = []

    # tof_h = 400
    # tof_w = 640

    # step_h = int(all_line / (tof_h-1))
    # step_w = int(all_line_2/ (tof_w-1))
    
    # print("=====================================", step_h)
    # print("=====================================", step_w)
    # print("=====================================", all_line)


    # mask_d = np.zeros_like(depth_array)
    # mask_d = np.load('/home/<USER>/ecoaitoolkit/tools/data_create/indoor/tof_depth_mask1.npy')

    # depth_array = depth_array * mask_d
    #points = crop_depth_image_vectorized(depth_array, intrin, y_range=(-1.45, 0.03))

    for i in range(start, end, 4):
        for j in range(start_2, end_2, 4):
    # # for i in range(start, end, step_h):
    # #     for j in range(start_2, end_2, step_w):

            if depth_array[i][j]:
                # mask_d[i][j] = 1
                z3 = depth_array[i][j] / 1000

                x3 = (j - cx) * z3 / fx
                y3 = (i - cy) * z3 / fy

                point = np.stack((x3, y3, z3), axis=-1)
                points.append(point)
                
    # print(len(points))
    ans_points = np.array(points)
    return ans_points  # ,mask_d

    #return points

def depth2point(depth_array, intrinsic):
    
    height = depth_array.shape[0]
    width = depth_array.shape[1]
    fx = intrinsic[0][0]  # 焦距x
    fy = intrinsic[1][1]  # 焦距y
    cx = intrinsic[0][2]  # 中心点x
    cy = intrinsic[1][2]  # 中心点y
    
    # points = []
    # for i in range(0, height, 1):
    #     for j in range(0, width, 1):
    #         if (depth_array[i][j] > 0):
    #             z3 = depth_array[i][j] / 1000
    #             x3 = (j - cx) * z3 / fx
    #             y3 = (i - cy) * z3 / fy
    #             point = np.stack((x3, y3, z3), axis=-1)
    #             points.append(point)
    # ans_points = np.array(points)
    
    # # 创建 u, v 网格
    # u, v = np.meshgrid(np.arange(width), np.arange(height))
    # # 将深度值转换为浮点数并单位转换为米
    # Z = depth_array.astype(np.float32) / 1000.0  # 假设深度图以毫米为单位
    
    # 设置间隔
    step = 4
    # 创建 u, v 网格并应用间隔
    u, v = np.meshgrid(np.arange(0, width, step), np.arange(0, height, step))

    # 提取对应的深度值
    Z = depth_array[::step, ::step].astype(np.float32) / 1000.0  # 假设深度图以毫米为单位

    # 忽略无效深度值 (假设无效深度值为0)
    valid = Z > 0
    Z = Z[valid]
    u = u[valid]
    v = v[valid]

    # 计算 X, Y, Z 坐标
    X = (u - cx) * Z / fx
    Y = (v - cy) * Z / fy

    # 将结果组合为 (N, 3) 形状的点云
    points = np.vstack((X, Y, Z)).transpose()
    
    # point_cloud_o3d = o3d.geometry.PointCloud()
    # point_cloud_o3d.points = o3d.utility.Vector3dVector(np.array(points))
    # tof_range = np.array([[-2, 0., 0], [2, 4, 1.45]])
    # point_cloud_o3d_filter = crop_point_cloud(point_cloud_o3d, tof_range)
    # origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0,0,0])
    # o3d.visualization.draw_geometries([point_cloud_o3d,origin])

    return points  # ,mask_d

def points2image(depth_path, image_path, intrinsic):
    
    depth_names = os.listdir(depth_path)
    depth_names = [f for f in depth_names if f.endswith('.png')]
    depth_names.sort()
    img_names = os.listdir(image_path)
    img_names = [f for f in img_names if f.endswith('.jpg')]
    img_names.sort()
    
    for i in range(len(depth_names)):
        depth_file_path = os.path.join(depth_path, depth_names[i])
        image_file_path = os.path.join(image_path, img_names[i])
        
        image = cv2.imread(image_file_path)
        depth = cv2.imread(depth_file_path, cv2.IMREAD_UNCHANGED)
        h, w = image.shape[:2]
        points_np = depth2point(depth, intrinsic)
        
        depth = points_np[:, 2]
        
        scaler = MinMaxScaler()
        depth_normalized = scaler.fit_transform(depth.reshape(-1, 1))
        colors = plt.cm.jet(depth_normalized)  # 使用jet色图
        
        pt_1 = points_np / np.expand_dims(points_np[:,2],1)
        u = np.round(intrinsic[0][0] * pt_1[:,0] + intrinsic[0][2])
        v = np.round(intrinsic[1][1] * pt_1[:,1] + intrinsic[1][2])
        
        v = v[(u>=0) & (u< w-1)]
        depth = depth[(u>=0) & (u< w-1)]
        colors = colors[(u>=0) & (u< w-1)]
        u = u[(u>=0) & (u< w-1)]
        
        u = u[(v>=0) & (v<h-1)]
        depth = depth[(v>=0) & (v<h-1)]
        colors = colors[(v>=0) & (v<h-1)]
        v = v[(v>=0) & (v<h-1)]

        v = v.astype("int32")
        u = u.astype("int32")
        
        colors_squeezed = np.squeeze(colors, axis=1)
        image[v,u] = (colors_squeezed[:, :3] * 255).astype("int32")
        image[v+1,u] = (colors_squeezed[:, :3] * 255).astype("int32")
        image[v,u+1] = (colors_squeezed[:, :3] * 255).astype("int32")
        image[v+1,u+1] = (colors_squeezed[:, :3] * 255).astype("int32")
        
        # cv2.imshow('Original Image', image)
        # cv2.waitKey(0)
        # cv2.destroyAllWindows()

        img_savePath = "/home/<USER>/work_bhp/data/grassland_outdoor/result/" + img_names[i]
        cv2.imwrite(img_savePath, image)
        print(img_savePath)

def extract_intrinsics_matrix(s_list):
    # s_list = s.split()  # 将字符串转换为字符串列表
    numbers = []
    for item in s_list:
        try:
            num = float(item)  # 尝试将每个元素转换为浮点数
            numbers.append(num)  # 如果转换成功，添加到数字列表中
        except ValueError:
            pass  # 如果转换失败，忽略该元素
    # 构建相机内参矩阵
    matrix = [[numbers[i * 3 + j] for j in range(3)] for i in range(3)]
    return matrix

def parse_conf(root_path):
    
    # 创建文件夹
    scene_id = "17DRP5sb8fy"
    new_cam1_rgb_dir = os.path.join(root_path, scene_id, "cam1_rgb")
    new_cam1_depth_dir = os.path.join(root_path, scene_id, "cam1_depth")
    cam_intrinsics_dir = os.path.join(root_path, scene_id, "cam_intrinsics")
    make_dir(new_cam1_rgb_dir)
    make_dir(new_cam1_depth_dir)
    make_dir(cam_intrinsics_dir)
    intrinsic_save_count = 0          #相机内参只保存1相机
    
    
    config_file_name = scene_id + ".conf"
    # 17DRP5sb8fy/undistorted_camera_parameters/17DRP5sb8fy/undistorted_camera_parameters/17DRP5sb8fy.conf"
    config_file_path = os.path.join(root_path, scene_id, FILETYPES[0], scene_id, FILETYPES[0], config_file_name)
    cam1_rgb_path = os.path.join(root_path, scene_id, FILETYPES[1], scene_id, FILETYPES[1])
    cam1_depth_path = os.path.join(root_path, scene_id, FILETYPES[2], scene_id, FILETYPES[2])
    
    with open(config_file_path, 'r') as file:
        scan_lines = file.readlines()
        for scan_line in scan_lines:
            # scan_id = scan_line.rstrip('\n')
            word_list = scan_line.split(" ")
            if (word_list[0] == "intrinsics_matrix" and intrinsic_save_count <= 1):
                if(intrinsic_save_count != 1): intrinsic_save_count += 1
                else:
                    intrinsics_matrix = extract_intrinsics_matrix(word_list)
                    cam1_intrinsics_name = "cam1_intrinsics.txt"
                    cam1_intrinsics_filepath = os.path.join(cam_intrinsics_dir, cam1_intrinsics_name)
                    write_to_txt(intrinsics_matrix, cam1_intrinsics_filepath)
                    intrinsic_save_count += 1

            elif word_list[0] == "scan":
                depth_name = word_list[1]
                image_name = word_list[2]
                if(depth_name[-7] == "1"):
                    source_rgb_file_path = os.path.join(cam1_rgb_path, image_name)
                    destination_rgb_file_path = os.path.join(new_cam1_rgb_dir, image_name)
                    shutil.copy(source_rgb_file_path, destination_rgb_file_path)
                    
                    source_depth_file_path = os.path.join(cam1_depth_path, depth_name)
                    destination_depth_file_path = os.path.join(new_cam1_depth_dir, depth_name)
                    shutil.copy(source_depth_file_path, destination_depth_file_path)
                    print(destination_depth_file_path)

def _get_rays_camxyz(occupancy_range, instrinsics, img_size, gridsize):
    # occupancy_range = np.array([[-1.975, -0.125, 0.025], [2, 0.03, 4]])
    # gridsize = 0.05
    # instrinsics = np.array([
    #                 [627.361, 0, 630.733],
    #                 [0, 627.189, 531.027],
    #                 [0, 0, 1]], dtype=np.float64)
    # img_size = (1072, 1280)

    # fov
    # img_size = (1072, 1280)
    fx = instrinsics[0][0]
    fy = instrinsics[1][1]
    cx = instrinsics[0][2]
    cy = instrinsics[1][2]

    fov_x = (math.atan(img_size[1] / (2 * fx)) * 2) * (180 / math.pi)  # 将弧度转换为度数
    fov_y = (math.atan(img_size[0] / (2 * fy)) * 2) * (180 / math.pi)  # 将弧度转换为度数

    # 定义左、右、前、上、下边界
    bound_left = occupancy_range[0][0]
    bound_right = occupancy_range[1][0]
    bound_far = occupancy_range[1][2]
    bound_near = occupancy_range[0][2]
    bound_up = occupancy_range[1][1]
    bound_down = occupancy_range[0][1]

    # num_x=math.ceil((-occupancy_range[0][0]-gridsize/2)/gridsize)+math.ceil((occupancy_range[1][0]-gridsize/2)/gridsize)+1
    # num_y=math.ceil((-occupancy_range[0][1]-gridsize/2)/gridsize)+math.ceil((occupancy_range[1][1]-gridsize/2)/gridsize)+1
    # num_z=math.ceil((-occupancy_range[0][2]-gridsize/2)/gridsize)+math.ceil((occupancy_range[1][2]-gridsize/2)/gridsize)+1

    num_x = math.ceil((occupancy_range[1][0] - occupancy_range[0][0]) / gridsize)
    num_y = math.ceil((occupancy_range[1][1] - occupancy_range[0][1]) / gridsize)   # y方向：3.099 => 4
    num_z = math.ceil((occupancy_range[1][2] - occupancy_range[0][2]) / gridsize)

    # 几个平面的每个grid所处的中心
    grid_centers = []

    # 左面
    # x = bound_left  # + gridsize / 2 从左侧格子的中心开始
    x = bound_left - gridsize / 2 + gridsize / 6
    for i in range(num_y * 3 - 1):  # 分成更小的格子
        for j in range(num_z * 3 - 1):
            y = bound_down + i * gridsize / 3 + gridsize / 6  # 中心点仍为 gridsize/2, 是否应为gridsize/6？
            z = bound_near + j * gridsize / 3 + gridsize / 6
            if abs(math.atan(x / z) * 2 * (180 / math.pi)) < fov_x and abs(   # 计算每个点是否在fov之内
                    math.atan(y / z) * 2 * (180 / math.pi)) < fov_y:
                grid_centers.append((x, y, z))

    # 右面
    # x = bound_right - gridsize / 2  # 从最右侧格子的中心开始
    x = bound_right - gridsize / 6  # 从最右侧格子的中心开始
    for i in range(num_y * 3 - 1):
        for j in range(num_z * 3 - 1):
            y = bound_down + i * gridsize / 3 + gridsize / 6
            z = bound_near + j * gridsize / 3 + gridsize / 6
            if abs(math.atan(x / z) * 2 * (180 / math.pi)) < fov_x and abs(
                    math.atan(y / z) * 2 * (180 / math.pi)) < fov_y:
                grid_centers.append((x, y, z))

    # 视锥深度方向平面
    # z = bound_far - gridsize / 2
    z = bound_far - gridsize / 6
    for i in range(num_x * 3 - 1):
        for j in range(num_y * 3 - 1):
            x = bound_left + i * gridsize / 3 + gridsize / 6
            y = bound_down + j * gridsize / 3 + gridsize / 6
            if abs(math.atan(x / z) * 2 * (180 / math.pi)) < fov_x and abs(
                    math.atan(y / z) * 2 * (180 / math.pi)) < fov_y:
                grid_centers.append((x, y, z))

    # 上平面
    # y = bound_down #+ gridsize / 2   # 这里已经是格子的中心了，还要 + gridsize / 2？
    y = bound_down - gridsize / 2 + gridsize / 6
    for i in range(num_x * 3 - 1):
        for j in range(num_z * 3 - 1):
            x = bound_left + i * gridsize / 3 + gridsize / 6
            z = bound_near + j * gridsize / 3 + gridsize / 6
            if abs(math.atan(x / z) * 2 * (180 / math.pi)) < fov_x and abs(
                    math.atan(y / z) * 2 * (180 / math.pi)) < fov_y:
                grid_centers.append((x, y, z))

    # up方向通过down进行定位，仍然是0.05（相机以下5cm）
    y = bound_up - gridsize / 6
    # y = bound_down + math.ceil((bound_up - bound_down) / gridsize) * gridsize - gridsize / 2
    for i in range(num_x * 3 - 1):
        for j in range(num_z * 3 - 1):
            x = bound_left + i * gridsize / 3 + gridsize / 6
            z = bound_near + j * gridsize / 3 + gridsize / 6
            if abs(math.atan(x / z) * 2 * (180 / math.pi)) < fov_x and abs(
                    math.atan(y / z) * 2 * (180 / math.pi)) < fov_y:
                grid_centers.append((x, y, z))
    return grid_centers

def get_points(occupancy_range, voxel_size, min_distance=0.1):
    """
    occupancy_range: 感知范围#以房顶为中心4x4x2.9的范围，2.9表示向下0.1m到3米的范围
    voxel_size：单个格子尺寸
    """
    box_min_bound = occupancy_range[0]
    box_max_bound = occupancy_range[1]

    # num_x=math.ceil((-occupancy_range[0][0]-voxel_size/2)/voxel_size)+math.ceil((occupancy_range[1][0]-voxel_size/2)/voxel_size)+1
    # num_y=math.ceil((-occupancy_range[0][1]-voxel_size/2)/voxel_size)+math.ceil((occupancy_range[1][1]-voxel_size/2)/voxel_size)+1
    # num_z=math.ceil((-occupancy_range[0][2]-voxel_size/2)/voxel_size)+math.ceil((occupancy_range[1][2]-voxel_size/2)/voxel_size)+1

    # cube_resultion=(num_x,num_y,num_z)
    cube_resultion = (box_max_bound - box_min_bound + voxel_size / 2) / voxel_size

    x_grid = np.linspace(box_min_bound[0], box_max_bound[0] - voxel_size / 2., math.ceil(cube_resultion[0]))
    y_grid = np.linspace(box_min_bound[1], box_max_bound[1] - voxel_size / 2., math.ceil(cube_resultion[1]))
    z_grid = np.linspace(box_min_bound[2], box_max_bound[2] - voxel_size / 2., math.ceil(cube_resultion[2]))
    cube_grid = np.asarray(np.meshgrid(x_grid, y_grid, z_grid)).transpose(2, 1, 3, 0).reshape(-1, 3)
    return cube_grid.astype(np.float32)

    # x_grid=np.linspace(box_min_bound[0]+voxel_size/2.,box_max_bound[0]-voxel_size/2.,round(cube_resultion[0]))
    # y_grid=np.linspace(box_min_bound[1]+voxel_size/2.,box_max_bound[1]-voxel_size/2.,round(cube_resultion[1]))
    # z_grid=np.linspace(box_min_bound[2]+voxel_size/2.,box_max_bound[2]-voxel_size/2.,round(cube_resultion[2]))
    # cube_grid=np.asarray(np.meshgrid(x_grid,y_grid,z_grid)).transpose(1,2,3,0).reshape(-1,3)
    # return cube_grid.astype(np.float32)

def grid_rays_to_labels(voxel_grid, camera_rays, sample_distance, total_points, voxel_size, H, W, occupancy_range):
    # voxel_grid是lidar扫到的占用的格子，occupancy_voxel_gird是camera看得到的格子

    # 相机到每个栅格边平面的射线，分成100份。
    camera_rays = camera_rays.reshape(1, -1)  # (N=88023, 3) => (1, N*3)
    sample_distance = sample_distance.reshape(-1, 1)    # (100)  =>  (100, 1)
    sample_rays = sample_distance @ camera_rays  # camera_rays表示图像平面每个点的世界坐标，与sample_rays相乘表示到摄像头之间的所有点, (100, N*3)
    camera_rays = camera_rays.reshape(-1, 3)  # (N=88023, 3)
    sample_rays_points = sample_rays.reshape(-1, 3)  # (N*100, 3) 等角度扇形展开，密集采样

    # voxel_grid空间是一个预置的立方体空间，比如左右2m，上下？m，前后2m；sample_rays_points是相机的射线空间
    # valid_mask是bev空间的mask，检查每个采样点是否位于预置栅格内
    valid_mask = voxel_grid.check_if_included(o3d.utility.Vector3dVector(sample_rays_points))   # list: N*100
    valid_mask_temp = np.asarray(valid_mask).reshape(sample_distance.shape[0], camera_rays.shape[0])    # (100, N)
    surface_index = valid_mask_temp.argmax(axis=0)  # (N) 找到每条射线上第一个True，也就是第一个遮挡的栅格

    occupancy_mask = np.zeros((sample_distance.shape[0], camera_rays.shape[0]), dtype=bool)  # (100, N) 视锥体空间的mask
    visiable_mask = np.zeros((sample_distance.shape[0], camera_rays.shape[0]), dtype=bool)  # (100, N) 视锥体空间的mask

    # empty_mask=np.ones((sample_distance.shape[0], camera_rays.shape[0]), dtype=bool)
    # visiable_mask_tensor[:surface_index_tensor+1]=torch.where(surface_index_tensor>0,        True       ,       False   )
    # 将每条射线上，第一个遮挡的位置，以及该位置到相机之间置为可视
    for i, val in enumerate(surface_index):
        if val == 0:  # 射线方向上没有点 即mesh对应处为空
            # empty_mask[:, i] = False
            continue
        else:
            visiable_mask[:val + 1, i] = True  # 表面和表面到相机之间的设置为true, 可视
            occupancy_mask[val, i] = True
    sample_rays_points = sample_rays_points.reshape(sample_distance.shape[0], camera_rays.shape[0], 3)  # （100, N, 3）视锥体空间
    occupancy_points = sample_rays_points[occupancy_mask]  # surface,视锥体空间
    visiable_points = sample_rays_points[visiable_mask]  # surface前面的点
    # ray_empty_points = sample_rays_points[empty_mask]#射线打过去完全空洞，但是这个解决不了某些情况

    show_lable = False
    if show_lable == True:
        vis_pcd = o3d.geometry.PointCloud()
        origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0, 0, 0])
        vis_pcd.points = o3d.utility.Vector3dVector(occupancy_points)
        # points_num=np.array(camera_coord_mesh.points).shape[0]
        # camera_coord_mesh.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(points_num, 3)))
        # voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd, voxel_size=0.05)
        o3d.visualization.draw_geometries([vis_pcd, origin])

    occupancy_pcd = o3d.geometry.PointCloud()
    occupancy_pcd.points = o3d.utility.Vector3dVector(occupancy_points)  # 视锥体空间

    # occupancy_range = np.array([[-1.975, -0.125, 0.025], [2, 0.02, 4]])
    occupancy_pcd = crop_point_cloud(occupancy_pcd, occupancy_range)

    new_robust_point = np.array([[occupancy_range[0][0], occupancy_range[0][1], occupancy_range[0][2]]])
    for point in new_robust_point:
        occupancy_pcd.points.append(point)

    # colors = np.zeros(occupancy_points.shape)
    # colors[:,2] = 255
    # occupancy_pcd.colors = o3d.utility.Vector3dVector(colors)
    # occupancy_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(occupancy_points.shape[0], 3)))

    # camera可以看见的格子数量, 把点放到格子里，很多点放到了同一个格子
    occupancy_voxel_gird = o3d.geometry.VoxelGrid.create_from_point_cloud(occupancy_pcd, voxel_size=voxel_size)

    # 这一步骤有点问题，将占据的点转化为True
    occupancy_labels = occupancy_voxel_gird.check_if_included(o3d.utility.Vector3dVector(total_points))

    # 处理可视空间，可视部分都为True
    visiable_pcd = o3d.geometry.PointCloud()
    visiable_pcd.points = o3d.utility.Vector3dVector(visiable_points)
    # visiable_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(points_num, 3)))
    visiable_voxel_gird = o3d.geometry.VoxelGrid.create_from_point_cloud(visiable_pcd, voxel_size=voxel_size)
    visiable_labels = visiable_voxel_gird.check_if_included(o3d.utility.Vector3dVector(total_points))

    # ray_empty_pcd = o3d.geometry.PointCloud()
    # ray_empty_pcd.points = o3d.utility.Vector3dVector(ray_empty_points)
    # # ray_empty_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(points_num, 3)))
    # ray_empty_voxel_gird = o3d.geometry.VoxelGrid.create_from_point_cloud(ray_empty_pcd, voxel_size=voxel_size)
    # ray_empty_labels = ray_empty_voxel_gird.check_if_included(o3d.utility.Vector3dVector(total_points))

    return occupancy_labels, visiable_labels  # ,ray_empty_labels

def gen_occupancy_pcd(pcd, dir_occupancy_save, img_size, occupancy_range, voxel_size, camera_rays, min_distance=0.05):
    """ 注意:3D坐标系是open3d所在的坐标系,但这里我修改成了相机坐标系了，生成的标签以相机为准
    """
    occupancy_range_ = occupancy_range.copy()
    # occupancy_range_[1][1] = 0.05

    camera_rays = np.array(camera_rays)

    max_distance = 1.0
    # min_distance = 0.05
    sample_distance = np.linspace(min_distance, max_distance, round((max_distance - min_distance) / 0.01))  # 100份儿切段
    # 生成80*80*4个格子
    total_points = get_points(occupancy_range_, voxel_size, min_distance=min_distance)

    # lidar 虑除
    # distances = np.sqrt(np.sum(points ** 2, axis=1))
    # pcd = pcd[distances > 0.5]

    pcd_copy = copy.deepcopy(pcd)  # Twl
    # aabb = pcd_copy.get_axis_aligned_bounding_box()
    # print("aabb:", aabb)
    
    # 计算定向边界框
    # obb = pcd_copy.get_oriented_bounding_box()
    # obb.color = (0, 1, 0)
    # 可视化
    # origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0,0,0])
    # o3d.visualization.draw_geometries([pcd_copy, origin, aabb, obb],
    #                               zoom=0.7,
    #                               front=[0.5439, -0.2333, -0.8060],
    #                               lookat=[2.4615, 2.1331, 1.338],
    #                               up=[-0.1781, -0.9708, 0.1608])


    # occupancy_range = np.array(
    #             [[-6.73797, -2.01431, 3.756], [3.31641, 5.88889, 13.039]])
    
    # occupancy_range = np.array(
    #             [[-2, 4, 0], [2, 5.5, 4]])
    cropped_pcd = crop_point_cloud(pcd_copy, occupancy_range)  # 面片#以cam坐标系去筛选范围内的点

    # origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0,0,0])
    # o3d.visualization.draw_geometries([cropped_pcd,origin])
    # 添加边界点
    new_robust_point = np.array([[occupancy_range[0][0], occupancy_range[0][1], occupancy_range[0][2]]])
    for point in new_robust_point:
        cropped_pcd.points.append(point)
        
        
    voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(cropped_pcd, voxel_size)  # 整个局部空间所有的体素占用情况

    # 只生成fov内的栅格状态，必须使用 total_points_fov
    # occupancy_labels = voxel_grid.check_if_included(o3d.utility.Vector3dVector(total_points))

    left_camera_rays = camera_rays

    # left_occupancy_labels：将每条射线第一个打到的表面置为True
    # left_visiable_labels： 将相机到每条射线第一次打到表面的点之间置为可视
    left_occupancy_labels, left_visiable_labels = grid_rays_to_labels(voxel_grid, left_camera_rays, sample_distance,
                                                                      total_points, voxel_size, img_size[1],
                                                                      img_size[0], occupancy_range)

    occupancy_labels = np.bitwise_or(left_occupancy_labels, left_occupancy_labels)  # 双目用来集成占据标签的

    labels = np.zeros_like(occupancy_labels, dtype=np.int8)
    labels[occupancy_labels] = 1
    # save
    np.save(dir_occupancy_save, labels)

def gen_occupancy_lable(pcd, img_size, occupancy_range, voxel_size, camera_rays, min_distance=0.05):
    """ 注意:3D坐标系是open3d所在的坐标系,但这里我修改成了相机坐标系了，生成的标签以相机为准
    """
    # occupancy_range_ = occupancy_range.copy()
    # occupancy_range_[1][1] = 0.05

    camera_rays = np.array(camera_rays)

    max_distance = 1.0
    # min_distance = 0.05
    sample_distance = np.linspace(min_distance, max_distance, round((max_distance - min_distance) / 0.01))  # 100份儿切段
    # 生成80*80*4个格子
    total_points = get_points(occupancy_range, voxel_size, min_distance=min_distance)

    # lidar 虑除
    # distances = np.sqrt(np.sum(points ** 2, axis=1))
    # pcd = pcd[distances > 0.5]

    # pcd_copy = copy.deepcopy(pcd)  # Twl
    # aabb = pcd_copy.get_axis_aligned_bounding_box()
    # print("aabb:", aabb)
    
    # 计算定向边界框
    # obb = pcd_copy.get_oriented_bounding_box()
    # obb.color = (0, 1, 0)
    # 可视化
    # origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0,0,0])
    # o3d.visualization.draw_geometries([pcd_copy, origin, aabb, obb],
    #                               zoom=0.7,
    #                               front=[0.5439, -0.2333, -0.8060],
    #                               lookat=[2.4615, 2.1331, 1.338],
    #                               up=[-0.1781, -0.9708, 0.1608])


    # occupancy_range = np.array(
    #             [[-6.73797, -2.01431, 3.756], [3.31641, 5.88889, 13.039]])
    
    # occupancy_range = np.array(
    #             [[-2, 4, 0], [2, 5.5, 4]])
    cropped_pcd = crop_point_cloud(pcd, occupancy_range)  # 面片#以cam坐标系去筛选范围内的点

    # origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0,0,0])
    # o3d.visualization.draw_geometries([cropped_pcd,origin])
    # 添加边界点
    new_robust_point = np.array([[occupancy_range[0][0], occupancy_range[0][1], occupancy_range[0][2]]])
    for point in new_robust_point:
        cropped_pcd.points.append(point)
        
        
    voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(cropped_pcd, voxel_size)  # 整个局部空间所有的体素占用情况

    # 只生成fov内的栅格状态，必须使用 total_points_fov
    # occupancy_labels = voxel_grid.check_if_included(o3d.utility.Vector3dVector(total_points))

    left_camera_rays = camera_rays

    # left_occupancy_labels：将每条射线第一个打到的表面置为True
    # left_visiable_labels： 将相机到每条射线第一次打到表面的点之间置为可视
    left_occupancy_labels, left_visiable_labels = grid_rays_to_labels(voxel_grid, left_camera_rays, sample_distance,
                                                                      total_points, voxel_size, img_size[1],
                                                                      img_size[0], occupancy_range)

    occupancy_labels = np.bitwise_or(left_occupancy_labels, left_occupancy_labels)  # 双目用来集成占据标签的

    labels = np.zeros_like(occupancy_labels, dtype=np.int8)
    labels[occupancy_labels] = 1
    
    return labels, len(labels[occupancy_labels])
    # save
    # np.save(dir_occupancy_save, labels)

def gen_occ_label(root_path, instrinsic):
    
    scene_id = "17DRP5sb8fy"
    cam1_depth_path = os.path.join(root_path, scene_id, scene_id, "cam1_depth")
    depth_names = os.listdir(cam1_depth_path)
    depth_names.sort()
    
    occ_label_dir = os.path.join(root_path, scene_id, scene_id, "occ_label")
    make_dir(occ_label_dir)
    
    # occupancy_range = np.array(
    #             [[-1.975, -0.125, 0.025], [2, 0.02, 4]])  # 设置技巧：最小值设为边界+voxel/2，最大值设为边界-1e-6，高度设置注意不穿模即可
    
    # occupancy_range = np.array(
    #             [[-1.975, 0.525, 0.025], [2, 2.0, 4]])
    
    # occupancy_range = np.array(
    #             [[-6.73797, -2.01431, 3.756], [3.31641, 5.88889, 13.039]])
    
    occupancy_range = np.array(
                [[-1.975, 0.325, 0.025], [2, 1.8, 10]])
    
    img_size = (1024, 1280)
    voxel_size = 0.05
    camera_rays = _get_rays_camxyz(occupancy_range, instrinsic, img_size, voxel_size)  # 先计算fov再根据格子与相机中心相连接形成射线

    for i in range(len(depth_names)):
        depth_file_path = os.path.join(cam1_depth_path, depth_names[i])
        depth = cv2.imread(depth_file_path, cv2.IMREAD_UNCHANGED)
        # h, w = depth.shape
        points_np = depth2point(depth, intrinsic)
        
        point_cloud_o3d = o3d.geometry.PointCloud()
        point_cloud_o3d.points = o3d.utility.Vector3dVector(points_np)
        
        occ_label_save_path = os.path.join(occ_label_dir, depth_names[i])
        occ_label_save_path = occ_label_save_path.replace(".png", "_occ.npy")
        
        gen_occupancy_pcd(point_cloud_o3d, occ_label_save_path, img_size, occupancy_range, voxel_size, camera_rays, min_distance=0.05)
        
        tof_range = np.array([[-2, 0., 0], [2, 4, 1.45]])
        # point_cloud_o3d_filter = crop_point_cloud(point_cloud_o3d, tof_range)
        # origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0,0,0])
        # o3d.visualization.draw_geometries([point_cloud_o3d,origin])

def vis_occ_lable(path):
    
    # npy_file_path = os.path.join(predict_path, npy_names[i])
    lab_x, lab_z, lab_y = 80, 200, 30
    occ = np.load(path)
    occ = occ.reshape(lab_x, lab_y, lab_z)
    # occ = occ[:,:,1:]
    
    # point_cloud_o3d = o3d.geometry.PointCloud()
    # points_array = []
        
    # 使用 NumPy 的逻辑索引来并行筛选出非零元素的坐标
    indices = np.argwhere(occ!= 0)
    
    # # occupancy_range = np.array(
    #             [[-1.975, 0.325, 0.025], [2, 1.8, 10]])
    # 计算对应的点坐标
    points_array = np.array([[-1.975 + 0.05 * indices[j, 0], 0.325 + 0.05 * indices[j, 1], 0.025 + 0.05 * indices[j, 2]] for j in range(indices.shape[0])])
    
    point_cloud_o3d = o3d.geometry.PointCloud()
    point_cloud_o3d.points = o3d.utility.Vector3dVector(points_array)
    origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0,0,0])
    o3d.visualization.draw_geometries([point_cloud_o3d,origin])

def process_all_data(root_path):
    home_name_list = os.listdir(root_path)
    home_name_list.sort()
    
    # home_name_list = home_name_list[:1]
    # home_name_list = home_name_list[:10]
    # home_name_list = home_name_list[10:20]
    # home_name_list = home_name_list[20:30]
    # home_name_list = home_name_list[30:40]
    # home_name_list = home_name_list[40:50]
    # home_name_list = home_name_list[50:60]
    # home_name_list = home_name_list[60:70]
    # home_name_list = home_name_list[70:80]
    home_name_list = home_name_list[80:]


    for home_name in home_name_list:
        
        new_cam1_rgb_dir = os.path.join(root_path, home_name, home_name,"cam1_rgb_front4")
        new_cam1_depth_dir = os.path.join(root_path, home_name, home_name, "cam1_depth_front4")
        cam_intrinsics_dir = os.path.join(root_path, home_name, home_name, "cam_intrinsics")
        occ_label_dir = os.path.join(root_path, home_name, home_name, "occ_label_front4")
        tof_points_dir = os.path.join(root_path, home_name, home_name, "tof_points_front4")
        
        make_dir(occ_label_dir)
        make_dir(new_cam1_rgb_dir)
        make_dir(new_cam1_depth_dir)
        make_dir(cam_intrinsics_dir)
        make_dir(tof_points_dir)
        
        clear_files(occ_label_dir)
        clear_files(new_cam1_rgb_dir)
        clear_files(new_cam1_depth_dir)
        clear_files(cam_intrinsics_dir)
        clear_files(tof_points_dir)
        
        # if(not make_dir(occ_label_dir)): 
        #     print(occ_label_dir)
        #     continue
        # if(not make_dir(new_cam1_rgb_dir)): 
        #     print(new_cam1_rgb_dir)
        #     continue
        # if(not make_dir(new_cam1_depth_dir)):
        #     print(new_cam1_depth_dir)
        #     continue
        # if(not make_dir(cam_intrinsics_dir)):
        #     print(cam_intrinsics_dir)
        #     continue
        # if(not make_dir(tof_points_dir)):
        #     print(tof_points_dir)
        #     continue
        
        intrinsic_save_count = 0          #相机内参只保存1相机
        config_file_name = home_name + ".conf"
        config_file_path = os.path.join(root_path, home_name, home_name, FILETYPES[0], config_file_name)
        cam1_rgb_path = os.path.join(root_path, home_name, home_name, FILETYPES[1])
        cam1_depth_path = os.path.join(root_path, home_name, home_name, FILETYPES[2])
        
        #需要的参数
        intrinsics_matrix = []
        occupancy_range = np.array(
                [[-1.975, 0.325, 4.025], [2, 1.8, 8]])
        
        img_size = (1024, 1280)
        voxel_size = 0.05
        cam2lidar = np.array([
            [1, 0, 0, 0],
            [0, 0, 1, 0],
            [0, -1, 0, 0],
            [0, 0, 0, 1]], dtype=np.float64)
        # camera_rays = _get_rays_camxyz(occupancy_range, instrinsic, img_size, voxel_size)  # 先计算fov再根据格子与相机中心相连接形成射线
        
        with open(config_file_path, 'r') as file:
            scan_lines = file.readlines()
            #先解析内参
            for scan_line in scan_lines:
                word_list = scan_line.split(" ")
                if (word_list[0] == "intrinsics_matrix" and intrinsic_save_count <= 1):
                    if(intrinsic_save_count != 1): intrinsic_save_count += 1
                    else:
                        intrinsics_matrix = extract_intrinsics_matrix(word_list)
                        cam1_intrinsics_name = "cam1_intrinsics.txt"
                        cam1_intrinsics_filepath = os.path.join(cam_intrinsics_dir, cam1_intrinsics_name)
                        
                        # 保存相机内参
                        write_to_txt(intrinsics_matrix, cam1_intrinsics_filepath)
                        intrinsic_save_count += 1
                        break
            
            #在进行depth截取以及计算occ,最后保存image，depth，croped_points，occ
            camera_rays = _get_rays_camxyz(occupancy_range, intrinsics_matrix, img_size, voxel_size)  # 先计算fov再根据格子与相机中心相连接形成射线
            for scan_line in scan_lines:
                word_list = scan_line.split(" ")
                if word_list[0] == "scan":
                    depth_name = word_list[1]
                    image_name = word_list[2]
                    if(depth_name[-7] == "1"):
                        source_depth_file_path = os.path.join(cam1_depth_path, depth_name)
                        # 从深度图中截取fov点云
                        depth = cv2.imread(source_depth_file_path, cv2.IMREAD_UNCHANGED)
                        points_np_croped = get_pcd_from_depth(depth, intrinsics_matrix)
                        
                        depth_pcd_o = o3d.geometry.PointCloud()
                        depth_pcd_o.points = o3d.utility.Vector3dVector(points_np_croped)
                        depth_pcd_croped = crop_point_cloud(depth_pcd_o, occupancy_range)
                        depth_pcd_croped.transform(cam2lidar)
                        if(len(depth_pcd_croped.points) < 100): continue
                        
                        points_np = depth2point(depth, intrinsics_matrix)
                        point_cloud_o3d = o3d.geometry.PointCloud()
                        point_cloud_o3d.points = o3d.utility.Vector3dVector(points_np)
                        
                        occ_label, occ_size = gen_occupancy_lable(point_cloud_o3d, img_size, occupancy_range, voxel_size, camera_rays, min_distance=0.05)
                        if(occ_size < 100): continue
                        
                        # 保存depth
                        destination_depth_file_path = os.path.join(new_cam1_depth_dir, depth_name)
                        shutil.copy(source_depth_file_path, destination_depth_file_path)
                        # 保存rgb
                        source_rgb_file_path = os.path.join(cam1_rgb_path, image_name)
                        destination_rgb_file_path = os.path.join(new_cam1_rgb_dir, image_name)
                        shutil.copy(source_rgb_file_path, destination_rgb_file_path)
                        # 保存occ_label
                        occ_label_save_path = os.path.join(occ_label_dir, depth_name)
                        occ_label_save_path = occ_label_save_path.replace(".png", "_occ.npy")
                        np.save(occ_label_save_path, occ_label)
                        # 保存tofpoints
                        tof_points_save_path = os.path.join(tof_points_dir, depth_name)
                        tof_points_save_path = tof_points_save_path.replace(".png", "_tof_points.npy")
                        
 
                        np.save(tof_points_save_path, np.array(depth_pcd_croped.points))
                        
                        print(tof_points_save_path)

def process_all_tof_data(root_path):
    home_name_list = os.listdir(root_path)
    home_name_list.sort()
    
    # home_name_list = home_name_list[:1]
    # home_name_list = home_name_list[:10]
    # home_name_list = home_name_list[10:20]
    # home_name_list = home_name_list[20:30]
    # home_name_list = home_name_list[30:40]
    # home_name_list = home_name_list[40:50]
    # home_name_list = home_name_list[50:60]
    # home_name_list = home_name_list[60:70]
    # home_name_list = home_name_list[70:80]
    home_name_list = home_name_list[80:]

    for home_name in home_name_list:
        
        tof_points_dir = os.path.join(root_path, home_name, home_name, "tof_points_front4_8_shang70")
        # cam_intrinsics_dir = os.path.join(root_path, home_name, home_name, "cam_intrinsics")
        
        make_dir(tof_points_dir)
        
        clear_files(tof_points_dir)
        
        config_file_name = home_name + ".conf"
        config_file_path = os.path.join(root_path, home_name, home_name, FILETYPES[0], config_file_name)
        cam_intrinsics_path = os.path.join(root_path, home_name, home_name, "cam_intrinsics", "cam1_intrinsics.txt")
        # cam1_rgb_path = os.path.join(root_path, home_name, home_name, FILETYPES[1])
        cam1_depth_path = os.path.join(root_path, home_name, home_name, FILETYPES[2])
        
        #需要的参数
        intrinsics_matrix = np.loadtxt(cam_intrinsics_path)
        occupancy_range = np.array(
                [[-1.975, 0.325, 4.025], [2, 1, 8]])
        
        img_size = (1024, 1280)
        voxel_size = 0.05
        cam2lidar = np.array([
            [1, 0, 0, 0],
            [0, 0, 1, 0],
            [0, -1, 0, 0],
            [0, 0, 0, 1]], dtype=np.float64)
        # camera_rays = _get_rays_camxyz(occupancy_range, instrinsic, img_size, voxel_size)  # 先计算fov再根据格子与相机中心相连接形成射线
        
        with open(config_file_path, 'r') as file:
            scan_lines = file.readlines()
            
            for scan_line in scan_lines:
                word_list = scan_line.split(" ")
                if word_list[0] == "scan":
                    depth_name = word_list[1]
                    image_name = word_list[2]
                    if(depth_name[-7] == "1"):
                        source_depth_file_path = os.path.join(cam1_depth_path, depth_name)
                        # 从深度图中截取fov点云
                        depth = cv2.imread(source_depth_file_path, cv2.IMREAD_UNCHANGED)
                        points_np_croped = get_pcd_from_depth(depth, intrinsics_matrix)
                        if(len(points_np_croped) < 100): continue
                        
                        depth_pcd_o = o3d.geometry.PointCloud()
                        depth_pcd_o.points = o3d.utility.Vector3dVector(points_np_croped)
                        depth_pcd_croped = crop_point_cloud(depth_pcd_o, occupancy_range)
                        depth_pcd_croped.transform(cam2lidar)
                        if(len(depth_pcd_croped.points) < 100): continue
                        
                        # 保存tofpoints
                        tof_points_save_path = os.path.join(tof_points_dir, depth_name)
                        tof_points_save_path = tof_points_save_path.replace(".png", "_tof_points.npy")
                        
                        np.save(tof_points_save_path, np.array(depth_pcd_croped.points))
                        
                        print(tof_points_save_path)


if __name__ == '__main__':
    
    depth_root_path = "/run/user/1000/gvfs/sftp:host=***********/raid/yp/Datasets/indoor_matterport3D/v1/scans/17DRP5sb8fy/cam1_depth"
    image_root_path = "/run/user/1000/gvfs/sftp:host=***********/raid/yp/Datasets/indoor_matterport3D/v1/scans/17DRP5sb8fy/cam1_rgb"
    
    config_path = "/run/user/1000/gvfs/sftp:host=***********/raid/yp/Datasets/indoor_matterport3D/v1/scans/17DRP5sb8fy/undistorted_camera_parameters/17DRP5sb8fy/undistorted_camera_parameters/17DRP5sb8fy.conf"
    root_path = "/run/user/1000/gvfs/sftp:host=***********/raid/yp/Datasets/indoor_matterport3D/v1/scans"
    npy_test_path = "/run/user/1000/gvfs/sftp:host=***********/raid/yp/Datasets/indoor_matterport3D/v1/scans/17DRP5sb8fy/17DRP5sb8fy/occ_label/00ebbf3782c64d74aaf7dd39cd561175_d1_0_occ.npy"
    
    root_path = "/home/<USER>/yp/Datasets/indoor_matterport3D/v1/scans"
    # intrinsic = np.array([
    #             [1075.94, 0 ,  629.693],
    #             [0, 1076.09, 501.638],
    #             [0, 0, 1]], dtype=np.float64)
    
    # process_all_data(root_path)
    process_all_tof_data(root_path)
    
    # vis_occ_lable(npy_test_path)
    
    # parse_conf(root_path)  
    # points2image(depth_root_path, image_root_path, intrinsic)
    # gen_occ_label(root_path, intrinsic)
    
    # home_name_list = os.listdir(root_path)
    # home_name_list.sort()
    
    # for it in home_name_list:
    #     zip_path = os.path.join(root_path, it)
    #     unzip_and_delete(zip_path)
        # pass

    