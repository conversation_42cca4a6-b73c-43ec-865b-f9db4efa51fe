import os
import shutil
from PIL import Image

def is_single_channel(image_path):
    """检查给定图像是否为单通道图像。"""
    try:
        img = Image.open(image_path)
        return img.mode not in ["RGB", "RGBA"]
    except IOError:
        print(f"打开图像文件时出错: {image_path}")
        return False

def move_files_from_folder(src_folder, dst_folder):
    """将一个文件夹中的所有文件移动到另一个文件夹。"""
    for filename in os.listdir(src_folder):
        src_path = os.path.join(src_folder, filename)
        dst_path = os.path.join(dst_folder, filename)
        shutil.move(src_path, dst_path)

def process_scene(scene_path):
    """处理单个场景，包括移动文件、删除文件和文件夹、处理图像文件。"""
    # 将cameras_sampling_a中的文件移出，并删除cameras_sampling_a和cameras_sampling_b
    cameras_sampling_a_path = os.path.join(scene_path, 'cameras_sampling_a')
    if os.path.isdir(cameras_sampling_a_path):
        move_files_from_folder(cameras_sampling_a_path, scene_path)
        shutil.rmtree(cameras_sampling_a_path)
    cameras_sampling_b_path = os.path.join(scene_path, 'cameras_sampling_b')
    if os.path.isdir(cameras_sampling_b_path):
        shutil.rmtree(cameras_sampling_b_path)

    # # 删除多余文件，以及重构camera内子文件夹
    # for camera_folder in [f'camera{i}' for i in range(7)] + ['camera3-B']:
    #     camera_path = os.path.join(scene_path, camera_folder)
    #     if os.path.isdir(camera_path):
    #         # 删除特定类型的文件
    #         for file in os.listdir(camera_path):
    #             if file.endswith(('semantic.png', 'detection3d.txt', 'detection2d.json', 'pose.json')):
    #                 os.remove(os.path.join(camera_path, file))
    #         # 创建depth和rgb文件夹，并移动对应的图像文件
    #         for subfolder in ['rgb', 'depth']:
    #             subfolder_path = os.path.join(camera_path, subfolder)
    #             os.makedirs(subfolder_path, exist_ok=True)
    #         for file in os.listdir(camera_path):
    #             if file.endswith('rgb.jpg'):
    #                 shutil.move(os.path.join(camera_path, file), os.path.join(camera_path, 'rgb', file))
    #             elif file.endswith('depth.png'):
    #                 shutil.move(os.path.join(camera_path, file), os.path.join(camera_path, 'depth', file))

def check_for_single_channel_images(scene_path):
    """检查并删除单通道图像。"""
    single_channel_image_count = 0

    for camera in [f'camera{i}' for i in range(7)] + ['camera3-B']:
        rgb_path = os.path.join(scene_path, camera)
        if os.path.isdir(rgb_path):
            for file in os.listdir(rgb_path):
                file_path = os.path.join(rgb_path, file)

                # 合并条件：文件名以_rgb.jpg结尾且为单通道图像
                if file.endswith("_rgb.jpg") and is_single_channel(file_path):
                    single_channel_image_count += 1
                    print(f"\n单通道图像: {file_path[50:]}")
                    image_index = file.split('_')[-2]

                    # 为所有相机构建正确的图像文件名并删除对应的rgb图像
                    # for cam_to_delete in [f'camera{i}' for i in range(7)] + ['camera3-B']:
                    #     # 构建正确的文件名，例如 "3FO3P2IOGK4Q_camera0_000002_rgb.jpg"
                    #     image_to_delete = os.path.join(scene_path, cam_to_delete, f"{file.split('_')[0]}_{cam_to_delete}_{image_index}_rgb.jpg")
                    #     if os.path.exists(image_to_delete):
                    #         # os.remove(image_to_delete)
                    #         print(f"删除单通道图像: {image_to_delete}")

    print(f"\n总共发现了 {single_channel_image_count} 张单通道图像。")

def process_dataset(root_path):
    """遍历根目录下的所有场景，并对每个场景进行处理。"""
    for scene in os.listdir(root_path):
        scene_path = os.path.join(root_path, scene)
        if os.path.isdir(scene_path):
            # process_scene(scene_path)
            check_for_single_channel_images(scene_path)

# 使用你的数据集的路径替换下面的路径
process_dataset("/home/<USER>/Datasets/kujiale_data/kws_deliver_0509/scenes/")
