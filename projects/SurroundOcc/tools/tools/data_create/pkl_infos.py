import pickle
import numpy as np

def format_array_for_text(array, level):
    """
    Formats a NumPy array into a string with proper indentation for each level.
    """
    indent = '    '  # Define the indentation (4 spaces in this case)
    indent_str = indent * level
    formatted_str = ''

    if array.ndim == 1:  # Handling 1D array
        formatted_str += '[' + ', '.join(map(str, array)) + ']'
    elif array.ndim == 2:  # Handling 2D array (matrix)
        lines = []
        for row in array:
            line = '[' + ', '.join(map(str, row)) + ']'
            lines.append(line)
        formatted_str += '[\n' + ',\n'.join([indent_str + line for line in lines]) + '\n' + indent_str + ']'

    return formatted_str

def format_dict_for_text(d, level=0, parent_idx="", parent_key=""):
    """
    Formats a dictionary into a string with hierarchical key structure for text representation,
    with indentation for each level, parent key information for items in lists, and extra newlines
    for readability between first and second level items.
    """
    indent = '    '  # Define the indentation (4 spaces in this case)
    formatted_str = ''
    if isinstance(d, dict):
        for idx, (key, value) in enumerate(d.items(), 1):
            current_idx = f"{parent_idx}{idx}" if parent_idx else f"{idx}"
            indent_str = indent * level

            if isinstance(value, (list, tuple)):
                if len(value) > 0 and isinstance(value[0], dict):
                    item_key = f"{key}_Item"  # Naming convention for items in a list
                    formatted_str += f"{indent_str}{current_idx}) {key} (Number of items: {len(value)})\n"
                    for item_idx, item in enumerate(value, 1):
                        item_idx_str = f"{current_idx}.{item_idx}"
                        formatted_str += f"{indent_str}{item_idx_str}) {item_key}\n{format_dict_for_text(item, level + 1, item_idx_str + '.', item_key)}"
                else:
                    # Handling arrays specifically
                    if isinstance(value, np.ndarray):
                        formatted_array = format_array_for_text(value, level + 1)
                        formatted_str += f"{indent_str}{current_idx}) {key}: {formatted_array}\n"
                    else:
                        formatted_list = ', '.join(map(str, value))
                        formatted_str += f"{indent_str}{current_idx}) {key}: [{formatted_list}]\n"
            elif isinstance(value, dict):
                formatted_str += f"{indent_str}{current_idx}) {key}\n{format_dict_for_text(value, level + 1, current_idx + '.', key)}"
            else:
                formatted_str += f"{indent_str}{current_idx}) {key}: {value}\n"
    return formatted_str

def convert_pkl_to_formatted_txt(paths):
    input_pkl_path = paths['input_pkl_path']
    output_txt_path = paths['output_txt_path']

    with open(input_pkl_path, 'rb') as file:
        data = pickle.load(file)

    formatted_data = format_dict_for_text(data)

    with open(output_txt_path, 'w') as txt_file:
        txt_file.write(formatted_data)


if __name__ == "__main__":
    paths = {
        'input_pkl_path': '/home/<USER>/yp/Datasets/kws_deliver_0509/pkl/kujiale_fusion_0509_175cm_val.pkl',
        'output_txt_path': '/home/<USER>/yp/Datasets/kws_deliver_0509/pkl/kujiale_fusion_0509_175cm_val.txt'
    }

    convert_pkl_to_formatted_txt(paths)
