import os


def delete_scenes(originals, cameras, prefix_path):
    """
    根据原始字符串列表和camera列表生成新的路径列表，尝试删除对应的文件，并根据结果打印消息。

    :param originals: 包含原始字符串的列表。
    :param cameras: 包含camera编号的列表。
    :param prefix_path: 要添加到每个路径前的前缀字符串。
    """
    for original in originals:
        prefix, _, suffix = original.split('_', 2)
        for camera in cameras:
            path = f"{prefix_path}{prefix}/{camera}/{prefix}_{camera}_{suffix}"
            try:
                os.remove(path)
                print(f"成功删除文件：{path}")
            except FileNotFoundError:
                print(f"文件不存在：{path}")

        print('\n')


# 调用主函数
if __name__ == "__main__":

    # 定义camera列表
    cameras = ['camera0', 'camera1', 'camera2', 'camera3', 'camera3-B', 'camera4', 'camera5', 'camera6']

    # 定义要添加的路径前缀
    prefix_path = '/home/<USER>/Datasets/kws_deliver_0410/scenes/'

    rm_rgb = [
        '3FO3NO7JKHFU_camera0_000668_rgb.jpg',
        '3FO3NO7JKHFU_camera0_000669_rgb.jpg',
        '3FO3NO7JKHFU_camera1_000523_rgb.jpg',

        '3FO3NSRIQA5A_camera1_000377_rgb.jpg',

        '3FO3NTO4NBAH_camera2_000375_rgb.jpg',
        '3FO3NTO4NBAH_camera3_000367_rgb.jpg',
        '3FO3NTO4NBAH_camera3_000368_rgb.jpg',
        '3FO3NTO4NBAH_camera3_000369_rgb.jpg',
        '3FO3NTO4NBAH_camera3_000372_rgb.jpg',
        '3FO3NTO4NBAH_camera3_000373_rgb.jpg',

        '3FO3NTP46SQW_camera5_000566_rgb.jpg',

        '3FO3NTPERAIP_camera0_000585_rgb.jpg',

        '3FO3NTPPFTAF_camera5_000417_rgb.jpg',
        '3FO3NTPPFTAF_camera5_000418_rgb.jpg',

        '3FO3NWGDUIX4_camera6_000009_rgb.jpg',
        '3FO3NWGDUIX4_camera6_000010_rgb.jpg',
        '3FO3NWGDUIX4_camera6_000011_rgb.jpg',
        '3FO3NWGDUIX4_camera6_000012_rgb.jpg',
        '3FO3NWGDUIX4_camera6_000013_rgb.jpg',

        '3FO3NWH948IQ_camera0_000001_rgb.jpg',
        '3FO3NWH948IQ_camera0_000002_rgb.jpg',
        '3FO3NWH948IQ_camera1_000001_rgb.jpg',

        '3FO3NWJMM37V_camera1_000358_rgb.jpg',
        '3FO3NWJMM37V_camera4_000079_rgb.jpg',
        '3FO3NWJMM37V_camera4_000080_rgb.jpg',
        '3FO3NWJMM37V_camera4_000081_rgb.jpg',
        '3FO3NWJMM37V_camera4_000082_rgb.jpg',

        '3FO3OBD7U578_camera5_000421_rgb.jpg',
        '3FO3OBD7U578_camera5_000422_rgb.jpg',
        '3FO3OBD7U578_camera5_000423_rgb.jpg',
        '3FO3OBD7U578_camera5_000424_rgb.jpg',
        '3FO3OBD7U578_camera5_000451_rgb.jpg',
        '3FO3OBD7U578_camera5_000452_rgb.jpg',
        '3FO3OBD7U578_camera5_000453_rgb.jpg',
        '3FO3OBD7U578_camera5_000454_rgb.jpg',
        '3FO3OBD7U578_camera5_000455_rgb.jpg',
        '3FO3OBD7U578_camera5_000456_rgb.jpg',
        '3FO3OBD7U578_camera5_000457_rgb.jpg',
        '3FO3OBD7U578_camera5_000458_rgb.jpg',
        '3FO3OBD7U578_camera5_000459_rgb.jpg',
        '3FO3OBD7U578_camera5_000460_rgb.jpg',
        '3FO3OBD7U578_camera5_000461_rgb.jpg',
        '3FO3OBD7U578_camera5_000462_rgb.jpg',
        '3FO3OBD7U578_camera5_000463_rgb.jpg',
        '3FO3OBD7U578_camera5_000464_rgb.jpg',

        '3FO3P1MI391O_camera1_000066_rgb.jpg',

    ]

    print(len(rm_rgb))
    # delete_scenes(rm_rgb, cameras, prefix_path)
