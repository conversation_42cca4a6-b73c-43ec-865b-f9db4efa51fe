# here put the import lib
import numpy as np
import cv2 as cv
import os
import matplotlib.pyplot as plt
import logging
from scipy.spatial.transform import Rotation as R
import open3d as o3d
import numpy as np
from glob import glob
import os
import cv2
from sklearn.utils import assert_all_finite
import json
import os.path as osp
import copy
import math
from tqdm import tqdm

# https://blog.csdn.net/yuegooxi/article/details/122015346
# k1，k2，p1，p2，k3～k6,这里只用前4个参数
# autoware 给的时k1，k2，k3，p1，p2


plt.figure()


def read_pose(path):
    pose = open(path, 'r').read().split('\n')[1].split(' ')
    T = [pose[1], pose[2], pose[3], 1]
    Rq = [pose[4], pose[5], pose[6], pose[7]]
    r = R.from_quat(Rq)
    Rm = r.as_matrix()
    Tlc = np.zeros((4, 4), dtype=np.float64)
    Tlc[:3, :3] = Rm
    Tlc[:, 3] = T
    return Tlc


# def get_rays_pytorch(H, W, K):
#     i, j = torch.meshgrid(torch.linspace(0, W-1, W), torch.linspace(0, H-1, H))  # pytorch's meshgrid has indexing='ij'
#     i = i.t()
#     j = j.t()
#     rays = torch.stack([(i-K[0][2])/K[0][0], -(j-K[1][2])/K[1][1], -torch.ones_like(i)], -1)
#     return rays


def get_rays_nerf(H, W, instrinsics):
    i, j = np.meshgrid(np.linspace(0, W - 1, W), np.linspace(0, H - 1, H))  # pytorch's meshgrid has indexing='ij'
    i = i[:, :, np.newaxis]
    j = j[:, :, np.newaxis]
    rays = np.concatenate(
        [(i - instrinsics[0][2]) / instrinsics[0][0], -(j - instrinsics[1][2]) / instrinsics[1][1], -np.ones_like(i)],
        -1)
    show = False
    if show == True:
        import matplotlib.pyplot as plt
        fig = plt.figure()
        # ax = fig.gca(projection="3d")
        ax = fig.add_subplot(projection='3d')
        ax.scatter(rays[:, :, 0], rays[:, :, 1], rays[:, :, 2])
        plt.show()

    return rays


def get_rays_camxyz(occupancy_range, instrinsics, img_size, gridsize):
    fx = instrinsics[0][0]
    fy = instrinsics[1][1]
    cx = instrinsics[0][2]
    cy = instrinsics[1][2]
    w_angle = (math.atan(img_size[0] / (2 * fx)) * 2) * (180 / math.pi)  # 将弧度转换为度数
    h_angle = (math.atan(img_size[1] / (2 * fy)) * 2) * (180 / math.pi)  # 将弧度转换为度数

    # occupancy_range=np.array([[-1.974, -0.049999, 0.0251], [2.024,0.05, 4.02499]] )   
    # 定义左、右、前、上、下边界
    bound_left = occupancy_range[0][0]
    bound_right = occupancy_range[1][0]
    bound_far = occupancy_range[1][2]
    bound_near = occupancy_range[0][2]
    bound_up = occupancy_range[1][1]
    bound_down = occupancy_range[0][1]

    # num_x=math.ceil((-occupancy_range[0][0]-gridsize/2)/gridsize)+math.ceil((occupancy_range[1][0]-gridsize/2)/gridsize)+1
    # num_y=math.ceil((-occupancy_range[0][1]-gridsize/2)/gridsize)+math.ceil((occupancy_range[1][1]-gridsize/2)/gridsize)+1
    # num_z=math.ceil((-occupancy_range[0][2]-gridsize/2)/gridsize)+math.ceil((occupancy_range[1][2]-gridsize/2)/gridsize)+1

    num_x = math.ceil((occupancy_range[1][0] - occupancy_range[0][0]) / gridsize)
    num_y = math.ceil((occupancy_range[1][1] - occupancy_range[0][1]) / gridsize)
    num_z = math.ceil((occupancy_range[1][2] - occupancy_range[0][2]) / gridsize)

    # 几个平面的每个grid所处的中心
    grid_centers = []

    # #左面
    x = bound_left  # + gridsize / 2
    for i in range(num_y * 3):
        for j in range(num_z * 3):
            y = bound_down + i * gridsize / 3 + gridsize / 2
            z = bound_near + j * gridsize / 3 + gridsize / 2
            if abs(math.atan(x / z) * 2 * (180 / math.pi)) < w_angle and abs(
                    math.atan(y / z) * 2 * (180 / math.pi)) < h_angle:
                grid_centers.append((x, y, z))

    # 右面
    x = bound_right - gridsize / 2
    for i in range(num_y * 3):
        for j in range(num_z * 3):
            y = bound_down + i * gridsize / 3 + gridsize / 2
            z = bound_near + j * gridsize / 3 + gridsize / 2
            if abs(math.atan(x / z) * 2 * (180 / math.pi)) < w_angle and abs(
                    math.atan(y / z) * 2 * (180 / math.pi)) < h_angle:
                grid_centers.append((x, y, z))

    # 视锥深度方向平面
    z = bound_far - gridsize / 2
    for i in range(num_x * 3):
        for j in range(num_y * 3):
            x = bound_left + i * gridsize / 3 + gridsize / 2
            y = bound_down + j * gridsize / 3 + gridsize / 2
            if abs(math.atan(x / z) * 2 * (180 / math.pi)) < w_angle and abs(
                    math.atan(y / z) * 2 * (180 / math.pi)) < h_angle:
                grid_centers.append((x, y, z))

    # 上平面
    y = bound_down + gridsize / 2
    for i in range(num_x * 3):
        for j in range(num_z * 3):
            x = bound_left + i * gridsize / 3 + gridsize / 2
            z = bound_near + j * gridsize / 3 + gridsize / 2
            if abs(math.atan(x / z) * 2 * (180 / math.pi)) < w_angle and abs(
                    math.atan(y / z) * 2 * (180 / math.pi)) < h_angle:
                grid_centers.append((x, y, z))

    y = bound_down + math.ceil((bound_up - bound_down) / gridsize) * gridsize - gridsize / 2
    for i in range(num_x * 3):
        for j in range(num_z * 3):
            x = bound_left + i * gridsize / 3 + gridsize / 2
            z = bound_near + j * gridsize / 3 + gridsize / 2
            if abs(math.atan(x / z) * 2 * (180 / math.pi)) < w_angle and abs(
                    math.atan(y / z) * 2 * (180 / math.pi)) < h_angle:
                grid_centers.append((x, y, z))
    return grid_centers


def get_rays(H, W, instrinsics):
    i, j = np.meshgrid(np.linspace(0, W - 1, W), np.linspace(0, H - 1, H))  # pytorch's meshgrid has indexing='ij'
    i = i[:, :, np.newaxis]
    j = j[:, :, np.newaxis]
    rays = np.concatenate(
        [(i - instrinsics[0][2]) / instrinsics[0][0], (j - instrinsics[1][2]) / instrinsics[1][1], np.ones_like(i)], -1)
    show = False
    if show == True:
        import matplotlib.pyplot as plt
        fig = plt.figure()
        # ax = fig.gca(projection="3d")
        ax = fig.add_subplot(projection='3d')
        ax.scatter(rays[:, :, 0], rays[:, :, 1], rays[:, :, 2])
        plt.show()

    return rays


def hit(rays, pcd):
    pass


def get_points(occupancy_range, voxel_size, min_distance=0.1):
    """
    occupancy_range: 感知范围#以房顶为中心4x4x2.9的范围，2.9表示向下0.1m到3米的范围
    voxel_size：单个格子尺寸
    """
    box_min_bound = occupancy_range[0]
    box_max_bound = occupancy_range[1]

    # num_x=math.ceil((-occupancy_range[0][0]-voxel_size/2)/voxel_size)+math.ceil((occupancy_range[1][0]-voxel_size/2)/voxel_size)+1
    # num_y=math.ceil((-occupancy_range[0][1]-voxel_size/2)/voxel_size)+math.ceil((occupancy_range[1][1]-voxel_size/2)/voxel_size)+1
    # num_z=math.ceil((-occupancy_range[0][2]-voxel_size/2)/voxel_size)+math.ceil((occupancy_range[1][2]-voxel_size/2)/voxel_size)+1

    # cube_resultion=(num_x,num_y,num_z)
    cube_resultion = (box_max_bound - box_min_bound + voxel_size / 2) / voxel_size

    x_grid = np.linspace(box_min_bound[0], box_max_bound[0] - voxel_size / 2., math.ceil(cube_resultion[0]))
    y_grid = np.linspace(box_min_bound[1], box_max_bound[1] - voxel_size / 2., math.ceil(cube_resultion[1]))
    z_grid = np.linspace(box_min_bound[2], box_max_bound[2] - voxel_size / 2., math.ceil(cube_resultion[2]))
    cube_grid = np.asarray(np.meshgrid(x_grid, y_grid, z_grid)).transpose(2, 1, 3, 0).reshape(-1, 3)
    return cube_grid.astype(np.float32)

    # x_grid=np.linspace(box_min_bound[0]+voxel_size/2.,box_max_bound[0]-voxel_size/2.,round(cube_resultion[0]))
    # y_grid=np.linspace(box_min_bound[1]+voxel_size/2.,box_max_bound[1]-voxel_size/2.,round(cube_resultion[1]))
    # z_grid=np.linspace(box_min_bound[2]+voxel_size/2.,box_max_bound[2]-voxel_size/2.,round(cube_resultion[2]))
    # cube_grid=np.asarray(np.meshgrid(x_grid,y_grid,z_grid)).transpose(1,2,3,0).reshape(-1,3)
    # return cube_grid.astype(np.float32)


def calCrossPoint(line_point, line_vector, plane_point, plane_vector):
    """calculate cross point between line and plane

    Args:
        line_point (np.shape(3, 1)): 直线上一点
        line_vector (np.shape(3, 1)): 直线方向
        plane_point (np.shape(3, 1)): 平面上一点
        plane_vector (np.shape(3, 1)): 平面法向量
    """
    dot_product = np.dot(line_vector, plane_vector)
    if (dot_product == 0):
        return [0, 0, 0]
    else:
        m = (plane_point[0] - line_point[0]) * plane_vector[0] + \
            (plane_point[1] - line_point[1]) * plane_vector[1] + \
            (plane_point[2] - line_point[2]) * plane_vector[2] / dot_product
    return line_point + line_vector * m


def crop_point_cloud(pcd, crop_range):
    bounding_box = o3d.geometry.AxisAlignedBoundingBox(crop_range[0], crop_range[1])
    return pcd.crop(bounding_box)


def grid_rays_to_labels(voxel_grid, camera_rays, sample_distance, total_points, voxel_size, H, W):
    # voxel_grid是lidar扫到的占用的格子，occupancy_voxel_gird是camera看得到的格子
    camera_rays = camera_rays.reshape(1, -1)  # 8472(Nx3)
    sample_distance = sample_distance.reshape(-1, 1)
    sample_rays = sample_distance @ camera_rays  # camera_rays表示图像平面每个点的世界坐标，与sample_rays相乘表示到摄像头之间的所有点
    camera_rays = camera_rays.reshape(-1, 3)  # 8472(Nx3)
    sample_rays_points = sample_rays.reshape(-1, 3)  # Nx3 #等角度扇形展开，密集采样
    # voxel_grid空间是一个预置的立方体空间，比如左右2m，上下？m，前后2m；sample_rays_points是相机的射线空间
    valid_mask = voxel_grid.check_if_included(o3d.utility.Vector3dVector(sample_rays_points))  # bev空间的mask
    valid_mask_temp = np.asarray(valid_mask).reshape(sample_distance.shape[0], camera_rays.shape[0])
    surface_index = valid_mask_temp.argmax(axis=0)

    occupancy_mask = np.zeros((sample_distance.shape[0], camera_rays.shape[0]), dtype=bool)  # 视锥体空间的mask
    visiable_mask = np.zeros((sample_distance.shape[0], camera_rays.shape[0]), dtype=bool)  # 视锥体空间的mask

    # empty_mask=np.ones((sample_distance.shape[0], camera_rays.shape[0]), dtype=bool)

    # visiable_mask_tensor[:surface_index_tensor+1]=torch.where(surface_index_tensor>0,        True       ,       False   )
    for i, val in enumerate(surface_index):
        if val == 0:  # 射线方向上没有点 即mesh对应处为空
            # empty_mask[:, i] = False
            continue
        else:
            visiable_mask[:val + 1, i] = True  # 表面和表面到相机之间的设置为true
            occupancy_mask[val, i] = True
    sample_rays_points = sample_rays_points.reshape(sample_distance.shape[0], camera_rays.shape[0], 3)  # 视锥体空间
    occupancy_points = sample_rays_points[occupancy_mask]  # surface,视锥体空间
    visiable_points = sample_rays_points[visiable_mask]  # surface前面的点
    # ray_empty_points = sample_rays_points[empty_mask]#射线打过去完全空洞，但是这个解决不了某些情况

    show_lable = False
    if show_lable == True:
        vis_pcd = o3d.geometry.PointCloud()
        origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0, 0, 0])
        vis_pcd.points = o3d.utility.Vector3dVector(occupancy_points)
        # points_num=np.array(camera_coord_mesh.points).shape[0]
        # camera_coord_mesh.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(points_num, 3)))
        # voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd, voxel_size=0.05)
        o3d.visualization.draw_geometries([vis_pcd, origin])

    occupancy_pcd = o3d.geometry.PointCloud()
    occupancy_pcd.points = o3d.utility.Vector3dVector(occupancy_points)  # 视锥体空间
    # colors = np.zeros(occupancy_points.shape)
    # colors[:,2] = 255
    # occupancy_pcd.colors = o3d.utility.Vector3dVector(colors)
    # occupancy_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(occupancy_points.shape[0], 3)))

    # camera可以看见的格子数量
    occupancy_voxel_gird = o3d.geometry.VoxelGrid.create_from_point_cloud(occupancy_pcd, voxel_size=voxel_size)

    # 这一步骤有点问题
    occupancy_labels = occupancy_voxel_gird.check_if_included(o3d.utility.Vector3dVector(total_points))

    visiable_pcd = o3d.geometry.PointCloud()
    visiable_pcd.points = o3d.utility.Vector3dVector(visiable_points)
    # visiable_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(points_num, 3)))
    visiable_voxel_gird = o3d.geometry.VoxelGrid.create_from_point_cloud(visiable_pcd, voxel_size=voxel_size)
    visiable_labels = visiable_voxel_gird.check_if_included(o3d.utility.Vector3dVector(total_points))

    # ray_empty_pcd = o3d.geometry.PointCloud()
    # ray_empty_pcd.points = o3d.utility.Vector3dVector(ray_empty_points)
    # # ray_empty_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(points_num, 3)))
    # ray_empty_voxel_gird = o3d.geometry.VoxelGrid.create_from_point_cloud(ray_empty_pcd, voxel_size=voxel_size)
    # ray_empty_labels = ray_empty_voxel_gird.check_if_included(o3d.utility.Vector3dVector(total_points))

    return occupancy_labels, visiable_labels  # ,ray_empty_labels


def rays_to_labels(voxel_grid, camera_rays, sample_distance, total_points, voxel_size, H, W):
    # voxel_grid是lidar扫到的占用的格子，occupancy_voxel_gird是camera看得到的格子
    camera_rays = camera_rays.reshape(1, -1)
    sample_distance = sample_distance.reshape(-1, 1)
    sample_rays = sample_distance @ camera_rays  # camera_rays表示图像平面每个点的世界坐标，与sample_rays相乘表示到摄像头之间的所有点
    sample_rays_points = sample_rays.reshape(-1, 3)  # Nx3 #等角度扇形展开，密集采样
    # voxel_grid空间是一个预置的立方体空间，比如左右2m，上下？m，前后2m；sample_rays_points是相机的射线空间
    valid_mask = voxel_grid.check_if_included(o3d.utility.Vector3dVector(sample_rays_points))  # bev空间的mask
    valid_mask_temp = np.asarray(valid_mask).reshape(sample_distance.shape[0], H * W)
    surface_index = valid_mask_temp.argmax(axis=0)

    occupancy_mask = np.zeros((sample_distance.shape[0], H * W), dtype=bool)  # 视锥体空间的mask
    visiable_mask = np.zeros((sample_distance.shape[0], H * W), dtype=bool)  # 视锥体空间的mask

    # visiable_mask_tensor[:surface_index_tensor+1]=torch.where(surface_index_tensor>0,        True       ,       False   )
    for i, val in enumerate(surface_index):
        if val == 0:  # 射线方向上没有点 即mesh对应处为空
            continue
        else:
            visiable_mask[:val + 1, i] = True  # 表面和表面到相机之间的设置为true
            occupancy_mask[val, i] = True
    sample_rays_points = sample_rays_points.reshape(sample_distance.shape[0], H * W, 3)  # 视锥体空间
    occupancy_points = sample_rays_points[occupancy_mask]  # surface,视锥体空间
    visiable_points = sample_rays_points[visiable_mask]  # surface前面的点

    show_lable = True
    if show_lable == True:
        vis_pcd = o3d.geometry.PointCloud()
        origin = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=[0, 0, 0])
        vis_pcd.points = o3d.utility.Vector3dVector(occupancy_points)
        # points_num=np.array(camera_coord_mesh.points).shape[0]
        # camera_coord_mesh.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(points_num, 3)))
        # voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(vis_pcd, voxel_size=0.05)
        o3d.visualization.draw_geometries([vis_pcd, origin])

    occupancy_pcd = o3d.geometry.PointCloud()
    occupancy_pcd.points = o3d.utility.Vector3dVector(occupancy_points)  # 视锥体空间
    # colors = np.zeros(occupancy_points.shape)
    # colors[:,2] = 255
    # occupancy_pcd.colors = o3d.utility.Vector3dVector(colors)
    # occupancy_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(occupancy_points.shape[0], 3)))

    # camera可以看见的格子数量
    occupancy_voxel_gird = o3d.geometry.VoxelGrid.create_from_point_cloud(occupancy_pcd, voxel_size=voxel_size)

    occupancy_labels = occupancy_voxel_gird.check_if_included(o3d.utility.Vector3dVector(total_points))

    visiable_pcd = o3d.geometry.PointCloud()
    visiable_pcd.points = o3d.utility.Vector3dVector(visiable_points)
    # visiable_pcd.colors = o3d.utility.Vector3dVector(np.random.uniform(0, 1, size=(points_num, 3)))
    visiable_voxel_gird = o3d.geometry.VoxelGrid.create_from_point_cloud(visiable_pcd, voxel_size=voxel_size)
    visiable_labels = visiable_voxel_gird.check_if_included(o3d.utility.Vector3dVector(total_points))

    return occupancy_labels, visiable_labels


def gen_occupancy_pcd(pcd, pose, img_path, save_path, img_size, occupancy_range, voxel_size, camera_rays, min_distance=0.05):
    """ 注意：3D坐标系是open3d所在的坐标系,但这里我修改成了相机坐标系了，生成的标签以相机为准
    """

    occupancy_range_ = occupancy_range.copy()
    occupancy_range_[1][1] = 0.05

    camera_rays = np.array(camera_rays)

    max_distance = 1.05
    sample_distance = np.linspace(min_distance, max_distance, round((max_distance - min_distance) / 0.01))  # 100份儿切段


    total_points = get_points(occupancy_range_, voxel_size, min_distance=min_distance)


    # lidar 虑除
    # distances = np.sqrt(np.sum(points ** 2, axis=1))
    # pcd = pcd[distances > 0.5]

    pcd_copy = copy.deepcopy(pcd)  # Twl

    T_lidar_dk = np.array([
        [-0.00500609, -0.0257948, 0.999655, 0.127719],
        [-0.999974, -0.00514616, -0.00514048, -0.0323843],
        [0.00527698, -0.999654, -0.0257684, -0.0869298],
        [0, 0, 0, 1]], dtype=np.float64)
    pcd_copy.transform(T_lidar_dk)
    pcd_copy.transform(pose)  # pose: T_oms_lidar

    # # lidar 虑除
    # if '-2' in home:
    #     points = np.array(pcd_copy.points)
    #     points = points[points[:, 2] > 1.0]
    #     pcd_copy.points = o3d.utility.Vector3dVector(points)
    #
    #     print(home)

    cropped_pcd = crop_point_cloud(pcd_copy, occupancy_range)  # 面片#以cam坐标系去筛选范围内的点
    new_robust_point = np.array([[occupancy_range[0][0], occupancy_range[0][1], occupancy_range[0][2]]])
    for point in new_robust_point:
        cropped_pcd.points.append(point)

    voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(cropped_pcd, voxel_size)  # 整个局部空间所有的体素占用情况

    # 只生成fov内的栅格状态，必须使用total_points_fov
    # occupancy_labels = voxel_grid.check_if_included(o3d.utility.Vector3dVector(total_points))

    left_camera_rays = camera_rays
    left_occupancy_labels, left_visiable_labels = grid_rays_to_labels(voxel_grid, left_camera_rays, sample_distance,
                                                                      total_points, voxel_size, img_size[1],
                                                                      img_size[0])

    occupancy_labels = np.bitwise_or(left_occupancy_labels, left_occupancy_labels)  # 表面遮挡物

    labels = np.zeros_like(occupancy_labels, dtype=np.int8)
    labels[occupancy_labels] = 1

    # save
    img_path_name = img_path.split('/')[-1].split('.')[0]
    save_path = os.path.join(save_path, img_path_name) + '_occ.npy'
    np.save(save_path, labels)


class Calibrate():
    def __init__(self, cam_mats, dist_corfs, rot_mat, trans_vec):
        self.cam_mats = cam_mats
        self.rot_mat = rot_mat
        self.dist_corfs = dist_corfs
        self.rot_mat_inv = np.linalg.inv(self.rot_mat)
        self.trans_vec = trans_vec
        self.trans_vec_inv = -self.trans_vec


class Stereo():
    """双目工装数据处理，如yuv2rgb，矫正等
       注意：
           0：左目；
           1：右目；
           module1：模组1，基线为12cm；
           module2：模组2，基线为16cm；
           module3：模组3，基线为20cm；
    """

    def __init__(self, img_size):
        """
        args:
            img_size: (width, height)
        """
        self.img_size = img_size

        # 模组1
        self.cam_mats_module1 = {   # 内参
            '0': np.array([
                [321.4475202058989, 0, 649.3810871940957],
                [0, 321.5622469758229, 485.7818157326032],
                [0, 0, 1]], dtype=np.float64),
            '1': np.array([
                [320.0634980848295, 0, 639.8470471118967],
                [0, 320.4112267666799, 471.8594892100028],
                [0, 0, 1]], dtype=np.float64)}
        self.dist_corfs_module1 = { # 畸变参数
            '0': np.array([[
                0.7825840518623307, 0.7857393546334689, 4.710335937870699e-05, \
                0.0003712693693960966, 0.03567583162711111, 0.8190525500510427, \
                0.8333147294194869, 0.1129123410465364]], dtype=np.float64).reshape(8, 1),
            '1': np.array([[
                0.6232572014846256, 0.6784064946488555, 2.382494241423335e-05, \
                -1.035138953403252e-05, 0.02699721217089060, 0.6600461860713133, \
                0.7213044525030704, 0.09141727985805929]], dtype=np.float64).reshape(8, 1)}
        self.rot_mat_module1 = np.array([   # 外参
            [0.9999757522946446, -0.0058246186402728, 0.003816888844214655],
            [0.00583457540184895, 0.9999795916735226, -0.002602684446733787],
            [-0.0038016513035578113, 0.002624891263370244, 0.9999893286396689]], dtype=np.float64)
        self.trans_vec_module1 = np.array([0.11965661932361803, 0.00021152199765414763, -0.0006454811417098998],
                                          dtype=np.float64)

        # 模组2
        # 内参
        self.cam_mats_module2 = {
            '0': np.array([
                [321.4865556576209, 0, 646.1025610235099],
                [0, 321.0097496624281, 466.6922027997995],
                [0, 0, 1]], dtype=np.float64),
            '1': np.array([
                [320.8912131565299, 0, 644.2261177625821],
                [0, 321.0097496624281, 473.6775290921570],
                [0, 0, 1]], dtype=np.float64)}
        # 畸变参数
        self.dist_corfs_module2 = {
            '0': np.array([[
                0.7825840518623307, 0.7857393546334689, 4.710335937870699e-05, \
                0.0003712693693960966, 0.03567583162711111, 0.8190525500510427, \
                0.8333147294194869, 0.1129123410465364]], dtype=np.float64).reshape(8, 1),
            '1': np.array([[
                0.6232572014846256, 0.6784064946488555, 2.382494241423335e-05, \
                -1.035138953403252e-05, 0.02699721217089060, 0.6600461860713133, \
                0.7213044525030704, 0.09141727985805929]], dtype=np.float64).reshape(8, 1)}
        # 外参 第一次标注
        self.rot_mat_module2 = np.array([
            [0.999493017790356, 0.00492971851390951, -0.03145481304505169],
            [-0.005324917470504143, 0.9999077852467023, -0.012492648116618496],
            [0.03139032720851848, 0.012653808849787075, 0.9994271001325378]], dtype=np.float64)
        self.trans_vec_module2 = np.array([0.15927528810552818, -9.548906403178666e-05, 0.0006838184440500877],
                                          dtype=np.float64)

        # 外参 0524更新
        # self.rot_mat_module1 = np.array([
        #     [0.9997485408519743, 0.005319338649918189, -0.021784391216652727],
        #     [-0.005480530844647172, 0.999957995993991, -0.007346429700653558],
        #     [0.02174439803750752, 0.007463972401694711, 0.9997357002077987]], dtype=np.float64)
        # self.trans_vec_module1 = np.array([0.16542854941848475, 0.0009154233474289518, -0.008772485900331202], dtype=np.float64)

        # 模组3
        self.cam_mats_module3 = {
            '0': np.array([
                [321.1236583095948, 0, 647.8429636689347],
                [0, 321.2383027147355, 466.6727495667608],
                [0, 0, 1]], dtype=np.float64),
            '1': np.array([
                [321.6200316569539, 0, 656.1731739940485],
                [0, 321.8193576337943, 483.7313501751819],
                [0, 0, 1]], dtype=np.float64)}
        self.dist_corfs_module3 = {
            '0': np.array([[
                0.7825840518623307, 0.7857393546334689, 4.710335937870699e-05, \
                0.0003712693693960966, 0.03567583162711111, 0.8190525500510427, \
                0.8333147294194869, 0.1129123410465364]], dtype=np.float64).reshape(8, 1),
            '1': np.array([[
                0.6232572014846256, 0.6784064946488555, 2.382494241423335e-05, \
                -1.035138953403252e-05, 0.02699721217089060, 0.6600461860713133, \
                0.7213044525030704, 0.09141727985805929]], dtype=np.float64).reshape(8, 1)}
        self.rot_mat_module3 = np.array([
            [0.9999541317625686, 0.0005857210487854145, -0.009559879801184503],
            [-0.0006550899245874188, 0.9999734695072935, -0.0072547321610909685],
            [0.009555376923533148, 0.007260661980251753, 0.9999279861867305]], dtype=np.float64)
        self.trans_vec_module3 = np.array([0.1997465801714262, -0.0002328099129024403, 0.0007567377143897466],
                                          dtype=np.float64)

        # zed 内外参 畸变参数 对应 module_id = 4
        self.cam_mats_module4 = {
            '0': np.array([
                [1061.6700, 0, 1110.6600],
                [0, 1061.1400, 614.6030],
                [0, 0, 1]], dtype=np.float64),
            '1': np.array([
                [1063.1801, 0, 1074.2500],
                [0, 1062.8101, 626.4870],
                [0, 0, 1]], dtype=np.float64)}
        self.dist_corfs_module4 = {
            '0': np.array([[
                -0.0443, 0.0139, 0.0001, -0.0004]], dtype=np.float64).reshape(4, 1),
            '1': np.array([[
                -0.0452, 0.0126, 0.0001, -0.0004]], dtype=np.float64).reshape(4, 1)}
        self.rot_mat_module4 = R.from_rotvec(np.array([0.0026, -0.0001, 0.0002])).as_matrix()
        self.trans_vec_module4 = np.array([120.1240, -0.2858, 0.1092], dtype=np.float64) / 1000.0

        # zed 内外参 畸变参数 对应 module_id = 5,分辨率672x376
        self.cam_mats_module5 = {
            '0': np.array([
                [265.4175, 0, 336.415],
                [0, 265.285, 185.15075],
                [0, 0, 1]], dtype=np.float64),
            '1': np.array([
                [265.795, 0, 327.3125],
                [0, 265.7025, 188.12175],
                [0, 0, 1]], dtype=np.float64)}

        self.dist_corfs_module5 = {
            '0': np.array([[
                -0.0443, 0.0139, 0.0001, -0.0004]], dtype=np.float64).reshape(4, 1),

            '1': np.array([[
                -0.0452, 0.0126, 0.0001, -0.0004]], dtype=np.float64).reshape(4, 1)}

        self.rot_mat_module5 = R.from_rotvec(np.array([0.0026, -0.0001, 0.0002])).as_matrix()
        self.trans_vec_module5 = np.array([120.1240, -0.2858, 0.1092], dtype=np.float64) / 1000.0

        # zed 内外参 畸变参数 对应 module_id = 5,分辨率672x376
        self.cam_mats_module6 = {
            '0': np.array([
                [367.6901181887478, 0, 620.0],
                [0, 367.6901181887478, 462.5],
                [0, 0, 1]], dtype=np.float64),
            '1': np.array([
                [367.6901181887478, 0, 620.0],
                [0, 367.6901181887478, 462.5],
                [0, 0, 1]], dtype=np.float64)}

        self.dist_corfs_module6 = {
            '0': np.array([[
                0, 0, 0, 0]], dtype=np.float64).reshape(4, 1),

            '1': np.array([[
                0, 0, 0, 0]], dtype=np.float64).reshape(4, 1)}

        self.rot_mat_module6 = R.from_rotvec(np.array([0.0, 0.0, 0.0])).as_matrix()
        self.trans_vec_module6 = np.array([120, 0, 0], dtype=np.float64) / 1000.0

        dist_corfs_zero = {
            '0': np.zeros((4, 1), dtype=np.float64),
            '1': np.zeros((4, 1), dtype=np.float64)
        }

        self.calib_module1 = Calibrate(self.cam_mats_module1, dist_corfs_zero, self.rot_mat_module1,
                                       self.trans_vec_module1)
        self.calib_module2 = Calibrate(self.cam_mats_module2, dist_corfs_zero, self.rot_mat_module2,
                                       self.trans_vec_module2)
        self.calib_module3 = Calibrate(self.cam_mats_module3, dist_corfs_zero, self.rot_mat_module3,
                                       self.trans_vec_module3)
        self.calib_module4 = Calibrate(self.cam_mats_module4, dist_corfs_zero, self.rot_mat_module4,
                                       self.trans_vec_module4)
        self.calib_module5 = Calibrate(self.cam_mats_module5, dist_corfs_zero, self.rot_mat_module5,
                                       self.trans_vec_module5)
        self.calib_module6 = Calibrate(self.cam_mats_module6, dist_corfs_zero, self.rot_mat_module6,
                                       self.trans_vec_module6)

        self.cam_mats_module7 = {
            '0': np.array([
                [627.361, 0, 630.733],
                [0, 627.189, 531.027],
                [0, 0, 1]], dtype=np.float64),
        }
        self.dist_corfs_module7 = {
            '0': np.array([-0.0225176, 0.04943477, 0.0002409, 0.00033179539], dtype=np.float64).reshape(4, 1)}

        self.rot_mat_module7 = np.array([
            [0.9999996074191118, -0.0001663949801876781, -0.0008703300150569494],
            [0.00016526031905401343, 0.9999991366274906, -0.001303623144355979],
            [0.0008705461799852165, 0.0013034788015624753, 0.9999987715454277]], dtype=np.float64)
        self.trans_vec_module7 = np.array(
            [-0.06282940483827766, 7.545870246627618e-05, -0.00011347695302428774],
            dtype=np.float64)

        self.calib_module7 = Calibrate(self.cam_mats_module7, dist_corfs_zero, self.rot_mat_module7,
                                       self.trans_vec_module7)


    def yuv2bgr(self, filepath, save_dir=None, camera_id=None):
        """
        :param filepath: 待处理 YUV 视频的名字
        :param save_dir: 转换为bgr图像的保存路径
        :camera_id: 摄像机id, 支持 ‘0’：左目；‘1’：右目
        :return: None
        """
        bgr_img = np.zeros((960, 1280, 3))
        with open(filepath, 'rb') as f:
            yuvdata = np.fromfile(f, dtype=np.uint8)
            try:
                bgr_img = cv.cvtColor(yuvdata.reshape((960 * 3 // 2, 1280)), cv.COLOR_YUV2BGR_NV12)
                filename = filepath.split("/")[-1]
                if save_dir is not None:
                    if camera_id is not None:
                        camera_data = 'image_02/data' if camera_id == 0 else 'image_03/data'
                    else:
                        camera_data = ''
                    save_dir = os.path.join(save_dir, camera_data)
                    os.makedirs(save_dir, exist_ok=True)
                    savepath = os.path.join(save_dir, filename.replace("yuv", "jpg"))
                    cv.imwrite(savepath, bgr_img)
            except:
                logging.info("file {} can not open".format(filepath))
        return bgr_img

    def stereo_rectify(self, img0, img1, cali_params, show_results=False):
        """
        args:
            img0: 左目图像
            img1: 右目图像
            cali_params: 矫正参数
        """
        R0, R1, P0, P1, Q, _, _ = cv.stereoRectify(cali_params.cam_mats['0'],
                                                   cali_params.dist_corfs['0'],
                                                   cali_params.cam_mats['1'],
                                                   cali_params.dist_corfs['1'],
                                                   self.img_size,
                                                   cali_params.rot_mat,
                                                   cali_params.trans_vec,
                                                   )

        map00, map01 = cv.initUndistortRectifyMap(cali_params.cam_mats['0'], cali_params.dist_corfs['0'], R0, P0,
                                                  self.img_size, cv.CV_32FC1)
        map10, map11 = cv.initUndistortRectifyMap(cali_params.cam_mats['1'], cali_params.dist_corfs['1'], R1, P1,
                                                  self.img_size, cv.CV_32FC1)

        undistorted_rectify0 = cv.remap(img0, map00, map01, cv.INTER_LINEAR)
        undistorted_rectify1 = cv.remap(img1, map10, map11, cv.INTER_LINEAR)

        undistorted_rectify_cat = np.concatenate((undistorted_rectify0, undistorted_rectify1), axis=1)
        undistorted_rectify_sub = np.subtract(undistorted_rectify1.astype(np.float32),
                                              undistorted_rectify0.astype(np.float32))
        # undistorted_rectify_sub[undistorted_rectify_sub < 0] = 255
        # undistorted_rectify_sub = np.abs(undistorted_rectify_sub).astype(np.uint8)
        undistorted_rectify_abs = np.abs(
            np.subtract(undistorted_rectify1.astype(np.float32), undistorted_rectify0.astype(np.float32))).astype(
            np.uint8)
        undistorted_rectify_abs_gray = cv.cvtColor(undistorted_rectify_abs, cv.COLOR_BGR2GRAY)
        undistorted_rectify_abs_mean = np.mean(undistorted_rectify_sub, axis=2)
        undistorted_rectify_abs_1 = undistorted_rectify_abs_mean.copy()
        undistorted_rectify_abs_1[undistorted_rectify_abs_1 > 0] = 255
        undistorted_rectify_abs_1[undistorted_rectify_abs_1 < 0] = 0

        if show_results == True:
            undistorted_rectify_cat[100, :] = [0, 0, 255]
            undistorted_rectify_cat[200, :] = [0, 0, 255]
            undistorted_rectify_cat[300, :] = [0, 0, 255]
            # undistorted_rectify_cat[400, :] = [0, 0, 255]
            # undistorted_rectify_cat[500, :] = [0, 0, 255]
            # undistorted_rectify_cat[600, :] = [0, 0, 255]
            # undistorted_rectify_cat[700, :] = [0, 0, 255]
            # undistorted_rectify_cat[800, :] = [0, 0, 255]
            # undistorted_rectify_cat[900, :] = [0, 0, 255]

            cv.namedWindow('src img0', cv.WINDOW_NORMAL)
            cv.namedWindow('src img1', cv.WINDOW_NORMAL)
            cv.namedWindow('undistorted img0', cv.WINDOW_NORMAL)
            cv.namedWindow('undistorted img1', cv.WINDOW_NORMAL)
            cv.namedWindow('undistorted_rectify_cat', cv.WINDOW_NORMAL)
            cv.namedWindow('undistorted_rectify_abs', cv.WINDOW_NORMAL)
            cv.namedWindow('undistorted_rectify_abs_gray', cv.WINDOW_NORMAL)

            cv.imshow('src img0', img0)
            cv.imshow('src img1', img1)
            cv.imshow('undistorted img0', undistorted_rectify0)
            cv.imshow('undistorted img1', undistorted_rectify1)
            cv.imshow('undistorted_rectify_abs', undistorted_rectify_abs)
            cv.imshow('undistorted_rectify_cat', undistorted_rectify_cat)

            cv.imshow('undistorted_rectify_abs_gray', undistorted_rectify_abs_gray)
            cv.waitKey(0)
            print(1)

        if show_results == False:
            undistorted_rectify1 = cv.cvtColor(undistorted_rectify1, cv.COLOR_BGRA2RGB)
            undistorted_rectify0 = cv.cvtColor(undistorted_rectify0, cv.COLOR_BGRA2RGB)

            plt.subplot(231)
            plt.imshow(undistorted_rectify0.astype(np.uint8))
            plt.subplot(232)
            plt.imshow(undistorted_rectify1.astype(np.uint8))
            plt.subplot(233)
            plt.imshow(undistorted_rectify_abs_1)  # 基线矫正的两图相减
            plt.subplot(234)
            plt.imshow(undistorted_rectify_abs)
            plt.subplot(235)
            plt.imshow(undistorted_rectify_abs_mean)
            # plt.subplot(236)
            # plt.imshow(undistorted_rectify_abs_1)
            plt.pause(0.1)
            plt.clf()
            plt.show()

        return undistorted_rectify0, undistorted_rectify1

    def process(self, img_0_path, module_id):
        """
        module_id: 双目模组对应的id
            4:zed模组对应内参
        """
        if img_0_path.endswith('jpg') or img_0_path.endswith('png'):
            img0 = cv.imread(img_0_path)
        elif img_0_path.endswith('yuv'):
            img0 = self.yuv2bgr(img_0_path)
        else:
            raise Exception
            print('image format not supported !')
        # cv.namedWindow('src img00', cv.WINDOW_NORMAL)
        # cv.namedWindow('src img01', cv.WINDOW_NORMAL)
        # cv.imshow('src img00', img0)
        # cv.imshow('src img01', img1)

        if module_id == 1:
            img0 = cv.undistort(img0, self.cam_mats_module1['0'], self.dist_corfs_module1['0'], None,
                                self.cam_mats_module1['0'])
            img1 = cv.undistort(img1, self.cam_mats_module1['1'], self.dist_corfs_module1['1'], None,
                                self.cam_mats_module1['1'])
            rectified_img0, rectified_img1 = self.stereo_rectify(img0, img1, self.calib_module1, show_results=True)
        elif module_id == 2:
            img0 = cv.undistort(img0, self.cam_mats_module2['0'], self.dist_corfs_module2['0'], None,
                                self.cam_mats_module2['0'])
            img1 = cv.undistort(img1, self.cam_mats_module2['1'], self.dist_corfs_module2['1'], None,
                                self.cam_mats_module2['1'])
            rectified_img0, rectified_img1 = self.stereo_rectify(img0, img1, self.calib_module2, show_results=True)
        elif module_id == 3:
            img0 = cv.undistort(img0, self.cam_mats_module3['0'], self.dist_corfs_module3['0'], None,
                                self.cam_mats_module3['0'])
            img1 = cv.undistort(img1, self.cam_mats_module3['1'], self.dist_corfs_module3['1'], None,
                                self.cam_mats_module3['1'])
            cv.namedWindow('img0', cv.WINDOW_NORMAL)
            cv.namedWindow('img1', cv.WINDOW_NORMAL)
            cv.imshow('img0', img0)
            cv.imshow('img1', img1)
            rectified_img0, rectified_img1 = self.stereo_rectify(img0, img1, self.calib_module3, show_results=True)
        elif module_id == 4:
            img0 = cv.undistort(img0, self.cam_mats_module4['0'], self.dist_corfs_module4['0'], None,
                                self.cam_mats_module4['0'])
            img1 = cv.undistort(img1, self.cam_mats_module4['1'], self.dist_corfs_module4['1'], None,
                                self.cam_mats_module4['1'])
            cv.namedWindow('img0', cv.WINDOW_NORMAL)
            cv.namedWindow('img1', cv.WINDOW_NORMAL)
            cv.imshow('img0', img0)
            cv.imshow('img1', img1)
            rectified_img0, rectified_img1 = self.stereo_rectify(img0, img1, self.calib_module4, show_results=True)
        elif module_id == 5:
            img0 = cv.undistort(img0, self.cam_mats_module5['0'], self.dist_corfs_module5['0'], None,
                                self.cam_mats_module5['0'])
            img1 = cv.undistort(img1, self.cam_mats_module5['1'], self.dist_corfs_module5['1'], None,
                                self.cam_mats_module5['1'])
        elif module_id == 6:
            # 去畸变
            img0 = cv.undistort(img0, self.cam_mats_module6['0'], self.dist_corfs_module6['0'], None,
                                self.cam_mats_module6['0'])
        elif module_id == 7:
            # 去畸变
            img0 = cv.undistort(img0, self.cam_mats_module7['0'], self.dist_corfs_module7['0'], None,
                                self.cam_mats_module7['0'])


            # img1 = cv.undistort(img1, self.cam_mats_module6['1'], self.dist_corfs_module6['1'], None,
            #                     self.cam_mats_module6['1'])
            # cv.namedWindow('img0', cv.WINDOW_NORMAL)
            # cv.namedWindow('img1', cv.WINDOW_NORMAL)
            # cv.imshow('img0', img0)
            # cv.imshow('img1', img1)
            # cv.waitKey(200)
            # rectified_img0, rectified_img1 = self.stereo_rectify(img0, img1, self.calib_module5, show_results=True)    
        return img0, self.cam_mats_module7['0'], self.rot_mat_module7, self.trans_vec_module7  # 返回左目的内参,和右目外参Tlr


def find_files_with_string(files, search_str):
    list_file = []
    for file in files:
        if search_str in file:
            list_file.append(file)
    return list_file


def find_folders_with_string(root_path, search_str):
    list_mid_camera_path = []
    for dirpath, dirnames, filenames in os.walk(root_path):
        if search_str in dirpath:
            list_mid_camera_path.append(dirpath)
    return list_mid_camera_path


# 深度图稀疏化
def get_pcd_from_depth_indoor(depth_array, intrin):
    H = depth_array.shape[0]

    angle_camera = 77.02
    angle_tof = 6

    H_tof = np.tan(angle_tof * np.pi / 360) * 2 * intrin[1, 1]  # 反向计算对应的H

    # all_line = angle_tof / angle_camera * H

    all_line = H_tof

    start = int(H / 2 - all_line / 2)
    end = int(H / 2 + all_line / 2)

    depth_array[0:start, :] = 0
    depth_array[end:, :] = 0

    fx = intrin[0][0]  # 焦距x
    fy = intrin[1][1]  # 焦距y
    cx = intrin[0][2]  # 中心点x
    cy = intrin[1][2]  # 中心点y

    points = []

    tof_h = 6
    tof_w = 224

    step_h = int(all_line / (tof_h - 1))
    step_w = int(depth_array.shape[1] / tof_w)

    for i in range(start, end, step_h):
        for j in range(0, depth_array.shape[1], step_w):

            if depth_array[i][j]:
                z3 = depth_array[i][j]
                x3 = (j - cx) * z3 / fx
                y3 = (i - cy) * z3 / fy

                point = np.stack((x3, y3, z3), axis=-1)
                points.append(point)

    ans_points = np.array(points)

    return ans_points


# tof 生成深度图
def draw_point2uv(points_, img_, intrinsic_, distortion_coeffs_):
    height, width = 180, 240
    points_np = np.asarray(points_.points)

    pt = points_np
    angle = np.arctan2(pt[:, 0], pt[:, 2]) * 180 / math.pi

    pt = pt[angle < 60]
    angle = angle[angle < 60]
    pt = pt[angle > -60]

    pt_1 = pt / np.expand_dims(pt[:, 2], 1)
    r = np.expand_dims((pt_1[:, 0] ** 2 + pt_1[:, 1] ** 2) ** (1 / 2), 1)
    r = (pt_1[:, 0] ** 2 + pt_1[:, 1] ** 2) ** (1 / 2)

    r2 = r * r
    r4 = r2 * r2
    r6 = r4 * r2

    # radial_distortion = 1.0 + distortion_coeffs_[0] * r2 + distortion_coeffs_[1] * r4 + distortion_coeffs_[4] * r6
    # distorted_point = pt_1 * np.expand_dims(radial_distortion,1)

    # tangentialX = 2.0 * (distortion_coeffs_[2]) * (pt_1[:,0]) * (pt_1[:,1]) + (distortion_coeffs_[3]) * (r2 + 2.0 * (pt_1[:,0]) * (pt_1[:,0]))
    # tangentialY = distortion_coeffs_[2] * (r2 + 2.0 * pt_1[:,1] * pt_1[:,1]) + 2.0 * distortion_coeffs_[3] * pt_1[:,0] * pt_1[:,1]

    # distorted_point[:,0] += tangentialX
    # distorted_point[:,1] += tangentialY

    # 这里暂未考虑去除图像畸变
    # u = round(intrinsic_[0][0] * distorted_point[0] + intrinsic_[0][2])
    # v = round(intrinsic_[1][1] * distorted_point[1] + intrinsic_[1][2])

    # u = round(intrinsic_[0][0] * distorted_point[0] + intrinsic_[0][2])
    # v = round(intrinsic_[1][1] * distorted_point[1] + intrinsic_[1][2])

    # u = np.round(intrinsic_[0][0] * distorted_point[:,0] + intrinsic_[0][2])
    # v = np.round(intrinsic_[1][1] * distorted_point[:,1] + intrinsic_[1][2])

    u = np.round(intrinsic_[0][0] * pt_1[:, 0] + intrinsic_[0][2])
    v = np.round(intrinsic_[1][1] * pt_1[:, 1] + intrinsic_[1][2])

    v = v[u >= 1]
    pt = pt[u >= 1]
    u = u[u >= 1]

    v = v[u < width - 1]
    pt = pt[u < width - 1]
    u = u[u < width - 1]

    u = u[v >= 1]
    pt = pt[v >= 1]
    v = v[v >= 1]

    u = u[v < height - 1]
    pt = pt[v < height - 1]
    v = v[v < height - 1]

    # 这里写成1与height-1是为了后面好多上色，正常应该是0，heght

    pic_img = img_ * 1.0

    v = v.astype("int32")
    u = u.astype("int32")
    pic_img[v, u] = pt[:, 2]

    return pic_img

def tof_to_6line(tof_pcd_path, ):
    global count
    mesh = o3d.io.read_point_cloud(tof_pcd_path)

    T_oms_tof = np.array(
        [[0.9999, -0.00124883, -0.00460191, 0.019164],
         [0.00122781, 0.9999, -0.00456, 0.0038442],
         [0.00460, 0.00456, 0.9999, -0.01337119],
         [0, 0, 0, 1]], dtype=np.float64)

    T_lidar1_oms = np.array(
        [[1., 0., 0., 0.],
         [0., 0., 1., 0.],
         [0., -1., 0., 0.],
         [0., 0., 0., 1.]], dtype=np.float64)

    # 去除000
    temp_points = np.array(mesh.points)
    mask_p = abs(temp_points - np.array([0, 0, 0])).max(1) > 1e-2
    temp_points = temp_points[mask_p]
    mesh.points = o3d.utility.Vector3dVector(temp_points)

    # 将原始tof数据转到lidar1坐标系下，并保存
    mesh1 = o3d.geometry.PointCloud()
    mesh1.points = o3d.utility.Vector3dVector(temp_points.copy().astype(float))
    mesh1 = mesh1.transform(T_oms_tof).transform(T_lidar1_oms)
    tof_all_points = np.array(mesh1.points)
    tof_save_path = tof_pcd_path.replace('oms_points', 'tof_all').replace('.pcd', '.npy')
    os.makedirs(tof_save_path[:-16], exist_ok=True)
    np.save(tof_save_path, tof_all_points)

    H = 180
    W = 240

    img = np.zeros((H, W))
    intric = np.zeros((3, 3))

    intric[0, 0] = 113.105000
    intric[0, 2] = 123.835000
    intric[1, 1] = 113.105000
    intric[1, 2] = 85.830500
    intric[2, 2] = 1

    dist = np.array([-0.053488, -0.000228, 0.000440, -0.000024, -0.020353])  # k1 k2 p1 p2 k3

    depth_ = draw_point2uv(mesh, img, intric, dist)
    tof_points = get_pcd_from_depth_indoor(depth_, intric)
    # print(tof_pcd_path)
    if len(tof_points) > 0:
        tof_pcd = o3d.geometry.PointCloud()
        tof_pcd.points = o3d.utility.Vector3dVector(tof_points.astype(float))
        tof_pcd = tof_pcd.transform(T_oms_tof).transform(T_lidar1_oms)

        tof_save_path = tof_pcd_path.replace('oms_points', 'tof_6').replace('.pcd', '.npy')
        os.makedirs(tof_save_path[:-16], exist_ok=True)
        np.save(tof_save_path, np.array(tof_pcd.points))
    else:
        count += 1


if __name__ == '__main__':
    """注意：需要修改src_dir, dst_dir, module_id
    """
    img_size = (1072, 1280)
    module_id = 7
    image_format = '.jpg'
    cloud_format = 'pcd'
    indoor_path = '/home/<USER>/Datasets/indoor'

    dir_sensor = 'dk_oms'
    dir_rgb = 'oms_rgb'
    dir_dk_points = 'dk_points'
    dir_occupancy_save = 'occ_rgbd_rays'
    dir_ubdistort_img_save = 'ubdistort_rgb'
    count = 0

    home_name_list = os.listdir(indoor_path)
    home_name_list.sort()

    for home in home_name_list:
        input_path_1 = osp.join(indoor_path, home, dir_sensor)
        scenes_points_path = os.listdir(osp.join(input_path_1, dir_dk_points))
        scenes_points_path.sort()
        for index, points_path in enumerate(tqdm(scenes_points_path)):
            points_path = osp.join(input_path_1, dir_dk_points, points_path)
            show = False

            if cloud_format == 'mesh':  # 如果是mesh
                whole_pcd = o3d.io.read_triangle_mesh(points_path)
                whole_pcd.compute_vertex_normals()
            else:
                whole_pcd = o3d.io.read_point_cloud(points_path)  # 如果是pcds
            points = np.asarray(whole_pcd.points)

            whole_pcd.points = o3d.utility.Vector3dVector(points)

            pic_path = osp.join(input_path_1, dir_rgb)

            occupancy_path = osp.join(input_path_1, dir_occupancy_save)
            os.makedirs(occupancy_path, exist_ok=True)

            src_dirs = os.listdir(pic_path)
            src_dirs.sort()
            camera_imgs = [os.path.join(pic_path, dirname) for dirname in src_dirs]

            stereo = Stereo(img_size)
            if show:
                vis = o3d.visualization.Visualizer()
                vis.create_window()

            # 这里背景介绍：
            occupancy_range = np.array(
                [[-1.975, -0.125, 0.025], [2, 0.02, 4]])  # 设置技巧：最小值设为边界+voxel/2，最大值设为边界-1e-6，高度设置注意不穿模即可
            # occupancy_range1=np.array([[-1.974, -0.049999, 0.0251], [2.024,0.05, 2.02499]])
            voxel_size = 0.05
            instrinsic = stereo.cam_mats_module7['0']
            camera_rays = get_rays_camxyz(occupancy_range, instrinsic, img_size, voxel_size)  # 先计算fov再根据格子与相机中心相连接形成射线

            # vis.add_geometry(whole_pcd)
            camera_img = camera_imgs[index]

            # pose_path = camera_img.replace('rgb.jpg', 'pose.json')
            # with open(pose_path, 'r') as f:
            #     data = json.load(f)
            undistort_img0, Intrinsic_l, stereo_extrinsics_rot, stereo_extrinsics_trans = stereo.process(camera_img, module_id=module_id)
            # stereo_extrinsics = np.eye(4, dtype=np.float32)
            # stereo_extrinsics[:3, :3] = stereo_extrinsics_rot
            # stereo_extrinsics[:3, 3] = stereo_extrinsics_trans
            # stereo_extrinsics[3, :] = [0, 0, 0, 1]

            # T_lidar_oms
            T_lidar_oms = np.array(
                [[0.0038578, 0.24938, 0.96839, 0.207344],
                 [-0.9998, 0.00601088, 0.0024357, -0.0144162],
                 [-0.005213, -0.968386, 0.24939867, -0.17134],
                 [0, 0, 0, 1]], dtype=np.float64)

            # T_oms_lidar
            T_oms_lidar = np.linalg.inv(T_lidar_oms)

            tof_path = camera_img.replace('oms_rgb', 'oms_points').replace('.jpg', '.pcd')
            tof_to_6line(tof_path)

            # 保存去畸变后的图像
            # undistort_save_path = osp.join(input_path_1, dir_ubdistort_img_save)
            # os.makedirs(undistort_save_path, exist_ok=True)
            # cv2.imwrite(osp.join(undistort_save_path, camera_img.split('/')[-1]), undistort_img0)


            # 4.输入pcd，制作标签
            gen_occupancy_pcd(whole_pcd, T_oms_lidar, camera_img, occupancy_path, img_size, occupancy_range, voxel_size, camera_rays)
    print(count)