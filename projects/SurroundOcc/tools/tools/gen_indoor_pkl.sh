# --root-path 数据的路径
# --out-dir 输出的pkl路径
# --extra-tag 与第一个参数保持一致
# --pkl-name 存下的pkl文件前缀，代码中会自动加上后缀 '_train.pkl' 和 '_val.pkl'


# # oms_dk
# python tools/create_data.py \
#     indoor_oms_dk \
#     --root-path /home/<USER>/indoor/oms_dk \
#     --out-dir /home/<USER>/indoor/oms_dk/pkl \
#     --extra-tag indoor_oms_dk \
#     --pkl-name indoor_oms_dk_0718



# # oms_lidar
# python tools/create_data.py \
#     indoor_oms_lidar \
#     --root-path /home/<USER>/indoor/oms_lidar \
#     --out-dir /home/<USER>/indoor/oms_lidar/pkl \
#     --extra-tag indoor_oms_lidar \
#     --pkl-name indoor_oms_lidar_0718



# zed_lidar
python tools/create_data.py \
    indoor_zed_lidar \
    --root-path /home/<USER>/indoor/zed_lidar \
    --out-dir /home/<USER>/indoor/zed_lidar/pkl \
    --extra-tag indoor_zed_lidar \
    --pkl-name indoor_zed_lidar_0718

