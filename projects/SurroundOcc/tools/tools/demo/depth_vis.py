import os
from PIL import Image
import matplotlib.pyplot as plt
import numpy as np
import glob

# 输入文件夹路径
input_folder = '/home/<USER>/图片/demo_0423/Fusion_BEV/depth'
output_folder = '/home/<USER>/图片/demo_0423/Fusion_BEV/depth_vis'

# 创建输出文件夹（如果不存在）
if not os.path.exists(output_folder):
    os.makedirs(output_folder)

# 获取所有深度图像文件的路径
depth_image_paths = glob.glob(os.path.join(input_folder, '*.png'))  # 假设图像文件是PNG格式

for image_path in depth_image_paths:
    # 加载图像
    depth_image = Image.open(image_path)
    depth_array = np.array(depth_image)
    depth_array[0: 150] = 0
    depth_array[-150: ] = 0


    # 使用matplotlib的colormap进行上色
    plt.figure(figsize=(depth_image.width / 100, depth_image.height / 100), dpi=100)
    plt.axis('off')  # 不显示坐标轴
    plt.imshow(depth_array)  # 'plasma'是colormap之一，你可以选择其他的

    # 构建输出文件名并保存图像
    base_name = os.path.basename(image_path)
    output_path = os.path.join(output_folder, f"colored_{base_name}")
    plt.savefig(output_path, bbox_inches='tight', pad_inches=0)
    plt.close()  # 关闭当前图表，释放内存

print("Colored depth images have been saved.")


