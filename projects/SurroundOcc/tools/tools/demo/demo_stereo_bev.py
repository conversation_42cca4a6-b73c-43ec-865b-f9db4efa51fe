import os
from PIL import Image, ImageDraw, ImageFont
import re
import glob


# 定义文件夹路径和其他参数
folder_left_eye = "/home/<USER>/to_ping_0539/tof1"
folder_right_eye = "/home/<USER>/to_ping_0539/tof1_s"
folder_gt = "/home/<USER>/to_ping_0539/tof3"
folder_pred = "/home/<USER>/to_ping_0539/tof6"
sizes = [(526, 400), (526, 400), (526, 400), (526, 400)]
horizontal_spacing_row1 = 30
horizontal_spacing_row2 = 150
vertical_spacing = 80
output_folder = "/home/<USER>/to_ping_0539/Output"

if not os.path.exists(output_folder):
    os.makedirs(output_folder)

# 获取排序后的图像路径的函数
def get_sorted_image_paths(folder, suffix):
    return sorted([os.path.join(folder, file) for file in os.listdir(folder) if file.endswith(suffix)])

left_eye_images = get_sorted_image_paths(folder_left_eye, ".jpg")
# right_eye_images = get_sorted_image_paths(folder_right_eye, ".jpg")
right_eye_images = get_sorted_image_paths(folder_right_eye, ".jpg")
gt_images = get_sorted_image_paths(folder_gt, ".jpg")
pred_images = get_sorted_image_paths(folder_pred, ".jpg")

# 加载字体
try:
    font = ImageFont.truetype("Inconsolata-SemiCondensedSemiBold.ttf", 30)
except IOError:
    font = ImageFont.load_default()

texts = ["Single-line TOF A", "Single-line TOF B", "3-line TOF ", "6-line TOF"]  # 要添加的文本

# 循环并保存每组大图
for i in range(min(len(left_eye_images), len(right_eye_images), len(gt_images), len(pred_images))):
    print(i)
    images = [Image.open(x).resize(sizes[j], Image.NEAREST) for j, x in enumerate([left_eye_images[i], right_eye_images[i], gt_images[i], pred_images[i]])]
    total_width = max(sizes[0][0] + sizes[1][0] + horizontal_spacing_row1, sizes[2][0] + sizes[3][0] + horizontal_spacing_row2)
    total_height = sum([max(sizes[0][1], sizes[1][1]), max(sizes[2][1], sizes[3][1])]) + vertical_spacing + 40  # 加40以留出文本空间
    big_image = Image.new('RGB', (total_width, total_height), 'white')
    draw = ImageDraw.Draw(big_image)

    # 计算并调整每个图像的位置
    x_offset_row1 = (total_width - sizes[0][0] - sizes[1][0] - horizontal_spacing_row1) // 2
    x_offset_row2 = (total_width - sizes[2][0] - sizes[3][0] - horizontal_spacing_row2) // 2
    y_offset_row1 = 0
    y_offset_row2 = max(sizes[0][1], sizes[1][1]) + vertical_spacing

    positions = [(x_offset_row1, y_offset_row1), (x_offset_row1 + sizes[0][0] + horizontal_spacing_row1, y_offset_row1),
                 (x_offset_row2, y_offset_row2), (x_offset_row2 + sizes[2][0] + horizontal_spacing_row2, y_offset_row2)]

    # 粘贴图像到大图并添加文本
    for j, (img, pos) in enumerate(zip(images, positions)):
        big_image.paste(img, pos)
        text_width, text_height = draw.textsize(texts[j], font=font)
        text_x = pos[0] + (sizes[j][0] - text_width) / 2
        text_y = pos[1] + sizes[j % 2][1] + 10  # 使文本在图片下方10像素处
        draw.text((text_x, text_y), texts[j], fill="black", font=font)

    save_path = left_eye_images[i].split('/')[-1].replace('_rgb.jpg', '.png')
    output_path = os.path.join(output_folder, save_path)
    big_image.save(output_path)

    print(f"Saved: {output_path}")

def sort_key(filename):
    # 使用正则表达式从文件名中提取数字
    numbers = re.findall(r'\d+', filename)
    # 将提取的数字转换为整数
    return int(numbers[-1]) if numbers else 0


def sort_filename(filename):
    numbers = re.findall(r"\d+", filename)
    return int(numbers[-1]) if numbers else -1

# 输出文件夹和GIF文件名
# output_folder = "/home/<USER>/图片/障碍物demo/fusion/output"
# gif_filename = "/home/<USER>/图片/demo_0423/Fusion/BEV_output/animated.gif"
gif_filename = "/home/<USER>/to_ping_0539/Output/1Stereo_BEV.gif"

image_files = [os.path.join(output_folder, f) for f in os.listdir(output_folder) if f.endswith('.png')]
image_files = sorted(image_files, key=sort_filename)

# 读取第一张图片并创建一个Pillow图像列表
images = [Image.open(image_file) for image_file in image_files]
# 创建GIF# save_all参数表示保存所有图像帧# append_images是除了第一帧之外的所有帧# duration是每帧之间的延迟，以毫秒为单位，根据需要调整# loop表示GIF的循环次数，0表示无限循环

images[0].save(
    gif_filename,
    save_all=True,
    append_images=images[1:],
    duration=300, # 调整为所需的帧间延迟
    loop=0)

print(f"GIF saved to {gif_filename}")



