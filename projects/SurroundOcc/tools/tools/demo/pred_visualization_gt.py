import open3d as o3d
import numpy as np
import os
import re
import glob
from PIL import Image
import pyauto<PERSON>i


def display_ply_files_side_by_side(vis, pred_ply, gt_ply):
    fixed_translation_distance = 0  # 例如，5个单位距离

    translation = np.array([fixed_translation_distance, 0, 0]).reshape((3, 1))
    pred_ply.translate(translation)

    vis.clear_geometries()
    vis.add_geometry(gt_ply)

def screenshot_callback(vis, sample_paths, idx):

    # 截图并保存
    token = sample_paths[idx[0]][1].split('/')[-1].replace('.ply', '.png')[5:]
    token = '/home/<USER>/音乐/感知难点可视化/rgbd问题/Occ_GT/' + token
    vis.capture_screen_image(token)

    # 裁剪图片
    image = Image.open(token)
    crop_area = (750, 300, 1150, 700)
    cropped_image = image.crop(crop_area)
    cropped_image.save(token)
    print("Screenshot saved as", token)

def ctrlv():
    # 模拟 Ctrl+V 操作
    pyautogui.hotkey('ctrl', 'v')

    # # 等待操作完成
    # pyautogui.sleep(0.5)

def load_sample_paths(folder_path):
    sample_paths = []
    for sample_folder in os.listdir(folder_path):
        sample_path = os.path.join(folder_path, sample_folder)
        if os.path.isdir(sample_path):
            pred_file = os.path.join(sample_path, sample_folder + '_pred.ply')
            gt_file = os.path.join(sample_path, sample_folder + '_gt.ply')
            if os.path.isfile(pred_file) and os.path.isfile(gt_file):
                sample_paths.append((pred_file, gt_file))
    sample_paths = sorted(sample_paths)
    return sample_paths


def flip_z(ply):
    points = np.asarray(ply.points)
    points[:, 2] = -points[:, 2]  # 翻转 Z 轴
    ply.points = o3d.utility.Vector3dVector(points)


def key_callback(vis, sample_paths, idx, forward=True):
    if forward and idx[0] < len(sample_paths) - 1:
        idx[0] += 1
    elif not forward and idx[0] > 0:
        idx[0] -= 1

    print(sample_paths[idx[0]][0])
    print(sample_paths[idx[0]][1])
    print('\n')

    pred_ply = o3d.io.read_point_cloud(sample_paths[idx[0]][0])
    gt_ply = o3d.io.read_point_cloud(sample_paths[idx[0]][1])

    display_ply_files_side_by_side(vis, pred_ply, gt_ply)


def extract_number(file_path):
    match = re.search(r'_(\d+)_\w+\.ply$', file_path)
    return int(match.group(1)) if match else None

def visualize_samples(folder_path):
    sample_paths = load_sample_paths(folder_path)
    if not sample_paths:
        print("No samples found")
        return

    idx = [382]  # Current sample index
    vis = o3d.visualization.VisualizerWithKeyCallback()
    vis.create_window(window_name="GT")  # 设置窗口标题

    key_callback(vis, sample_paths, idx)  # Display the first sample

    # Register key callback for next and previous samples
    vis.register_key_callback(262, lambda vis: key_callback(vis, sample_paths, idx, True))  # Right arrow key
    vis.register_key_callback(263, lambda vis: key_callback(vis, sample_paths, idx, False))  # Left arrow key
    vis.register_key_callback(ord('A'), lambda vis: ctrlv())  # A key for screenshot
    vis.register_key_callback(ord('S'), lambda vis: screenshot_callback(vis, sample_paths, idx))  # S key for screenshot

    vis.run()
    vis.destroy_window()


# Example usage
folder_path = '/home/<USER>/音乐/0509_fusion_occ/3FO3NTQMXFQN/camera3'
visualize_samples(folder_path)
