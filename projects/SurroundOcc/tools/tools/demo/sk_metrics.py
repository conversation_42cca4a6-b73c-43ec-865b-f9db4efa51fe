import os
import numpy as np
from skimage.io import imread
from sklearn.metrics import jaccard_score

# Define paths to the predicted and ground truth directories
pred_dir = '/home/<USER>/图片/0425bev/pred'
gt_dir = '/home/<USER>/图片/0425bev/gt'

# List files in both directories
pred_files = sorted(os.listdir(pred_dir))
gt_files = sorted(os.listdir(gt_dir))

# Function to load and preprocess images
def load_and_preprocess(file_path):
    img = imread(file_path)
    img[img == 255] = 1   # Convert 255 to 1
    img[img == 128] = 0   # Convert 128 to 0
    return img

# Load images and calculate IoU for each corresponding pair for each class
ious_class_0 = []
ious_class_1 = []

for pred_file, gt_file in zip(pred_files, gt_files):
    pred_img = load_and_preprocess(os.path.join(pred_dir, pred_file))
    gt_img = load_and_preprocess(os.path.join(gt_dir, gt_file))
    # Calculate IoU for class 0 (background)
    iou_0 = jaccard_score(gt_img.flatten(), pred_img.flatten(), average=None, labels=[0])[0]
    ious_class_0.append(iou_0)
    # Calculate IoU for class 1 (foreground)
    iou_1 = jaccard_score(gt_img.flatten(), pred_img.flatten(), average=None, labels=[1])[0]
    ious_class_1.append(iou_1)

# Calculate the average IoU for each class
average_iou_class_0 = np.mean(ious_class_0)
average_iou_class_1 = np.mean(ious_class_1)

# Output results
print(f"Average IoU for Class 0 (background): {average_iou_class_0}")
print(f"Average IoU for Class 1 (foreground): {average_iou_class_1}")
