import numpy as np
import open3d as o3d
import os
import glob


class NPYVisualizer:
    def __init__(self, folder_path):
        self.folder_path = folder_path
        # 获取所有npy文件并进行排序
        self.files = sorted(glob.glob(os.path.join(folder_path, '*.npy')))
        self.files1 = glob.glob(os.path.join(folder_path, '*.npy'))
        self.index = 0
        self.vis = o3d.visualization.VisualizerWithKeyCallback()

        # 设置键盘回调函数
        self.vis.register_key_callback(262, self.next_file)  # 右键
        self.vis.register_key_callback(263, self.prev_file)  # 左键

    def load_and_process_npy(self, file_path):
        occ = np.load(file_path)

        # (occ[:, :, 0])[occ[:, :, 1] == 1] = 1
        # print(occ.shape)

        occ = occ.reshape(80, -1, 80).transpose(0, 2, 1)
        # # print(occ.shape)
        occ = np.flip(occ, 2).astype(np.float32)
        occ[occ == 2] = 0
        # fov_mask = np.load('/home/<USER>/文档/mask1.npy')  # 双目 fov
        # fov_mask = np.load('/home/<USER>/文档/mask_rgbd_occ.npy')  # rgbd fov
        # fov_mask = np.repeat(fov_mask[:, :, np.newaxis], repeats=occ.shape[2], axis=2)
        # occ = occ * fov_mask
        occ[0, 0, :] = 1
        occ[19, 0, :] = 1
        occ[39, 0, :] = 1
        occ[59, 0, :] = 1
        occ[79, 0, :] = 1
        occ[0, 19, :] = 1
        occ[0, 39, :] = 1
        occ[0, 59, :] = 1
        occ[0, 79, :] = 1

        return occ

    def visualize_npy(self, occ):
        # 创建Open3D point cloud
        pcd = o3d.geometry.PointCloud()
        points = np.argwhere(occ)  # 获取非零点坐标
        pcd.points = o3d.utility.Vector3dVector(points)

        # 创建一个坐标系
        # axis = o3d.geometry.TriangleMesh.create_coordinate_frame(size=10, origin=[0, 0, 0])

        self.vis.clear_geometries()
        self.vis.add_geometry(pcd)
        self.vis.poll_events()
        self.vis.update_renderer()

    def next_file(self, vis):
        self.index = (self.index + 1) % len(self.files)
        # 打印完整的文件路径
        print(f"当前文件: {self.files[self.index]}")
        occ = self.load_and_process_npy(self.files[self.index])
        self.visualize_npy(occ)

    def prev_file(self, vis):
        self.index = (self.index - 1 + len(self.files)) % len(self.files)
        # 打印完整的文件路径
        print(f"当前文件: {self.files[self.index]}")
        occ = self.load_and_process_npy(self.files[self.index])
        self.visualize_npy(occ)

    def run(self):
        self.vis.create_window()
        occ = self.load_and_process_npy(self.files[self.index])
        self.visualize_npy(occ)
        self.vis.run()


# 主函数
if __name__ == "__main__":
    folder_path = "/run/us/1/k/s/10.113.3.79:/h/r/yp/D/kws_deliver_05/s/3FO3NN/points_tof_1/camera3"
    # folder_path = "/home/<USER>/Datasets/indoor/scenes1_3/lidar_oms/3-53kjl_rays"
    visualizer = NPYVisualizer(folder_path)
    visualizer.run()
