from PIL import Image
import re
import glob

# 图片路径
image_files = glob.glob("/home/<USER>/桌面/压缩文件/rgbd_new/Occ_demo_img/*.png")  # 确保路径和格式正确
# print(image_files)

# 自定义排序函数：按文件名中的数字排序
def sort_key(filename):
    # 使用正则表达式从文件名中提取数字
    numbers = re.findall(r'\d+', filename)
    # 将提取的数字转换为整数
    return int(numbers[-1]) if numbers else 0

# 使用sort方法直接对列表进行排序
image_files.sort(key=sort_key)
# print(image_files)

# 读取图片
images = [Image.open(image) for image in image_files]

# 将第一张图片以外的其他图片保存为一个GIF
images[0].save('/home/<USER>/桌面/压缩文件/rgbd_new/Occ_demo_img/RGBD_Occ.gif', save_all=True, append_images=images[1:], optimize=False, duration=500, loop=0)


