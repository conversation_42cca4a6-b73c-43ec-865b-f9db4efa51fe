import re
import numpy as np
import datetime
from matplotlib import pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib.ticker import FuncFormatter
import matplotlib.colors as mcolors

class DataPlotter:
    def __init__(self, data_config):
        self.font = data_config.get('font', 'DejaVu Sans Mono')
        self.max_epoch = data_config.get('max_epoch', 24)
        self.legend_loc = data_config.get('legend_loc', 'upper right')
        self.charts = data_config.get('charts', [])
        self.filename = data_config.get('save_file', )

    @staticmethod
    def scientific_formatter(x, pos):
        if x == 0:
            return '0'
        return f'{x:.1e}'.replace('e-0', 'e-').replace('e+0', 'e+')

    @staticmethod
    def parse_data(file_path, max_epoch):

        def apply_threshold(value, threshold):
            return value if value >= threshold else 0

        epoch_data = []
        loss_data = []
        iter_data = []
        lr_data = []
        iou_data = []
        occupancy_0_1m = []
        occupancy_1_2m = []
        occupancy_2_3m = []
        occupancy_3_4m = []
        patch_mean_data = []

        threshold = 1e-5  # 设置阈值

        with open(file_path, 'r') as file:
            for line in file:
                # 解析损失和学习率
                train_match = re.search(
                    r'Epoch\(train\)\s+\[(\d+)\]\[\s*(\d+)/(\d+)\].*lr: (\d+\.\d+e-\d+|\d+\.\d+).*loss: (\d+\.\d+)',
                    line)
                if train_match:
                    epoch = int(train_match.group(1))
                    current_iter = int(train_match.group(2))
                    total_iters_per_epoch = int(train_match.group(3))
                    lr = float(train_match.group(4))
                    loss = float(train_match.group(5))
                    if epoch <= max_epoch:
                        total_iter = (epoch - 1) * total_iters_per_epoch + current_iter
                        iter_data.append(total_iter)
                        loss_data.append(loss)
                        lr_data.append(lr)
                    print(
                        f"Matched LR and Loss: Epoch {epoch}, Iter {current_iter}/{total_iters_per_epoch}, LR {lr}, Loss {loss}")

                # 解析验证的 IoU 和 patch_mean_IoU 值
                val_match = re.search(
                    r'Epoch\(val\)\s+\[(\d+)\].*\'0-1m\':\s*\{[^}]*\'stop_line\':\s*([\d\.e+-]+).*\'1~2m\':\s*\{[^}]*\'stop_line\':\s*([\d\.e+-]+).*\'2~3m\':\s*\{[^}]*\'stop_line\':\s*([\d\.e+-]+).*\'3~4m\':\s*\{[^}]*\'stop_line\':\s*([\d\.e+-]+).*patch_mean\':\s*\{[^}]*\'stop_line\':\s*([\d\.e+-]+).*total_overall:\s*\{[^}]*\'stop_line\':\s*([\d\.e+-]+)',
                    line)
                if val_match:
                    epoch = int(val_match.group(1))
                    occupancy_0_1m_value = apply_threshold(float(val_match.group(2)), threshold)
                    occupancy_1_2m_value = apply_threshold(float(val_match.group(3)), threshold)
                    occupancy_2_3m_value = apply_threshold(float(val_match.group(4)), threshold)
                    occupancy_3_4m_value = apply_threshold(float(val_match.group(5)), threshold)
                    patch_mean_occupancy = apply_threshold(float(val_match.group(6)), threshold)
                    total_overall_occupancy = apply_threshold(float(val_match.group(7)), threshold)

                    if epoch <= max_epoch:
                        epoch_data.append(epoch)
                        occupancy_0_1m.append(occupancy_0_1m_value)
                        occupancy_1_2m.append(occupancy_1_2m_value)
                        occupancy_2_3m.append(occupancy_2_3m_value)
                        occupancy_3_4m.append(occupancy_3_4m_value)
                        patch_mean_data.append(patch_mean_occupancy)
                        iou_data.append(total_overall_occupancy)
                    print(
                        f"Matched Validation: Epoch {epoch}, 0-1m occupancy {occupancy_0_1m_value}, 1-2m occupancy {occupancy_1_2m_value}, 2-3m occupancy {occupancy_2_3m_value}, 3-4m occupancy {occupancy_3_4m_value}, patch_mean occupancy {patch_mean_occupancy}, total_overall occupancy {total_overall_occupancy}")

        return epoch_data, loss_data, lr_data, iter_data, iou_data, patch_mean_data, occupancy_0_1m, occupancy_1_2m, occupancy_2_3m, occupancy_3_4m

    def calculate_and_set_xticks(self, ax, iter_data_lists):
        """计算并设置x轴刻度。"""
        max_iter = max(max(data) for data in iter_data_lists)
        tick_interval = np.ceil(max_iter / 5 / 1000) * 1000
        ticks = list(range(0, int(max_iter + 1), int(tick_interval)))
        tick_labels = [f"{int(x / 1000)}k" if x != 0 else "0" for x in ticks]
        ax.set_xticks(ticks)
        ax.set_xticklabels(tick_labels)

    def setup_common_axis_features(self, ax, x_label, y_label, title, invert_yaxis=False, loc='lower right'):
        """设置通用的轴特征，如标签、标题和图例。"""
        ax.set_xlabel(x_label, fontsize=12, fontname=self.font)
        ax.set_ylabel(y_label, fontsize=12, fontname=self.font)
        if invert_yaxis:
            ax.invert_yaxis()
        ax.set_title(title, fontsize=12, fontname=self.font)
        ax.grid(alpha=0.3, linestyle='--')
        ax.legend(loc=loc, fontsize=12, prop={'family': self.font})

    def annotate_max_value(self, ax, epoch_data, value_data, chart, if_max=True, label_format='[{x}, {y:.3f}]'):
        target_value = max(value_data) if if_max else min(value_data)
        target_epoch = epoch_data[value_data.index(target_value)]

        # 准备标注文本和位置
        annotation_text = label_format.format(x=target_epoch, y=target_value)
        xytext = (5, -15)  # 修改为向下偏移，以放置标注在曲线下方

        # 将十六进制颜色代码转换为RGBA格式
        rgba_color = mcolors.to_rgba(chart['color'], alpha=0.35)

        # 检查是否靠近图表右边缘
        if target_epoch > max(epoch_data) - 5:  # 如果是最后几个epoch
            xytext = (-60, -15)  # 向左和向下偏移标注

        # 检查是否靠近图表顶部边缘
        y_lim = ax.get_ylim()
        # 针对CD值（最小值在图表上方）的情况
        if not if_max:
            if target_value > y_lim[0] + (y_lim[1] - y_lim[0]) * 0.9:  # 如果最小值靠近图表上方
                xytext = (5, 25)  # 向上偏移标注以避免超出图表边界

        # 添加标注
        ax.annotate(annotation_text, xy=(target_epoch, target_value), xytext=xytext,
                    textcoords="offset points", ha='center', va='top',  # 调整垂直对齐为顶部对齐
                    arrowprops=dict(arrowstyle="->", color=rgba_color),
                    color=rgba_color, fontweight='bold')

    def plot_loss(self, ax, title, x_label='Total Iteration', y_label='Loss'):
        iter_interval = 5  # 数据采样间隔
        iter_data_lists = []

        for chart in self.charts:
            _, loss_data, _, iter_data, _, _, _, _, _, _ = self.parse_data(chart['file_path'], self.max_epoch)
            if iter_data and loss_data:  # 确保数据非空
                # 使用切片进行数据采样
                loss_data = np.array(loss_data)
                sampled_iter_data = iter_data[::iter_interval]
                sampled_loss_data = loss_data[::iter_interval]
                ax.plot(sampled_iter_data, sampled_loss_data, color=chart['color'], label=chart['label'], linewidth=2.0)
                iter_data_lists.append(sampled_iter_data)

        self.calculate_and_set_xticks(ax, iter_data_lists)
        self.setup_common_axis_features(ax, x_label, y_label, title, loc='upper right')

    def plot_learning_rate(self, ax, title, x_label='Total Iteration', y_label='Learning Rate'):
        iter_data_lists = []

        for chart in self.charts:
            _, _, lr_data, iter_data, _, _, _, _, _, _ = self.parse_data(chart['file_path'], self.max_epoch)
            if len(iter_data) == len(lr_data) and iter_data and lr_data:  # 确保数据非空并且长度相同
                ax.plot(iter_data, lr_data, color=chart['color'], label=chart['label'], linewidth=2.0)
                iter_data_lists.append(iter_data)

        # 调用 calculate_and_set_xticks 方法以设置 x 轴刻度
        self.calculate_and_set_xticks(ax, iter_data_lists)

        # 科学计数法设置 y 轴刻度
        ax.yaxis.set_major_formatter(FuncFormatter(self.scientific_formatter))

        self.setup_common_axis_features(ax, x_label, y_label, title, loc='upper right')

    def plot_iou(self, ax, title, x_label='Epoch', y_label='IoU'):
        for chart in self.charts:
            epoch_data, _, _, _, iou_data, _, _, _, _, _ = self.parse_data(chart['file_path'], self.max_epoch)
            ax.plot(epoch_data, iou_data, color=chart['color'], marker=chart['marker'], label=chart['label'], linewidth=2.0)
            self.annotate_max_value(ax, epoch_data, iou_data, chart)

        self.setup_common_axis_features(ax, x_label, y_label, title, loc='center right')


    def plot_patch_mean(self, ax, title, x_label='Epoch', y_label='IoU'):
        for chart in self.charts:
            epoch_data, _, _, _, _, patch_mean_data, _, _, _, _ = self.parse_data(chart['file_path'], self.max_epoch)
            ax.plot(epoch_data, patch_mean_data, color=chart['color'], marker=chart['marker'], label=chart['label'], linewidth=2.0)
            self.annotate_max_value(ax, epoch_data, patch_mean_data, chart)

        self.setup_common_axis_features(ax, x_label, y_label, title, loc='center right')

    def plot_patch1(self, ax, title, x_label='Epoch', y_label='IoU'):
        for chart in self.charts:
            epoch_data, _, _, _, _, _, patch1_data, _, _, _ = self.parse_data(chart['file_path'], self.max_epoch)
            ax.plot(epoch_data, patch1_data, color=chart['color'], marker=chart['marker'], label=chart['label'], linewidth=2.0)
            self.annotate_max_value(ax, epoch_data, patch1_data, chart)

        self.setup_common_axis_features(ax, x_label, y_label, title, loc='best')

    def plot_patch2(self, ax, title, x_label='Epoch', y_label='IoU'):
        for chart in self.charts:
            epoch_data, _, _, _, _, _, _, patch2_data, _, _ = self.parse_data(chart['file_path'], self.max_epoch)
            ax.plot(epoch_data, patch2_data, color=chart['color'], marker=chart['marker'], label=chart['label'], linewidth=2.0)
            self.annotate_max_value(ax, epoch_data, patch2_data, chart)

        self.setup_common_axis_features(ax, x_label, y_label, title, loc='center right')

    def plot_patch3(self, ax, title, x_label='Epoch', y_label='IoU'):
        for chart in self.charts:
            epoch_data, _, _, _, _, _, _, _, patch3_data, _ = self.parse_data(chart['file_path'], self.max_epoch)
            ax.plot(epoch_data, patch3_data, color=chart['color'], marker=chart['marker'], label=chart['label'], linewidth=2.0)
            self.annotate_max_value(ax, epoch_data, patch3_data, chart)

        self.setup_common_axis_features(ax, x_label, y_label, title, loc='center right')


    def plot_patch4(self, ax, title, x_label='Epoch', y_label='IoU'):
        for chart in self.charts:
            epoch_data, _, _, _, _, _, _, _, _, patch4_data = self.parse_data(chart['file_path'], self.max_epoch)
            ax.plot(epoch_data, patch4_data, color=chart['color'], marker=chart['marker'], label=chart['label'], linewidth=2.0)
            self.annotate_max_value(ax, epoch_data, patch4_data, chart)

        self.setup_common_axis_features(ax, x_label, y_label, title, loc='center right')

    def plot_f_score(self, ax, title, x_label='Epoch', y_label='F-score'):
        for chart in self.charts:
            epoch_data, _, f_score_data, _ = self.parse_data(chart['file_path'], self.max_epoch)
            ax.plot(epoch_data, f_score_data, color=chart['color'], marker=chart['marker'], label=chart['label'], linewidth=2.0)
            self.annotate_max_value(ax, epoch_data, f_score_data, chart)

        self.setup_common_axis_features(ax, x_label, y_label, title)

    def plot_flops_params(self, ax, models, flops, params, title, chart_colors, x_label='BevEncoder', y_label='Values'):
        x = np.arange(len(models))
        max_value = max(max(flops), max(params))
        ax.set_ylim(0, max_value * 1.28)
        width = 0.35
        rects1 = ax.bar(x - width / 2, flops, width, label='FLOPs (G)', color='#ef7b7b')
        rects2 = ax.bar(x + width / 2, params, width, label='Params (Mb)', color='#21a675')

        def autolabel(rects):
            for rect in rects:
                height = rect.get_height()
                ax.annotate(f'{height}', xy=(rect.get_x() + rect.get_width() / 2, height),
                            xytext=(0, 3), textcoords="offset points", ha='center', va='bottom')
        autolabel(rects1)
        autolabel(rects2)

        self.setup_common_axis_features(ax, x_label, y_label, title, loc='upper right')
        ax.set_xticks(x)
        ax.set_xticklabels(models)

    def plot_divider(self, ax):
        ax.set_xticks([])
        ax.set_yticks([])
        ax.axhline(color='grey', linestyle='--')
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['bottom'].set_visible(False)
        ax.spines['left'].set_visible(False)

    def plot_accuracy(self, ax, title, x_label='Epoch', y_label='Acc'):
        for chart in self.charts:
            epoch_data, _, _, acc_data = self.parse_data(chart['file_path'], self.max_epoch)
            ax.plot(epoch_data, acc_data, color=chart['color'], marker=chart['marker'], label=chart['label'], linewidth=2.0)
        self.setup_common_axis_features(ax, x_label, y_label, title, invert_yaxis=True)

    def plot_completeness(self, ax, title, x_label='Epoch', y_label='Comp'):
        for chart in self.charts:
            epoch_data, _, _, comp_data = self.parse_data(chart['file_path'], self.max_epoch)
            ax.plot(epoch_data, comp_data, color=chart['color'], marker=chart['marker'], label=chart['label'], linewidth=2.0)
        self.setup_common_axis_features(ax, x_label, y_label, title, invert_yaxis=True)

    def plot_precision(self, ax, title, x_label='Epoch', y_label='Prec'):
        for chart in self.charts:
            epoch_data, _, _, prec_data = self.parse_data(chart['file_path'], self.max_epoch)
            ax.plot(epoch_data, prec_data, color=chart['color'], marker=chart['marker'], label=chart['label'], linewidth=2.0)
        self.setup_common_axis_features(ax, x_label, y_label, title)

    def plot_recall(self, ax, title, x_label='Epoch', y_label='Rec'):
        for chart in self.charts:
            epoch_data, _, _, recall_data = self.parse_data(chart['file_path'], self.max_epoch)
            ax.plot(epoch_data, recall_data, color=chart['color'], marker=chart['marker'], label=chart['label'], linewidth=2.0)
        self.setup_common_axis_features(ax, x_label, y_label, title)

    def plot_data(self):
        plt.figure(figsize=(10, 20), dpi=500)
        plt.suptitle('Comparison of Different Models', fontsize=14, fontweight='bold', font='DejaVu Sans Mono')
        gs = gridspec.GridSpec(6, 2, height_ratios=[1, 1, 0.1, 1, 1, 1])

        ax_loss = plt.subplot(gs[0, 0])
        self.plot_loss(
            title='Total Loss over Iterations',
            ax=ax_loss,
        )

        ax_lr = plt.subplot(gs[0, 1])
        self.plot_learning_rate(
            title='Learning Rate over Iterations',
            ax=ax_lr,
        )

        ax_iou = plt.subplot(gs[1, 0])
        self.plot_iou(
            title='Occupancy Class IoU Values over Epochs',
            ax=ax_iou,
        )

        ax_patch_mean = plt.subplot(gs[3, 0])
        self.plot_patch_mean(
            title='Patch Mean Occupancy Class IoU Values over Epochs',
            ax=ax_patch_mean,
        )

        ax_divider = plt.subplot(gs[2, :])
        self.plot_divider(ax=ax_divider)


        ax_patch1 = plt.subplot(gs[1, 1])
        self.plot_patch1(
            title='0-1m Occupancy Class IoU Values over Epochs',
            ax=ax_patch1,
        )

        ax_patch2 = plt.subplot(gs[3, 1])
        self.plot_patch2(
            title='1-2m Occupancy Class IoU Values over Epochs',
            ax=ax_patch2,
        )


        ax_patch3 = plt.subplot(gs[4, 0])
        self.plot_patch3(
            title='2-3m Occupancy Class IoU Values over Epochs',
            ax=ax_patch3,
        )

        ax_patch4 = plt.subplot(gs[4, 1])
        self.plot_patch4(
            title='3-4m Occupancy Class IoU Values over Epochs',
            ax=ax_patch4,
        )

        ax_flops_params = plt.subplot(gs[5, 0])
        models = ['w/o BevEncoder', 'Vol2Vol', 'EfficientUNet3d']
        flops = [117.83, 279.85, 226.91]  # FLOPs（G）
        params = [52.6, 144.55, 72.53]  # Params（Mb）
        self.plot_flops_params(
            title='FLOPs and Params Comparison',
            ax=ax_flops_params,
            models=models,
            flops=flops,
            params=params,
            chart_colors=[chart['color'] for chart in self.charts]
        )

        plt.tight_layout(rect=[0, 0.03, 1, 0.95])
        plt.subplots_adjust(wspace=0.22, hspace=0.5)
        self.save_plot_with_watermarks(self.filename, '<EMAIL>')
        # plt.show()

    def save_plot_with_watermarks(self, filename, base_text, rows=5, cols=3, angle=45, alpha=0.2, fontsize=23,
                                  color='grey', fontname='Droid Naskh Shift Alt'):
        fig = plt.gcf()

        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        watermark_text = f"{base_text}\n{current_time}"

        x_delta = 1.0 / (cols - 1)
        y_delta = 1.0 / (rows - 0.8)

        for i in range(rows):
            for j in range(cols):
                x = 0.5 * x_delta + j * x_delta - 0.25
                y = 1 - (0.5 * y_delta + i * y_delta - 0.25)
                fig.text(x, y, watermark_text, fontsize=fontsize, color=color, alpha=alpha, ha='center', va='center',
                         rotation=angle, fontname=fontname, transform=fig.transFigure)

        plt.savefig(filename)
        plt.close(fig)


if __name__ == '__main__':

    color_list = ['#ef7b7b', '#21a675', '#70a1d7', '#cca8e9']
    # Example Usage
    data_config = {
        'max_epoch': 24,
        'save_file': '/home/<USER>/ecoaitoolkit/tools/demo/oms_dk_bev.pdf',
        'legend_loc': 'lower right',
        'font': 'Droid Naskh Shift Alt',
        'charts': [
            {
                'file_path': '/home/<USER>/ecoaitoolkit/work_dirs/indoor_oms_dk_bev/0709_bev2_nopretrain/20240710_153211/20240710_153211.log',
                # 'file_path': '/home/<USER>/projects/SurroundOcc/temp/0514_fusion_occ.log',
                'label': 'RGBD BEV',
                'color': color_list[0],
                'marker': 's',
            },

            # {
            #     'file_path': '/home/<USER>/PycharmProjects/ecoaitoolkit/projects/SurroundOcc/temp/0514_stereo_occ.log',
            #     'label': 'Stereo Occ',
            #     'color': color_list[1],
            #     # 'marker': 'None',
            #     'marker': 'v',
            # },

        ]
    }

    plotter = DataPlotter(data_config)
    plotter.plot_data()
