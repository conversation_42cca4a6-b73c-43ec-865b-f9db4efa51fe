import numpy as np
import open3d as o3d
import os
import glob

class NPYVisualizer:
    def __init__(self, gt_folder_path, pred_folder_path):
        self.gt_folder_path = gt_folder_path
        self.pred_folder_path = pred_folder_path
        self.gt_files = sorted(glob.glob(os.path.join(gt_folder_path, '*.npy')))
        self.pred_files = sorted(glob.glob(os.path.join(pred_folder_path, '*.npy')))
        self.index = 0
        self.vis = o3d.visualization.VisualizerWithKeyCallback()

        self.vis.register_key_callback(262, self.next_file)  # 右键
        self.vis.register_key_callback(263, self.prev_file)  # 左键

        self.view_params = {
            "lookat": [35.0, 41.5, 14.5],
            "front": [0.077796824317824129, -0.98552107624458796, 0.15065146001207833],
            "up": [-0.045334829219056981, 0.14745612229711974, 0.98802907105852711],
            "zoom": 0.7
        }

        # Use hexadecimal colors and convert them to normalized RGB
        self.correct_color = self.hex_to_rgb("#00FF00")  # Green
        self.false_negative_color = self.hex_to_rgb("#0000FF")  # Blue
        self.false_positive_color = self.hex_to_rgb("#FF0000")  # Red

    def hex_to_rgb(self, hex_color):
        hex_color = hex_color.lstrip('#')
        return [int(hex_color[i:i+2], 16) / 255.0 for i in (0, 2, 4)]

    def load_and_process_npy(self, file_path):
        occ = np.load(file_path)
        # occ = occ.reshape(80, -1, 80).transpose(0, 2, 1)
        # occ = np.flip(occ, 2).astype(np.float32)
        # occ[occ == 2] = 0
        return occ

    def set_view(self):
        ctr = self.vis.get_view_control()
        ctr.set_lookat(self.view_params["lookat"])
        ctr.set_front(self.view_params["front"])
        ctr.set_up(self.view_params["up"])
        ctr.set_zoom(self.view_params["zoom"])

    def visualize_comparison(self, gt, pred):
        gt = gt.astype(bool)
        pred = pred.astype(bool)

        correct_pred = np.argwhere(gt & pred)
        false_negatives = np.argwhere(gt & ~pred)
        false_positives = np.argwhere(~gt & pred)

        all_points = np.vstack((correct_pred, false_negatives, false_positives))
        colors = np.zeros((all_points.shape[0], 3))

        colors[:len(correct_pred)] = self.correct_color  # Green for correct predictions
        colors[len(correct_pred):len(correct_pred) + len(false_negatives)] = self.false_negative_color  # Blue for false negatives
        colors[len(correct_pred) + len(false_negatives):] = self.false_positive_color  # Red for false positives

        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(all_points)
        pcd.colors = o3d.utility.Vector3dVector(colors)

        self.vis.clear_geometries()
        self.vis.add_geometry(pcd)
        self.set_view()  # 设置视角
        self.vis.poll_events()
        self.vis.update_renderer()

    def next_file(self, vis):
        self.index = (self.index + 1) % len(self.gt_files)
        gt_file = self.gt_files[self.index]
        pred_file = self.pred_files[self.index]
        print(f"当前文件: {gt_file}, {pred_file}")
        gt = self.load_and_process_npy(gt_file)
        pred = self.load_and_process_npy(pred_file)
        self.visualize_comparison(gt, pred)

    def prev_file(self, vis):
        self.index = (self.index - 1 + len(self.gt_files)) % len(self.gt_files)
        gt_file = self.gt_files[self.index]
        pred_file = self.pred_files[self.index]
        print(f"当前文件: {gt_file}, {pred_file}")
        gt = self.load_and_process_npy(gt_file)
        pred = self.load_and_process_npy(pred_file)
        self.visualize_comparison(gt, pred)

    def run(self):
        self.vis.create_window()
        gt = self.load_and_process_npy(self.gt_files[self.index])
        pred = self.load_and_process_npy(self.pred_files[self.index])
        self.visualize_comparison(gt, pred)
        self.vis.run()

# 主函数
if __name__ == "__main__":
    gt_folder_path = "/run/user/1000/gvfs/sftp:host=***********/raid/yp/ecoaitoolkit/ecoaitoolkit/visual_dirs/kjl_jiexiang/all_kjlFusionOcc_0509_20cm_new_tof1/3FO3NTO3MURV/gt"
    pred_folder_path = "/run/user/1000/gvfs/sftp:host=***********/raid/yp/ecoaitoolkit/ecoaitoolkit/visual_dirs/kjl_jiexiang/all_kjlFusionOcc_0509_20cm_new_tof1/3FO3NTO3MURV/pred"
    visualizer = NPYVisualizer(gt_folder_path, pred_folder_path)
    visualizer.run()