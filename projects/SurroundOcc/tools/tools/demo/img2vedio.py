import cv2
import os

def images_to_video(image_folder, video_name, fps):
    images = [img for img in sorted(os.listdir(image_folder)) if img.endswith(".jpg") or img.endswith(".png")]
    # images = images[154:230]
    if not images:
        print("No images found in the folder.")
        return
    
    # Read the first image to get the dimensions
    frame = cv2.imread(os.path.join(image_folder, images[0]))
    height, width, layers = frame.shape

    video = cv2.VideoWriter(video_name, cv2.VideoWriter_fourcc(*'DIVX'), fps, (width, height))

    for image in images:
        frame = cv2.imread(os.path.join(image_folder, image))
        video.write(frame)

    video.release()
    print(f"Video saved as {video_name}")

# 使用示例
image_folder = '/home/<USER>/'  # 替换为你的图片文件夹路径
video_name = '/home/<USER>'             # 输出视频文件名
fps = 5                                    # 每秒帧数

images_to_video(image_folder, video_name, fps)
