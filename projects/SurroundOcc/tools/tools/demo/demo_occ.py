import os
from PIL import Image, ImageDraw, ImageFont
import re

# 定义文件夹路径和其他参数
folder_left_eye = "/home/<USER>/音乐/demo_0523/Stereo/left_img"
folder_right_eye = "/home/<USER>/音乐/demo_0523/Stereo/right_img"
folder_pred = "/home/<USER>/音乐/demo_0523/Stereo/Occ_Pred"
folder_gt = "/home/<USER>/音乐/demo_0523/Stereo/Occ_GT"
sizes = [(526, 400), (526, 400), (526, 526), (526, 526)]  # 更新尺寸以适应新布局
horizontal_spacing_row1 = 30
vertical_spacing = 80
output_folder = "/home/<USER>/音乐/demo_0523/Stereo/Occ_output"

if not os.path.exists(output_folder):
    os.makedirs(output_folder)

# 获取排序后的图像路径的函数
def get_sorted_image_paths(folder, suffix):
    pattern = re.compile(r'\d+')  # 假设文件名包含数字
    return sorted(
        [os.path.join(folder, file) for file in os.listdir(folder) if file.endswith(suffix)],
        key=lambda x: int(pattern.findall(x.split('/')[-1])[0]) if pattern.findall(x.split('/')[-1]) else 0
    )

left_eye_images = get_sorted_image_paths(folder_left_eye, ".jpg")
right_eye_images = get_sorted_image_paths(folder_right_eye, ".jpg")
left_eye_images.sort()
right_eye_images.sort()

gt_images = get_sorted_image_paths(folder_gt, ".png")
pred_images = get_sorted_image_paths(folder_pred, ".png")
gt_images = sorted(gt_images, key=lambda x: re.findall(r'\d+', x))
pred_images = sorted(pred_images, key=lambda x: re.findall(r'\d+', x))

# 加载字体
try:
    font = ImageFont.truetype("Inconsolata-SemiCondensedSemiBold.ttf", 30)
except IOError:
    font = ImageFont.load_default()

texts = ["Camera Left", "Camera Right", "Stereo Occ GT", "Stereo Occ Pred"]  # 更新文本以反映实际内容
# texts = ["Camera", "Depth", "Fusion Occ GT                                Fusion Occ Pred"]  # 更新文本以反映实际内容

# 循环并保存每组大图
for i in range(min(len(left_eye_images), len(right_eye_images), len(pred_images), len(gt_images))):
    # 第二行只包含一个图像，因此只加载三个图像
    print(pred_images[i])
    print(gt_images[i])
    images = [Image.open(x).resize(sizes[j], Image.NEAREST) for j, x in enumerate([left_eye_images[i], right_eye_images[i], gt_images[i], pred_images[i]])]
    total_width = sizes[0][0] + sizes[1][0] + horizontal_spacing_row1
    total_height = sizes[0][1] + vertical_spacing + sizes[2][1] + 40  # 为文本添加额外空间
    big_image = Image.new('RGB', (total_width, total_height), 'white')
    draw = ImageDraw.Draw(big_image)

    # 计算并调整图像的位置
    x_offset_row1 = (total_width - sizes[0][0] - sizes[1][0] - horizontal_spacing_row1) // 2
    positions_row1 = [(x_offset_row1, 0), (x_offset_row1 + sizes[0][0] + horizontal_spacing_row1, 0)]
    x_offset_row2 = (total_width - sizes[2][0] - sizes[3][0] - horizontal_spacing_row1) // 2
    positions_row2 = [(x_offset_row2, 0), (x_offset_row2 + sizes[2][0] + horizontal_spacing_row1, 0)]

    # 粘贴第一行图像到大图并添加文本
    for j, (img, pos) in enumerate(zip(images[:2], positions_row2)):
        big_image.paste(img, pos)
        text_width, text_height = draw.textsize(texts[j], font=font)
        text_x = pos[0] + (sizes[j][0] - text_width) / 2
        text_y = pos[1] + sizes[j][1] + 10  # 图片下方10像素处
        draw.text((text_x, text_y), texts[j], fill="black", font=font)

    # # 粘贴第二行图像到大图并添加文本
    # for j, (img, pos) in enumerate(zip(images[2:], positions_row2)):
    #     big_image.paste(img, pos)
    #     text_width, text_height = draw.textsize(texts[j+2], font=font)
    #     text_x = pos[0] + (sizes[j+2][0] - text_width) / 2
    #     text_y = pos[1] + sizes[j+2][1] + 10  # 图片下方10像素处
    #     draw.text((text_x, text_y), texts[j+2], fill="black", font=font)

    # 保存大图
    output_path = os.path.join(output_folder, f"combined_{i}.png")
    print(output_path)
    big_image.save(output_path)

def sort_filename(filename):
    numbers = re.findall(r"\d+", filename)
    return int(numbers[-1]) if numbers else -1



# gif_filename = "/home/<USER>/图片/demo_0423/Fusion/Occ_output/animated.gif"
# image_files = [os.path.join(output_folder, f) for f in os.listdir(output_folder) if f.endswith('.png')]
# image_files = sorted(image_files, key=sort_filename)
#
# # 读取第一张图片并创建一个Pillow图像列表
# images = [Image.open(image_file) for image_file in image_files]
# # images.sort()
# print(image_files)
# # 创建GIF# save_all参数表示保存所有图像帧# append_images是除了第一帧之外的所有帧# duration是每帧之间的延迟，以毫秒为单位，根据需要调整# loop表示GIF的循环次数，0表示无限循环
#
# images[0].save(
#     gif_filename,
#     save_all=True,
#     append_images=images[1:],
#     duration=300, # 调整为所需的帧间延迟
#     loop=0)
#
# print(f"GIF saved to {gif_filename}")
#
