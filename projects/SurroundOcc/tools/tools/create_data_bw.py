# ---------------------------------------------
# Copyright (c) OpenMMLab. All rights reserved.
# ---------------------------------------------
#  Modified by <PERSON><PERSON><PERSON>
# ---------------------------------------------
import sys
import os
sys.path.append(os.getcwd())
# from bw_data_converter.create_gt_database import create_groundtruth_database
from bw_data_converter import indoor_converter as indoor
import argparse
from os import path as osp
# import sys
# sys.path.append('.')
import numpy as np

def single_view_data_prep(root_path, info_prefix, out_dir, workers):
    """Prepare the info file for scannet dataset.

    Args:
        root_path (str): Path of dataset root.
        info_prefix (str): The prefix of info filenames.
        out_dir (str): Output directory of the generated info file.
        workers (int): Number of threads to be used.
    """
    indoor.create_indoor_info_file(
        root_path, info_prefix, out_dir, workers=workers)


parser = argparse.ArgumentParser(description='Data converter arg parser')
parser.add_argument('--dataset', metavar='single-view',default='single-view', help='name of the dataset')
parser.add_argument(
    '--root-path',
    type=str,
    default='/media/hbw/raid/xhs/code/BEVFormerCode/KJL_DATA/scenes',#这个需要进行对应调整
    help='specify the root path of dataset')
parser.add_argument(
    '--canbus',
    type=str,
    default='./data',
    help='specify the root path of nuScenes canbus')
parser.add_argument(
    '--version',
    type=str,
    default='v1.0',
    required=False,
    help='specify the dataset version, no need for kitti')
parser.add_argument(
    '--max-sweeps',
    type=int,
    default=10,
    required=False,
    help='specify sweeps of lidar per example')
parser.add_argument(
    '--out-dir',
    type=str,
    default='./',
    help='name of info pkl')
parser.add_argument('--extra-tag', type=str, default='single-view')
parser.add_argument(
    '--workers', type=int, default=4, help='number of threads to be used')
args = parser.parse_args()

if __name__ == '__main__':
    if args.dataset == 'single-view':
            single_view_data_prep(
            root_path=args.root_path,
            info_prefix=args.extra_tag,
            out_dir=args.out_dir,
            workers=args.workers
        )
            

# dict_keys(['lidar_path', 'token', 'sweeps', 'cams', 'lidar2ego_translation', 'lidar2ego_rotation', 'ego2global_translation', 'ego2global_rotation', 'timestamp', 'location', 'gt_boxes', 'gt_names', 'gt_velocity', 'num_lidar_pts', 'num_radar_pts', 'valid_flag'])


# lidar2ego_translation = np.array([0,0,0], dtype=np.float64)

# lidar2ego_rotation = np.array([
#                         [1., 0., 0.0],
#                         [0.0, 1., 0.],
#                         [0., 0., 1.]], dtype=np.float64)

# ego2global_translation = np.array([0,0,0], dtype=np.float64)

# ego2global_rotation = np.array([
#                         [1., 0., 0.0],
#                         [0.0, 1., 0.],
#                         [0., 0., 1.]], dtype=np.float64)

# cam_list = {}
# cam_list['data_path'] = ".png"
# cam_list['type'] = 'CAM_FRONT'
# cam_list['sample_data_token'] = None

# cam_list['sensor2ego_translation'] = np.array([0,0,0], dtype=np.float64)
# cam_list['sensor2ego_rotation'] = np.array([
#                                             [1., 0., 0.0],
#                                             [0.0, 1., 0.],
#                                             [0., 0., 1.]], dtype=np.float64)



# ego2global_translation
# ego2global_rotation
# timestamp
# sensor2lidar_rotation
# sensor2lidar_translation
# camera_intrinsics = np.array([
#                                 [321.4475202058989, 0, 649.3810871940957],
#                                 [0, 321.5622469758229, 485.7818157326032],
#                                 [0, 0, 1]]).astype(np.float64) / 2.0
# cams = {"CAM_FRONT":cam_list}
