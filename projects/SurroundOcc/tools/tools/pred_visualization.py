import open3d as o3d
import numpy as np
import os
import re
import glob


def display_ply_files_side_by_side(vis, pred_ply, gt_ply):
    fixed_translation_distance = 6  # 例如，5个单位距离

    translation = np.array([fixed_translation_distance, 0, 0]).reshape((3, 1))
    pred_ply.translate(translation)

    vis.clear_geometries()
    vis.add_geometry(pred_ply)
    vis.add_geometry(gt_ply)

def load_sample_paths(folder_path):
    sample_paths = []
    for sample_folder in os.listdir(folder_path):
        sample_path = os.path.join(folder_path, sample_folder)
        if os.path.isdir(sample_path):
            pred_file = os.path.join(sample_path, sample_folder + '_pred.ply')
            gt_file = os.path.join(sample_path, sample_folder + '_gt.ply')
            if os.path.isfile(pred_file) and os.path.isfile(gt_file):
                sample_paths.append((pred_file, gt_file))
    sample_paths.sort(key=lambda x: extract_number(x[0]))
    return sample_paths

# def load_sample_paths(folder_path):
#     gt_folder = None
#     pred_folder = None
#
#     # 查找以 _gt 和 _pred 结尾的文件夹
#     for sub_folder in os.listdir(folder_path):
#         if sub_folder.endswith('_gt_new'):
#             gt_folder = os.path.join(folder_path, sub_folder)
#         elif sub_folder.endswith('_pred_new'):
#             pred_folderstereo_occupancy_dataset = os.path.join(folder_path, sub_folder)
#
#     if not gt_folder or not pred_folder:
#         print("Error: _gt or _pred folder not found.")
#         return []
#
#     # 获取所有 .ply 文件
#     gt_files = sorted(glob.glob(os.path.join(gt_folder, '*.ply')))
#     pred_files = sorted(glob.glob(os.path.join(pred_folder, '*.ply')))
#
#     # 确保两个文件夹中的文件数量相同
#     if len(gt_files) != len(pred_files):
#         print("Error: Number of files in _gt and _pred folders do not match.")
#         return []
#
#     # 匹配 _gt 和 _pred 文件
#     sample_paths = []
#     for gt, pred in zip(gt_files, pred_files):
#         if extract_number(gt) == extract_number(pred):
#             sample_paths.append((pred, gt))
#
#     return sample_paths

def flip_z(ply):
    points = np.asarray(ply.points)
    points[:, 2] = -points[:, 2]  # 翻转 Z 轴
    ply.points = o3d.utility.Vector3dVector(points)

def key_callback(vis, sample_paths, idx, forward=True):
    if forward and idx[0] < len(sample_paths) - 1:
        idx[0] += 1
    elif not forward and idx[0] > 0:
        idx[0] -= 1

    print(sample_paths[idx[0]][0])

    pred_ply = o3d.io.read_point_cloud(sample_paths[idx[0]][0])
    gt_ply = o3d.io.read_point_cloud(sample_paths[idx[0]][1])

    # # 在 Z 轴上翻转 pred_ply 和 gt_ply
    # pred_ply.points = o3d.utility.Vector3dVector(
    #     np.asarray(pred_ply.points) * np.array([1, 1, -1])
    # )
    # gt_ply.points = o3d.utility.Vector3dVector(
    #     np.asarray(gt_ply.points) * np.array([1, 1, -1])
    # )

    # flip_z(pred_ply)
    # flip_z(gt_ply)

    display_ply_files_side_by_side(vis, pred_ply, gt_ply)

def extract_number(file_path):
    # 正则表达式匹配文件名中最后一个下划线后的数字序列
    match = re.search(r'_(\d+)_\w+\.ply$', file_path)
    return int(match.group(1)) if match else None

def visualize_samples(folder_path):

    sample_paths = load_sample_paths(folder_path)
    # print(sample_paths)

    if not sample_paths:
        print("No samples found")
        return

    idx = [0]  # Current sample index
    vis = o3d.visualization.VisualizerWithKeyCallback()
    vis.create_window()

    key_callback(vis, sample_paths, idx)  # Display the first sample

    # Register key callback for next and previous samples
    vis.register_key_callback(262, lambda vis: key_callback(vis, sample_paths, idx, True))  # Right arrow key
    vis.register_key_callback(263, lambda vis: key_callback(vis, sample_paths, idx, False)) # Left arrow key

    vis.run()
    vis.destroy_window()

# Example usage
# folder_path = '/home/<USER>/KJL_Related/occ_result_0226'
folder_path = '/home/<USER>/PycharmProjects/ecoaitoolkit/projects/SurroundOcc/depoly/visual_dir/horizon_d5/3FO3P2INJDB4/camera2'
# folder_path = '/home/<USER>/PycharmProjects/szfwq/visual_dir/kujiale_flashocc_f'
visualize_samples(folder_path)

