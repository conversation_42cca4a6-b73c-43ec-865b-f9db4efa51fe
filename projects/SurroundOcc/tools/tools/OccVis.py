import open3d as o3d

import numpy as np
import os
from tqdm import tqdm
import re

pc_range = [-2, 0, -0.05, 2, 4, 0.15]  # x y z
occ_size = [80, 80, 4]  # x y z


def process_file(gt_occ_path, save_path):
    gt_occ = np.load(gt_occ_path)
    lab_x, lab_z, lab_y = occ_size
    occ = gt_occ.reshape(lab_x, lab_y, lab_z).transpose(0, 2, 1)
    occ = np.flip(occ, 2).astype(np.float32)
    occ[occ == 2] = 0

    gt = torch.from_numpy(occ).cuda()

    filename = gt_occ_path.split('/')[-1].split('.npy')[0]
    scene_idx = gt_occ_path.split('/')[-1].split('.npy')[0]

    x = torch.linspace(0, gt.shape[0] - 1, gt.shape[0])
    y = torch.linspace(0, gt.shape[1] - 1, gt.shape[1])
    z = torch.linspace(0, gt.shape[2] - 1, gt.shape[2])
    X, Y, Z = torch.meshgrid(x, y, z)
    vv = torch.stack([X, Y, Z], dim=-1).cuda()

    vertices = vv[gt >= 0.5]
    vertices[:, 0] = (vertices[:, 0] + 0.5) * (pc_range[3] - pc_range[0]) / occ_size[0] + pc_range[0]
    vertices[:, 1] = (vertices[:, 1] + 0.5) * (pc_range[4] - pc_range[1]) / occ_size[1] + pc_range[1]
    vertices[:, 2] = (vertices[:, 2] + 0.5) * (pc_range[5] - pc_range[2]) / occ_size[2] + pc_range[2]

    vertices = vertices.cpu().numpy()

    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(vertices)

    save_dir = os.path.join(save_path, scene_idx)
    os.makedirs(save_dir, exist_ok=True)

    o3d.io.write_point_cloud(os.path.join(save_dir, filename + '_gt.ply'), pcd)
    np.save(os.path.join(save_dir, filename + '_gt.npy'), vertices)


def process_folder(folder_path, save_path):
    for root, dirs, files in os.walk(folder_path):
        for file in tqdm(files):
            if file.endswith('.npy'):
                file_path = os.path.join(root, file)
                process_file(file_path, save_path)

def extract_number(file_path):
    # 正则表达式匹配文件名中最后一个下划线后的数字序列
    match = re.search(r'_(\d+)_\w+\.ply$', file_path)
    return int(match.group(1)) if match else None


def load_and_visualize_ply(folder_path):
    # 初始化一个列表来收集所有PLY文件的路径
    ply_files = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.ply'):
                full_path = os.path.join(root, file)
                ply_files.append(full_path)

    ply_files.sort()  # 对文件路径进行排序


    if not ply_files:
        print("No PLY files found in the specified directory.")
        return

    current_index = [0]  # 使用列表包装索引，以便在函数内部修改

    vis = o3d.visualization.VisualizerWithKeyCallback()
    vis.create_window()

    def load_ply(index):
        pcd = o3d.io.read_point_cloud(ply_files[index])
        print(f"Current PLY file: {ply_files[index]}")
        vis.clear_geometries()
        vis.add_geometry(pcd)

    def show_current_ply():
        load_ply(current_index[0])

    def next_ply(vis):
        current_index[0] = (current_index[0] + 1) % len(ply_files)
        show_current_ply()

    def prev_ply(vis):
        current_index[0] = (current_index[0] - 1) % len(ply_files)
        show_current_ply()

    vis.register_key_callback(262, next_ply)  # Right arrow key
    vis.register_key_callback(263, prev_ply)  # Left arrow key

    show_current_ply()  # 初始化显示第一个点云

    vis.run()
    vis.destroy_window()

if __name__ == '__main__':

    folder_path = '/home/<USER>/scenes/3FO3P2JN3PUS/无人场景/12cm/occupancy/data'
    save_path = '/tmp/pycharm_project_443/visual_dir/a'

    # import torch
    # process_folder(folder_path, save_path)


    vis_dir = '/raid/a'
    load_and_visualize_ply(vis_dir)

