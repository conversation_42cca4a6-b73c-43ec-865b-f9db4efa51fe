import torch
import torch.nn as nn
from copy import deepcopy
from torch import Tensor
from typing import List, Optional, Tuple
from mmyolo.models.layers import ImplicitA, ImplicitM
from mmyolo.models import RepVGGBlock
from mmyolo.models.dense_heads import (<PERSON><PERSON><PERSON><PERSON>Head, RTMDetHead, <PERSON><PERSON>Ov<PERSON><PERSON>ead,
                                       YOLOv7Head, Y<PERSON>Ov<PERSON><PERSON>ead, YOLOXHead, YOLOv8HeadModule)
from projects.easydeploy.model.backend import M<PERSON><PERSON>OL<PERSON>Backend
from projects.easydeploy.backbone import DeployFocus, GConvFocus, NcnnFocus
from mmdet.models.layers import ChannelAttention
from mmdet.models.backbones.csp_darknet import Focus
class MutitaskDeployModel(nn.Module):
    transpose = False
    def __init__(self,
                 baseModel: nn.Module,
                 backend: MMYOLOBackend):
        super().__init__()
        self.baseModel = baseModel
        self.backend = backend
        self.__switch_deploy()


    @staticmethod
    def forward_single(x: Tensor, convs: nn.Module) -> Tuple[Tensor]:
        if isinstance(convs, nn.Sequential) and any(
                type(m) in (ImplicitA, ImplicitM) for m in convs):
            a, c, m = convs
            aw = a.implicit.clone()
            mw = m.implicit.clone()
            c = deepcopy(c)
            nw, cw, _, _ = c.weight.shape
            na, ca, _, _ = aw.shape
            nm, cm, _, _ = mw.shape
            c.bias = nn.Parameter(c.bias + (
                c.weight.reshape(nw, cw) @ aw.reshape(ca, na)).squeeze(1))
            c.bias = nn.Parameter(c.bias * mw.reshape(cm))
            c.weight = nn.Parameter(c.weight * mw.transpose(0, 1))
            convs = c
        feat = convs(x)
        return (feat, )

    def __switch_deploy(self):
        if self.backend in (MMYOLOBackend.HORIZONX3, MMYOLOBackend.NCNN,
                            MMYOLOBackend.TORCHSCRIPT):
            self.transpose = True
        for layer in self.baseModel.modules():
            if type(layer) == YOLOv5Head or type(layer) == YOLOv7Head:
                layer.head_module.forward_single = self.forward_single
            elif type(layer) == PPYOLOEHead or type(layer) == YOLOv8Head:
                layer.head_module.reg_max = 0
            if isinstance(layer, RepVGGBlock):
                layer.switch_to_deploy()
            elif isinstance(layer, ChannelAttention):
                layer.global_avgpool.forward = self.forward_gvp
            elif isinstance(layer, Focus):
                # onnxruntime openvino tensorrt8 tensorrt7
                if self.backend in (MMYOLOBackend.ONNXRUNTIME,
                                    MMYOLOBackend.OPENVINO,
                                    MMYOLOBackend.TENSORRT8,
                                    MMYOLOBackend.TENSORRT7):
                    self.baseModel.backbone.stem = DeployFocus(layer)
                # ncnn
                elif self.backend == MMYOLOBackend.NCNN:
                    self.baseModel.backbone.stem = NcnnFocus(layer)
                # switch focus to group conv
                else:
                    self.baseModel.backbone.stem = GConvFocus(layer)
    def forward(self, inputs: Tensor):
        outputs = self.baseModel(inputs)
        model_outputs = []
        if self.transpose:
            for feats in zip(*outputs):
                if self.backend in (MMYOLOBackend.NCNN,
                                    MMYOLOBackend.TORCHSCRIPT):
                    model_outputs.append(
                        torch.cat(
                            [feat.permute(0, 2, 3, 1) for feat in feats],
                            -1))
                else:
                    model_outputs.append(torch.cat(feats, 1).permute(0, 2, 3, 1))
        else:
            for out in outputs:
                for feats in zip(*out):
                    model_outputs.append(torch.cat(feats, 1))
        return tuple(model_outputs)