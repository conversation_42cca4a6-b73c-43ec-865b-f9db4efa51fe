import os
import sys
import torch
import argparse
import cv2
from mmengine.config import Config, DictAction
from mmdet.registry import MODELS
from horizon_plugin_pytorch.utils.onnx_helper import export_to_onnx
from tqdm import tqdm
import numpy as np


ROOT = str(os.getcwd())
if ROOT not in sys.path:
    sys.path.append(ROOT)


def find_tensors_on_different_devices(model):
    device = None
    for name, param in model.named_parameters():
        if device is None:
            device = param.device
        elif device != param.device:
            print(f"Parameter {name} is on {param.device}, but others are on {device}")
            return
    print("All parameters are on the same device.")

def main(args):
    cfg = Config.fromfile(args.config)
    # 此处由于export onnx时会报数据在cpu和cuda不一致问题
    # 所以将model和输入放至cpu进行导出onnx
    model = MODELS.build(cfg['depoly_model']).eval()

    if args.checkpoint_path is not None:
        path_checkpoint = args.checkpoint_path
        checkpoint = torch.load(path_checkpoint)
        model.load_state_dict(checkpoint['state_dict'], strict=False)
        print("Loaded checkpoint111")

        # eval_results_all=[]
    # for img in tqdm(sorted(os.listdir('data/kjl_processed_data/scenes/3FO3OXSKLRC6/无人场景/12cm/image_02/data'))):
    #     # print(img)

    #     img1 = os.path.join('data/kjl_processed_data/scenes/3FO3OXSKLRC6/无人场景/12cm/image_02/data',img)
    #     img2 = img1.replace('image_02','image_03').replace('camera2','camera4')
    #     gt=img1.replace('image_02','occupancy').replace('_rgb.jpg','.npy')
    #     gt_occ=np.flip(np.load(gt).reshape(80,4,80).transpose(0, 2, 1).astype(np.float32), 2)

    #     gt_occ[gt_occ==2]=0
    #     gt_occ_new =  torch.from_numpy(gt_occ.copy()).unsqueeze(0).cuda()

    #     # save_name = img1
    #     img1 = cv2.imread(img1).astype(np.float32)
    #     img2 = cv2.imread(img2).astype(np.float32)

    #     img1 = cv2.resize(img1, (512,384))
    #     img2 = cv2.resize(img2, (512,384))

    #     # 对img1 进行三个通道均值方差归一化
    #     mean_value = np.array([103.530, 116.280, 123.675])
    #     mean_value = np.float64(mean_value.reshape(1, -1))
    #     cv2.subtract(img1, mean_value, img1) # inplace
    #     cv2.subtract(img2, mean_value, img2) # inplace

    #     img1 =  torch.from_numpy(np.expand_dims(img1.astype(np.float32), axis=0).transpose(0,3,1,2))
    #     img2 = torch.from_numpy(np.expand_dims(img2.astype(np.float32), axis=0).transpose(0,3,1,2))
    #     img_meta={}

    #     ouput=model(img1,img2,img_meta)
    #     # np.save('test_onnx/'+img.replace('_rgb.jpg','.npy'),ouput.detach().cpu().numpy())    

    #     # ouput=torch.from_numpy(np.load('test/'+img.replace('_rgb.jpg','.npy')))
    #     pred=torch.sigmoid(ouput.squeeze(0).cuda())

    #     img_metas={}
    #     img_metas['pc_range']=[-2, 0, -0.05, 2, 4.0, 0.15]
    #     img_metas['occ_size'] = [80, 80,4]
    #     # generate_output(pred.unsqueeze(0), gt_occ_new,img_metas,None)

    #     eval_results = evaluation_reconstruction_occ3d_no_mask(pred, gt_occ_new, img_metas)
    #     eval_results_all.append(eval_results)

    #     # dir="./test/"+img.replace(".jpg",".npy")
    #     # np.save(dir,ouput.detach().cpu().numpy())
    #     # print(1)

    # results_dict = {}

    # results = np.stack(eval_results_all, axis=0)
    # results[np.isnan(results)] = 0
    # results = results.mean(0).squeeze()
    # # print('results: ', results)
    # results_dict={'Acc':results[0],
    #                 'Comp':results[1],
    #                 'CD':results[2],
    #                 'Prec':results[3],
    #                 'Recall':results[4],
    #                 'F-score':results[5],'Iou':results[6],
    #                 }   

    # print(results_dict)

    dummy_input_img1 = torch.randn((1, 3, 384, 512))
    dummy_input_img2 = torch.randn((1, 3, 384, 512))

    find_tensors_on_different_devices(model)
    onnx_save_name = os.path.join(
        'projects/SurroundOcc/depoly/',
        args.onnx_name + '.onnx'
    )
    # meta={}
    # out=model(dummy_input_img1,dummy_input_img1,meta)
    with torch.no_grad():
        # torch.onnx.export(model,
        #     (dummy_input_img1, dummy_input_img2),
        #     onnx_save_name,
        #     input_names=['input_view1', 'input_view2'], opset_version=11)
        export_to_onnx(
            model,
            (dummy_input_img1, dummy_input_img2),
            onnx_save_name,
            input_names=['input_view1', 'input_view2'],
        )

    if args.onnxsimplyfy:  # 简化版本的onnx
        from onnxsim import simplify
        import onnx
        onnx_model = onnx.load(onnx_save_name)  # load onnx model
        model_simp, check = simplify(onnx_model)
        assert check, "Simplified ONNX model could not be validated"
        onnx.save(model_simp, onnx_save_name.replace('.onnx', '_sim.onnx'))


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('config', help='train config file path')
    parser.add_argument('--onnx_name', default='test', help='train config file path')
    parser.add_argument('--onnxsimplyfy', default=False, help='train config file path')
    parser.add_argument('--checkpoint_path', default=None, help='trained model parameters')
    args = parser.parse_args()
    main(args)
