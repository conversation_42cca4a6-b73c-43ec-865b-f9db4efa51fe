import os
import sys
import torch
import argparse
import numpy as np
from tqdm import tqdm
from mmengine.config import Config, DictAction
from mmdet.registry import MODELS
from horizon_plugin_pytorch.utils.onnx_helper import export_to_onnx
import cv2
import chamfer
from horizon_tc_ui import HB_ONNXRuntime
from tqdm import tqdm

import concurrent.futures
import threading



ROOT = str(os.getcwd())
if ROOT not in sys.path:
    sys.path.append(ROOT)

pc_range = [-2, 0, -0.05, 2, 4, 0.15]
occ_size = [80, 80, 4]

def voxel_to_vertices_occ3d(voxel, mask_camera=None, thresh=0.5):



    x = torch.linspace(0, voxel.shape[0] - 1, voxel.shape[0])
    y = torch.linspace(0, voxel.shape[1] - 1, voxel.shape[1])
    z = torch.linspace(0, voxel.shape[2] - 1, voxel.shape[2])
    X, Y, Z = torch.meshgrid(x, y, z)
    vv = torch.stack([X, Y, Z], dim=-1).to(voxel.device)

    if mask_camera is None:
        vertices = vv[voxel > thresh]
    else:
        vertices = vv[mask_camera[0]*voxel > thresh]
    vertices[:, 0] = (vertices[:, 0] + 0.5) * (pc_range[3] - pc_range[0]) /  occ_size[0]  + pc_range[0]
    vertices[:, 1] = (vertices[:, 1] + 0.5) * (pc_range[4] - pc_range[1]) /  occ_size[1]  + pc_range[1]
    vertices[:, 2] = (vertices[:, 2] + 0.5) * (pc_range[5] - pc_range[2]) /  occ_size[2]  + pc_range[2]

    return vertices


def eval_3d(verts_pred, verts_trgt, threshold=0.05):
    d1, d2, idx1, idx2 = chamfer.forward(verts_pred.unsqueeze(0).type(torch.float),
                                         verts_trgt.unsqueeze(0).type(torch.float))
    dist1 = torch.sqrt(d1).cpu().numpy()
    dist2 = torch.sqrt(d2).cpu().numpy()

    cd = dist1.mean() + dist2.mean()
    precision = np.mean((dist1 < threshold).astype('float'))
    recal = np.mean((dist2 < threshold).astype('float'))
    fscore = 2 * precision * recal / (precision + recal + 1e-10)
    metrics = np.array([np.mean(dist1), np.mean(dist2), cd, precision, recal, fscore])
    return metrics


onnx_save_name = os.path.join(
    'projects/SurroundOcc/depoly/',
    'rgbd_test_int32' + '.onnx'
)

sess = HB_ONNXRuntime(model_file=onnx_save_name)
sess.set_dim_param(0, 0, '?')    
output_name = sess.output_names   



def process_image(img_path):
    img_preocessed_path = os.path.join(img_root,img_path)
    img_processed = np.load(img_preocessed_path)
    if not os.path.exists(img_preocessed_path.replace('img_processed','voxel_200')):
        return None

    voxel_data = np.load(img_preocessed_path.replace('img_processed','voxel_200'))
    coord_data = np.load(img_preocessed_path.replace('img_processed','coord_200')).astype(np.int32)
    num_data = np.load(img_preocessed_path.replace('img_processed','num_200'))

    out = sess.hb_session.run(output_name, {sess.input_names[0]: img_processed,
                                    sess.input_names[1]: voxel_data,
                                    sess.input_names[2]: coord_data,
                                    sess.input_names[3]: num_data})

    gt_occ_path = os.path.join(
        '/zhulei/proj/dataset/kjl/0325/scenes/3FO3OFAUA1GR/occ_rgbd',
        img_preocessed_path.split('/')[-1].split('_')[1],
        img_preocessed_path.split('/')[-1].replace('.npy','_occ.npy')
    )

    gt_occ = np.load(gt_occ_path)
    gt_occ=np.flip(gt_occ.reshape(80,4,80).transpose(0, 2, 1).astype(np.float32), 2)
    gt_occ[gt_occ==2]=0
    gt_occ =  torch.from_numpy(gt_occ.copy()).cuda()

    out = out[0]
    out = torch.from_numpy(out)
    pred=torch.sigmoid(out.squeeze(0).cuda())
    pred = pred[0]

    vertices_pred = voxel_to_vertices_occ3d(pred, None, thresh=0.25)
    vertices_gt = voxel_to_vertices_occ3d(gt_occ,  None, thresh=0.25)
    metrics = eval_3d(vertices_pred.type(torch.double), vertices_gt.type(torch.double), threshold=0.05)
    print(metrics)
    return metrics

def process_images(img_paths):
    eval_results_all=[]
    with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
        results = executor.map(process_image, img_paths)
        # 使用tqdm显示线程的执行进度
        with tqdm(total=len(img_paths)) as pbar:
            for result in results:
                if result is not None:
                    eval_results_all.append(result)
                pbar.update(1)



    results_dict = {}

    results = np.stack(eval_results_all, axis=0)
    results[np.isnan(results)] = 0
    result = results.mean(0)

    results_dict = {
        'Acc': result[0],
        'Comp': result[1],
        'cd': result[2],
        'precision':result[3],
        'recal':result[4],
        'fscore':result[5],
        'IoU': (result[3] * result[4]) / (result[3] + result[4] - result[3] * result[4] + 1e-10)
    }
    print(results_dict)

img_root = '/zhulei/proj/rgbd_offline_data/rgbd_offline_data/img_processed'
img_paths = sorted(os.listdir(img_root))

process_images(img_paths)



