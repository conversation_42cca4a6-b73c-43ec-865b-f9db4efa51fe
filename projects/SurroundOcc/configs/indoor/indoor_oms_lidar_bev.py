# Import Custom Modules
import math

custom_imports = dict(
    imports=[
        'projects.SurroundOcc.surroundocc.models',
        'projects.SurroundOcc.surroundocc.datasets',
    ],
    allow_failed_imports=False)

# runtime 配置
default_scope = 'mmdet'
# find_unused_parameters = True
work_dir = 'work_dirs/indoor_oms/oms_lidar_0715/lidar_bev/tof1_re'

randomness=dict(seed=777)

default_hooks = dict(
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=100),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(type='CheckpointHook', interval=1),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    visualization=dict(type='mmdet3d.Det3DVisualizationHook'))

env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=1),
    dist_cfg=dict(backend='nccl'),
)

vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(
    type='mmdet3d.Det3DLocalVisualizer',
    vis_backends=vis_backends,
    name='visualizer')
log_processor = dict(type='LogProcessor', window_size=50, by_epoch=True)

log_level = 'INFO'
load_from = '/home/<USER>/ecoaitoolkit/projects/SurroundOcc/depoly/0509_fusion_bev_epc24.pth'
resume = False

# schedule
train_cfg = dict(type='EpochBasedTrainLoop', max_epochs=12, val_interval=1)
val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')

# optimizer
optim_wrapper = dict(
    # 此处可配置 为  AmpOptimWrapper 以便混合精度训练
    # type='OptimWrapper',
    type='AmpOptimWrapper',
    clip_grad=dict(
        max_norm=5, norm_type=2
    ),
    optimizer=dict(type='AdamW',
                   lr=1e-4,
                   weight_decay=1e-2),
    # paramwise_cfg=dict(
    #     custom_keys={
    #         'img_backbone': dict(lr_mult=0.1),
    #     }),
)

# learning rate
# 此处与以前 需要两处配置学习率
param_scheduler = [
    # 预热
    dict(
        type='LinearLR',
        start_factor=1.0 / 3,
        begin=0,
        end=500,
        by_epoch=False
    ),
    # 主学习率设置
    dict(
        type='CosineAnnealingLR',
        by_epoch=True
    )]

# Default setting for scaling LR automatically
#   - `enable` means enable scaling LR automatically
#       or not by default.
#   - `base_batch_size` = (8 GPUs) x (2 samples per GPU).
auto_scale_lr = dict(enable=False, base_batch_size=4)

# dataset setting
dataset_type = 'EcoAi.KujialeFusionDataset'
data_root = '/home/<USER>/indoor/oms_lidar'

img_norm_cfg = dict(
    mean=[103.530, 116.280, 123.675], std=[1.0, 1.0, 1.0], to_rgb=False)

voxel_size = [0.05, 0.05, 0.075]
point_cloud_range = [-2.0, 0., -0.02, 2.0, 4., 0.055]
occ_size = [80, 80, 1]  # x y z
use_semantic = True

keys = ('img', 'points')
meta_keys = ('lidar2img', 'img_shape', 'pc_range', 'occ_size', 'gt_occ', 'filename')
class_names = ['drivable_area', 'stop_line', 'unknow']

train_pipeline = [
    dict(type='LoadMultiViewImageFromFiles_OCC', to_float32=True),
    dict(type='LoadBEVSegmentationECO', xbound=[0.0, 4.0, 0.05], ybound=[-2.0, 2.0, 0.05], classes=class_names),
    dict(type='LoadPointsFromFileSingle', coord_type='LIDAR', load_dim=3, use_dim=3, reduce_beams=32, ),
    dict(type='PointsRangeFilter', point_cloud_range=point_cloud_range),
    # dict(type='PointShuffle'),
    dict(type='PhotoMetricDistortionMultiViewImage'),
    dict(type='ScaleImageMultiViewImage', scales=[384, 512]),
    dict(type='NormalizeMultiviewImage', **img_norm_cfg),
    dict(type='mmdet3d.Pack3DDetInputs', keys=keys, meta_keys=meta_keys)
]

val_pipeline = [
    dict(type='LoadMultiViewImageFromFiles_OCC', to_float32=True),
    dict(type='LoadBEVSegmentationECO', xbound=[0.0, 4.0, 0.05], ybound=[-2.0, 2.0, 0.05], classes=class_names),
    dict(type='LoadPointsFromFileSingle', coord_type='LIDAR', load_dim=3, use_dim=3, reduce_beams=32, ),
    dict(type='PointsRangeFilter', point_cloud_range=point_cloud_range),
    # dict(type='PointShuffle'),
    dict(type='ScaleImageMultiViewImage', scales=[384, 512]),
    dict(type='NormalizeMultiviewImage', **img_norm_cfg),
    dict(type='mmdet3d.Pack3DDetInputs', keys=keys, meta_keys=meta_keys)
]
test_pipeline = val_pipeline

train_dataloader = dict(
    batch_size=16,
    num_workers=8,
    persistent_workers=True,
    drop_last=True,  # 是否丢弃最后一个 batch
    sampler=dict(
        type='DefaultSampler', shuffle=True),
    pin_memory=True,
    prefetch_factor=8,
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        ann_file='pkl/indoor_oms_lidar_0718_train.pkl',
        pipeline=train_pipeline,
        test_mode=False,
        occ_size=occ_size,
        pc_range=point_cloud_range,
        use_semantic=use_semantic,
        classes=class_names,
    )
)

val_dataloader = dict(
    batch_size=16,
    num_workers=8,
    persistent_workers=True,
    drop_last=True,
    sampler=dict(
        type='DefaultSampler', shuffle=False),
    # val dataset
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        ann_file='pkl/indoor_oms_lidar_0718_val.pkl',
        pipeline=val_pipeline,
        occ_size=occ_size,
        pc_range=point_cloud_range,
        use_semantic=use_semantic,
        classes=class_names,
    )
)

test_dataloader = val_dataloader

val_evaluator = dict(
    type='BEVMetric',
    classes=class_names,
    use_semantic=use_semantic,
    eval_patch='fn',
    distance_ranges=['0-1m', '1~2m', '2~3m', '3~4m'],
    # eval_patch='ud',
    # distance_ranges = ['0-5cm', '5~10cm', '10~15cm', '15~20cm']
    eval_metrics3d=False,
)

test_evaluator = val_evaluator


def get_grid_quant_scale(grid_shape, view_shape):
    max_coord = max(*grid_shape, *view_shape)
    coord_bit_num = math.ceil(math.log(max_coord + 1, 2))
    coord_shift = 15 - coord_bit_num
    coord_shift = max(min(coord_shift, 8), 0)
    grid_quant_scale = 1.0 / (1 << coord_shift)
    return grid_quant_scale


bn_kwargs = dict(eps=2e-5, momentum=0.1)

numC_Trans = 128

data_shape = (3, 384, 512)
lss_depth = 120
lss_num_points = 2
lss_grid_size = (80, 80)
lss_z_range = (-0.02, 0.055)
lss_bev_size = [point_cloud_range[0], point_cloud_range[3], point_cloud_range[1], point_cloud_range[4], 0.05]

view_shape = [data_shape[1] / 16, data_shape[2] / 16]
depthview_shape = [6 * lss_depth, view_shape[0] * view_shape[1]]

featview_shape = [view_shape[0] * 6, view_shape[1]]
grid_quant_scale = get_grid_quant_scale(lss_grid_size, featview_shape)
depth_quant_scale = get_grid_quant_scale(lss_grid_size, depthview_shape)

model = dict(
    type='EcoBEVFusion',
    use_grid_mask=True,
    is_vis=False,
    lambda_dice=1,

    norm_range=point_cloud_range,
    lidar_config=dict(
        max_num_points=10,
        point_cloud_range=point_cloud_range,
        voxel_size=voxel_size,
        max_voxels=[90000, 120000],
    ),

    # in necks
    reader=dict(
        type='PillarFeatureNet_ld',
        num_input_features=3,  # 这个对应点云维度
        num_filters=tuple([64]),
        with_distance=False,
        pool_size=tuple([10, 1]),
        voxel_size=voxel_size,
        pc_range=point_cloud_range,
        bn_kwargs=None,
        quantize=False,
        use_4dim=True,
        use_conv=True,
        hw_reverse=True
    ),

    # in necks
    scatter=dict(
        type='PointPillarScatter_dpx',
        num_input_features=64,
        use_horizon_pillar_scatter=True,
        quantize=True
    ),

    img_backbone=dict(
        type="efficientnet",
        bn_kwargs=bn_kwargs,
        model_type="b0",
        num_classes=1000,
        include_top=False,
        activation="relu",
        use_se_block=False,
    ),

    img_neck=dict(
        type="FastSCNNNeck",
        in_channels=[112, 320],
        feat_channels=[64, 64],
        indexes=[-2, -1],
        bn_kwargs=bn_kwargs,
        scale_factor=2,
    ),

    view_transformer=dict(
        type="LSSTransformer_Fusion",
        in_channels=64,
        feat_channels=64,
        z_range=lss_z_range,
        depth=lss_depth,
        num_points=lss_num_points,
        bev_size=lss_bev_size,
        grid_size=lss_grid_size,
        num_views=1,
        grid_quant_scale=grid_quant_scale,
        depth_grid_quant_scale=depth_quant_scale,
    ),

    bev_encoder=dict(
        type='BEVEncoder2D',
        custom_resnet_params=dict(
            numC_input=numC_Trans,
            num_channels=[numC_Trans * 2, numC_Trans * 4, numC_Trans * 8]
        ),
        fpn_lss_params=dict(
            in_channels=numC_Trans * 8 + numC_Trans * 2,
            out_channels=256
        )
    ),

    bev_decoder=dict(
        type='FlashOcc',
        in_dim=256,
        out_dim=256,
        Dz=1,
        num_classes=3,
    ),
)

depoly_model = dict(
    type='EcoBEVFusionDepoly',
    use_grid_mask=False,

    lidar_config=dict(
        max_num_points=10,
        point_cloud_range=point_cloud_range,
        voxel_size=voxel_size,
        max_voxels=[90000, 120000],
    ),

    reader=dict(
        type='PillarFeatureNet_ld',
        num_input_features=3,  # 这个对应点云维度
        num_filters=tuple([64]),
        with_distance=False,
        pool_size=tuple([10, 1]),
        voxel_size=voxel_size,
        pc_range=point_cloud_range,
        bn_kwargs=None,
        quantize=False,
        use_4dim=True,
        use_conv=True,
        hw_reverse=True
    ),

    scatter=dict(
        type='PointPillarScatter_dpx',
        num_input_features=64,
        use_horizon_pillar_scatter=True,
        quantize=True
    ),

    img_backbone=dict(
        type='EfficientNetB0',
    ),

    img_neck=dict(
        type="FastSCNNNeck",
        in_channels=[112, 320],
        feat_channels=[64, 64],
        indexes=[-2, -1],
        bn_kwargs=bn_kwargs,
        scale_factor=2,
    ),

    view_transformer=dict(
        type="LSSTransformer_Fusion",
        in_channels=64,
        feat_channels=64,
        z_range=lss_z_range,
        depth=lss_depth,
        num_points=lss_num_points,
        bev_size=lss_bev_size,
        grid_size=lss_grid_size,
        num_views=1,
        grid_quant_scale=grid_quant_scale,
        depth_grid_quant_scale=depth_quant_scale,
        depoly=True
    ),

    bev_encoder=dict(
        type='BEVEncoder2D',
        custom_resnet_params=dict(
            numC_input=numC_Trans,
            num_channels=[numC_Trans * 2, numC_Trans * 4, numC_Trans * 8]
        ),
        fpn_lss_params=dict(
            in_channels=numC_Trans * 8 + numC_Trans * 2,
            out_channels=256
        )
    ),

    bev_decoder=dict(
        type='FlashOcc',
        in_dim=256,
        out_dim=256,
        Dz=1,
        num_classes=3,
    ),
)
