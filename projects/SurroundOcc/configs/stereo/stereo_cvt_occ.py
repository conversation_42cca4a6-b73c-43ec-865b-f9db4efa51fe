# Import Custom Modules
import math

custom_imports = dict(
    imports=[
        # 'projects.Surround_occ.surroundocc.models.structures.surroundocc_v2',
        'projects.SurroundOcc.surroundocc.models',
        'projects.SurroundOcc.surroundocc.datasets',
    ],
    allow_failed_imports=False)

# runtime 配置
default_scope = 'mmdet'
find_unused_parameters = True
work_dir = 'work_dirs/stereo_bev_0428/path_metrics'

default_hooks = dict(
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=100),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(type='CheckpointHook', interval=1),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    visualization=dict(type='mmdet3d.Det3DVisualizationHook'))

env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=1),
    dist_cfg=dict(backend='nccl'),
)

vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(
    type='mmdet3d.Det3DLocalVisualizer',
    vis_backends=vis_backends,
    name='visualizer')
log_processor = dict(type='LogProcessor', window_size=50, by_epoch=True)

log_level = 'INFO'
load_from = None
resume = False

# schedule
train_cfg = dict(type='EpochBasedTrainLoop', max_epochs=24, val_interval=1)
val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')

# optimizer
optim_wrapper = dict(
    # 此处可配置 为  AmpOptimWrapper 以便混合精度训练
    # type='OptimWrapper',
    type='AmpOptimWrapper',
    clip_grad=dict(
        max_norm=5, norm_type=2
    ),
    optimizer=dict(type='AdamW',
                   lr=1e-4,
                   weight_decay=1e-2),
    # paramwise_cfg=dict(
    #     custom_keys={
    #         'img_backbone': dict(lr_mult=0.1),
    #     }),
)

# learning rate
# 此处与以前 需要两处配置学习率
param_scheduler = [
    # 预热
   dict(
       type='LinearLR',
       start_factor=1.0 / 3,
       begin=0,
       end=500,
       by_epoch=False
    ),
    # 主学习率设置
    dict(
       type='CosineAnnealingLR',
       by_epoch=True
   )]

# Default setting for scaling LR automatically
#   - `enable` means enable scaling LR automatically
#       or not by default.
#   - `base_batch_size` = (8 GPUs) x (2 samples per GPU).
auto_scale_lr = dict(enable=False, base_batch_size=4)

# dataset setting
dataset_type = 'EcoAi.KujialeStereoDataset_LMDB'
data_root = '/home/<USER>/kws_deliver_0325'

img_norm_cfg = dict(
    mean=[103.530, 116.280, 123.675], std=[1.0, 1.0, 1.0], to_rgb=False)

point_cloud_range =[-2, 0, -0.05, 2, 4, 0.15] 
bev_size = [point_cloud_range[0], point_cloud_range[3], point_cloud_range[1], point_cloud_range[4], 0.05]
occ_size = [80, 80, 4]  # x y z
use_semantic = True

keys = ('img', 'points')
meta_keys = ('lidar2img', 'img_shape', 'pc_range', 'occ_size', 'gt_occ', 'filename','cam_intrinsic','lidar2cam')
class_names = ['no_occupancy', 'occupancy']


train_pipeline = [
    dict(type='LoadMultiViewImageFromFiles_OCC', to_float32=True),
    dict(type='LoadKJLOccupancy', use_semantic=use_semantic),
    dict(type='PhotoMetricDistortionMultiViewImage'),
    dict(type='ScaleImageMultiViewImage', scales=[384, 512]),
    dict(type='NormalizeMultiviewImage', **img_norm_cfg),
    dict(type='mmdet3d.Pack3DDetInputs', keys=keys, meta_keys=meta_keys)
]

val_pipeline = [
    dict(type='LoadMultiViewImageFromFiles_OCC', to_float32=True),
    dict(type='LoadKJLOccupancy', use_semantic=use_semantic),
    dict(type='ScaleImageMultiViewImage', scales=[384, 512]),
    dict(type='NormalizeMultiviewImage', **img_norm_cfg),
    dict(type='mmdet3d.Pack3DDetInputs', keys=keys, meta_keys=meta_keys)
]
test_pipeline = val_pipeline

train_dataloader = dict(
    batch_size=8,
    num_workers=8,
    persistent_workers=True,
    drop_last=False,  # 是否丢弃最后一个 batch
    sampler=dict(
        type='DefaultSampler', shuffle=True),
    pin_memory=True,
    prefetch_factor=8,
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        ann_file='lmdb/kujiale_stereo_0325n_train',
        pipeline=train_pipeline,
        test_mode=False,
        occ_size=occ_size,
        pc_range=point_cloud_range,
        use_semantic=use_semantic,
        classes=class_names,
    )
)

val_dataloader = dict(
    batch_size=4,
    num_workers=4,
    persistent_workers=True,
    drop_last=False,
    sampler=dict(
        type='DefaultSampler', shuffle=False),
    # val dataset
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        ann_file='lmdb/kujiale_stereo_0325n_val',
        pipeline=val_pipeline,
        occ_size=occ_size,
        pc_range=point_cloud_range,
        use_semantic=use_semantic,
        classes=class_names,
    )
)

test_dataloader = val_dataloader

val_evaluator = dict(
    type='BEVMetric',
    classes=class_names,
    use_semantic=use_semantic,
    eval_patch='fn',
    distance_ranges=['0-1m', '1~2m', '2~3m', '3~4m'],
    # eval_patch='ud',
    # distance_ranges = ['0-5cm', '5~10cm', '10~15cm', '15~20cm']
    eval_metrics3d=False,
)

test_evaluator = val_evaluator


def get_grid_quant_scale(grid_shape, view_shape):
    max_coord = max(*grid_shape, *view_shape)
    coord_bit_num = math.ceil(math.log(max_coord + 1, 2))
    coord_shift = 15 - coord_bit_num
    coord_shift = max(min(coord_shift, 8), 0)
    grid_quant_scale = 1.0 / (1 << coord_shift)
    return grid_quant_scale


bn_kwargs = dict(eps=2e-5, momentum=0.1)

numC_Trans = 64
dim_last = 64
data_shape = (3, 384, 512)

model = dict(
    type='EcoOcc_transformVision',
    use_grid_mask=True,
    lambda_dice=0.1,
    is_vis=False,
    index=[4],  # backbone的4特征

    img_backbone=dict(
        type="efficientnet",
        bn_kwargs=bn_kwargs,
        model_type="b0",
        num_classes=1000,
        include_top=False,
        activation="relu",
        use_se_block=False,
    ),

    view_transformer=dict(
        type="CVT",
        dim=64,
        depoly=False,
        output_shapes=[
            # [1,24,int(data_shape[1]/4),int(data_shape[2]/4)],
            # [1,112,int(data_shape[1]/16),int(data_shape[2]/16)]],
            [1, 320, int(data_shape[1] / 32), int(data_shape[2] / 32)]],

        bev_embedding=dict(
            sigma=1.0,
            bev_height=80,
            bev_width=80,
            h_meters=4.0,
            w_meters=4.0,
            offset=0.0,
            decoder_blocks=[1, 1, 1],
        ),
        cross_view=dict(
            heads=4,
            dim_head=32,
            qkv_bias=True,
            skip=True,
            no_image_features=False,
            image_height=480,
            image_width=640,
        ),
        decoder=dict(
            dim=dim_last,
            blocks=[dim_last, dim_last, dim_last],
            residual=True,
            factor=1,
        ),
        # decoder=dict(
        # dim=dim_last,
        # blocks=[dim_last, dim_last, dim_last],
        # residual=True,
        # factor=2,
        # ),
        dim_last=numC_Trans,

    ),

    bev_encoder=dict(
        type='BEVEncoder2D',
        custom_resnet_params=dict(
            numC_input=numC_Trans,
            num_channels=[numC_Trans * 2, numC_Trans * 4, numC_Trans * 8]
        ),
        fpn_lss_params=dict(
            in_channels=numC_Trans * 8 + numC_Trans * 2,
            out_channels=256
        )
    ),

    bev_decoder=dict(
        type='FlashOcc',
        in_dim=256,
        out_dim=256,
        Dz=4,
        num_classes=1,
    ),
)

depoly_model = dict(
    type='EcoOcc_transformVisionDeploy',
    index=[4],  # backbone的1和3个特征
    img_backbone=dict(
        type="efficientnet",
        bn_kwargs=bn_kwargs,
        model_type="b0",
        num_classes=1000,
        include_top=False,
        activation="relu",
        use_se_block=False,
    ),

    view_transformer=dict(
        type="CVT",
        dim=64,

        depoly=True,
        output_shapes=[
            # [1,112,int(data_shape[1]/16),int(data_shape[2]/16)],
            [1, 320, int(data_shape[1] / 32), int(data_shape[2] / 32)]],

        bev_embedding=dict(
            sigma=1.0,
            bev_height=80,
            bev_width=80,
            h_meters=4.0,
            w_meters=4.0,
            offset=0.0,
            decoder_blocks=[1, 1, 1],
        ),
        cross_view=dict(
            heads=4,
            dim_head=32,
            qkv_bias=True,
            skip=True,
            no_image_features=False,
            image_height=480,
            image_width=640,
        ),
        decoder=dict(
            dim=dim_last,
            blocks=[dim_last, dim_last, dim_last],
            residual=True,
            factor=1,
        ),
        dim_last=numC_Trans,

    ),

    bev_encoder=dict(
        type='BEVEncoder2D',
        custom_resnet_params=dict(
            numC_input=numC_Trans,
            num_channels=[numC_Trans * 2, numC_Trans * 4, numC_Trans * 8]
        ),
        fpn_lss_params=dict(
            in_channels=numC_Trans * 8 + numC_Trans * 2,
            out_channels=256
        )
    ),

    bev_decoder=dict(
        type='FlashOcc',
        in_dim=256,
        out_dim=256,
        Dz=4,
        num_classes=1,
    ),
)
