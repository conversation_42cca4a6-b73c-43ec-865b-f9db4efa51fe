# Import Custom Modules
import math

custom_imports = dict(
    imports=[
        'projects.SurroundOcc.surroundocc.models',
        'projects.SurroundOcc.surroundocc.datasets',
    ],
    allow_failed_imports=False)
# runtime 配置
default_scope = 'mmdet'
find_unused_parameters = True
work_dir = 'work_dirs/stereo/stereo-occ_effib0-old-0.1lovasz'

default_hooks = dict(
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=10),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(type='CheckpointHook', interval=1),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    visualization=dict(type='mmdet3d.Det3DVisualizationHook'))

env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=1),
    dist_cfg=dict(backend='nccl'),
)

vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(
    type='mmdet3d.Det3DLocalVisualizer',
    vis_backends=vis_backends,
    name='visualizer')
log_processor = dict(type='LogProcessor', window_size=50, by_epoch=True)

log_level = 'INFO'
load_from = None
resume = False

# schedule
train_cfg = dict(type='EpochBasedTrainLoop', max_epochs=24, val_interval=1)
val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')

# optimizer
optim_wrapper = dict(
    # 此处可配置 为  AmpOptimWrapper 以便混合精度训练
    # type='OptimWrapper',
    type='AmpOptimWrapper',
    clip_grad=dict(
        max_norm=5, norm_type=2
    ),
    optimizer=dict(type='AdamW',
                   lr=1e-4,
                   weight_decay=1e-2),
    # paramwise_cfg=dict(
    #     custom_keys={
    #         'img_backbone': dict(lr_mult=0.1),
    #     }),
)

# learning rate
# 此处与以前 需要两处配置学习率
param_scheduler = [
    # 预热
    dict(
        type='LinearLR',
        start_factor=1.0 / 3,
        begin=0,
        end=500,
        by_epoch=False
    ),
    # 主学习率设置
    dict(
        type='CosineAnnealingLR',
        by_epoch=True
    )]

# Default setting for scaling LR automatically
#   - `enable` means enable scaling LR automatically
#       or not by default.
#   - `base_batch_size` = (8 GPUs) x (2 samples per GPU).
auto_scale_lr = dict(enable=False, base_batch_size=4)

# dataset setting
dataset_type = 'EcoAi.KujialeStereoDataset'
data_root = '/home/<USER>/new_grass_scene'

img_norm_cfg = dict(
    mean=[103.530, 116.280, 123.675], std=[1.0, 1.0, 1.0], to_rgb=False)

point_cloud_range = [-5, 0, -2, 5, 10, 2]  # x y z
occ_size = [200, 200, 80]  # x y z
use_semantic = True

keys = ('img',)
meta_keys = ('lidar2img', 'img_shape', 'pc_range', 'occ_size', 'gt_occ')
class_names = ['no_occupancy', 'occupancy']

train_pipeline = [
    dict(type='LoadMultiViewImageFromFiles_OCC', to_float32=True),
    dict(type='LoadKJLOccupancy', use_semantic=use_semantic, fov_mask=True),
    dict(type='PhotoMetricDistortionMultiViewImage'),
    dict(type='ScaleImageMultiViewImage', scales=[384, 672]),
    dict(type='NormalizeMultiviewImage', **img_norm_cfg),
    dict(type='mmdet3d.Pack3DDetInputs', keys=keys, meta_keys=meta_keys)
]

val_pipeline = [
    dict(type='LoadMultiViewImageFromFiles_OCC', to_float32=True),
    dict(type='LoadKJLOccupancy', use_semantic=use_semantic, fov_mask=True),
    dict(type='ScaleImageMultiViewImage', scales=[384, 672]),
    dict(type='NormalizeMultiviewImage', **img_norm_cfg),
    dict(type='mmdet3d.Pack3DDetInputs', keys=keys, meta_keys=meta_keys)
]
test_pipeline = val_pipeline

train_dataloader = dict(
    batch_size=8,
    num_workers=8,
    persistent_workers=True,
    drop_last=False,  # 是否丢弃最后一个 batch
    sampler=dict(
        type='DefaultSampler', shuffle=True),
    pin_memory=True,
    prefetch_factor=8,
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        ann_file='pkl/gecaoji_gecaoji_train.pkl',
        pipeline=train_pipeline,
        test_mode=False,
        occ_size=occ_size,
        pc_range=point_cloud_range,
        use_semantic=use_semantic,
        classes=class_names,
    )
)

val_dataloader = dict(
    batch_size=8,
    num_workers=8,
    persistent_workers=True,
    drop_last=False,
    sampler=dict(
        type='DefaultSampler', shuffle=False),
    # val dataset
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        ann_file='pkl/gecaoji_gecaoji_val.pkl',
        pipeline=val_pipeline,
        occ_size=occ_size,
        pc_range=point_cloud_range,
        use_semantic=use_semantic,
        classes=class_names,
    )
)

test_dataloader = val_dataloader

val_evaluator = dict(
    type='BEVMetric',
    classes=class_names,
    use_semantic=use_semantic,
    eval_patch='fn',
    distance_ranges=['0-1m', '1~2m', '2~3m', '3~4m'],
    # eval_patch='ud',
    # distance_ranges = ['0-5cm', '5~10cm', '10~15cm', '15~20cm']
    eval_metrics3d=False,
)

test_evaluator = val_evaluator


def get_grid_quant_scale(grid_shape, view_shape):
    max_coord = max(*grid_shape, *view_shape)
    coord_bit_num = math.ceil(math.log(max_coord + 1, 2))
    coord_shift = 15 - coord_bit_num
    coord_shift = max(min(coord_shift, 8), 0)
    grid_quant_scale = 1.0 / (1 << coord_shift)
    return grid_quant_scale


bn_kwargs = dict(eps=2e-5, momentum=0.1)

numC_Trans = 64

data_shape = (3, 384, 672)
lss_depth = 300
lss_num_points = 8
lss_grid_size = (200, 200)
lss_z_range = (-2, 2)
lss_bev_size = [point_cloud_range[0], point_cloud_range[3], point_cloud_range[1], point_cloud_range[4], 0.05]

view_shape = [data_shape[1] / 16, data_shape[2] / 16]
depthview_shape = [6 * lss_depth, view_shape[0] * view_shape[1]]

featview_shape = [view_shape[0] * 6, view_shape[1]]
grid_quant_scale = get_grid_quant_scale(lss_grid_size, featview_shape)
depth_quant_scale = get_grid_quant_scale(lss_grid_size, depthview_shape)

model = dict(
    type='EcoOccVision',
    use_grid_mask=True,
    is_vis=False,
    lambda_dice=1,
    # old_efficientnet
    img_backbone=dict(
       type='EfficientNetB0',
    ),

     # img_backbone=dict(
     # type="efficientnet",
     # bn_kwargs=bn_kwargs,
     # model_type="b0",
     # num_classes=1000,
     # include_top=False,
     # activation="relu",
     # use_se_block=False,
     # ),

    img_neck=dict(
        type="FastSCNNNeck",
        in_channels=[112, 320],
        feat_channels=[64, 64],
        indexes=[-2, -1],
        bn_kwargs=bn_kwargs,
        scale_factor=2,
    ),

    # img_backbone=dict(
    #     type='ResNet',
    #     depth=18,
    #     num_stages=4,
    #     out_indices=(2, 3),
    #     frozen_stages=-1,
    #     norm_cfg=dict(type='BN', requires_grad=True),
    #     norm_eval=False,
    #     with_cp=False,
    #     style='pytorch',
    #     init_cfg=dict(
    #         type='Pretrained',
    #        checkpoint='projects/SurroundOcc/depoly/resnet18-f37072fd.pth')
    # ),
    # img_neck=dict(
    #     type='CustomFPN',
    #     in_channels=[256, 512],
    #     out_channels=64,
    #     start_level=0,
    #     add_extra_convs='on_output',
    #     num_outs=1,
    #     relu_before_extra_convs=True,
    #     out_ids=[0]
    # ),

    view_transformer=dict(
        type="LSSTransformer",
        in_channels=64,
        feat_channels=64,
        z_range=lss_z_range,
        depth=lss_depth,
        num_points=lss_num_points,
        bev_size=lss_bev_size,
        grid_size=lss_grid_size,
        num_views=2,
        # grid_quant_scale=grid_quant_scale,
        # depth_grid_quant_scale=depth_quant_scale,
    ),

    bev_encoder=dict(
        type='BEVEncoder2D',
        custom_resnet_params=dict(
            numC_input=numC_Trans,
            num_channels=[numC_Trans * 2, numC_Trans * 4, numC_Trans * 8]
        ),
        fpn_lss_params=dict(
            in_channels=numC_Trans * 8 + numC_Trans * 2,
            out_channels=256
        )
    ),

    bev_decoder=dict(
        type='FlashOcc',
        in_dim=256,
        out_dim=256,
        Dz=80,
        num_classes=1,
    ),
)

depoly_model = dict(
    type='EcoOccVisionDepoly',
    use_grid_mask=False,

    # old_efficientnet
    img_backbone=dict(
        type='EfficientNetB0',
    ),
     # img_backbone=dict(
     # type="efficientnet",
     # bn_kwargs=bn_kwargs,
     # model_type="b0",
     # num_classes=1000,
     # include_top=False,
     # activation="relu",
     # use_se_block=False,
     # ),

    img_neck=dict(
        type="FastSCNNNeck",
        in_channels=[112, 320],
        feat_channels=[64, 64],
        indexes=[-2, -1],
        bn_kwargs=bn_kwargs,
        scale_factor=2,
    ),

    # img_backbone=dict(
    #     type='ResNet',
    #     depth=18,
    #     num_stages=4,
    #     out_indices=(2, 3),
    #     frozen_stages=-1,
    #     norm_cfg=dict(type='BN', requires_grad=True),
    #     norm_eval=False,
    #     with_cp=False,
    #     style='pytorch',
    #     #init_cfg=dict(
    #         #type='Pretrained',
    #         #checkpoint='projects/SurroundOcc/depoly/resnet18-f37072fd.pth')
    # ),
    # img_neck=dict(
    #     type='CustomFPN',
    #     in_channels=[256, 512],
    #     out_channels=64,
    #     start_level=0,
    #     add_extra_convs='on_output',
    #     num_outs=1,
    #     relu_before_extra_convs=True,
    #     out_ids=[0]
    # ),

    view_transformer=dict(

        type="LSSTransformer",
        depoly=True,
        in_channels=64,
        feat_channels=64,
        z_range=lss_z_range,
        depth=lss_depth,
        num_points=lss_num_points,
        bev_size=lss_bev_size,
        grid_size=lss_grid_size,
        num_views=2,
        # grid_quant_scale=grid_quant_scale,
        # depth_grid_quant_scale=depth_quant_scale,
    ),

    bev_encoder=dict(
        type='BEVEncoder2D',
        custom_resnet_params=dict(
            numC_input=numC_Trans,
            num_channels=[numC_Trans * 2, numC_Trans * 4, numC_Trans * 8]
        ),
        fpn_lss_params=dict(
            in_channels=numC_Trans * 8 + numC_Trans * 2,
            out_channels=256
        )
    ),

    bev_decoder=dict(
        type='FlashOcc',
        in_dim=256,
        out_dim=256,
        Dz=80,
        num_classes=1,
    ),
)
