import numpy as np
import os 


gt_new_path = '/home/<USER>/ecoaitoolkit/visual_dirs/kjl_jiexiang/kjlStereoOcc_0509_new/3FO3NTO3MURV/gt_new'
gt_new = os.listdir(gt_new_path)

for occ in gt_new:
    occ_path = os.path.join(gt_new_path, occ)
    occ = np.load(occ_path)

    lab_x, lab_z, lab_y = (80, 80, 4)
    occ = occ.reshape(lab_x, lab_y, lab_z).transpose(0, 2, 1).astype(np.float32)
    occ = np.flip(occ, 2)

    np.save(os.path.join(gt_new_path, occ_path).replace('gt_new', 'gt_new_trans'), occ)
