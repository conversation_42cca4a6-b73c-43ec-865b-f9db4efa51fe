# 基于mm series 搭建 Ecovacs Algorithm Toolkit
## BEV
### 1. 训练
- [x] 库家乐全流程
- [ ] 割草机全流程

### 2.导出onnx

```
python tools/onnx_export/bev_export_onnx.py \
<config.py> \
--onnx_name <prefix of onnx, default:test> \
--onnxsimplyfy <True or False, default:False>
``` 

### 3. issues
#### 在ecoaitoolkit目录下执行

- chamfer install
```
mkdir -p SurroundOcc/temp \
&& cd SurroundOcc/temp \
&& wget --content-disposition http://10.88.82.15/michael.zhu/sharedfiles/-/raw/master/chamfer_dist.tar.gz \
&& tar -zxvf chamfer_dist.tar.gz \
&& cd chamfer_dist \
&& python setup.py install --user \
&& cd ../../../../ 

```

- bev_pool_v2 $ voxel
```
cd SurroundOcc/surroundocc/models/ops \
&& rm -rf build \
&& rm -rf dist \
&& rm -rf bevocc.egg-info \
&& python setup.py develop \
&& rm -rf build \
&& rm -rf dist \
&& rm -rf bevocc.egg-info \
&& cd - 
```


## 酷家乐数据制作
### 1. 处理文件夹
- [ ] 移动camera文件夹，以及删除cameras_samping_a等子文件夹
- [ ] 虑除单通道camera及其对应的其他camera文件
- [ ] 创建scenes文件夹并将所有场景放入，创建meta_data文件夹声明训练场景和验证场景

```
python tools/data_create/data_proc.py
``` 

### 2. 创建rgbd的BEV和Occ标签

```
python tools/data_create/rgbd_gen_bev_lab.py #生成bev标签和tof点云输入
python tools/data_create/rgbd_gen_occ_lab.py #只生成occ标签
``` 

### 3. 创建双目的Occ标签

```
python tools/data_create/stereo_gen_occ_lab.py #
``` 

### 4. 可视化生成的 occ lab，查看是否正确
- [ ] 需要open3d显示，在本机环境运行而不是容器中

```
python tools/demo/showNpyPoints.py
``` 

### 5. 将rgbd和stero数据及其lab生成为pkl
- [ ] rgbd: camera0，camera3，camera5，points_tof_56, bev_rgbd, occ_rgbd
- [ ] stereo
  - left_image: camera0, camera2, camera5
  - right_image: camera1, camera2, camera6
  - occ_stereo


```
bash tools/gen_kujiale_pkl.sh
``` 

### 6. 将pkl转为LMDB

```
cd /home/<USER>/kws_delivers_0325/pkl
python tools/data_create/create_lmdb.py
``` 
