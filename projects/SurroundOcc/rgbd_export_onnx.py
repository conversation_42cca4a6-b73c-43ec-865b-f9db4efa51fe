import os
import sys
import torch
import argparse
import numpy as np
from tqdm import tqdm
from mmengine.config import Config, DictAction
from mmdet.registry import MODELS
from horizon_plugin_pytorch.utils.onnx_helper import export_to_onnx
import cv2
import chamfer
from horizon_tc_ui import HB_ONNXRuntime
from tqdm import tqdm

import gc

ROOT = str(os.getcwd())
if ROOT not in sys.path:
    sys.path.append(ROOT)

pc_range = [-2, 0, -0.05, 2, 4, 0.15]
occ_size = [80, 80, 4]


def voxel_to_vertices_occ3d(voxel, mask_camera=None, thresh=0.5):
    x = torch.linspace(0, voxel.shape[0] - 1, voxel.shape[0])
    y = torch.linspace(0, voxel.shape[1] - 1, voxel.shape[1])
    z = torch.linspace(0, voxel.shape[2] - 1, voxel.shape[2])
    X, Y, Z = torch.meshgrid(x, y, z)
    vv = torch.stack([X, Y, Z], dim=-1).to(voxel.device)

    if mask_camera is None:
        vertices = vv[voxel > thresh]
    else:
        vertices = vv[mask_camera[0] * voxel > thresh]
    vertices[:, 0] = (vertices[:, 0] + 0.5) * (pc_range[3] - pc_range[0]) / occ_size[0] + pc_range[0]
    vertices[:, 1] = (vertices[:, 1] + 0.5) * (pc_range[4] - pc_range[1]) / occ_size[1] + pc_range[1]
    vertices[:, 2] = (vertices[:, 2] + 0.5) * (pc_range[5] - pc_range[2]) / occ_size[2] + pc_range[2]

    return vertices


def eval_3d(verts_pred, verts_trgt, threshold=0.05):
    d1, d2, idx1, idx2 = chamfer.forward(verts_pred.unsqueeze(0).type(torch.float),
                                         verts_trgt.unsqueeze(0).type(torch.float))
    dist1 = torch.sqrt(d1).cpu().numpy()
    dist2 = torch.sqrt(d2).cpu().numpy()

    cd = dist1.mean() + dist2.mean()
    precision = np.mean((dist1 < threshold).astype('float'))
    recal = np.mean((dist2 < threshold).astype('float'))
    fscore = 2 * precision * recal / (precision + recal + 1e-10)
    metrics = np.array([np.mean(dist1), np.mean(dist2), cd, precision, recal, fscore])
    return metrics


def main(args):
    cfg = Config.fromfile(args.config)
    # 此处由于export onnx时会报数据在cpu和cuda不一致问题
    # 所以将model和输入放至cpu进行导出onnx
    model = MODELS.build(cfg['depoly_model']).eval()

    # checkpoint_path = '/home/<USER>/projects/SurroundOcc/depoly/0509_fusion_occ_epc24.pth'
    # checkpoint = torch.load(checkpoint_path)
    # model.load_state_dict(checkpoint['state_dict'], strict=True)
    # print('Lodding', 1111111111)

    input_ = {
        'img': torch.from_numpy(np.random.rand(1, 3, 448, 544).astype('float32')),
        'pts_feature': torch.from_numpy(np.random.rand(200, 10, 3).astype('float32')),
        'pts_coord': torch.from_numpy(np.random.randint(low=0, high=1, size=(200, 4)).astype("int32")),
        'pts_num': torch.from_numpy(np.random.randint(low=0, high=9, size=200).astype("float32"))
    }

    onnx_save_name = os.path.join(
        'projects/SurroundOcc/depoly/',
        '_fusion-B_occ' + '.onnx'
    )

    with torch.no_grad():
        export_to_onnx(
            model,
            (input_['img'], input_['pts_feature'], input_['pts_coord'], input_['pts_num']),
            onnx_save_name,
            input_names=['input_img', 'pts_feature', 'pts_coord', 'pts_num'],
        )

    exit()

    sess = HB_ONNXRuntime(model_file=onnx_save_name)
    sess.set_dim_param(0, 0, '?')
    output_name = sess.output_names

    eval_results_all = []
    img_root = '/zhulei/proj/rgbd_offline_data/rgbd_offline_data/img_processed'
    for img_path in tqdm(sorted(os.listdir(img_root))):
        # img_path = '3FO3OFAUA1GR_camera0_000001.npy'
        img_preocessed_path = os.path.join(img_root, img_path)
        img_processed = np.load(img_preocessed_path)
        if not os.path.exists(img_preocessed_path.replace('img_processed', 'voxel_200')):
            continue
        voxel_data = np.load(img_preocessed_path.replace('img_processed', 'voxel_200'))
        print(voxel_data.dtype)
        coord_data = np.load(img_preocessed_path.replace('img_processed', 'coord_200')).astype(np.int32)
        print(coord_data.dtype)
        num_data = np.load(img_preocessed_path.replace('img_processed', 'num_200'))
        print(num_data.dtype)

        with torch.no_grad():

            a = torch.from_numpy(img_processed)
            b = torch.from_numpy(voxel_data)
            c = torch.from_numpy(coord_data)
            d = torch.from_numpy(num_data)

            out1 = model(
                torch.from_numpy(img_processed),
                torch.from_numpy(voxel_data),
                torch.from_numpy(coord_data),
                torch.from_numpy(num_data)
            )

            # out2 = sess.hb_session.run(output_name, {sess.input_names[0]: img_processed.copy(),
            # sess.input_names[1]: voxel_data.copy(),
            # sess.input_names[2]: coord_data.copy(),
            # sess.input_names[3]: num_data.copy(),
            # }
            # )

        # img_processed = torch.from_numpy(img_processed).cuda()

        # 此处使用原图读取图片 并做图片预处理 转化为np.float32 不可少
        # img_ori = cv2.imread(img_preocessed_path.replace('img_processed','img_ori').replace('.npy','_rgb.jpg')).astype(np.float32)
        # img1 = cv2.resize(img_ori, (512,384),interpolation=cv2.INTER_LINEAR)
        # mean_value = np.array([103.530, 116.280, 123.675])
        # mean_value = np.float64(mean_value.reshape(1, -1))
        # cv2.subtract(img1, mean_value, img1) # inplace
        # img1 =  torch.from_numpy(np.expand_dims(img1.astype(np.float32), axis=0).transpose(0,3,1,2)).cuda()

        # voxel_data = torch.from_numpy(voxel_data).cuda()
        # coord_data = torch.from_numpy(coord_data).cuda()
        # num_data = torch.from_numpy(num_data).cuda()

        # out = model(img_processed,voxel_data,coord_data,num_data)

        gt_occ_path = os.path.join(
            '/zhulei/proj/dataset/kjl/0325/scenes/3FO3OFAUA1GR/occ_rgbd',
            img_preocessed_path.split('/')[-1].split('_')[1],
            img_preocessed_path.split('/')[-1].replace('.npy', '_occ.npy')
        )

        gt_occ = np.load(gt_occ_path)

        gt_occ = np.flip(gt_occ.reshape(80, 4, 80).transpose(0, 2, 1).astype(np.float32), 2)
        gt_occ[gt_occ == 2] = 0
        gt_occ = torch.from_numpy(gt_occ.copy()).cuda()

        out = out1[0]
        # out = torch.from_numpy(out)
        pred = torch.sigmoid(out.squeeze(0).cuda())
        # pred = pred[0]

        vertices_pred = voxel_to_vertices_occ3d(pred, None, thresh=0.25)  # set low thresh for class imbalance problem
        vertices_gt = voxel_to_vertices_occ3d(gt_occ, None, thresh=0.25)  # set low thresh for class imbalance problem
        metrics = eval_3d(vertices_pred.type(torch.double), vertices_gt.type(torch.double),
                          threshold=0.05)  # must convert to double, a bug in chamfer
        print(metrics)
        eval_results_all.append(metrics)

    results_dict = {}

    results = np.stack(eval_results_all, axis=0)
    results[np.isnan(results)] = 0
    result = results.mean(0)

    results_dict = {
        'Acc': result[0],
        'Comp': result[1],
        'cd': result[2],
        'precision': result[3],
        'recal': result[4],
        'fscore': result[5],
        'IoU': (result[3] * result[4]) / (result[3] + result[4] - result[3] * result[4] + 1e-10)
    }
    print(results_dict)

    if args.onnxsimplyfy:
        from onnxsim import simplify
        import onnx
        onnx_model = onnx.load(onnx_save_name)  # load onnx model
        model_simp, check = simplify(onnx_model)
        assert check, "Simplified ONNX model could not be validated"
        onnx.save(model_simp, onnx_save_name.replace('.onnx', '_sim.onnx'))


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('config', help='train config file path')
    parser.add_argument('--onnx_name', default='fusion', help='train config file path')
    parser.add_argument('--onnxsimplyfy', default=False, help='train config file path')
    parser.add_argument('--checkpoint_path', default=None, help='trained model parameters')
    args = parser.parse_args()
    main(args)
