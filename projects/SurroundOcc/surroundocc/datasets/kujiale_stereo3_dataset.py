from os import path as osp
import numpy as np
from typing import List, Union
import lmdb
import pickle

from mmengine.dataset import BaseDataset
from mmdet3d.datasets import NuScenesDataset
from mmengine.fileio import load

from EcoAi.registry import ECO_DATASETS
from .compose_lmdb import ComposeWithLmdb



@ECO_DATASETS.register_module()
class KujialeStereo3Dataset(BaseDataset):

    def __init__(self,
                 data_root,
                 ann_file,
                 pipeline,
                 occ_size,
                 pc_range,
                 use_semantic=False,
                 classes=None,
                 **kwargs):
        self.occ_size = occ_size
        self.pc_range = pc_range
        self.use_semantic = use_semantic
        self.class_names = classes

        super().__init__(
            data_root=data_root,
            ann_file=ann_file,
            pipeline=pipeline,
            **kwargs)

    # get_data_info
    def parse_data_info(self, info: dict) -> Union[List[dict], dict]:

        camera_info = dict(
            occ_path=osp.join(self.data_root, info['occ_path']),
            occ_size=np.array(self.occ_size),
            pc_range=np.array(self.pc_range)
        )

        image_paths = [
            osp.join(self.data_root, info['left_image_path']),
            osp.join(self.data_root, info['right_image_path']),
            osp.join(self.data_root, info['middle_image_path']),
        ]

        cam2left_rts = [self.l2l, self.r2l, self.m2l]     # extrinsics
        intrinsics = [self.l_intri, self.r_intri, self.m_intri]   # intrinsics
        cam_intrinsics, lidar2img_rts, lidar2cam_rts = [], [], []

        for cam2left_rt, intrinsic in zip(cam2left_rts, intrinsics):
            left2cam_rt = np.linalg.inv(cam2left_rt)    # [l2l, l2r]
            viewpad = np.eye(4)
            viewpad[:intrinsic.shape[0], :intrinsic.shape[1]] = intrinsic   # intrinsic 齐次到 4*4 矩阵

            # 坐标系本身变换，lidar to cam
            lidar2left_rt = np.array([  # T_left_lidar
                [1, 0, 0, 0],
                [0, 0, -1, 0],
                [0, 1, 0, 0],
                [0, 0, 0, 1]
            ], dtype=np.float32)

            lidar2cam_rt = left2cam_rt @ lidar2left_rt  # T_cam_lidar = T_cam_left * T_left_lidar
            lidar2cam_rts.append(lidar2cam_rt)

            lidar2img_rt = (viewpad @ lidar2cam_rt)     # T_img_lidar = T_intri * T_cam_lidar
            lidar2img_rts.append(lidar2img_rt)
            cam_intrinsics.append(viewpad)

        camera_info.update(
            dict(
                img_filename=image_paths,
                # img_depth_filename=image_depth_paths,
                lidar2img=lidar2img_rts,
                cam_intrinsic=cam_intrinsics,
                lidar2cam=lidar2cam_rts,
            ))

        data_list = camera_info

        return data_list

    # def __len__(self):
    #     return 20


    #  初始化中 重写load_data_list
    def load_data_list(self) -> List[dict]:

        if self.ann_file is not None:
            data = load(self.ann_file)
            raw_data_list = data['infos']

        self.r2l = data['right2left']
        self.l2l = data['left2left']
        self.m2l = data['middle2left']
        self.l_intri = data['left_intrinsic']
        self.r_intri = data['right_intrinsic']
        self.l_intri = data['left_intrinsic']
        self.m_intri = data['middle_intrinsic']

        data_list = []
        for raw_data_info in raw_data_list:
            # parse raw data information to target format
            data_info = self.parse_data_info(raw_data_info)
            if isinstance(data_info, dict):
                # For image tasks, `data_info` should information if single
                # image, such as dict(img_path='xxx', width=360, ...)
                data_list.append(data_info)
            elif isinstance(data_info, list):
                # For video tasks, `data_info` could contain image
                # information of multiple frames, such as
                # [dict(video_path='xxx', timestamps=...),
                #  dict(video_path='xxx', timestamps=...)]
                for item in data_info:
                    if not isinstance(item, dict):
                        raise TypeError('data_info must be list of dict, but '
                                        f'got {type(item)}')
                data_list.extend(data_info)
            else:
                raise TypeError('data_info should be a dict or list of dict, '
                                f'but got {type(data_info)}')

        return data_list


@ECO_DATASETS.register_module()
class KujialeStereo3Dataset_LMDB(BaseDataset):

    def __init__(self,
                 data_root,
                 ann_file,
                 pipeline,
                 occ_size,
                 pc_range,
                 use_semantic=False,
                 classes=None,
                 **kwargs):
        self.occ_size = occ_size
        self.pc_range = pc_range
        self.use_semantic = use_semantic
        self.class_names = classes

        super().__init__(
            data_root=data_root,
            ann_file=ann_file,
            pipeline=pipeline,
            **kwargs)
        if pipeline is not None:
            self.pipeline = ComposeWithLmdb(pipeline, self.lmdb_env)

    def parse_data_info(self, info: dict) -> Union[List[dict], dict]:

        camera_info = dict(
            occ_path=info['occ_path'],
            bev_path=info['bev_path'] if 'bev_path' in info else None,
            occ_size=np.array(self.occ_size),
            pc_range=np.array(self.pc_range)
        )

        image_paths = [
            info['left_image_path'],
            info['right_image_path'],
            info['middle_image_path']
        ]
        # image_depth_paths = [info['camera2_depth_path'], info['camera4_depth_path']]

        cam2left_rts = [self.l2l, self.r2l, self.m2l]
        intrinsics = [self.l_intri, self.r_intri, self.m_intri]
        cam_intrinsics, lidar2img_rts, lidar2cam_rts = [], [], []
        for cam2left_rt, intrinsic in zip(cam2left_rts, intrinsics):
            left2cam_rt = np.linalg.inv(cam2left_rt)
            viewpad = np.eye(4)
            viewpad[:intrinsic.shape[0], :intrinsic.shape[1]] = intrinsic

            # 坐标系本身变换，lidar to cam
            lidar2left_rt = np.array([
                [1, 0, 0, 0],
                [0, 0, -1, 0],
                [0, 1, 0, 0],
                [0, 0, 0, 1]
            ], dtype=np.float32)

            lidar2cam_rt = left2cam_rt @ lidar2left_rt  # T_cam_left * T_left_lid => T_cam_lid
            lidar2cam_rts.append(lidar2cam_rt)

            lidar2img_rt = (viewpad @ lidar2cam_rt)
            lidar2img_rts.append(lidar2img_rt)
            cam_intrinsics.append(viewpad)

        camera_info.update(
            dict(
                img_filename=image_paths,
                # img_depth_filename=image_depth_paths,
                lidar2img=lidar2img_rts,
                cam_intrinsic=cam_intrinsics,
                lidar2cam=lidar2cam_rts,
            ))
        data_list = camera_info

        return data_list

    # def __len__(self):
    #     return 20

    def load_data_list(self) -> List[dict]:

        # load_annotations
        lmdb_env = lmdb.open(self.ann_file, readonly=True, lock=False)
        self.lmdb_env = lmdb_env
        with lmdb_env.begin() as txn:
            pkl_data = txn.get('pkl'.encode())
            if pkl_data is not None:
                data = pickle.loads(pkl_data)
                raw_data_list = data['infos']

                self.r2l = data['right2left']   # Tlr
                self.l2l = data['left2left']    # Tll
                self.m2l = data['middle2left']    # Tll
                self.l_intri = data['left_intrinsic']
                self.r_intri = data['right_intrinsic']
                self.m_intri = data['middle_intrinsic']

            else:
                raise ValueError("The 'pkl' key is not found in the LMDB database.")

            data_list = []
            for raw_data_info in raw_data_list:
                # parse raw data information to target format
                data_info = self.parse_data_info(raw_data_info)
                if isinstance(data_info, dict):
                    # For image tasks, `data_info` should information if single
                    # image, such as dict(img_path='xxx', width=360, ...)
                    data_list.append(data_info)
                elif isinstance(data_info, list):
                    # For video tasks, `data_info` could contain image
                    # information of multiple frames, such as
                    # [dict(video_path='xxx', timestamps=...),
                    #  dict(video_path='xxx', timestamps=...)]
                    for item in data_info:
                        if not isinstance(item, dict):
                            raise TypeError('data_info must be list of dict, but '
                                            f'got {type(item)}')
                    data_list.extend(data_info)
                else:
                    raise TypeError('data_info should be a dict or list of dict, '
                                    f'but got {type(data_info)}')

        return data_list


if __name__ == "__main__":
    pass
