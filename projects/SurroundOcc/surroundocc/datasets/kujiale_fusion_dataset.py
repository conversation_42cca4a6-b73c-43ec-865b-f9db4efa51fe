from os import path as osp
import numpy as np
from typing import List, Union
import lmdb
import pickle

from mmengine.dataset import BaseDataset
from mmdet3d.datasets import NuScenesDataset
from mmengine.fileio import load

from EcoAi.registry import ECO_DATASETS
from .compose_lmdb import ComposeWithLmdb

@ECO_DATASETS.register_module()
class KujialeFusionDataset(BaseDataset):

    def __init__(self,
                 data_root,
                 ann_file,
                 pipeline,
                 occ_size,
                 pc_range,
                 use_semantic=False,
                 classes=None,
                 **kwargs):
        self.occ_size = occ_size
        self.pc_range = pc_range
        self.use_semantic = use_semantic
        self.class_names = classes

        super().__init__(
            data_root=data_root,
            ann_file=ann_file,
            pipeline=pipeline,
            **kwargs)

    # get_data_info
    def parse_data_info(self, info: dict) -> Union[List[dict], dict]:
        # lidar_path = osp.join(self.data_root, info["lidar_path_150cm"])
        # lidar_path = osp.join(self.data_root, info["lidar_path"])
        # lidar_path = osp.join(self.data_root, info["lidar1_path"])
        lidar_path = osp.join(self.data_root, info["tof_path"])

        camera_info = dict(
            token=info["token"],
            lidar_path=lidar_path,
            # lidar_path=info["lidar_path"],
            occ_size=np.array(self.occ_size),
            pc_range=np.array(self.pc_range),
            # occ_path=osp.join(self.data_root, info['lidar_path'][2:])
        )

        camera_info["img_filename"] = []
        camera_info["lidar2img"] = []
        camera_info["cam_intrinsic"] = []

        # cams = osp.join(self.data_root, info["cams"])
        cams = osp.join(self.data_root, info["rgb_path"])
        camera_info["img_filename"].append(cams)
        # camera_info["img_filename"].append(info["cams"])

        # camera intrinsics
        camera_intrinsics = np.eye(4).astype(np.float32)
        camera_intrinsics[:3, :3] = info['intrinsic']
        camera_info["cam_intrinsic"].append(camera_intrinsics)

        # lidar to image transform
        cam2lidar = self.cam2lidar
        lidar2camera = np.linalg.inv(cam2lidar)
        lidar2image = camera_intrinsics @ lidar2camera

        camera_info["lidar2img"].append(lidar2image)
        # camera_info["bev_path"] = osp.join(self.data_root, info["bev_path"])
        # camera_info["bev_path"] = None
        camera_info["occ_path"] = osp.join(self.data_root, info["occ_path"])

        # camera_info["cam2lidar"].append(lidar2camera)

        data_list = camera_info

        return data_list

    #  初始化中 重写load_data_list
    def load_data_list(self) -> List[dict]:

        if self.ann_file is not None:
            data = load(self.ann_file)
            raw_data_list = data['infos']
            self.cam2lidar = data['cam2lidar']

        data_list = []
        for raw_data_info in raw_data_list:
            # parse raw data information to target format
            data_info = self.parse_data_info(raw_data_info)
            if isinstance(data_info, dict):
                data_list.append(data_info)
            elif isinstance(data_info, list):
                for item in data_info:
                    if not isinstance(item, dict):
                        raise TypeError('data_info must be list of dict, but '
                                        f'got {type(item)}')
                data_list.extend(data_info)
            else:
                raise TypeError('data_info should be a dict or list of dict, '
                                f'but got {type(data_info)}')

        return data_list
    
    # def __len__(self):
    #     return 20

@ECO_DATASETS.register_module()
class KujialeFusionDataset_LMDB(BaseDataset):

    def __init__(self,
                 data_root,
                 ann_file,
                 pipeline,
                 occ_size,
                 pc_range,
                 use_semantic=False,
                 classes=None,
                 **kwargs):
        self.occ_size = occ_size
        self.pc_range = pc_range
        self.use_semantic = use_semantic
        self.class_names = classes

        super().__init__(
            data_root=data_root,
            ann_file=ann_file,
            pipeline=pipeline,
            **kwargs)
        if pipeline is not None:
            self.pipeline = ComposeWithLmdb(pipeline, self.lmdb_env)

    # get_data_info
    def parse_data_info(self, info: dict) -> Union[List[dict], dict]:

        camera_info = dict(
            token=info["token"],
            lidar_path=info["lidar_path"],
            # lidar_path=info["lidar1_path"],           
            occ_size=np.array(self.occ_size),
            pc_range=np.array(self.pc_range),
            # occ_path=osp.join(self.data_root, info['lidar_path'][2:])
        )

        camera_info["img_filename"] = []
        camera_info["lidar2img"] = []
        camera_info["cam_intrinsic"] = []

        camera_info["img_filename"].append(info["cams"])

        # camera intrinsics
        camera_intrinsics = np.eye(4).astype(np.float32)
        camera_intrinsics[:3, :3] = info['intrinsic']
        camera_info["cam_intrinsic"].append(camera_intrinsics)

        # lidar to image transform
        cam2lidar = self.cam2lidar
        lidar2camera = np.linalg.inv(cam2lidar)
        lidar2image = camera_intrinsics @ lidar2camera

        camera_info["lidar2img"].append(lidar2image)
        camera_info["bev_path"] = info["bev_path"]
        camera_info["occ_path"] = info["occ_path"]

        data_list = camera_info

        return data_list

    # def __len__(self):
    #     return 20

    #  初始化中 重写load_data_list
    def load_data_list(self) -> List[dict]:

        # load_annotations
        lmdb_env = lmdb.open(self.ann_file, readonly=True, lock=False)
        self.lmdb_env = lmdb_env
        with lmdb_env.begin() as txn:
            pkl_data = txn.get('pkl'.encode())
            if pkl_data is not None:
                data = pickle.loads(pkl_data)
                raw_data_list = data['infos']

                self.cam2lidar = data['cam2lidar']

            else:
                raise ValueError("The 'pkl' key is not found in the LMDB database.")

        data_list = []
        for raw_data_info in raw_data_list:
            # parse raw data information to target format
            data_info = self.parse_data_info(raw_data_info)
            if isinstance(data_info, dict):
                data_list.append(data_info)
            elif isinstance(data_info, list):
                for item in data_info:
                    if not isinstance(item, dict):
                        raise TypeError('data_info must be list of dict, but '
                                        f'got {type(item)}')
                data_list.extend(data_info)
            else:
                raise TypeError('data_info should be a dict or list of dict, '
                                f'but got {type(data_info)}')

        return data_list

if __name__ == "__main__":
    pass