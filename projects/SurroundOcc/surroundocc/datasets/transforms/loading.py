import os
import io
import numpy as np
from PIL import Image
from io import Bytes<PERSON>
from typing import Any, Dict, <PERSON><PERSON>

import cv2
from mmdet.registry import T<PERSON>NSFORMS
import mmcv
from mmcv.transforms import BaseTransform
from mmengine.utils import check_file_exist
from mmdet3d.structures.points import BasePoints, get_points_type

from .loading_utils import load_augmented_point_cloud, reduce_LiDAR_beams


@TRANSFORMS.register_module()
class LoadMultiViewImageFromFiles_OCC(BaseTransform):
    def __init__(self, to_float32=False, color_type='unchanged') -> None:
        self.to_float32 = to_float32
        self.color_type = color_type

        self.B_target = 0.06
        self.focal_length = 367.6901181887478

    def transform(self, results):
        filename = results['img_filename']
        img = np.stack(
            [mmcv.imread(name, self.color_type) for name in filename], axis=-1)

        # img1 = mmcv.imread(filename[0], self.color_type)
        # img2 = mmcv.imread(filename[1], self.color_type)
        # img2 = cv2.resize(img2, img1.shape[:2][::-1], interpolation=cv2.INTER_CUBIC)
        # img = np.stack([img1, img2], axis=-1)
        if self.to_float32:
            img = img.astype(np.float32)

        # h, w = img[..., 0].shape[:2]
        # camera_matrix = np.array([
        #     [367.69011819, 0., 620.],
        #     [0., 367.69011819, 462.5],
        #     [0., 0., 1.]
        # ])
        #
        # distortion_coeffs = np.array([-0.05, 0.025, 0, 0, 0])        # 初始化畸变映射
        # # distortion_coeffs = np.array([0.1, -0.05, 0, 0, 0])
        # map1, map2 = cv2.initUndistortRectifyMap(
        #     camera_matrix, distortion_coeffs,
        #     None,
        #     camera_matrix,
        #     (w, h),
        #     cv2.CV_32FC1)
        #
        # # 应用畸变映射
        # img[..., 0] = cv2.remap(img[..., 0], map1, map2, interpolation=cv2.INTER_LINEAR)
        # img[..., 1] = cv2.remap(img[..., 1], map1, map2, interpolation=cv2.INTER_LINEAR)


        # right_depth = cv2.imread(results['img_depth_filename'][1], -1)
        # # 计算目标视差图
        # disparity_target = self.B_target * self.focal_length / ((right_depth + 1e-4) / 1000)
        # # 创建新的右目图像
        # height, width, channels = img[..., 1].shape
        # new_right_image = np.zeros_like(img[..., 1])
        #
        # # 创建映射表
        # map_x = np.tile(np.arange(width), (height, 1)).astype(np.float32)
        # map_y = np.tile(np.arange(height).reshape(-1, 1), (1, width)).astype(np.float32)
        #
        # # 平移映射表
        # map_x -= disparity_target
        #
        # # 使用remap函数进行高精度插值
        # img[..., 1] = cv2.remap(img[..., 1], map_x, map_y, interpolation=cv2.INTER_LINEAR)


        # img[:, :-10] = img[:, 10:]    # 左移10个像素
        # img[:, -10:] = 0  0 0000000000000

        # img[:, :-10] = img[:, 10:]    # 左移10个像素，反射填充
        # img[:, -10:] = img[:, 10:0:-1]

        # img[:-10, :] = img[10:, :]
        # img[-10:, :] = img[-20:-10, :]

        # img = img[:, 10:-10]

        # img[:10, :] = img[10:0:-1, :]
        # img[10:, :] = img[:-10, :]
        # img[:10, :] = img[10:0:-1, :]

        results['filename'] = filename
        # unravel to list, see `DefaultFormatBundle` in formating.py
        # which will transpose each image separately and then stack into array
        results['img'] = [img[..., i] for i in range(img.shape[-1])]
        results['img_shape'] = img.shape
        results['ori_shape'] = img.shape
        # Set initial values for default meta_keys
        results['pad_shape'] = img.shape
        results['scale_factor'] = 1.0
        num_channels = 1 if len(img.shape) < 3 else img.shape[2]
        results['img_norm_cfg'] = dict(
            mean=np.zeros(num_channels, dtype=np.float32),
            std=np.ones(num_channels, dtype=np.float32),
            to_rgb=False)
        return results

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f'(to_float32={self.to_float32}, '
        repr_str += f"color_type='{self.color_type}')"
        return repr_str


@TRANSFORMS.register_module()
class LoadMultiViewImageFromFiles_LMDB(object):

    def __init__(self, to_float32=False, color_type='unchanged'):
        self.to_float32 = to_float32
        self.color_type = color_type
        self.lmdb_env = None

    def set_lmdb_env(self, lmdb_env):
        self.lmdb_env = lmdb_env

    def __call__(self, results):
        if self.lmdb_env is None:
            raise RuntimeError('LMDB environment is not set for LoadMultiViewImageFromFiles_LMDB')
        # start_time = time.time()
        with self.lmdb_env.begin(write=False) as txn:

            filename = results['img_filename']
            imgs = []  # img is of shape (h, w, c, num_views)
            for _filename in filename:

                # 从硬盘读取数据
                img_binary = txn.get(_filename.encode())

                if img_binary is not None:
                    # 将二进制数据转换为图像
                    img = Image.open(io.BytesIO(img_binary))

                    img = np.array(img, dtype=np.float32 if self.to_float32 else np.uint8)
                    img = img[..., ::-1]  # RGB -> BGR
                    imgs.append(img)
                else:
                    raise ValueError(f"The key {_filename} is not found in the LMDB database.")

        # 堆叠图像形成一个多视图数组
        img = np.stack(imgs, axis=-1)

        results['filename'] = filename
        # unravel to list, see `DefaultFormatBundle` in formating.py
        # which will transpose each image separately and then stack into array
        results['img'] = [img[..., i] for i in range(img.shape[-1])]
        results['img_shape'] = img.shape
        results['ori_shape'] = img.shape
        # Set initial values for default meta_keys
        results['pad_shape'] = img.shape
        results['scale_factor'] = 1.0
        num_channels = 1 if len(img.shape) < 3 else img.shape[2]
        results['img_norm_cfg'] = dict(
            mean=np.zeros(num_channels, dtype=np.float32),
            std=np.ones(num_channels, dtype=np.float32),
            to_rgb=False)
        return results

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f'(to_float32={self.to_float32}, '
        repr_str += f"color_type='{self.color_type}')"
        return repr_str


@TRANSFORMS.register_module()
class LoadMultiViewImageDepthFromFiles_LMDB(object):

    def __init__(self, to_float32=False, color_type='unchanged'):
        self.to_float32 = to_float32
        self.color_type = color_type
        self.lmdb_env = None

    def set_lmdb_env(self, lmdb_env):
        self.lmdb_env = lmdb_env

    def __call__(self, results):
        if self.lmdb_env is None:
            raise RuntimeError('LMDB environment is not set for LoadMultiViewImageFromFiles_LMDB')
        with self.lmdb_env.begin(write=False) as txn:

            depth_filename = results['img_depth_filename']
            img_depths = []  # img is of shape (h, w, c, num_views)
            for _depth_filename in depth_filename:

                # 从硬盘读取数据
                img_depth_binary = txn.get(_depth_filename.encode())

                if img_depth_binary is not None:
                    # 将二进制数据转换为图像
                    img_depth = Image.open(io.BytesIO(img_depth_binary))
                    img_depth = np.array(img_depth)
                    if self.to_float32:
                        img_depth = img_depth.astype(np.float32)

                    img_depths.append(img_depth)
                else:
                    raise ValueError(f"The key {_depth_filename} is not found in the LMDB database.")

        results['img_depth'] = img_depths

        return results

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f'(to_float32={self.to_float32}, '
        repr_str += f"color_type='{self.color_type}')"
        return repr_str


@TRANSFORMS.register_module()
class LoadKJLOccupancy_LMDB(object):
    """Load occupancy groundtruth.

    Expects results['occ_path'] to be a list of filenames.

    The ground truth is a (N, 4) tensor, N is the occupied voxel number,
    The first three channels represent xyz voxel coordinate and last channel is semantic class.
    """

    def __init__(self, use_semantic=True, fov_mask=None):
        self.use_semantic = use_semantic
        self.lmdb_env = None
        self.fov_mask = fov_mask

    def set_lmdb_env(self, lmdb_env):
        self.lmdb_env = lmdb_env

    def __call__(self, results):
        with self.lmdb_env.begin() as txn:
            occ_path_key = results['occ_path']

            # 从硬盘获取数据
            occ_data_binary = txn.get(occ_path_key.encode())

            if occ_data_binary is not None:
                with io.BytesIO(occ_data_binary) as f:
                    occ = np.load(f).astype(np.float32)
            else:
                raise ValueError(f"The key {results['occ_path']} is not found in the LMDB database.")

        lab_x, lab_z, lab_y = results['occ_size']
        occ = occ.reshape(lab_x, lab_y, lab_z).transpose(0, 2, 1)
        occ = np.flip(occ, 2).astype(np.float32)
        occ[occ == 2] = 0

        if self.fov_mask:
            fov_mask = np.load('projects/SurroundOcc/numpyfile/mask_fov_81.86.npy')
            fov_mask[fov_mask == 255] = 1
            fov_mask = np.repeat(fov_mask[:, :, np.newaxis], repeats=80, axis=2)
            occ = occ * fov_mask

        results['gt_occ'] = occ.copy()

        return results

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        return repr_str


@TRANSFORMS.register_module()
class LoadKJLBev_LMDB(object):

    def __init__(self, use_semantic=True):
        self.use_semantic = use_semantic
        self.lmdb_env = None

    def set_lmdb_env(self, lmdb_env):
        self.lmdb_env = lmdb_env

    def __call__(self, results):
        with self.lmdb_env.begin() as txn:
            bev3_path_key = results['bev_path']

            # 从硬盘获取数据
            bev3_data_binary = txn.get(bev3_path_key.encode())

            if bev3_data_binary is not None:
                # 将二进制数据转换为图像
                bev3 = Image.open(io.BytesIO(bev3_data_binary))
                bev3 = np.array(bev3).astype(np.float32)
            else:
                raise ValueError(f"The key {results['bev_path']} is not found in the LMDB database.")

        # bev3 = bev3[40:, :]

        # 3 class
        bev3[bev3 == 255] = 1
        bev3[bev3 == 0] = 2
        bev3[bev3 == 128] = 0

        # flip
        bev3 = np.flip(bev3, 0).transpose(1, 0)
        results['gt_occ'] = bev3.copy()

        return results

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        return repr_str


@TRANSFORMS.register_module()
class LoadKJLOccupancy(object):
    """Load occupancy groundtruth.

    Expects results['occ_path'] to be a list of filenames.

    The ground truth is a (N, 4) tensor, N is the occupied voxel number,
    The first three channels represent xyz voxel coordinate and last channel is semantic class.
    """

    def __init__(self, use_semantic=True, fill_grid=True):
        self.use_semantic = use_semantic
        self.fill_grid = fill_grid
        # self.lmdb_env = None

    def __call__(self, results):
        occ = np.load(results['occ_path'])

        lab_x, lab_z, lab_y = 80, 200, 30
        # lab_x, lab_z, lab_y = results['occ_size']
        occ = occ.reshape(lab_x, lab_y, lab_z).transpose(0, 2, 1).astype(np.float32)
        occ = np.flip(occ, 2)
        
        occ = occ[:, 80:160, :]

        occ[occ == 2] = 0
        # occ = occ[:, :40, :]
        # if self.fill_grid:
        #     # (occ[:, :, 0])[occ[:, :, 1] == 1] = 1   # 人工补栅格
        #     # print(1)
            
        #     occ[:, :, 0] = 1

        results['gt_occ'] = occ.copy()

        return results

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        return repr_str


@TRANSFORMS.register_module()
class LoadPointsFromFileSingle:
    """Load Points From File.

    Load sunrgbd and scannet points from file.

    Args:
        coord_type (str): The type of coordinates of points cloud.
            Available options includes:
            - 'LIDAR': Points in LiDAR coordinates.
            - 'DEPTH': Points in depth coordinates, usually for indoor dataset.
            - 'CAMERA': Points in camera coordinates.
        load_dim (int): The dimension of the loaded points.
            Defaults to 6.
        use_dim (list[int]): Which dimensions of the points to be used.
            Defaults to [0, 1, 2]. For KITTI dataset, set use_dim=4
            or use_dim=[0, 1, 2, 3] to use the intensity dimension.
        shift_height (bool): Whether to use shifted height. Defaults to False.
        use_color (bool): Whether to use color features. Defaults to False.
    """

    def __init__(
            self,
            coord_type,
            load_dim=6,
            use_dim=[0, 1, 2],
            shift_height=False,
            use_color=False,
            load_augmented=None,
            reduce_beams=None,
    ):
        self.shift_height = shift_height
        self.use_color = use_color
        if isinstance(use_dim, int):
            use_dim = list(range(use_dim))
        assert (
                max(use_dim) < load_dim
        ), f"Expect all used dimensions < {load_dim}, got {use_dim}"
        assert coord_type in ["CAMERA", "LIDAR", "DEPTH"]

        self.coord_type = coord_type
        self.load_dim = load_dim
        self.use_dim = use_dim
        self.load_augmented = load_augmented
        self.reduce_beams = reduce_beams

    def _load_points(self, lidar_path):
        """Private function to load point clouds data.

        Args:
            lidar_path (str): Filename of point clouds data.

        Returns:
            np.ndarray: An array containing point clouds data.
        """
        # mmcv.check_file_exist(lidar_path)
        check_file_exist(lidar_path)
        # print("load_augmented ",self.load_augmented)

        if self.load_augmented:
            assert self.load_augmented in ["pointpainting", "mvp"]
            virtual = self.load_augmented == "mvp"
            points = load_augmented_point_cloud(
                lidar_path, virtual=virtual, reduce_beams=self.reduce_beams
            )
        elif lidar_path.endswith(".npy"):
            points = np.load(lidar_path)
            if points.shape[0] == 0:
                print(1)
        else:
            points = np.fromfile(lidar_path, dtype=np.float32)

        return points

    def __call__(self, results):
        """Call function to load points data from file.

        Args:
            results (dict): Result dict containing point clouds data.

        Returns:
            dict: The result dict containing the point clouds data. \
                Added key and value are described below.

                - points (:obj:`BasePoints`): Point clouds data.
        """
        # lidar_path_ = results["lidar_path"].replace('points_tof1','points_tof_robot')[:-36]
        
        # npy_name = results["lidar_path"].split('/')[-1].replace('camera3','camera2')
        
        # lidar_path = os.path.join(lidar_path_,npy_name)
        
        lidar_path = results["lidar_path"]
        
        # print(lidar_path)
        
        
        
        # npy_dir =os.path.join( results["lidar_path"].split('/')[:-1])
        
        # print(npy_name)
        # print(npy_dir)
        
        
        #  lidar_path = results["lidar_path"].replace('points_tof1','points_tof_robot').
        
        # print(lidar_path)
        
        
        points = self._load_points(lidar_path)
        points = points.reshape(-1, self.load_dim)

        if self.reduce_beams and self.reduce_beams < 32:
            points = reduce_LiDAR_beams(points, self.reduce_beams)
        points = points[:, self.use_dim]

        attribute_dims = None

        if self.shift_height:  # ~False
            floor_height = np.percentile(points[:, 2], 0.99)
            height = points[:, 2] - floor_height
            points = np.concatenate(
                [points[:, :3], np.expand_dims(height, 1), points[:, 3:]], 1
            )
            attribute_dims = dict(height=3)

        if self.use_color:  # ~False
            assert len(self.use_dim) >= 6
            if attribute_dims is None:
                attribute_dims = dict()
            attribute_dims.update(
                dict(
                    color=[
                        points.shape[1] - 3,
                        points.shape[1] - 2,
                        points.shape[1] - 1,
                    ]
                )
            )

        points_class = get_points_type(self.coord_type)
        points = points_class(
            points,
            points_dim=points.shape[-1],
            attribute_dims=attribute_dims
        )
        results["points"] = points

        return results


@TRANSFORMS.register_module()
class LoadPointsFromFileSingle_LMDB:
    """Load Points From File.

    Load sunrgbd and scannet points from file.

    Args:
        coord_type (str): The type of coordinates of points cloud.
            Available options includes:
            - 'LIDAR': Points in LiDAR coordinates.
            - 'DEPTH': Points in depth coordinates, usually for indoor dataset.
            - 'CAMERA': Points in camera coordinates.
        load_dim (int): The dimension of the loaded points.
            Defaults to 6.
        use_dim (list[int]): Which dimensions of the points to be used.
            Defaults to [0, 1, 2]. For KITTI dataset, set use_dim=4
            or use_dim=[0, 1, 2, 3] to use the intensity dimension.
        shift_height (bool): Whether to use shifted height. Defaults to False.
        use_color (bool): Whether to use color features. Defaults to False.
    """

    def __init__(
            self,
            coord_type,
            load_dim=6,
            use_dim=[0, 1, 2],
            shift_height=False,
            use_color=False,
            load_augmented=None,
            reduce_beams=None,
    ):
        self.shift_height = shift_height
        self.use_color = use_color
        if isinstance(use_dim, int):
            use_dim = list(range(use_dim))
        assert (
                max(use_dim) < load_dim
        ), f"Expect all used dimensions < {load_dim}, got {use_dim}"
        assert coord_type in ["CAMERA", "LIDAR", "DEPTH"]

        self.coord_type = coord_type
        self.load_dim = load_dim
        self.use_dim = use_dim
        self.load_augmented = load_augmented
        self.reduce_beams = reduce_beams

    def _load_points(self, lidar_path):
        """Private function to load point clouds data.

        Args:
            lidar_path (str): Filename of point clouds data.

        Returns:
            np.ndarray: An array containing point clouds data.
        """
        # mmcv.check_file_exist(lidar_path)
        check_file_exist(lidar_path)
        # print("load_augmented ",self.load_augmented)

        if self.load_augmented:
            assert self.load_augmented in ["pointpainting", "mvp"]
            virtual = self.load_augmented == "mvp"
            points = load_augmented_point_cloud(
                lidar_path, virtual=virtual, reduce_beams=self.reduce_beams
            )
        elif lidar_path.endswith(".npy"):
            points = np.load(lidar_path)
        else:
            points = np.fromfile(lidar_path, dtype=np.float32)

        return points

    def set_lmdb_env(self, lmdb_env):
        self.lmdb_env = lmdb_env

    def __call__(self, results):
        """Call function to load points data from file.

        Args:
            results (dict): Result dict containing point clouds data.

        Returns:
            dict: The result dict containing the point clouds data. \
                Added key and value are described below.

                - points (:obj:`BasePoints`): Point clouds data.
        """
        # lidar_path = results["lidar_path"]
        with self.lmdb_env.begin() as txn:
            lidar_path_key = results['lidar_path']

            # 从硬盘获取数据
            lidar_data_binary = txn.get(lidar_path_key.encode())

            if lidar_data_binary is not None:
                lidar_path = np.load(BytesIO(lidar_data_binary), allow_pickle=True)
            else:
                raise ValueError(f"The key {results['lidar_path']} is not found in the LMDB database.")

        points = lidar_path.reshape(-1, self.load_dim).astype(np.float32)

        if self.reduce_beams and self.reduce_beams < 32:
            points = reduce_LiDAR_beams(points, self.reduce_beams)
        points = points[:, self.use_dim]

        attribute_dims = None

        if self.shift_height:  # ~False
            floor_height = np.percentile(points[:, 2], 0.99)
            height = points[:, 2] - floor_height
            points = np.concatenate(
                [points[:, :3], np.expand_dims(height, 1), points[:, 3:]], 1
            )
            attribute_dims = dict(height=3)

        if self.use_color:  # ~False
            assert len(self.use_dim) >= 6
            if attribute_dims is None:
                attribute_dims = dict()
            attribute_dims.update(
                dict(
                    color=[
                        points.shape[1] - 3,
                        points.shape[1] - 2,
                        points.shape[1] - 1,
                    ]
                )
            )

        points_class = get_points_type(self.coord_type)
        points = points_class(
            points,
            points_dim=points.shape[-1],
            attribute_dims=attribute_dims
        )
        results["points"] = points

        return results


@TRANSFORMS.register_module()
class LoadBEVSegmentationECO:
    def __init__(
            self,
            xbound: Tuple[float, float, float],
            ybound: Tuple[float, float, float],
            classes: Tuple[str, ...],
    ) -> None:
        super().__init__()
        patch_h = ybound[1] - ybound[0]
        patch_w = xbound[1] - xbound[0]
        canvas_h = int(patch_h / ybound[2])
        canvas_w = int(patch_w / xbound[2])
        self.patch_size = (patch_h, patch_w)
        self.canvas_size = (canvas_h, canvas_w)
        self.classes = classes

        self.maps = {}
        self.locations = []

    def __call__(self, data: Dict[str, Any]) -> Dict[str, Any]:
        # gt_map = cv2.imread(data['seg_map_path'])
        gt_map = cv2.imread(data['bev_path'], cv2.IMREAD_UNCHANGED)
        gt_map = cv2.flip(gt_map, 0).transpose(1, 0).copy()
        # gt_map = cv2.flip(gt_map, 0).copy()

        gt_map[gt_map == 255] = 1
        gt_map[gt_map == 0] = 2
        gt_map[gt_map == 128] = 0

        # num_classes = len(self.classes)  # 这里需要对目标区域分开进行gt设置
        # gt_labels = np.zeros((num_classes, *self.canvas_size), dtype=np.int64)
        #
        # for index, cc in enumerate(self.classes):
        #     temp_map = gt_map * 1.0  # 255 128 0
        #
        #     if cc == 'drivable_area':  # driver_area
        #         temp_map[temp_map == 255] = 0
        #         temp_map[temp_map == 128] = 1
        #
        #     if cc == 'stop_line':  # stop line
        #         temp_map[temp_map == 128] = 0
        #         temp_map[temp_map == 255] = 1
        #
        #     if cc == 'unknow':  # unknow
        #         temp_map[temp_map == 0] = 1
        #         temp_map[temp_map == 128] = 0
        #         temp_map[temp_map == 255] = 0
        #
        #     gt_labels[index, :, :] = temp_map

        # data["gt_masks_bev"] = gt_labels
        # data["gt_occ"] = gt_labels

        data["gt_occ"] = gt_map

        return data
