import torch
import torch.nn as nn
from torch.nn import <PERSON><PERSON><PERSON><PERSON><PERSON>, ModuleList, Sequential
import torch.utils.checkpoint as checkpoint
from torch.nn.quantized import FloatFunctional

from typing import Dict, List, Union, Tuple, Optional, Sequence

from mmdet.registry import MODELS

class ConvModule2d(nn.Sequential):
    """
    A conv block that bundles conv/norm/activation layers.

    Args:
        in_channels (int): Same as nn.Conv2d.
        out_channels (int): Same as nn.Conv2d.
        kernel_size (int | tuple[int]): Same as nn.Conv2d.
        stride (int | tuple[int]): Same as nn.Conv2d.
        padding (int | tuple[int]): Same as nn.Conv2d.
        dilation (int | tuple[int]): Same as nn.Conv2d.
        groups (int): Same as nn.Conv2d.
        bias (bool): Same as nn.Conv2d.
        padding_mode (str): Same as nn.Conv2d.
        norm_layer (nn.Module): Normalization layer.
        act_layer (nn.Module): Activation layer.
    """

    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        kernel_size: Union[int, Tuple[int, int]],
        stride: Union[int, Tuple[int, int]] = 1,
        padding: Union[int, Tuple[int, int]] = 0,
        dilation: Union[int, Tuple[int, int]] = 1,
        groups: int = 1,
        bias: bool = True,
        padding_mode: str = "zeros",
        norm_layer: Optional[nn.Module] = None,
        act_layer: Optional[nn.Module] = None,
    ):
        conv = nn.Conv2d(
            in_channels,
            out_channels,
            kernel_size,
            stride,
            padding,
            dilation,
            groups,
            bias,
            padding_mode,
        )
        conv_list = [conv, norm_layer, act_layer]
        self.conv_list = [layer for layer in conv_list if layer is not None]
        super(ConvModule2d, self).__init__(*self.conv_list)
        self.has_norm_layer = norm_layer is not None
        self.has_act_layer = act_layer is not None
        self.st_fn = None
        self.cp = False
        # if os.environ.get("HAT_USE_CHECKPOINT") is not None:
        #     self.cp = bool(int(os.environ.get("HAT_USE_CHECKPOINT", "0")))

    def forward(self, x):
        if self.cp and torch.is_tensor(x) and x.requires_grad:
            out = checkpoint.checkpoint(super().forward, x)
        elif self.st_fn is not None and self.training and torch.is_tensor(x):
            out = self.st_fn(x)
        else:
            out = super().forward(x)
        return out

    # def saved_tensor(self):
    #     # The following code will be called by ddp trainer.
    #     # Avoiding bn that can not be converted to sync bn.
    #     if support_saved_tensor() and self.has_norm_layer:
    #         convs_dict = {"conv": self[0]}
    #         convs_dict["bn"] = self[1]
    #         if self.has_act_layer:
    #             convs_dict["act"] = self[2]
    #         self.st_fn = checkpoint_convbn_with_saved_tensor(**convs_dict)
    #     else:
    #         self.st_fn = None

    # def fuse_model(self):
    #     if self.st_fn is not None:
    #         # remove saved tensor fn after fused module,
    #         # because there is only one conv remained after fused.
    #         self.st_fn = None

    #     if len(self.conv_list) > 1 and not isinstance(
    #         self.conv_list[-1], (nn.BatchNorm2d, nn.ReLU, nn.ReLU6)
    #     ):
    #         # not: conv2d+bn, conv2d+relu(6), conv2d+bn+relu(6)
    #         self.conv_list.pop()

    #     if len(self.conv_list) <= 1:
    #         # nn.Conv2d
    #         return

    #     try:
    #         from horizon_plugin_pytorch import quantization

    #         fuser_func = quantization.fuse_known_modules
    #     except Warning:
    #         logging.warning(
    #             "Please install horizon_plugin_pytorch first, otherwise use "
    #             "pytorch official quantification"
    #         )
    #         from torch.quantization.fuse_modules import fuse_known_modules

    #         fuser_func = fuse_known_modules

    #     fuse_list = ["0", "1", "2"]
    #     self.fuse_list = fuse_list[: len(self.conv_list)]
    #     torch.quantization.fuse_modules(
    #         self,
    #         self.fuse_list,
    #         inplace=True,
    #         fuser_func=fuser_func,
    #     )

def _as_list(obj) :
    """Convert the argument to a list if it is not already."""

    if isinstance(obj, (list, tuple)):
        return obj
    elif isinstance(obj, set):
        return list(obj)
    else:
        return [obj]

class EasyDict(dict):
    """
    Get attributes

    >>> d = EasyDict({'foo':3})
    >>> d['foo']
    3
    >>> d.foo
    3
    >>> d.bar
    Traceback (most recent call last):
    ...
    AttributeError: 'EasyDict' object has no attribute 'bar'

    Works recursively

    >>> d = EasyDict({'foo':3, 'bar':{'x':1, 'y':2}})
    >>> isinstance(d.bar, dict)
    True
    >>> d.bar.x
    1

    Bullet-proof

    >>> EasyDict({})
    {}
    >>> EasyDict(d={})
    {}
    >>> EasyDict(None)
    {}
    >>> d = {'a': 1}
    >>> EasyDict(**d)
    {'a': 1}

    Set attributes

    >>> d = EasyDict()
    >>> d.foo = 3
    >>> d.foo
    3
    >>> d.bar = {'prop': 'value'}
    >>> d.bar.prop
    'value'
    >>> d
    {'foo': 3, 'bar': {'prop': 'value'}}
    >>> d.bar.prop = 'newer'
    >>> d.bar.prop
    'newer'


    Values extraction

    >>> d = EasyDict({'foo':0, 'bar':[{'x':1, 'y':2}, {'x':3, 'y':4}]})
    >>> isinstance(d.bar, list)
    True
    >>> from operator import attrgetter
    >>> map(attrgetter('x'), d.bar)
    [1, 3]
    >>> map(attrgetter('y'), d.bar)
    [2, 4]
    >>> d = EasyDict()
    >>> d.keys()
    []
    >>> d = EasyDict(foo=3, bar=dict(x=1, y=2))
    >>> d.foo
    3
    >>> d.bar.x
    1

    Still like a dict though

    >>> o = EasyDict({'clean':True})
    >>> o.items()
    [('clean', True)]

    And like a class

    >>> class Flower(EasyDict):
    ...     power = 1
    ...
    >>> f = Flower()
    >>> f.power
    1
    >>> f = Flower({'height': 12})
    >>> f.height
    12
    >>> f['power']
    1
    >>> sorted(f.keys())
    ['height', 'power']
    """
    def __init__(self, d=None, **kwargs):
        if d is None:
            d = {}
        if kwargs:
            d.update(**kwargs)
        for k, v in d.items():
            setattr(self, k, v)
        # Class attributes
        for k in self.__class__.__dict__.keys():
            if not (k.startswith('__') and k.endswith('__')):
                setattr(self, k, getattr(self, k))

    def __setattr__(self, name, value):
        if isinstance(value, (list, tuple)):
            value = [self.__class__(x)
                     if isinstance(x, dict) else x for x in value]
        elif isinstance(value, dict) and not isinstance(value, self.__class__):
            value = self.__class__(value)
        super(EasyDict, self).__setattr__(name, value)
        super(EasyDict, self).__setitem__(name, value)

    __setitem__ = __setattr__

class MaybeApply1x1(nn.Module):
    """Use conv1x1 and bn to change channel.

    Args:
        in_channels (int): Number of input channels.
        out_channels (int): Number of output channels.
        use_bn (bool): if use bn

    """

    def __init__(self, in_channels, out_channels, use_bn=False):
        super(MaybeApply1x1, self).__init__()
        if in_channels == out_channels:
            return None
        self.lateral_conv = ModuleList()
        conv = nn.Conv2d(
            in_channels=in_channels,
            out_channels=out_channels,
            kernel_size=1,
            stride=1,
            padding=0,
        )
        self.lateral_conv.append(conv)
        if use_bn:
            norm = nn.BatchNorm2d(out_channels)
            self.lateral_conv.append(norm)

    def forward(self, x):
        if hasattr(self, "lateral_conv"):
            for module in self.lateral_conv:
                x = module(x)
            return x
        else:
            return x

# class Interpolate(torch.nn.Module):
#     r"""Resize for float training.

#     Support bilinear and nearest interpolate method and NCHW input.
#     The behaviour is same as torch.nn.functional.interpolate except the default
#     mode is 'bilinear'

#     Parameters
#     ----------
#     size : int or tuple of int, optional
#         the output shape of resize: if int, the output shape is (size, size)
#         else the output shape is (out_height, out_width), by default None
#         size and scale_factor shouldn't be set at the same time
#     scale_factor : float or tuple of float, optional
#         the ratio of output shape to input shape, ie. out_shape / in_shape,
#         or (out_height / in_height, out_width / in_width), by default None
#         size and scale_factor shouldn't be set at the same time
#     mode : str, optional
#         the interpolate method, by default "bilinear",
#         support "bilinear" and "nearest"
#     align_corners : bool, optional
#     recompute_scale_factor : bool, optional
#         did not support, by default None
#     antialias: bool, optional
#         flag to apply anti-aliasing, not supported yet
#     """

#     def __init__(
#         self,
#         size=None,
#         scale_factor=None,
#         mode="bilinear",
#         align_corners=None,
#         recompute_scale_factor=None,
#         antialias=False,
#     ):
#         super(Interpolate, self).__init__()
      
#         self.size = size
#         self.scale_factor = scale_factor
#         self.mode = mode
#         self.align_corners = align_corners
#         self.recompute_scale_factor = recompute_scale_factor

#     def forward(self, data):
#         return torch.nn.functional.interpolate(
#             data,
#             size=self.size,
#             scale_factor=self.scale_factor,
#             mode=self.mode,
#             align_corners=self.align_corners,
#             recompute_scale_factor=self.recompute_scale_factor,
#         )

class Resize(nn.Module):
    """The layer is used to change the feather map size or keep shape.

    # TODO(min.du, 1.0): move to hat/ops #

    Args:
        sampling: Sampling way, the candidate is ['down', 'up', 'keep'].
            e.g. 'down' : downsampling.
                 'up'   : upsampling.
                 'keep' : keep shape unchanged.
        in_channels: Number of input channels.
        out_channels: Number of output channels.
        pooling_type: Pooling type, the candidate is ['max', 'avg'].
        use_bn: if use bn.
        conv_after_downsample: Whether 1X1 conv is placed after downsample.
        upsample_type: use module or function unsample, the candidate is
            ['module', 'function', 'torch_c'].
            This parameter is added for compatibility considerations.
            Due to historical reasons,there are two different
            implementations of resize in the horizon_plugin_pytorch. They are
            exactly the same except their saved checkpoints. For 'module', the
            state dict of its qat model will have one more fake quant than
            'function'.
            'torch_c' optimize for torchDynamo/tensorRT
            use origin c op due to horizon_plugin_pytorch
            hook lead to trace graph break.
    """

    def __init__(
        self,
        sampling: str,
        in_channels: int,
        out_channels: int,
        pooling_type: str = "max",
        use_bn: bool = True,
        conv_after_downsample: bool = False,
        upsample_type: str = "module",
    ):
        super(Resize, self).__init__()
        assert sampling in ["down", "up", "keep"]
        assert pooling_type in ["max", "avg"]
        assert upsample_type in ["module", "function", "torch_c"]
        self.sampling = sampling
        self.resize_layer = ModuleList()
        if sampling == "down":
            if not conv_after_downsample:
                lateral_conv = MaybeApply1x1(in_channels, out_channels, use_bn)
                if lateral_conv:
                    self.resize_layer.append(lateral_conv)
            if pooling_type == "max":
                pooling = nn.MaxPool2d(kernel_size=2, stride=2, padding=0)
            elif pooling_type == "avg":
                pooling = nn.AvgPool2d(kernel_size=2, stride=2, padding=0)
            self.resize_layer.append(pooling)
            if conv_after_downsample:
                lateral_conv = MaybeApply1x1(in_channels, out_channels, use_bn)
                if lateral_conv:
                    self.resize_layer.append(lateral_conv)
        else:
            lateral_conv = MaybeApply1x1(in_channels, out_channels, use_bn)
            if lateral_conv:
                self.resize_layer.append(lateral_conv)

        self.upsample_type = upsample_type
        if sampling == "up" and upsample_type == "module":
            self.upsampling = Interpolate(
                scale_factor=2, mode="bilinear", recompute_scale_factor=True
            )

    def forward(self, x):
        """Forward feature.

        Args:
            x (tensor): input tensor

        Returns (tensor): resized tensor

        """

        for module in self.resize_layer:
            x = module(x)
        if self.sampling == "up":
            if self.upsample_type == "module":
                x = self.upsampling(x)
            elif self.upsample_type == "torch_c":
                # optimize for torchDynamo/tensorRT
                # use origin c op due to horizon_plugin_pytorch
                # hook lead to trace graph break.
                # For tensorRT optimize,
                # Please run before:
                # from hat.utils.trt_fx_extension import load_extension
                # load_extension("c_interpolate")
                x = torch._C._nn.upsample_bilinear2d(
                    x,
                    output_size=None,
                    align_corners=False,
                    scale_factors=(2, 2),
                )
            else:
                x = torch.nn.functional.interpolate(
                    x,
                    scale_factor=2,
                    mode="bilinear",
                    recompute_scale_factor=True,
                )
        return x

class Fusion(nn.Module):
    """Multi-level feature fusion.

    Args:
        weight_len: Number of the input tensors
        weight_method: sum or fastattn
        eps: Avoid except 0
        use_fx: Whether use fx mode qat. Default: False.

    """

    def __init__(
        self,
        in_channels: int,
        weight_len: int,
        weight_method: str = "sum",
        eps: float = 0.0001,
        use_fx: bool = False,
    ):
        super(Fusion, self).__init__()
        self.weight_method = weight_method
        self.eps = eps
        self.use_fx = use_fx
        if not use_fx:
            self.floatF = FloatFunctional()
        if weight_method == "fastattn":
            self.edge_weights = nn.Parameter(
                torch.ones(weight_len, dtype=torch.float32), requires_grad=True
            )
            self.relu = nn.ReLU(inplace=True)

    def float_function_sum(self, x):
        x = _as_list(x)
        for i, val in enumerate(x):
            if i == 0:
                res = val
            else:
                if self.use_fx:
                    # do not use "+="", since it is an inplace operation
                    res = res + val
                else:
                    res = self.floatF.add(res, val)
        return res

    def forward(self, x):
        x = list(x)
        if self.weight_method == "sum":
            return self.float_function_sum(x)
        elif self.weight_method == "fastattn":
            # edge_weights would be followed with relu to become positive
            relu_edge_weights = self.relu(self.edge_weights)
            weights_sum = self.float_function_sum(relu_edge_weights)
            for i in range(len(x)):
                x[i] = x[i] * relu_edge_weights[i]
                x[i] = x[i] / (weights_sum + self.eps)
            return self.float_function_sum(x)


class BifpnLayer(nn.Module):
    """The basic structure of BiFPN.

    Args:
        fpn_config: The dict is used for build the bifpn node
        out_index: Get final output tensor list
        use_fx: Whether use fx mode qat. Default: False.

    """

    def __init__(
        self,
        fpn_config: Dict,
        out_index: List[int] = None,
        use_fx: bool = False,
    ):

        super(BifpnLayer, self).__init__()
        self.fpn_config = fpn_config
        self.out_index = out_index
        level = fpn_config.level
        in_channels = fpn_config.in_channels
        out_channels = fpn_config.out_channels
        offset2inchannels = {
            0: out_channels[0],
            1: out_channels[1],
            2: out_channels[2],
            3: out_channels[3],
            4: out_channels[4],
            5: out_channels[3],
            6: out_channels[2],
            7: out_channels[1],
            8: out_channels[0],
            9: out_channels[1],
            10: out_channels[2],
            11: out_channels[3],
        }
        offset2out_channels = {
            0: {"keep": out_channels[0]},
            1: {"keep": out_channels[1]},
            2: {"keep": out_channels[2]},
            3: {"keep": out_channels[3]},
            4: {"keep": out_channels[4], "up": out_channels[3]},
            5: {"keep": out_channels[3], "up": out_channels[2]},
            6: {"keep": out_channels[2], "up": out_channels[1]},
            7: {"keep": out_channels[1], "up": out_channels[0]},
            8: {"down": out_channels[1]},
            9: {"down": out_channels[2]},
            10: {"down": out_channels[3]},
            11: {"down": out_channels[4]},
        }
        weight_method = fpn_config.weight_method
        self.all_nodes = ModuleDict()
        for i, fnode in enumerate(fpn_config.nodes):
            # rank_zero_info(f"fnode {i} : {fnode}")
            node = ModuleList()
            out_ch = 0
            # resize
            for offset, sampling, upsample_type in zip(
                fnode["inputs_offsets"],
                fnode["sampling"],
                fnode["upsample_type"],
            ):
                in_ch = (
                    in_channels[offset]
                    if offset < level
                    else offset2inchannels[offset]
                )
                out_ch = offset2out_channels[offset][sampling]
                node.append(
                    Resize(
                        sampling,
                        in_ch,
                        out_ch,
                        upsample_type=upsample_type,
                    )
                )

            # fusion
            node.append(
                Fusion(
                    out_ch,
                    len(fnode["inputs_offsets"]),
                    weight_method,
                    use_fx=use_fx,
                )
            )

            # relu, conv, bn
            node.append(
                Sequential(
                    nn.ReLU(inplace=True),
                    nn.Conv2d(
                        out_ch,
                        out_ch,
                        kernel_size=3,
                        stride=1,
                        bias=False,
                        groups=out_ch,
                        padding=1,
                    ),
                    ConvModule2d(
                        in_channels=out_ch,
                        out_channels=out_ch,
                        kernel_size=1,
                        stride=1,
                        groups=1,
                        padding=0,
                        norm_layer=nn.BatchNorm2d(out_ch),
                    ),
                )
            )
            # append with name
            self.all_nodes[str(i)] = node

    def forward(self, x):
        x = list(x)
        assert len(x) == self.fpn_config.level
        for i, fnode in enumerate(self.fpn_config.nodes):
            nodes = []
            for idx, input_offset in enumerate(fnode["inputs_offsets"]):
                input_node = x[input_offset]
                input_node = self.all_nodes[str(i)][idx](input_node)  # resize
                nodes.append(input_node)
            # fusion
            new_node = self.all_nodes[str(i)][idx + 1](nodes)
            # activation + separable conv + bn
            new_node = self.all_nodes[str(i)][idx + 2](new_node)
            x.append(new_node)

        all_outs = [x[i] for i in range(-self.fpn_config.level, 0, 1)]
        if self.out_index is not None:
            return [all_outs[i] for i in self.out_index]
        return all_outs


def get_fpn_config(
    fpn_name="bifpn_sum", out_channels=64, upsample_type="module"
):
    assert fpn_name in ["bifpn_sum", "bifpn_fa"]
    fpn_config = EasyDict()
    fpn_config.out_channels = out_channels
    fpn_config.level = 5
    # define connection method
    fpn_config.nodes = [
        {
            "inputs_offsets": [3, 4],
            "sampling": ["keep", "up"],
            "upsample_type": [upsample_type, upsample_type],
        },
        {
            "inputs_offsets": [2, 5],
            "sampling": ["keep", "up"],
            "upsample_type": [upsample_type, upsample_type],
        },
        {
            "inputs_offsets": [1, 6],
            "sampling": ["keep", "up"],
            "upsample_type": [upsample_type, upsample_type],
        },
        {
            "inputs_offsets": [0, 7],
            "sampling": ["keep", "up"],
            "upsample_type": [upsample_type, upsample_type],
        },
        {
            "inputs_offsets": [1, 7, 8],
            "sampling": ["keep", "keep", "down"],
            "upsample_type": [upsample_type, upsample_type, upsample_type],
        },
        {
            "inputs_offsets": [2, 6, 9],
            "sampling": ["keep", "keep", "down"],
            "upsample_type": [upsample_type, upsample_type, upsample_type],
        },
        {
            "inputs_offsets": [3, 5, 10],
            "sampling": ["keep", "keep", "down"],
            "upsample_type": [upsample_type, upsample_type, upsample_type],
        },
        {
            "inputs_offsets": [4, 11],
            "sampling": ["keep", "down"],
            "upsample_type": [upsample_type, upsample_type],
        },
    ]
    # define weighting method
    fpn_config.weight_method = "sum"
    if fpn_name == "bifpn_fa":
        fpn_config.weight_method = "fastattn"

    return fpn_config

def normal_init(module, mean=0, std=1, bias=0):
    nn.init.normal_(module.weight, mean, std)
    if hasattr(module, "bias") and module.bias is not None:
        nn.init.constant_(module.bias, bias)
        
def _check_strides(strides: Union[int, Sequence], valid_strides: Sequence):
    # TODO(min.du, 0.1): why return strides? #
    strides = _as_list(strides)
    for stride_i in strides:
        assert stride_i in valid_strides
    return strides

@MODELS.register_module()
class BiFPN(nn.Module):
    """Weighted Bi-directional Feature Pyramid Network(BiFPN).

    This is an implementation of - EfficientDet: Scalable and Efficient Object
    Detection (https://arxiv.org/abs/1911.09070)

    Args:
        in_strides: Stride of input feature map
        out_strides: Stride of output feature map
        stride2channels: The key:value is stride:channel ,
            the channles have been multipified by alpha
        out_channels: Channel number of output layer, the key:value
            is stride:channel.
        num_outs: Number of BifpnLayer's input, the value is must 5,
            because the bifpn layer is fixed
        stack: Number of BifpnLayer
        start_level: Index of the start input backbone level
            used to build the feature pyramid. Default: 0.
        end_level: Index of the end input backbone level (exclusive)
            to build the feature pyramid. Default: -1, means the last level.
        fpn_name: the value is mutst between with 'bifpn_sum', 'bifpn_fa'.
        upsample_type: use module or function unsample, the candidate is
            ['module', 'function'].
        use_fx: Whether use fx mode qat. Default: False.

    """

    def __init__(
        self,
        in_strides: List[int],
        out_strides: int,
        stride2channels: Dict,
        out_channels: Union[int, Dict],
        num_outs: int,
        stack: int = 3,
        start_level: int = 0,
        end_level: int = -1,
        fpn_name: str = "bifpn_sum",
        upsample_type: str = "module",
        use_fx: bool = False,
    ):
        super(BiFPN, self).__init__()

        self.in_strides = in_strides
        self.out_strides = out_strides
        self.stride2channels = stride2channels
        self.in_channels = [stride2channels[stride] for stride in in_strides]
        assert isinstance(out_channels, int) or isinstance(out_channels, dict)
        self.out_channels = (
            [out_channels[stride] for stride in out_strides]
            if isinstance(out_channels, dict)
            else [out_channels for _ in out_strides]
        )
        self.num_ins = len(in_strides)
        self.num_outs = num_outs

        # assert in_strides in stride2channels
        self.in_strides = _check_strides(
            in_strides, self.stride2channels.keys()
        )

        assert len(self.out_strides) <= num_outs
        assert stack >= 1
        self.stack = stack
        self.fpn_config = get_fpn_config(
            fpn_name,
            self.out_channels,
            upsample_type,
        )

        if end_level == -1:
            self.backbone_end_level = self.num_ins
            assert self.num_outs >= self.num_ins - start_level
        else:
            # if end_level < inputs, no extra level is allowed
            self.backbone_end_level = end_level
            assert end_level <= len(self.in_channels)
            assert self.num_outs == end_level - start_level

        self.start_level = start_level
        self.end_level = end_level
        # add extra downsample layers (stride-2 pooling or conv + pooling)
        # to build extra input features that are not from backbone.
        extra_levels = (
            self.num_outs - self.backbone_end_level + self.start_level
        )
        # channels for multi-level feature-map used in bifpn
        self.fpn_config.in_channels = self.in_channels[
            self.start_level : self.backbone_end_level
        ] + [
            self.out_channels[self.backbone_end_level - self.start_level + i]
            for i in range(extra_levels)
        ]
        # bifpn total out strides and out_index
        self.total_out_strides = self.in_strides[
            self.start_level : self.backbone_end_level
        ] + [
            self.in_strides[self.backbone_end_level - 1] * (2 ** (i + 1))
            for i in range(extra_levels)
        ]
        self.out_index = [
            self.total_out_strides.index(stride) for stride in self.out_strides
        ]
        # build extra_downsample layers
        self.extra_downsamples = ModuleList()
        for i in range(extra_levels):
            out_channels = self.out_channels[
                self.backbone_end_level - self.start_level + i
            ]
            downsample = Resize(
                sampling="down",
                in_channels=self.in_channels[-1],
                out_channels=out_channels,
                pooling_type="max",
                use_bn=True,
                conv_after_downsample=False,
                upsample_type=upsample_type,
            )
            self.in_channels[-1] = out_channels
            self.extra_downsamples.append(downsample)
        # repeat build bifpn layer many times
        self.bifpn_layers = ModuleList()
        for i in range(self.stack):
            # rank_zero_info("building bifpn cell %d" % (i))
            if i == self.stack - 1:
                out_index = self.out_index
            else:
                out_index = None
            bifpn_layer = BifpnLayer(
                EasyDict(self.fpn_config.copy()),
                out_index,
                use_fx=use_fx,
            )
            self.bifpn_layers.append(bifpn_layer)
            # update fpn_in_channels, only need to update once
            if i == 0:
                self.fpn_config.in_channels = [
                    self.out_channels[i] for i in range(self.num_outs)
                ]
        self._init_weights()

    def _init_weights(self):
        """Initialize the weights of BiFPN module."""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                normal_init(m, mean=0, std=0.01, bias=0)

    def forward(self, inputs):
        """Forward features.

        Args:
            inputs (list[tensor]): Input tensors

        Returns (list[tensor]): Output tensors

        """
        assert len(inputs) == len(self.in_channels)
        x = list(inputs[self.start_level : self.backbone_end_level])
        # build extra input features
        for downsample in self.extra_downsamples:
            x.append(downsample(x[-1]))
        # repeat bifpn
        for i in range(self.stack):
            x = self.bifpn_layers[i](x)
        return x

    # def fuse_model(self):
    #     try:
    #         from horizon_plugin_pytorch import quantization

    #         fuser_func = quantization.fuse_known_modules
    #     except ImportError:
    #         logging.warning(
    #             "Please install horizon_plugin_pytorch first, otherwise use "
    #             "pytorch official quantification"
    #         )
    #         from torch.quantization.fuse_modules import fuse_known_modules

    #         fuser_func = fuse_known_modules

    #     total_fuse = 0
    #     for m in self.modules():
    #         if type(m) == MaybeApply1x1:
    #             if hasattr(m, "lateral_conv"):
    #                 torch.quantization.fuse_modules(
    #                     m,
    #                     ["lateral_conv.0", "lateral_conv.1"],
    #                     inplace=True,
    #                     fuser_func=fuser_func,
    #                 )
    #                 total_fuse += 1
    #         elif type(m) == ConvModule2d:
    #             m.fuse_model()
    #             total_fuse += 1
        # rank_zero_info("neck total_fuse  {}".format(total_fuse))

    # def set_qconfig(self):
    #     from hat.utils import qconfig_manager

    #     self.qconfig = qconfig_manager.get_default_qat_qconfig()