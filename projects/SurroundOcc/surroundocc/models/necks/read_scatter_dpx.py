import torch
import torch.nn as nn
import torch.nn.functional as F

from typing import <PERSON>ple

from mmdet.registry import MODELS

from horizon_plugin_pytorch.nn.functional import point_pillars_scatter
from hat.utils.model_helpers import fx_wrap


@MODELS.register_module()
class PointPillarScatter_dpx(nn.Module):
    def __init__(
        self,
        num_input_features: int,
        use_horizon_pillar_scatter: bool = False,
        quantize=False,
        **kwargs,
    ):
        """Point Pillar's Scatter.

        Scatter the features back to the canvas to form a pseudo-image.
        The output pseudo-image has a shape of NCHW, where the H & W is
        determined by the point cloud's range and each voxel's size.

        Args:
            num_input_features (int): number of input features.
            use_horizon_pillar_scatter: Whether to use horizon pillar scatter,
                which is same with origin PillarScatter but support quantize.
        """
        super().__init__()
        self.nchannels = num_input_features
        self.nx = 0
        self.ny = 0
        self.use_horizon_pillar_scatter = use_horizon_pillar_scatter
        self.quantize = quantize

    @fx_wrap()
    def forward(
        self,
        voxel_features: torch.Tensor,
        coords: torch.Tensor,
        batch_size: int,
        input_shape: torch.Tensor,
    ):
        """Forward pass of the scatter module.

        Note: batch_size has to be passed in additionally, because voxel
        features are concatenated on the M-channel since the number of voxels
        in each frame differs and there is no easy way we concat them same as
        image (CHW -> NCHW). M-channel concatenation would require another
        tensor to record number of voxels per frame, which indicates batch_size
        consequently.

        Args:
            voxel_features (torch.Tensor): MxC tensor of pillar features, where
                M is number of pillars, C is each pillar's feature dim.
            coords (torch.Tensor): each pillar's original BEV coordinate.
            batch_size (int): batch size of the feature.
            input_shape (torch.Tensor): shape of the expected BEC map. Derived
                from point-cloud range and voxel size.

        Returns:
            [torch.Tensor]: a BEV view feature tensor with point features
                scattered on it.
        """
        self.nx = input_shape[0]
        self.ny = input_shape[1]

        P, C = voxel_features.size(-2), voxel_features.size(-1)
        voxel_features = voxel_features.reshape(P, C)

        if self.use_horizon_pillar_scatter:
            out_shape = (batch_size, self.nchannels, self.ny, self.nx)
            batch_canvas = point_pillars_scatter(
                voxel_features, coords, out_shape
            )
        else:
            # batch_canvas will be the final output.
            batch_canvas = []
            for batch_id in range(batch_size):
                # Create a canvas for this sample
                canvas = torch.zeros(
                    self.nchannels,
                    self.nx * self.ny,  # This is P. p = nx * ny.
                    dtype=voxel_features.dtype,
                    device=voxel_features.device,
                )

                # Only include non-empty pillars
                batch_mask = coords[:, 0] == batch_id

                this_coords = coords[batch_mask, :]
                indices = this_coords[:, 2] * self.nx + this_coords[:, 3]
                indices = indices.type(torch.long)
                voxels = voxel_features[batch_mask, :]
                voxels = voxels.t()

                # Scatter the blob back to teh canvas
                canvas[:, indices] = voxels

                # Append to a list for later stacking
                batch_canvas.append(canvas)

            # Stack to 3-dim tensor (batch-size, nchannels, nrows*ncols)
            batch_canvas = torch.stack(batch_canvas, 0)

            # Undo the column stacking to final 4-dim tensor
            batch_canvas = batch_canvas.view(
                batch_size, self.nchannels, self.ny, self.nx
            )
            # canvas = torch.zeros(
            #     self.nchannels,
            #     self.nx * self.ny,
            #     dtype=voxel_features.dtype,
            #     device=voxel_features.device)

            # indices = coords[:, 2] * float(self.nx) + coords[:, 3]
            # indices = indices.long()
            # voxels = voxel_features.t()
            # # Now scatter the blob back to the canvas.
            # canvas[:, indices] = voxels
            # # Undo the column stacking to final 4-dim tensor
            # canvas = canvas.view(1, self.nchannels, self.ny, self.nx)
            # return canvas

        return batch_canvas

    def set_qconfig(self):
        if self.quantize:
            from hat.utils import qconfig_manager

            self.qconfig = qconfig_manager.get_default_qat_qconfig()
        else:
            self.qconfig = None

class PFNLayer(nn.Module):
    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        bn_kwargs: dict = None,
        last_layer: bool = False,
        use_conv: bool = True,
        pool_size: Tuple[int, int] = (1, 1),
        hw_reverse: bool = False,
    ):
        """Pillar Feature Net Layer.

        This layer is used to convert point cloud into pseudo-image.
        Can stack multiple to form Pillar Feature Net.
        The original PointPillars paper uses only a single PFNLayer.

        Args:
            in_channels (int): number of input channels.
            out_channels (int): number of output channels.
            bn_kwrags (dict): batch normalization arguments. Defaults to None.
            last_layer (bool, optional): if True, there is no concatenation of
                layers. Defaults to False.
        """
        super().__init__()
        self.last_vfe = last_layer
        if not self.last_vfe:
            out_channels = out_channels // 2
        self.units = out_channels

        if bn_kwargs is None:
            bn_kwargs = dict(eps=1e-3, momentum=0.01)  # noqa C408

        self.bn_kwargs = bn_kwargs
        self.use_conv = use_conv
        self.hw_reverse = hw_reverse

        if not self.use_conv:
            self.linear = nn.Linear(in_channels, self.units, bias=False)
            self.norm = nn.BatchNorm1d(self.units, **self.bn_kwargs)
        else:
            self.linear = nn.Conv2d(
                in_channels, self.units, kernel_size=1, bias=False
            )
            self.norm = nn.BatchNorm2d(self.units, **bn_kwargs)
            self.relu = nn.ReLU(inplace=True)
            self.max_pool = nn.MaxPool2d(
                kernel_size=pool_size, stride=pool_size
            )

    def forward(self, inputs: torch.Tensor):
        if not self.use_conv:
            x = self.linear(inputs)
            x = (
                self.norm(x.permute(0, 2, 1).contiguous())
                .permute(0, 2, 1)
                .contiguous()
            )
            x = F.relu(x)

            x_max = torch.max(x, dim=1, keepdim=True)[0]

            if self.last_vfe:
                return x_max
            else:
                x_repeat = x_max.rpeat(1, inputs.shape[1], 1)
                x_concatenated = torch.cat([x, x_repeat], dim=2)
                return x_concatenated
        else:
            x = self.linear(inputs)
            x = self.norm(x)
            x = self.relu(x)
            x_max = self.max_pool(x)
            if self.hw_reverse:
                x_max = x_max.permute(0, 2, 3, 1).contiguous()
            else:
                x_max = x_max.permute(0, 3, 2, 1).contiguous()
            return x_max

    def fuse_model(self):
        if self.use_conv:
            try:
                from horizon_plugin_pytorch import quantization

                fuser_func = quantization.fuse_known_modules
            except Warning:
                logging.warning(
                    "Please install horizon_plugin_pytorch first, "
                    "otherwise use pytorch official quantification"
                )
                from torch.quantization.fuse_modules import fuse_known_modules

                fuser_func = fuse_known_modules

            fuse_list = ["linear", "norm", "relu"]
            torch.quantization.fuse_modules(
                self,
                fuse_list,
                inplace=True,
                fuser_func=fuser_func,
            )

    def set_qconfig(
        self,
    ):
        from hat.utils import qconfig_manager

        self.qconfig = qconfig_manager.get_default_qat_qconfig()