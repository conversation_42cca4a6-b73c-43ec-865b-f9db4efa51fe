from mmdet.registry import MODELS
from mmcv.cnn import build_conv_layer, build_norm_layer, build_upsample_layer
from torch import nn
from mmengine.model import BaseModule


@MODELS.register_module()
class SurroundOccDecoder(BaseModule):
    def __init__(self, num_classes,
                 upsample_strides,
                 out_indices,
                 conv_input,
                 conv_output,
                 use_semantic=False,
                 **kwargs):
        super(SurroundOccDecoder, self).__init__(**kwargs)
        self.conv_input = conv_input
        self.conv_output = conv_output
        self.num_classes = num_classes
        self.use_semantic = use_semantic
        self.upsample_strides = upsample_strides
        self.out_indices = out_indices
        self.init_layers()

    def init_layers(self):
        # 定义相关操作算子
        self.deblocks = nn.ModuleList()
        upsample_strides = self.upsample_strides
        out_channels = self.conv_output
        in_channels = self.conv_input
        norm_cfg = dict(type='GN', num_groups=16, requires_grad=True)
        upsample_cfg = dict(type='deconv3d', bias=False)
        conv_cfg = dict(type='Conv3d', bias=False)
        for i, out_channel in enumerate(out_channels):
            stride = upsample_strides[i]
            if stride > 1:
                upsample_layer = build_upsample_layer(
                    upsample_cfg,
                    in_channels=in_channels[i],
                    out_channels=out_channel,
                    kernel_size=upsample_strides[i],
                    stride=upsample_strides[i])
            else:
                upsample_layer = build_conv_layer(
                    conv_cfg,
                    in_channels=in_channels[i],
                    out_channels=out_channel,
                    kernel_size=3,
                    stride=1,
                    padding=1)

            deblock = nn.Sequential(upsample_layer,
                                    build_norm_layer(norm_cfg, out_channel)[1],
                                    nn.ReLU(inplace=True))
            self.deblocks.append(deblock)

        self.occ = nn.ModuleList()
        for i in self.out_indices:
            if self.use_semantic:
                occ = build_conv_layer(
                    conv_cfg,
                    in_channels=out_channels[i],
                    out_channels=self.num_classes,
                    kernel_size=1,
                    stride=1,
                    padding=0)
                self.occ.append(occ)
            else:
                occ = build_conv_layer(
                    conv_cfg,
                    in_channels=out_channels[i],
                    out_channels=1,
                    kernel_size=1,
                    stride=1,
                    padding=0)
                self.occ.append(occ)
        pass

    def init_weights(self):
        """Initialize the transformer weights."""
        # 初始化相关算子权重
        pass

    def forward(self, mlvl_volumes):
        outputs = []
        # encoder 对体素特征再次编码
        result = mlvl_volumes
        for i in range(len(self.deblocks)):
            result = self.deblocks[i](result)
            if i in self.out_indices:
                outputs.append(result)
            elif i < len(self.deblocks) - 2:  # we do not add skip connection at level 0
                volume_embed_temp = mlvl_volumes.pop()
                result = result + volume_embed_temp

        # decoder 解码得到占用预测
        occ_preds = []
        for i in range(len(outputs)):
            occ_pred = self.occ[i](outputs[i])
            occ_preds.append(occ_pred)

        # outs = {
        #     'occ_preds': occ_preds,
        # }

        return occ_preds[0][:, 0]
