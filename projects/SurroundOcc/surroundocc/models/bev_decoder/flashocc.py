import torch.nn as nn
import numpy as np
from mmdet.registry import MODEL<PERSON>
from mmcv.cnn import ConvModule
from mmengine.model import BaseModule


@MODELS.register_module()
class FlashOcc(BaseModule):
    def __init__(self,
                 in_dim=256,
                 out_dim=256,
                 Dz=4,
                 num_classes=1,
                 ):
        super(FlashOcc, self).__init__()
        self.in_dim = in_dim
        self.out_dim = out_dim
        self.Dz = Dz
        self.num_classes = num_classes

        if self.Dz == 1:
            self.conv_head = nn.Sequential(
                nn.Conv2d(in_channels=256, out_channels=128, kernel_size=3, padding=1),
                nn.ReLU(),
                nn.Conv2d(in_channels=128, out_channels=64, kernel_size=3, padding=1),
                nn.ReLU(),
                nn.Conv2d(in_channels=64, out_channels=num_classes, kernel_size=1, padding=0)
            )
        else:
            out_channels = out_dim

            self.final_conv = ConvModule(
                self.in_dim,
                out_channels,
                kernel_size=3,
                stride=1,
                padding=1,
                bias=True,
                conv_cfg=dict(type='Conv2d')
            )

            self.predicter = nn.Sequential(
                nn.Linear(self.out_dim, self.out_dim * 2),
                nn.Softplus(),
                nn.Linear(self.out_dim * 2, num_classes * Dz),
            )

    def forward(self, img_feats):
        """
        Args:
            img_feats: (B, C, Dy, Dx)

        Returns:

        """

        if self.Dz == 1:
            # (B, C, Dy, Dx) --> (B, C, Dx, Dy)
            occ_pred = self.conv_head(img_feats).permute(0, 1, 3, 2)
            # (B, C, Dy, Dx) --> (B, C, Dy, Dx) --> (B, Dx, Dy, C)
            # occ_pred = self.final_conv(img_feats).permute(0, 3, 2, 1)

            # # # (B, Dx, Dy, C) --> (B, Dx, Dy, 2*C) --> (B, Dx, Dy, Dz)
            # bs, Dx, Dy = occ_pred.shape[:3]
            # occ_pred = self.predicter(occ_pred)
            # # occ_pred = occ_pred.permute(0, 3, 1, 2)
            # occ_pred = occ_pred.view(bs, self.num_classes, Dy, Dx)

        else:
            # (B, C, Dy, Dx) --> (B, C, Dy, Dx) --> (B, Dx, Dy, C)
            occ_pred = self.final_conv(img_feats).permute(0, 3, 2, 1)

            # (B, Dx, Dy, C) --> (B, Dx, Dy, 2*C) --> (B, Dx, Dy, Dz * num_clasee)
            bs, Dx, Dy = occ_pred.shape[:3]
            occ_pred = self.predicter(occ_pred)
            occ_pred = occ_pred.view(bs, Dx, Dy, self.Dz)

        return occ_pred
