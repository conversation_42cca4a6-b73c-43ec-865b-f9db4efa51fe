import torch
import torch.nn as nn
import torch.nn.functional as F

from einops import rearrange, repeat
from torchvision.models.resnet import Bottleneck
from typing import List
import numpy as np
import math
ResNetBottleNeck = lambda c: Bottleneck(c, c // 4)

from mmdet.registry import MODELS

def generate_grid(height: int, width: int):
    xs = torch.linspace(0, 1, width)
    ys = torch.linspace(0, 1, height)

    indices = torch.stack(torch.meshgrid((xs, ys), indexing='xy'), 0)       # 2 h w
    indices = F.pad(indices, (0, 0, 0, 0, 0, 1), value=1)                   # 3 h w
    indices = indices[None]                                                 # 1 3 h w

    return indices


def get_view_matrix(h=200, w=200, h_meters=100.0, w_meters=100.0, offset=0.0):
    """
    copied from ..data.common but want to keep models standalone
    """
    sh = h / h_meters
    sw = w / w_meters

    return [
        [ 0., -sw,          w/2.],
        [-sh,  0., h*offset+h/2.],
        [ 0.,  0.,            1.]
    ]


class Normalize(nn.Module):
    def __init__(self, mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]):
        super().__init__()

        self.register_buffer('mean', torch.tensor(mean)[None, :, None, None], persistent=False)
        self.register_buffer('std', torch.tensor(std)[None, :, None, None], persistent=False)

    def forward(self, x):
        return (x - self.mean) / self.std


class RandomCos(nn.Module):
    def __init__(self, *args, stride=1, padding=0, **kwargs):
        super().__init__()

        linear = nn.Conv2d(*args, **kwargs)

        self.register_buffer('weight', linear.weight)
        self.register_buffer('bias', linear.bias)
        self.kwargs = {
            'stride': stride,
            'padding': padding,
        }

    def forward(self, x):
        return torch.cos(F.conv2d(x, self.weight, self.bias, **self.kwargs))


def get_points(occupancy_range, voxel_size, min_distance=0.1):
    """
    occupancy_range: 感知范围#以房顶为中心4x4x2.9的范围，2.9表示向下0.1m到3米的范围
    voxel_size：单个格子尺寸
    """
    box_min_bound=occupancy_range[0]
    box_max_bound=occupancy_range[1]
    
    # num_x=math.ceil((-occupancy_range[0][0]-voxel_size/2)/voxel_size)+math.ceil((occupancy_range[1][0]-voxel_size/2)/voxel_size)+1
    # num_y=math.ceil((-occupancy_range[0][1]-voxel_size/2)/voxel_size)+math.ceil((occupancy_range[1][1]-voxel_size/2)/voxel_size)+1
    # num_z=math.ceil((-occupancy_range[0][2]-voxel_size/2)/voxel_size)+math.ceil((occupancy_range[1][2]-voxel_size/2)/voxel_size)+1

    # cube_resultion=(num_x,num_y,num_z)
    cube_resultion =(box_max_bound - box_min_bound+voxel_size/2) / voxel_size
    
    x_grid=np.linspace(box_min_bound[0],box_max_bound[0]-voxel_size/2.,math.ceil(cube_resultion[0]))
    y_grid=np.linspace(box_min_bound[1],box_max_bound[1]-voxel_size/2.,math.ceil(cube_resultion[1]))
    z_grid=np.linspace(box_min_bound[2],box_max_bound[2]-voxel_size/2.,math.ceil(cube_resultion[2]))
    cube_grid=np.asarray(np.meshgrid(x_grid,y_grid,z_grid)).transpose(2,1,3,0).reshape(-1,3)
    return cube_grid.astype(np.float32)


class LidarEmbedding(nn.Module):
    def __init__(
        self,
        pc_range:list,
        cube_resultion:list,
        decoder_blocks: list,
    ):
        
        super().__init__()


        min_x=pc_range[0]+(pc_range[3]-pc_range[0])/cube_resultion[0]/2
        min_y=pc_range[1]+(pc_range[4]-pc_range[1])/cube_resultion[1]/2
        min_z=pc_range[2]+(pc_range[5]-pc_range[2])/cube_resultion[2]/2
        max_x=pc_range[3]-(pc_range[3]-pc_range[0])/cube_resultion[0]/2
        max_y=pc_range[4]-(pc_range[4]-pc_range[1])/cube_resultion[1]/2
        max_z=pc_range[5]-(pc_range[5]-pc_range[2])/cube_resultion[2]/2
        
        x_grid=np.linspace(min_x,max_x,math.ceil(cube_resultion[0]))
        y_grid=np.linspace(min_y,max_y,math.ceil(cube_resultion[1]))
        z_grid=np.linspace(min_z,max_z,math.ceil(cube_resultion[2]))
        cube_grid=torch.from_numpy(np.asarray(np.meshgrid(x_grid,y_grid,z_grid))).squeeze()
        
        # 沿着第一个维度(0维)拼接这两个tensor
        ones_tensor = torch.ones(1, cube_resultion[0], cube_resultion[1])
        cube_grid = torch.cat((cube_grid, ones_tensor), dim=0).unsqueeze(0).to(torch.float32)
        self.register_buffer('lidar_grid', cube_grid, persistent=False)                    # 3 h w
        # return cube_grid.astype(np.float32)

        # egocentric frame
        





class BEVEmbedding(nn.Module):
    def __init__(
        self,
        dim: int,
        sigma: int,
        bev_height: int,
        bev_width: int,
        h_meters: int,
        w_meters: int,
        offset: int,
        decoder_blocks: list,
    ):
        """
        Only real arguments are:

        dim: embedding size
        sigma: scale for initializing embedding

        The rest of the arguments are used for constructing the view matrix.

        In hindsight we should have just specified the view matrix in config
        and passed in the view matrix...
        """
        super().__init__()

        # each decoder block upsamples the bev embedding by a factor of 2
        h = bev_height // (2 ** len(decoder_blocks))
        w = bev_width // (2 ** len(decoder_blocks))

        # bev coordinates
        grid = generate_grid(h, w).squeeze(0)
        grid[0] = bev_width * grid[0]
        grid[1] = bev_height * grid[1]

        # map from bev coordinates to ego frame
        V = get_view_matrix(bev_height, bev_width, h_meters, w_meters, offset)  # 3 3
        V_inv = torch.FloatTensor(V).inverse()                                  # 3 3
        grid = V_inv @ rearrange(grid, 'd h w -> d (h w)')                      # 3 (h w)
        grid = rearrange(grid, 'd (h w) -> d h w', h=h, w=w)                    # 3 h w

        # egocentric frame
        self.register_buffer('grid', grid, persistent=False)                    # 3 h w
        self.learned_features = nn.Parameter(sigma * torch.randn(dim, h, w))    # d h w

    def get_prior(self):
        return self.learned_features


class CrossAttention(nn.Module):
    def __init__(self, dim, heads, dim_head, qkv_bias, norm=nn.LayerNorm):
        super().__init__()

        self.scale = dim_head ** -0.5

        self.heads = heads
        self.dim_head = dim_head

        self.to_q = nn.Sequential(norm(dim), nn.Linear(dim, heads * dim_head, bias=qkv_bias))
        self.to_k = nn.Sequential(norm(dim), nn.Linear(dim, heads * dim_head, bias=qkv_bias))
        self.to_v = nn.Sequential(norm(dim), nn.Linear(dim, heads * dim_head, bias=qkv_bias))

        self.proj = nn.Linear(heads * dim_head, dim)
        self.prenorm = norm(dim)
        self.mlp = nn.Sequential(nn.Linear(dim, 2 * dim), nn.GELU(), nn.Linear(2 * dim, dim))
        self.postnorm = norm(dim)

    # def forward(self, q, k, v, skip=None):
    #     """
    #     q: (b n d H W)
    #     k: (b n d h w)
    #     v: (b n d h w)
    #     """
    #     _, _, _, H, W = q.shape

    #     # Move feature dim to last for multi-head proj
    #     q = rearrange(q, 'b n d H W -> b n (H W) d')


    #     k = rearrange(k, 'b n d h w -> b n (h w) d')

        
    #     v = rearrange(v, 'b n d h w -> b (n h w) d')
        

    #     # Project with multiple heads
    #     q = self.to_q(q)                                # b (n H W) (heads dim_head)
    #     k = self.to_k(k)                                # b (n h w) (heads dim_head)
    #     v = self.to_v(v)                                # b (n h w) (heads dim_head)

    #     # Group the head dim with batch dim
        
    #     q = rearrange(q, 'b ... (m d) -> (b m) ... d', m=self.heads, d=self.dim_head)
    #     k = rearrange(k, 'b ... (m d) -> (b m) ... d', m=self.heads, d=self.dim_head)
    #     v = rearrange(v, 'b ... (m d) -> (b m) ... d', m=self.heads, d=self.dim_head)


    #     dot = self.scale * torch.einsum('b n Q d, b n K d -> b n Q K', q, k)
    #     dot = rearrange(dot, 'b n Q K -> b Q (n K)')

        
    #     att = dot.softmax(dim=-1)

    #     # Combine values (image level features).
    #     a = torch.einsum('b Q K, b K d -> b Q d', att, v)
    #     a=torch.matmul(att, v)  
    #     a = rearrange(a, '(b m) ... d -> b ... (m d)', m=self.heads, d=self.dim_head)

    #     # Combine multiple heads
    #     z = self.proj(a)

    #     # Optional skip connection
    #     if skip is not None:
    #         z = z + rearrange(skip, 'b d H W -> b (H W) d')

    #     z = self.prenorm(z)
    #     z = z + self.mlp(z)
    #     z = self.postnorm(z)
    #     z = rearrange(z, 'b (H W) d -> b d H W', H=H, W=W)

    #     return z
    
    def forward(self, q, k, v, skip=None):
        """
        q: (b n d H W)
        k: (b n d h w)
        v: (b n d h w)
        """
        _, _, _, H, W = q.shape


        q_permuted = q.permute(0, 1, 3, 4, 2)  
        q = q_permuted.reshape(*q_permuted.shape[:2], H * W, -1)

        k_permuted = k.permute(0, 1, 3, 4, 2)  
        k = k_permuted.reshape(k_permuted.shape[0],k_permuted.shape[1],-1, k_permuted.shape[-1])
        
        v_permuted = v.permute(0, 1, 3, 4, 2)  # 现在形状为 [b, n, h, w, d]
        v = v_permuted.reshape(v_permuted.shape[0],1,-1, v_permuted.shape[-1])

        q = self.to_q(q)                                # b (n H W) (heads dim_head)
        k = self.to_k(k)                                # b (n h w) (heads dim_head)
        v = self.to_v(v)                                # b (n h w) (heads dim_head)

        q= q.reshape(self.heads,*q.shape[1:-1], self.dim_head)
        k = k.reshape(self.heads,*k.shape[1:-1], self.dim_head)
        v = v.reshape(self.heads,*v.shape[2:-1], self.dim_head)
        
 

        dot = self.scale *torch.matmul(q, k.permute(0,1,3,2))
        dot_permuted = dot.permute(0, 2, 1, 3)  # 现在形状为 [b, Q, n, K]
        dot = dot_permuted.reshape(*dot_permuted.shape[:2],-1)  # 现在形状为 [b, Q, n*K]
        

        att = dot.softmax(dim=-1)
        a=torch.matmul(att, v)
        a_permuted=a.reshape(-1,self.heads,*a.shape[1:]).permute(0,2,1,3)
        a=a_permuted.reshape(*a_permuted.shape[:2],-1)

        # Combine multiple heads
        z = self.proj(a)

        # Optional skip connection
        if skip is not None:
            skip_permuted=skip.permute(0,2,3,1)
           
            z=z+skip_permuted.reshape(skip_permuted.shape[0],-1,skip_permuted.shape[3])
            

        z = self.prenorm(z)
        z = z + self.mlp(z)
        z = self.postnorm(z)
        z_permuted=z.permute(0,2,1)
        z=z_permuted.reshape(*z_permuted.shape[:2],H,W)

        return z
    
class CrossAttention_Fusion(nn.Module):
    def __init__(self, dim, heads, dim_head, qkv_bias, norm=nn.LayerNorm):
        super().__init__()

        self.scale = dim_head ** -0.5

        self.heads = heads
        self.dim_head = dim_head

        self.to_q = nn.Sequential(norm(dim), nn.Linear(dim, heads * dim_head, bias=qkv_bias))
        self.to_k = nn.Sequential(norm(dim), nn.Linear(dim, heads * dim_head, bias=qkv_bias))
        self.to_v = nn.Sequential(norm(dim), nn.Linear(dim, heads * dim_head, bias=qkv_bias))
        self.to_kl = nn.Sequential(norm(dim), nn.Linear(dim, heads * dim_head, bias=qkv_bias))
        self.to_vl = nn.Sequential(norm(dim), nn.Linear(dim, heads * dim_head, bias=qkv_bias))
        

        self.proj = nn.Linear(heads * dim_head, dim)
        self.prenorm = norm(dim)
        self.mlp = nn.Sequential(nn.Linear(dim, 2 * dim), nn.GELU(), nn.Linear(2 * dim, dim))
        self.postnorm = norm(dim)

    
    def forward(self, q, k, v,k_l,v_l, skip=None):
        """
        q: (b n d H W)
        k: (b n d h w)
        v: (b n d h w)
        """
        _, _, _, H, W = q.shape


        q_permuted = q.permute(0, 1, 3, 4, 2)  
        q = q_permuted.reshape(*q_permuted.shape[:2], H * W, -1)#2 1 1600 128 

        k_permuted = k.permute(0, 1, 3, 4, 2)  
        k = k_permuted.reshape(k_permuted.shape[0],k_permuted.shape[1],-1, k_permuted.shape[-1])  # 2 1 192 128
        
        kl_permuted = k_l.permute(0, 1, 3, 4, 2)  
        kl = kl_permuted.reshape(kl_permuted.shape[0],kl_permuted.shape[1],-1, kl_permuted.shape[-1])#2 1 1600 128 
        
        
        v_permuted = v.permute(0, 1, 3, 4, 2)  # 现在形状为 [b, n, h, w, d]
        v = v_permuted.reshape(v_permuted.shape[0],-1, v_permuted.shape[-1])#2 192 128 
        
        vl_permuted = v_l.permute(0, 1, 3, 4, 2)  # 现在形状为 [b, n, h, w, d]
        vl = vl_permuted.reshape(vl_permuted.shape[0],-1, vl_permuted.shape[-1])#2 1600 128

        q = self.to_q(q)                                # b (n H W) (heads dim_head)
        k = self.to_k(k)                                # b (n h w) (heads dim_head)
        v = self.to_v(v)                                # b (n h w) (heads dim_head)
        kl = self.to_k(kl)                                # b (n h w) (heads dim_head)
        vl = self.to_v(vl)                                # b (n h w) (heads dim_head)
       # 2 2 100 128  2 2 192 128   2 384 128

        q_permuted= q.reshape(*q.shape[:-1],-1,self.dim_head).permute(0,3,1,2,4) 
        q=q_permuted.reshape(-1,*q_permuted.shape[2:])#8 2 100 32 
        
        k_permuted= k.reshape(*k.shape[:-1],-1,self.dim_head).permute(0,3,1,2,4) 
        k=k_permuted.reshape(-1,*k_permuted.shape[2:])#8 2 192 32 
        
        v_permuted= v.reshape(*v.shape[:-1],-1,self.dim_head).permute(0,2,1,3) 
        v=v_permuted.reshape(-1,*v_permuted.shape[2:])# #8 384 32
        
        k_l_permuted= kl.reshape(*kl.shape[:-1],-1,self.dim_head).permute(0,3,1,2,4) 
        k_l=k_l_permuted.reshape(-1,*k_l_permuted.shape[2:])#8 2 192 32 
        
        v_l_permuted= vl.reshape(*vl.shape[:-1],-1,self.dim_head).permute(0,2,1,3) 
        v_l=v_l_permuted.reshape(-1,*v_l_permuted.shape[2:])# #8 384 32
        
 

        dot = self.scale *torch.matmul(q, k.permute(0,1,3,2))
        dot_permuted = dot.permute(0, 2, 1, 3)  # 现在形状为 [b, Q, n, K]
        dot = dot_permuted.reshape(*dot_permuted.shape[:2],-1)  # 现在形状为 [b, Q, n*K]
        
        dot1 = self.scale *torch.matmul(q, k_l.permute(0,1,3,2))
        dot1_permuted = dot1.permute(0, 2, 1, 3)  # 现在形状为 [b, Q, n, K]
        dot1 = dot1_permuted.reshape(*dot1_permuted.shape[:2],-1)  # 现在形状为 [b, Q, n*K]
        

        att = dot.softmax(dim=-1)#表达的是图像中每个像素权重
        a=torch.matmul(att, v)
        
        
        att1 = dot1.softmax(dim=-1)#表达的是图像中每个像素权重
        a1=torch.matmul(att1, v_l)
        
        
        a_permuted=a.reshape(-1,self.heads,*a.shape[1:]).permute(0,2,1,3)
        a=a_permuted.reshape(*a_permuted.shape[:2],-1)
        
        a1_permuted=a.reshape(-1,self.heads,*a1.shape[1:]).permute(0,2,1,3)
        a1=a1_permuted.reshape(*a1_permuted.shape[:2],-1)

        
        # Combine multiple heads
        z = self.proj(a+a1)

        # Optional skip connection
        if skip is not None:
            skip_permuted=skip.permute(0,2,3,1)
           
            z=z+skip_permuted.reshape(skip_permuted.shape[0],-1,skip_permuted.shape[3])
            

        z = self.prenorm(z)
        z = z + self.mlp(z)
        z = self.postnorm(z)
        z_permuted=z.permute(0,2,1)
        z=z_permuted.reshape(*z_permuted.shape[:2],H,W)

        return z    


class CrossViewAttention_Fusion(nn.Module):
    def __init__(
        self,
        feat_height: int,
        feat_width: int,
        feat_dim: int,
        dim: int,
        lidar_dim:int,
        image_height: int,
        image_width: int,
        qkv_bias: bool,
        heads: int = 4,
        dim_head: int = 32,
        no_image_features: bool = False,
        skip: bool = True,
    ):
        super().__init__()

        # 1 1 3 h w
        image_plane = generate_grid(feat_height, feat_width)[None]
        image_plane[:, :, 0] *= image_width
        image_plane[:, :, 1] *= image_height

        self.register_buffer('image_plane', image_plane, persistent=False)

        self.feature_linear = nn.Sequential(
            nn.BatchNorm2d(feat_dim),
            nn.ReLU(),
            nn.Conv2d(feat_dim, dim, 1, bias=False))
        
        self.lidar_feature_linear = nn.Sequential(
            nn.BatchNorm2d(lidar_dim),
            nn.ReLU(),
            nn.Conv2d(lidar_dim, dim, 1, bias=False))

        # if no_image_features:
        #     self.feature_proj = None
        # else:
        self.feature_proj = nn.Sequential(
            nn.BatchNorm2d(feat_dim),
            nn.ReLU(),
            nn.Conv2d(feat_dim, dim, 1, bias=False))
        
        self.lidar_feature_proj = nn.Sequential(
            nn.BatchNorm2d(lidar_dim),
            nn.ReLU(),
            nn.Conv2d(lidar_dim, dim, 1, bias=False))
        self.lidar_embed = nn.Conv2d(4, lidar_dim, 1, bias=False)

        self.bev_embed = nn.Conv2d(2, dim, 1)
        self.img_embed = nn.Conv2d(4, dim, 1, bias=False)
        self.cam_embed = nn.Conv2d(4, dim, 1, bias=False)

        self.cross_attend = CrossAttention_Fusion(dim, heads, dim_head, qkv_bias)
        self.skip = skip
        

    def forward(
        self,
        x: torch.FloatTensor,
        bev: BEVEmbedding,
        lidar:LidarEmbedding,
        feature: torch.FloatTensor,
        lidar_feature: torch.FloatTensor,
        I_inv: torch.FloatTensor,
        E_inv: torch.FloatTensor,
    ):
        """
        x: (b, c, H, W)
        feature: (b, n, dim_in, h, w)
        I_inv: (b, n, 3, 3)
        E_inv: (b, n, 4, 4)

        Returns: (b, d, H, W)
        """
        b, n, _, _ ,_= feature.shape

        pixel = self.image_plane                                                # b n 3 h w
        _, _, _, h, w = pixel.shape

        c = E_inv[..., -1:]                                                     # b n 4 1
        
        # c_flat = rearrange(c, 'b n ... -> (b n) ...')[..., None]                # (b n) 4 1 1
        c_flat = c.reshape(b*n,4,1,1)              # (b n) 4 1 1
        
        c_embed = self.cam_embed(c_flat)                                        # (b n) d 1 1

        # pixel_flat = rearrange(pixel, '... h w -> ... (h w)')                   # 1 1 3 (h w)
        pixel_flat =pixel.reshape(1,1,3,h*w)              # 1 1 3 (h w)
        
        cam = I_inv @ pixel_flat                                                # b n 3 (h w)
        cam = F.pad(cam, (0, 0, 0, 1, 0, 0, 0, 0), value=1)                     # b n 4 (h w)
        d = E_inv @ cam                                                         # b n 4 (h w)
        
        # d_flat = rearrange(d, 'b n d (h w) -> (b n) d h w', h=h, w=w)           # (b n) 4 h w
        d_flat =d.reshape(b*n,4,h,w)           # (b n) 4 h w
        
        d_embed = self.img_embed(d_flat)                                        # (b n) d h w

        img_embed = d_embed - c_embed                                           # (b n) d h w
        img_embed = img_embed / (img_embed.norm(dim=1, keepdim=True) + 1e-7)    # (b n) d h w

        world = bev.grid[:2]                                                    # 2 H W
        w_embed = self.bev_embed(world[None])                                   # 1 d H W
        bev_embed = w_embed - c_embed                                           # (b n) d H W
        bev_embed = bev_embed / (bev_embed.norm(dim=1, keepdim=True) + 1e-7)    # (b n) d H W
        
        # query_pos = rearrange(bev_embed, '(b n) ... -> b n ...', b=b, n=n)      # b n d H W
        query_pos = bev_embed.reshape(b,n,-1,world.shape[1],world.shape[1])    # b n d H W

        # feature_flat = rearrange(feature, 'b n ... -> (b n) ...')               # (b n) d h w
        feature_flat =feature.reshape(b*n,-1,h,w)            # (b n) d h w

        # if self.feature_proj is not None:
        key_flat = img_embed + self.feature_proj(feature_flat)              # (b n) d h w
        # else:
        #     key_flat = img_embed                                                # (b n) d h w
        
        val_flat = self.feature_linear(feature_flat)                            # (b n) d h w
        
        
        #lidar：
        
        lidar_world = lidar.lidar_grid.repeat(b, 1, 1,1)
        _,_,h_l,w_l=lidar_world.shape
        
        d1_embed=self.lidar_embed(lidar_world)
        
        lidar_embed=d1_embed-c_embed
        lidar_embed = lidar_embed / (lidar_embed.norm(dim=1, keepdim=True) + 1e-7)    # (b n) d h w
    
        key_lidar_flat =lidar_embed + self.lidar_feature_proj(lidar_feature)              # (b n) d h w
        val_lidar_flat = self.lidar_feature_linear(lidar_feature)                            # (b n) d h w
        
        
        # Expand + refine the BEV embedding
        query = query_pos + x[:, None]                                          # b n d H W
        
        # key = rearrange(key_flat, '(b n) ... -> b n ...', b=b, n=n)             # b n d h w
        key = key_flat.reshape(b,n,-1,h,w)        # b n d h w
        
        # val = rearrange(val_flat, '(b n) ... -> b n ...', b=b, n=n)             # b n d h w
        val =val_flat.reshape(b,n,-1,h,w)           # b n d h w
        
        key_lidar_flat= key_lidar_flat.reshape(b,n,-1,h_l,w_l)
        
        val_lidar_flat= val_lidar_flat.reshape(b,n,-1,h_l,w_l)

        return self.cross_attend(query, key, val,key_lidar_flat, val_lidar_flat,skip=x if self.skip else None)

class CrossViewAttention(nn.Module):
    def __init__(
        self,
        feat_height: int,
        feat_width: int,
        feat_dim: int,
        dim: int,
        image_height: int,
        image_width: int,
        qkv_bias: bool,
        heads: int = 4,
        dim_head: int = 32,
        no_image_features: bool = False,
        skip: bool = True,
    ):
        super().__init__()

        # 1 1 3 h w
        image_plane = generate_grid(feat_height, feat_width)[None]
        image_plane[:, :, 0] *= image_width
        image_plane[:, :, 1] *= image_height

        self.register_buffer('image_plane', image_plane, persistent=False)

        self.feature_linear = nn.Sequential(
            nn.BatchNorm2d(feat_dim),
            nn.ReLU(),
            nn.Conv2d(feat_dim, dim, 1, bias=False))
        
        # self.lidar_feature_linear = nn.Sequential(
        #     nn.BatchNorm2d(feat_dim),
        #     nn.ReLU(),
        #     nn.Conv2d(feat_dim, dim, 1, bias=False))

        # if no_image_features:
        #     self.feature_proj = None
        # else:
        self.feature_proj = nn.Sequential(
            nn.BatchNorm2d(feat_dim),
            nn.ReLU(),
            nn.Conv2d(feat_dim, dim, 1, bias=False))
        
        # self.lidar_feature_proj = nn.Sequential(
        #     nn.BatchNorm2d(feat_dim),
        #     nn.ReLU(),
        #     nn.Conv2d(feat_dim, dim, 1, bias=False))

        self.bev_embed = nn.Conv2d(2, dim, 1)
        self.img_embed = nn.Conv2d(4, dim, 1, bias=False)
        self.cam_embed = nn.Conv2d(4, dim, 1, bias=False)

        self.cross_attend = CrossAttention(dim, heads, dim_head, qkv_bias)
        self.skip = skip

    def forward(
        self,
        x: torch.FloatTensor,
        bev: BEVEmbedding,
        feature: torch.FloatTensor,
        # lidar_feature: torch.FloatTensor,
        I_inv: torch.FloatTensor,
        E_inv: torch.FloatTensor,
    ):
        """
        x: (b, c, H, W)
        feature: (b, n, dim_in, h, w)
        I_inv: (b, n, 3, 3)
        E_inv: (b, n, 4, 4)

        Returns: (b, d, H, W)
        """
        b, n, _, _, _ = feature.shape

        pixel = self.image_plane                                                # b n 3 h w
        _, _, _, h, w = pixel.shape

        c = E_inv[..., -1:]                                                     # b n 4 1
        
        # c_flat = rearrange(c, 'b n ... -> (b n) ...')[..., None]                # (b n) 4 1 1
        c_flat = c.reshape(b*n,4,1,1)              # (b n) 4 1 1
        
        c_embed = self.cam_embed(c_flat)                                        # (b n) d 1 1

        # pixel_flat = rearrange(pixel, '... h w -> ... (h w)')                   # 1 1 3 (h w)
        pixel_flat =pixel.reshape(1,1,3,h*w)              # 1 1 3 (h w)
        
        cam = I_inv @ pixel_flat                                                # b n 3 (h w)
        cam = F.pad(cam, (0, 0, 0, 1, 0, 0, 0, 0), value=1)                     # b n 4 (h w)
        d = E_inv @ cam                                                         # b n 4 (h w)
        
        # d_flat = rearrange(d, 'b n d (h w) -> (b n) d h w', h=h, w=w)           # (b n) 4 h w
        d_flat =d.reshape(b*n,4,h,w)           # (b n) 4 h w
        
        d_embed = self.img_embed(d_flat)                                        # (b n) d h w

        img_embed = d_embed - c_embed                                           # (b n) d h w
        img_embed = img_embed / (img_embed.norm(dim=1, keepdim=True) + 1e-7)    # (b n) d h w

        world = bev.grid[:2]                                                    # 2 H W
        w_embed = self.bev_embed(world[None])                                   # 1 d H W
        bev_embed = w_embed - c_embed                                           # (b n) d H W
        bev_embed = bev_embed / (bev_embed.norm(dim=1, keepdim=True) + 1e-7)    # (b n) d H W
        
        # query_pos = rearrange(bev_embed, '(b n) ... -> b n ...', b=b, n=n)      # b n d H W
        query_pos = bev_embed.reshape(b,n,-1,world.shape[1],world.shape[1])    # b n d H W

        # feature_flat = rearrange(feature, 'b n ... -> (b n) ...')               # (b n) d h w
        feature_flat =feature.reshape(b*n,-1,h,w)            # (b n) d h w

        # if self.feature_proj is not None:
        key_flat = img_embed + self.feature_proj(feature_flat)              # (b n) d h w
        # else:
        #     key_flat = img_embed                                                # (b n) d h w
        
        
        val_flat = self.feature_linear(feature_flat)                            # (b n) d h w
        
    

        # Expand + refine the BEV embedding
        query = query_pos + x[:, None]                                          # b n d H W
        
        # key = rearrange(key_flat, '(b n) ... -> b n ...', b=b, n=n)             # b n d h w
        key = key_flat.reshape(b,n,-1,h,w)        # b n d h w
        
        # val = rearrange(val_flat, '(b n) ... -> b n ...', b=b, n=n)             # b n d h w
        val =val_flat.reshape(b,n,-1,h,w)           # b n d h w

        return self.cross_attend(query, key, val, skip=x if self.skip else None)

class DecoderBlock(torch.nn.Module):
    def __init__(self, in_channels, out_channels, skip_dim, residual, factor):
        super().__init__()

        dim = out_channels // factor

        self.conv = nn.Sequential(
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True),
            nn.Conv2d(in_channels, dim, 3, padding=1, bias=False),
            nn.BatchNorm2d(dim),
            nn.ReLU(inplace=True),
            nn.Conv2d(dim, out_channels, 1, padding=0, bias=False),
            nn.BatchNorm2d(out_channels))

        if residual:
            self.up = nn.Conv2d(skip_dim, out_channels, 1)
        else:
            self.up = None

        self.relu = nn.ReLU(inplace=True)

    def forward(self, x, skip):
        x = self.conv(x)

        if self.up is not None:
            up = self.up(skip)
            up = F.interpolate(up, x.shape[-2:])

            x = x + up

        return self.relu(x)


class Decoder(nn.Module):
    def __init__(self, dim, blocks, residual=True, factor=2):
        super().__init__()

        layers = list()
        channels = dim

        for out_channels in blocks:
            layer = DecoderBlock(channels, out_channels, dim, residual, factor)
            layers.append(layer)

            channels = out_channels

        self.layers = nn.Sequential(*layers)
        self.out_channels = channels

    def forward(self, x):
        y = x

        for layer in self.layers:
            y = layer(y, x)

        return y



@MODELS.register_module()
class CVT_Vision(nn.Module):
    def __init__(
            self,
            dim_last:int,
            cross_view: dict,
            bev_embedding: dict,
            decoder:dict,
            dim: int = 128,
            middle: List[int] = [2, 2],
            scale: float = 1.0,
            output_shapes:List[int] =[ [1,3,2, 2],[1,3,2, 2]],
            depoly=False,
            ):
        super().__init__()
        self.dim_last=dim_last
        self.norm = Normalize()
        self.output_shapes=output_shapes

        if scale < 1.0:
            self.down = lambda x: F.interpolate(x, scale_factor=scale, recompute_scale_factor=False)
        else:
            self.down = lambda x: x

        cross_views = list()
        layers = list()

        for feat_shape, num_layers in zip(self.output_shapes, middle):
            _, feat_dim, feat_height, feat_width = self.down(torch.zeros(feat_shape)).shape

            cva = CrossViewAttention(feat_height, feat_width, feat_dim, dim, **cross_view)
            cross_views.append(cva)

            layer = nn.Sequential(*[ResNetBottleNeck(dim) for _ in range(num_layers)])
            layers.append(layer)

        self.bev_embedding = BEVEmbedding(dim, **bev_embedding)
        self.cross_views = nn.ModuleList(cross_views)
        self.layers = nn.ModuleList(layers)
        self.decoder = Decoder( **decoder)
        self.to_logits = nn.Sequential(
            nn.Conv2d(self.decoder.out_channels, self.dim_last, 3, padding=1, bias=False),
            nn.BatchNorm2d(self.dim_last),
            nn.ReLU(inplace=True),
            nn.Conv2d(self.dim_last, self.dim_last, 1))
        if depoly:
            self.E_inv = torch.from_numpy(np.load('projects/SurroundOcc/numpyfile/stereo_cvt/stereo_E_inv.npy'))  # fpoints
            self.I_inv = torch.from_numpy(np.load('projects/SurroundOcc/numpyfile/stereo_cvt/stereo_I_inv.npy'))  # fpoints


    def forward(self, features,n,data_samples,compile_model=True,**kwargs):
        
        if compile_model is True:
            b=1
            I_inv=self.I_inv[:,0:,:,:]
            E_inv=self.E_inv[:,0:1,:,:]
        else:
            b = len(data_samples)
            I_inv,E_inv = [],[]
            for i in range(b):
                I_inv.append(data_samples[i].cam_intrinsic)  
                E_inv.append(data_samples[i].lidar2cam)  
            I_inv = torch.from_numpy(np.asarray(I_inv).reshape(-1, 4, 4)).cuda().inverse().unsqueeze(1).type(torch.float32)[:,:,:3,:3]
            E_inv = torch.from_numpy(np.asarray(E_inv).reshape(-1, 4, 4)).cuda().inverse().unsqueeze(1).type(torch.float32)
            I_inv=I_inv.reshape(b,n,3,3)
            E_inv=E_inv.reshape(b,n,4,4)
            #np.save('stereo_E_inv.npy',E_inv.cpu().numpy())
            #
            
        x = self.bev_embedding.get_prior()  #初始化的可学习参数，作为初始bev特征# d H W
        x=x.unsqueeze(0)
        # x = repeat(x, '... -> b ...', b=b)              # b d H W
        
        for cross_view, feature, layer in zip(self.cross_views, features, self.layers):
            # feature = rearrange(feature, '(b n) ... -> b n ...', b=b, n=n)
            feature=feature.unsqueeze(0)
            x = cross_view(x, self.bev_embedding, feature, I_inv, E_inv)
            x = layer(x)
            
        # y = self.decoder(x)
        # z = self.to_logits(x)
        
        return x
    
    
    
@MODELS.register_module()
class CVT_Fusion(nn.Module):
    def __init__(
            self,
            dim_last:int,
            cross_view: dict,
            bev_embedding: dict,
            lidar_embedding:dict,
            decoder:dict,
            dim: int = 128,
            lidar_dim:int =128,
            middle: List[int] = [2, 2],
            scale: float = 1.0,
            output_shapes:List[int] =[ [1,3,2, 2],[1,3,2, 2]],
            depoly=False,
            ):
        super().__init__()
        self.dim_last=dim_last
        self.norm = Normalize()
        self.output_shapes=output_shapes
        self.lidar_dim=lidar_dim
        
        if scale < 1.0:
            self.down = lambda x: F.interpolate(x, scale_factor=scale, recompute_scale_factor=False)
        else:
            self.down = lambda x: x

        cross_views = list()
        layers = list()

        for feat_shape, num_layers in zip(self.output_shapes, middle):
            _, feat_dim, feat_height, feat_width = self.down(torch.zeros(feat_shape)).shape
            
            cva = CrossViewAttention_Fusion(feat_height, feat_width, feat_dim, self.lidar_dim,dim, **cross_view)
            cross_views.append(cva)

            layer = nn.Sequential(*[ResNetBottleNeck(dim) for _ in range(num_layers)])
            layers.append(layer)

        self.bev_embedding = BEVEmbedding(dim, **bev_embedding)
        self.lidar_embedding = LidarEmbedding( **lidar_embedding)
        
        self.cross_views = nn.ModuleList(cross_views)
        self.layers = nn.ModuleList(layers)
        self.decoder = Decoder( **decoder)
        self.to_logits = nn.Sequential(
            nn.Conv2d(self.decoder.out_channels, self.dim_last, 3, padding=1, bias=False),
            nn.BatchNorm2d(self.dim_last),
            nn.ReLU(inplace=True),
            nn.Conv2d(self.dim_last, self.dim_last, 1))
        if depoly:
            self.E_inv = torch.from_numpy(np.load('projects/SurroundOcc/numpyfile/stereo_cvt/stereo_E_inv.npy'))  # fpoints
            self.I_inv = torch.from_numpy(np.load('projects/SurroundOcc/numpyfile/stereo_cvt/stereo_I_inv.npy'))  # fpoints


    def forward(self, features,lidar_features,n,data_samples,compile_model=True,**kwargs):
        
        if compile_model is True:
            b=1
            I_inv=self.I_inv[:,0:,:,:]
            E_inv=self.E_inv[:,0:1,:,:]
        else:
            b = len(data_samples)
            I_inv,E_inv = [],[]
            for i in range(b):
                I_inv.append(data_samples[i].cam_intrinsic)  
                E_inv.append(data_samples[i].lidar2cam)  
            I_inv = torch.from_numpy(np.asarray(I_inv).reshape(-1, 4, 4)).cuda().inverse().unsqueeze(1).type(torch.float32)[:,:,:3,:3]
            E_inv = torch.from_numpy(np.asarray(E_inv).reshape(-1, 4, 4)).cuda().inverse().unsqueeze(1).type(torch.float32)
            I_inv=I_inv.reshape(b,n,3,3)
            E_inv=E_inv.reshape(b,n,4,4)
            #np.save('stereo_E_inv.npy',E_inv.cpu().numpy())
            #
            
        x = self.bev_embedding.get_prior()  #初始化的可学习参数，作为初始bev特征# d H W
        x=x.unsqueeze(0)
        # x = repeat(x, '... -> b ...', b=b)              # b d H W
        
        for cross_view, feature, lidar_feature,layer in zip(self.cross_views, features,lidar_features, self.layers):
            # feature = rearrange(feature, '(b n) ... -> b n ...', b=b, n=n)
            feature=feature.reshape(b,n,*feature.shape[1:])
            x = cross_view(x, self.bev_embedding,self.lidar_embedding, feature,lidar_feature, I_inv, E_inv)
            x = layer(x)
            
        y = self.decoder(x)
        z = self.to_logits(y)
        
        return z   
    
