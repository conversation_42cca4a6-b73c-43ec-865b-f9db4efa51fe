from typing import Tuple
import collections
from collections import Sequence

import horizon_plugin_pytorch.nn as hnn

import numpy as np
import torch
import torch.nn as nn
import math
from horizon_plugin_pytorch.dtype import qint16
from horizon_plugin_pytorch.nn.quantized import FloatFunctional
from horizon_plugin_pytorch.quantization import QuantStub
from hat.models.base_modules.conv_module import ConvModule2d
from hat.utils.model_helpers import fx_wrap
from hat.core.nus_box3d_utils import adjust_coords, get_min_max_coords

try:
    from hbdk.torch_script.placeholder import placeholder
except Exception:
    placeholder = None

from mmdet.registry import MODELS


class ViewTransformer(nn.Module):
    """The view transform structure for converting image view to bev view.

    Args:
        num_views: Number for image views.
        bev_size: Bev size.
        grid_size: Grid size.
        mode: Mode for grid sample.
        padding_mode: Padding mode for grid sample.
        grid_quant_scale: Quanti scale for grid sample.

    """

    def __init__(
            self,
            num_views: int,
            bev_size: Tuple[float],
            grid_size: Tuple[float],
            mode: str = "bilinear",
            padding_mode: str = "zeros",
            grid_quant_scale: float = 1 / 512,
    ):
        super(ViewTransformer, self).__init__()
        self.num_views = num_views
        self.bev_size = bev_size
        self.grid_size = grid_size
        self.floatFs = FloatFunctional()

        self.quant_stub = QuantStub(grid_quant_scale)
        self.grid_sample = hnn.GridSample(
            mode=mode,
            padding_mode=padding_mode,
        )

    def _gen_2d_points(self):
        bev_min_x, bev_max_x, bev_min_y, bev_max_y = get_min_max_coords(
            self.bev_size
        )

        W = self.grid_size[0]
        H = self.grid_size[1]
        x = (
            torch.linspace(bev_min_x, bev_max_x, W)
            .reshape((1, W))
            .repeat(H, 1)
        ).double()
        y = (
            torch.linspace(bev_min_y, bev_max_y, H)
            .reshape((H, 1))
            .repeat(1, W)
        ).double()

        ones = torch.ones((H, W)).double()
        coords = torch.stack([x, y, ones], dim=-1)
        return coords

    def get_min_max_coords(self, bev_size, ):
        min_x = bev_size[0] + bev_size[4] / 2
        max_x = bev_size[1] - bev_size[4] / 2
        min_y = bev_size[2] + bev_size[4] / 2
        max_y = bev_size[3] - bev_size[4] / 2
        return min_x, max_x, min_y, max_y

    def _gen_3d_points(self, z_range):
        bev_min_x, bev_max_x, bev_min_y, bev_max_y = self.get_min_max_coords(
            self.bev_size
        )

        W = self.grid_size[0]
        H = self.grid_size[1]

        # math.ceil() 向上取整，保证 BEV 任务中，Z=2
        Z = math.ceil((z_range[1] - z_range[0]) / self.bev_size[4])

        x = (
            torch.linspace(bev_min_x, bev_max_x, W)
            .reshape((1, W, 1))
            .repeat(H, 1, Z)
        ).double()
        y = (
            torch.linspace(bev_min_y, bev_max_y, H)
            .reshape((H, 1, 1))
            .repeat(1, W, Z)
        ).double()

        # z 没有考虑在每个格子的正中央的情况
        z = (
            torch.linspace(self.z_range[0], self.z_range[1], Z)
            .reshape((1, 1, Z))
            .repeat(H, W, 1)
        ).double()  # error

        ones = torch.ones((H, W, Z)).double()
        coords = torch.stack([x, y, z, ones], dim=-1)   # (X=80, Y=80, Z=4, 4)
        return coords

    def _convert_p2tensor(self, points):
        if isinstance(points, collections.Sequence):
            for i in range(len(points)):
                points[i] = self._convert_p2tensor(points[i])
        if placeholder is not None and isinstance(points, placeholder):
            points = points.sample
        return points

    def gen_reference_point(self, meta, feats):
        with torch.no_grad():
            homography = self._get_homography(meta, feats.shape[2:])    # 左右目的 lidar2img * scale
            points = self._gen_reference_point(homography, feats.shape[2:])
        return points

    def export_reference_points(self, meta, feats_wh):
        homography = self._get_homography(meta, feats_wh)
        points = self._gen_reference_point(homography, feats_wh)
        if not isinstance(points, Sequence):
            points = [points]

        ref_p_dict = {}
        for i, ref_p in enumerate(points):
            ref_p_dict[f"reference_points{i}"] = ref_p
        return ref_p_dict

    @fx_wrap()
    def forward(self, feats, meta, compile_model=True, **kwargs):
        feats = self._extract(feats)    # (B*N=8, C=128, H=24, W=32)
        if compile_model is True:
            # points = self._get_points_from_meta(meta)
            points = self.points
        else:
            with torch.no_grad():
                points = self.gen_reference_point(meta, feats)
        return self._spatial_transfom(feats, points)

    def _extract(self, feats):
        return feats

    # def _get_homography(self, meta, feat_hw):

    #     homography = []

    #     homography.append(np.load('lidar2img1.npy'))
    #     homography.append(np.load('lidar2img2.npy'))

    #     homography = torch.from_numpy(np.asarray(homography).reshape(-1, 4, 4))#

    #     orig_hw=(384,512,3)
    #     scales = (feat_hw[0] / orig_hw[0], feat_hw[1] / orig_hw[1])
    #     view = np.eye(4)
    #     view[0, 0] = scales[1]
    #     view[1, 1] = scales[0]

    #     view = torch.tensor(view).double()
    #     homography = torch.matmul(view, homography.double())
    #     return homography

    def _get_homography(self, meta, feat_hw):
        num = len(meta)

        homography = []
        for i in range(num):
            homography.append(meta[i].lidar2img)  # left2left和left2right,即Tll和Trl

        homography = np.asarray(homography).reshape(-1, 4, 4)
        homography = torch.from_numpy(homography).cuda().double()

        orig_hw = meta[0].img_shape[0]

        scales = (feat_hw[0] / orig_hw[0], feat_hw[1] / orig_hw[1])
        view = np.eye(4)
        view[0, 0] = scales[1]
        view[1, 1] = scales[0]
        view = torch.tensor(view).cuda().double()
        homography = torch.matmul(view, homography)
        return homography

    def set_qconfig(self):
        from hat.utils import qconfig_manager

        self.qconfig = qconfig_manager.get_default_qat_qconfig()

        self.quant_stub.qconfig = qconfig_manager.get_qconfig(
            activation_qat_qkwargs={"dtype": qint16, "saturate": True},
            activation_calibration_qkwargs={"dtype": qint16, "saturate": True},
        )


@MODELS.register_module()
class LSSTransformer(ViewTransformer):
    """The Lift-Splat-Shoot view transform for converting image view to bev view.

    Args:
        in_channels: In channel of feature.
        feat_channels: Feature channel of lift.
        z_range: The range of Z for bev coordarin.
        num_points: Num points for each voxel.
        depth: Depth value.
        mode: Mode for grid sample.
        padding_mode: Padding mode for grid sample.
        dgrid_quant_scale: Quanti scale for depth grid sample.

    """

    def __init__(
            self,
            in_channels: int,
            feat_channels: int,
            z_range: Tuple[float] = (-10.0, 10.0),
            num_points: int = 10,
            depth: int = 60,
            mode: str = "bilinear",
            padding_mode: str = "zeros",
            depth_grid_quant_scale: float = 1 / 512,
            depoly=False,
            **kwargs,
    ):
        super(LSSTransformer, self).__init__(
            mode=mode, padding_mode=padding_mode, **kwargs
        )
        self.depth = depth
        self.z_range = z_range
        self.depth_net = ConvModule2d(
            in_channels=in_channels,
            out_channels=depth,
            kernel_size=1,
            padding=0,
            stride=1,
            bias=False,
        )
        self.feat_net = ConvModule2d(
            in_channels=in_channels,
            out_channels=feat_channels,
            kernel_size=1,
            padding=0,
            stride=1,
            bias=False,
        )
        self.softmax = nn.Softmax(dim=1)
        self.dquant_stub = QuantStub(depth_grid_quant_scale)
        self.dgrid_sample = hnn.GridSample(
            mode=mode,
            padding_mode=padding_mode,
        )
        self.num_points = num_points

        if depoly:
            # self.points = [
            #     torch.from_numpy(np.load('projects/SurroundOcc/numpyfile/stereo_fpoints.npy')).cuda(),  # fpoints
            #     torch.from_numpy(np.load('projects/SurroundOcc/numpyfile/stereo_dpoints.npy')).cuda()  # dpoints
            # ]
            # 导出onnx时不需要放在cuda上
            self.points = [
                torch.from_numpy(np.load('projects/SurroundOcc/numpyfile/0514_fpoints_stereo_occ_npts8.npy')),
                torch.from_numpy(np.load('projects/SurroundOcc/numpyfile/0514_dpoints_stereo_occ_npts8.npy'))
            ]
        else:
            self.points = [
                # stereo
                torch.from_numpy(np.load('projects/SurroundOcc/numpyfile/0514_fpoints_stereo_occ_npts8.npy')).cuda(),
                torch.from_numpy(np.load('projects/SurroundOcc/numpyfile/0514_dpoints_stereo_occ_npts8.npy')).cuda()
            ]

    def _get_points_from_meta(self, meta):
        points = []
        for k in meta.keys():
            if k.startswith("points"):
                points.append(self._convert_p2tensor(meta[k]))
        assert len(points) == 2
        return points

    def gen_reference_point(self, meta, feats):
        return super().gen_reference_point(meta, feats[0])

    def _gen_reference_point(self, homography, feats_hw):

        # 根据预定义的栅格尺寸以及考虑格子中心位置，创建坐标
        coords = self._gen_3d_points(self.z_range).to(device=homography.device)  # (H=80, W=80, Z=4, [x, y, z, ones])
        H, W, Z = coords.shape[:3]
        new_coords = []

        # coords 转换
        for homo in homography:
            new_coord = torch.matmul(coords, homo.permute((1, 0))).float()
            new_coord = new_coord.permute((2, 0, 1, 3))
            new_coords.append(new_coord)
        new_coords = torch.stack(new_coords, dim=1)  # (Z=4, B=4, H=80, W=80, [x, y, z, ones])
        B = new_coords.shape[1] // self.num_views

        new_coords = (
            new_coords.view(-1, B, self.num_views, H, W, 4)  # (Z=4, B=4, N=2, H=80, W=80, [x, y, z, ones])
            .permute(0, 2, 1, 3, 4, 5)  # (Z=4, N=2, B=4, H=80, W=80, [x, y, z, ones])
            .contiguous()
        )

        # 归一化坐标 (Z=4, N=2, B=8, H=80, W=80)
        d = torch.clamp(new_coords[..., 2], min=0.025)  # 将z的最小值设置为0.025, 避免除以零
        X = (new_coords[..., 0] / d).long()
        Y = (new_coords[..., 1] / d).long()
        D = (new_coords[..., 2] / 0.05).long()

        # 相机索引
        idx = (
            (
                torch.linspace(0, self.num_views - 1, self.num_views)   # [0, 1]
                .reshape((1, self.num_views, 1, 1, 1))  # (1, N=2, 1, 1, 1)
                .repeat(Z, 1, B, H, W)  # (Z=4, N=2, B=8, H=80, W=80)
            )
            .long()
            .to(device=homography.device)
        )
        new_coords = torch.stack([X, Y, D, idx], dim=-1)  # (Z=4, N=2, B=8, H=80, W=80, [X, Y, D, idx])

        feat_h, feat_w = feats_hw
        invalid = (
                (new_coords[..., 0] < 0)
                | (new_coords[..., 0] >= feat_w)
                | (new_coords[..., 1] < 0)
                | (new_coords[..., 1] >= feat_h)
                | (new_coords[..., 2] < 0)
                | (new_coords[..., 2] >= 120)
        )

        new_coords[invalid] = torch.tensor(
            (feat_w - 1, feat_h - 1, self.depth, self.num_views - 1)
        ).to(device=homography.device)
        new_coords = new_coords.view(-1, B, H, W, 4)  # (Z*N=8, B=8, H=80, W=80, [X, Y, Z, idx])
        rank = (
                new_coords[..., 2] * feat_h * feat_w * self.num_views
                + new_coords[..., 1] * feat_w * self.num_views
                + new_coords[..., 0] * self.num_views
                + new_coords[..., 3]
        )  # (Z*N=8, B=8, H=80, W=80)
        rank, _ = rank.topk(self.num_points, dim=0, largest=False)  # (Z*N=8, B=8, H=80, W=80)
        D = rank // (feat_h * feat_w * self.num_views)
        rank = rank % (feat_h * feat_w * self.num_views)

        Y = rank // (feat_w * self.num_views)
        rank = rank % (feat_w * self.num_views)

        X = rank // self.num_views
        idx = rank % self.num_views

        idx_Y = idx * feat_h + Y
        feat_coords = torch.stack((X, idx_Y), dim=-1)  # (Z*N=8, B=8, H=80, W=80, 2)
        feat_points = adjust_coords(feat_coords, self.grid_size)  # (Z*N=8, B=8, H=80, W=80, 2)

        X_Y = Y * feat_w + X
        idx_D = idx * self.depth + D
        depth_coords = torch.stack((X_Y, idx_D), dim=-1)  # (Z*N=8, B=8, H=80, W=80, 2)
        depth_points = adjust_coords(depth_coords, self.grid_size)  # (Z*N=8, B=8, H=80, W=80, 2)
        feat_points = feat_points.view(-1, H, W, 2)  # (Z*N*B=64, H=80, W=80, 2)
        depth_points = depth_points.view(-1, H, W, 2)  # (Z*N*B=64, H=80, W=80, 2)

        return (feat_points, depth_points)

    def _extract(self, feats):
        new_feats = []  # feats (B*N=8, C=128, H=24, W=32)
        depth = self.softmax(self.depth_net(feats))
        new_feats = self.feat_net(feats)
        return new_feats, depth

    def _spatial_transfom(self, feats, points):

        # feat: (B*N=16, C=128, H=24, W=32)     dfeat: (B*N=16, D=120, H=24, W=32)
        # points: (Z*N*B=64, H=80, W=80, 2)
        feat, dfeat = feats
        fpoints, dpoints = points

        # 保存 points
        # print(fpoints.shape)
        # print(dpoints.shape)
        # np.save("projects/SurroundOcc/numpyfile/0514_fpoints_stereo_occ_npts8.npy", fpoints.cpu().numpy())
        # np.save("projects/SurroundOcc/numpyfile/0514_dpoints_stereo_occ_npts8.npy", dpoints.cpu().numpy())
        # exit()

        # fpoints = self.quant_stub(fpoints)
        # dpoints = self.dquant_stub(dpoints)

        B = feat.shape[0] // self.num_views
        C, H, W = feat.shape[1:]
        # num,dx,dy,xy=fpoints.shape
        # fpoints=torch.stack([fpoints for _ in range(B)],dim=0)

        # fpoints=fpoints.view(B*num,dx,dy,xy)
        # dpoints=torch.stack([dpoints for _ in range(B)],dim=0)
        # dpoints=dpoints.view(B*num,dx,dy,xy)

        if self.training or B > 1:
            feat = feat.view(B, self.num_views, C, H, W)  # (B=8, N=2, C=128, H=24, W=32)
            feat = feat.permute(0, 2, 1, 3, 4).contiguous()  # (B=8, C=128, N=2, H=24, W=32)
        else:
            feat = feat.permute(1, 0, 2, 3).contiguous()

        feat = feat.view(B, C, -1, W)  # (B=8, C=128, N*H=48, W=32)   ->   (B*N=16, C=128, H=24, W=32)

        dfeat = dfeat.view(B, 1, -1, H * W)  # (B=8, 1, D*N=240, H*W=768)    ->
        homo_feats = []

        for i in range(self.num_points):
            homo_feat = self.grid_sample(
                feat,
                fpoints[i * B: (i + 1) * B],    # (B=8, H=80, W=80, 2)
            )   # (B=8, C=128, H=80, W=80)

            homo_dfeat = self.dgrid_sample(
                dfeat,  # (B=8, 1, D*N=240, H*W=768)
                dpoints[i * B: (i + 1) * B],    # (B=8, H=80, W=80, 2)
            )   # (B=8, 1, H=80, W=80)
            homo_feat = self.floatFs.mul(homo_feat, homo_dfeat) # (B=8, C*1=128, H=80, W=20)
            homo_feats.append(homo_feat)

        trans_feat = homo_feats[0]
        for f in homo_feats[1:]:
            trans_feat = self.floatFs.add(trans_feat, f)
        return trans_feat

    def fuse_model(self):
        self.depth_net.fuse_model()
        self.feat_net.fuse_model()

    def set_qconfig(self):
        from hat.utils import qconfig_manager

        self.dquant_stub.qconfig = qconfig_manager.get_qconfig(
            activation_qat_qkwargs={"dtype": qint16, "saturate": True},
            activation_calibration_qkwargs={"dtype": qint16, "saturate": True},
        )
        super().set_qconfig()
