import torch
import torch.nn as nn
import torch.nn.functional as F
import time
def multiscale_supervision_occ3d_mask(mask, ratio):
    start = int(ratio // 2)
    mask_gt = mask[:, start::ratio, start::ratio, start::ratio]  # !这个相当于选中间的，而不是 右下角的
    return mask_gt


# occ-3d FORM XHS
def multiscale_supervision_occ3d(gt_occ, ratio, gt_shape):
    start = int(ratio // 2)
    gt = gt_occ[:, start::ratio, start::ratio, start::ratio]  # !这个相当于选中间的，而不是 右下角的
    return gt


# a[8::-2]     # 从a[8]开始逆序取数，间隔为2         所以start::ratio表示的是正序取，每ratio中以中间的状态作为语义状态

def multiscale_supervision(gt_occ, ratio, gt_shape):
    '''
    change ground truth shape as (B, W, H, Z) for each level supervision
    '''
    gt = torch.zeros([gt_shape[0], gt_shape[2], gt_shape[3], gt_shape[4]]).type(torch.float)
    for i in range(gt.shape[0]):
        # coords = gt_occ[i][:, :3].type(torch.long) // ratio
        # 多batch计算
        gt_occ_i = torch.from_numpy(gt_occ[i])
        coords = gt_occ_i[:, :3].type(torch.long) // ratio
        gt[i, coords[:, 0], coords[:, 1], coords[:, 2]] = gt_occ_i[:, 3].type(torch.float)

    return gt


def geo_scal_loss(pred, ssc_target, semantic=False):
    # Get softmax probabilities
    if semantic:
        pred = F.softmax(pred, dim=1)

        # Compute empty and nonempty probabilities
        empty_probs = pred[:, 0, :, :, :]
    else:
        empty_probs = 1 - torch.sigmoid(pred)
    nonempty_probs = 1 - empty_probs

    # Remove unknown voxels
    mask = ssc_target != 255
    nonempty_target = ssc_target != 0
    nonempty_target = nonempty_target[mask].float()
    nonempty_probs = nonempty_probs[mask]
    empty_probs = empty_probs[mask]

    intersection = (nonempty_target * nonempty_probs).sum()
    precision = intersection / (nonempty_probs.sum() + 1e-6)
    recall = intersection / (nonempty_target.sum() + 1e-6)
    spec = ((1 - nonempty_target) * (empty_probs)).sum() / ((1 - nonempty_target).sum() + 1e-6)
    if precision > 1 or precision < 0 or recall > 1 or recall < 0 or spec > 1 or spec < 0:
        print('precision:', precision, ' recall: ', recall, ' spec: ', spec)
        raise AssertionError
    return (
            F.binary_cross_entropy(precision, torch.ones_like(precision))
            + F.binary_cross_entropy(recall, torch.ones_like(recall))
            + F.binary_cross_entropy(spec, torch.ones_like(spec))
    )

def geo_scal_loss_with_logits(pred, ssc_target, semantic=False):
    # Get softmax probabilities
    if semantic:
        pred = F.softmax(pred, dim=1)

        # Compute empty and nonempty probabilities
        empty_probs = pred[:, 0, :, :, :]
    else:
        empty_probs = 1 - torch.sigmoid(pred)
    nonempty_probs = 1 - empty_probs

    # Remove unknown voxels
    mask = ssc_target != 255
    nonempty_target = ssc_target != 0
    nonempty_target = nonempty_target[mask].float()
    nonempty_probs = nonempty_probs[mask]
    empty_probs = empty_probs[mask]

    intersection = (nonempty_target * nonempty_probs).sum()
    precision = intersection / (nonempty_probs.sum() + 1e-6)
    recall = intersection / (nonempty_target.sum() + 1e-6)
    spec = ((1 - nonempty_target) * (empty_probs)).sum() / ((1 - nonempty_target).sum() + 1e-6)
    if precision > 1 or precision < 0 or recall > 1 or recall < 0 or spec > 1 or spec < 0:
        print('precision:', precision, ' recall: ', recall, ' spec: ', spec)
        raise AssertionError
    return (
            F.binary_cross_entropy_with_logits(precision, torch.ones_like(precision))
            + F.binary_cross_entropy_with_logits(recall, torch.ones_like(recall))
            + F.binary_cross_entropy_with_logits(spec, torch.ones_like(spec))
    )

def sem_scal_loss(pred, ssc_target):
    # Get softmax probabilities
    pred = F.softmax(pred, dim=1)
    loss = 0
    count = 0
    mask = ssc_target != 255
    n_classes = pred.shape[1]
    for i in range(0, n_classes):

        # Get probability of class i
        p = pred[:, i, :, :, :]

        # Remove unknown voxels
        target_ori = ssc_target
        p = p[mask]
        target = ssc_target[mask]

        completion_target = torch.ones_like(target)
        completion_target[target != i] = 0
        completion_target_ori = torch.ones_like(target_ori).float()
        completion_target_ori[target_ori != i] = 0
        if torch.sum(completion_target) > 0:
            count += 1.0
            nominator = torch.sum(p * completion_target)
            loss_class = 0
            if torch.sum(p) > 0:
                precision = nominator / (torch.sum(p))
                loss_precision = F.binary_cross_entropy(
                    precision, torch.ones_like(precision)
                )
                loss_class += loss_precision
            if torch.sum(completion_target) > 0:
                recall = nominator / (torch.sum(completion_target))
                loss_recall = F.binary_cross_entropy(recall, torch.ones_like(recall))
                loss_class += loss_recall
            if torch.sum(1 - completion_target) > 0:
                specificity = torch.sum((1 - p) * (1 - completion_target)) / (
                    torch.sum(1 - completion_target)
                )
                loss_specificity = F.binary_cross_entropy(
                    specificity, torch.ones_like(specificity)
                )
                loss_class += loss_specificity
            loss += loss_class
    return loss / count