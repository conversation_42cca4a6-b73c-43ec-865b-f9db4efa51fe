import numpy as np
import torch
import chamfer

def eval_3d(verts_pred, verts_trgt, threshold=0.05):
    d1, d2, idx1, idx2 = chamfer.forward(verts_pred.unsqueeze(0).type(torch.float),
                                         verts_trgt.unsqueeze(0).type(torch.float))
    dist1 = torch.sqrt(d1).cpu().numpy()
    dist2 = torch.sqrt(d2).cpu().numpy()

    cd = dist1.mean() + dist2.mean()
    precision = np.mean((dist1 < threshold).astype('float'))
    recal = np.mean((dist2 < threshold).astype('float'))
    fscore = 2 * precision * recal / (precision + recal + 1e-10)
    iou = (precision * recal) / (precision + recal - precision * recal)
    metrics = np.array([np.mean(dist1), np.mean(dist2), cd, precision, recal, fscore])
    return metrics
