from typing import Optional, Sequence, List

import torch
import json
from mmengine.evaluator import BaseMetric
from mmengine.registry import METRICS

import numpy as np
import os
from .evaluation_metrics import eval_3d


@METRICS.register_module()
class BEVMetric(BaseMetric):
    def __init__(self,
                 classes,
                 prefix='',
                 use_semantic=False,
                 eval_patch='fn',
                 distance_ranges=None,
                 eval_metrics3d=True,
                 ):
        self.use_semantic = use_semantic
        self.class_names = classes
        self.eval_patch = eval_patch    # 'fn' 用于远近分段，'ud'用于上下分段
        self.distance_ranges = distance_ranges
        self.eval_metrics3d = eval_metrics3d
        self.filename = []

        super().__init__(prefix=prefix)

    def process(self, data_batch, data_samples):

        gt_occ = []
        for i in range(len(data_batch['data_samples'])):
            gt_occ.append(data_batch['data_samples'][i].gt_occ)
            rgb_path = (data_batch['data_samples'][i].metainfo['filename'])[0]
            # filename = rgb_path.split('/')[5] + rgb_path.split('/')[6] + '_' + rgb_path.split('/')[-1]  # indoor
            # filename = rgb_path.split('/')[6] + '_' + rgb_path.split('/')[-1]  # indoor
            # self.filename.append(filename)
        gt_occ = np.stack(gt_occ, axis=0)
        pred_occ = torch.stack(data_samples, 0)

        if self.use_semantic:
            class_num = len(self.class_names)

            if self.eval_patch == 'fn':
                eval_results = self.evaluation_semantic_patch_fn(
                    pred_occ=pred_occ,
                    gt_occ=gt_occ,
                    class_num=class_num,
                    patch=len(self.distance_ranges)
                )
            elif self.eval_patch == 'ud':
                eval_results = self.evaluation_semantic_patch_ud(
                    pred_occ=pred_occ,
                    gt_occ=gt_occ,
                    class_num=class_num,
                    patch=len(self.distance_ranges)
                )

        else:
            eval_results = self.evaluation_reconstruction(
                pred_occs=data_samples,
                data_batch=data_batch,
                eval_metrics3d=self.eval_metrics3d
            )

        self.results.extend(eval_results)
        # a = {'no_occupancy': 0.9895765082953005, 'occupancy': 0.5194145259823193, 'mIoU': 0.7544955171388099}
        # self.results.extend(a)

    def compute_metrics(self, results):
        distance_ranges = self.distance_ranges
        results_dict = {'by_distance': {}, 'total_overall': {}}
        if self.use_semantic:
            # 确定类名和数量
            class_names = {i: name for i, name in enumerate(self.class_names)}
            class_num = len(self.class_names)

            _result = np.stack(results.copy(), axis=0)[:, 0, 1, :]
            _tp = _result[:, 0]
            _ap = _result[:, 1]
            _pp = _result[:, 2]
            _union = _ap + _pp - _tp
            _iou = np.where(_union != 0, _tp / _union, 0)

            _result_total = np.stack(results.copy(), axis=0)[:, :, 1, :]
            _tp_total = _result_total[:, :, 0].sum(axis=1)
            _ap_total = _result_total[:, :, 1].sum(axis=1)
            _pp_total = _result_total[:, :, 2].sum(axis=1)
            _union_total = _ap_total + _pp_total - _tp_total
            _iou_total = np.where(_union_total != 0, _tp_total / _union_total, 0)

            # 把结果沿batch维度取平均
            results = np.stack(results, axis=0).mean(axis=0)  # list (patch, class_num, 3) -> numpy (patch, class_num, 3)

            # 提取 TP, AP, PP
            tp = results[:, :, 0]
            ap = results[:, :, 1]
            pp = results[:, :, 2]
            union = ap + pp - tp
            iou = np.where(union != 0, tp / union, 0)

            # 累计整体的TP, AP, PP
            total_tp = tp.sum(axis=0)
            total_ap = ap.sum(axis=0)
            total_pp = pp.sum(axis=0)

            # 计算并存储每个分段的IoU
            for p, d_range in enumerate(distance_ranges):
                results_dict['by_distance'][d_range] = {class_names[i]: iou[p, i] for i in range(class_num)}
                results_dict['by_distance'][d_range]['mIoU'] = np.mean(iou[p])

            # 计算 patch 的平均IoU和mIoU
            mean_ious = iou.mean(axis=0)
            results_dict['by_distance']['patch_mean'] = {class_names[i]: mean_ious[i] for i in range(class_num)}
            results_dict['by_distance']['patch_mean']['mIoU'] = mean_ious.mean()

            # 计算整体的平均IoU和mIoU
            total_union = total_ap + total_pp - total_tp
            total_iou = np.where(total_union != 0, total_tp / total_union, 0)
            results_dict['total_overall'] = dict(zip(class_names.values(), total_iou))
            results_dict['total_overall']['mIoU'] = total_iou.mean()
            
            # for i, filename in enumerate(self.filename):
            #     # scene = filename.split('_frame')[0].replace('scenes', '')   # indoor
            #     scene = filename.split('_')[1]   # kjl
            #     txt_path = '/home/<USER>/ecoaitoolkit/visual_dirs/kjl_jiexiang/kjlStereoOcc_0509_new/' + scene + '/testIoU_new.txt'
            #     with open(txt_path, "a") as file:
            #         file.write(f"{filename} {_iou_total[i]} {_iou[i]}\n")

            print(json.dumps(results_dict, indent=4))

        else:
            results = np.stack(results, axis=0)
            results[np.isnan(results)] = 0
            result = results.mean(0)
            results_dict = {
                'Acc': result[0],
                'Comp': result[1],
                'cd': result[2],
                'precision': result[3],
                'recal': result[4],
                'fscore': result[5],
                'IoU': (result[3] * result[4]) / (result[3] + result[4] - result[3] * result[4] + 1e-10)
            }

        # 返回评测指标结果
        return results_dict

    def evaluation_semantic_patch_fn(self,
                                     pred_occ,
                                     gt_occ,
                                     class_num,
                                     patch,):

        if gt_occ.ndim == 4:  # Occ (B, H, W, Z=4)
            pred_occ = torch.sigmoid(pred_occ)
            pred_occ_ = torch.zeros_like(pred_occ).to(pred_occ.device)
            pred_occ_[pred_occ > 0.5] = 1
            pred_occ = pred_occ_
            patch_size = int(pred_occ.shape[-2] / patch)

        elif gt_occ.ndim == 3:  # BEV (B, C=3, H, W)
            _, pred_occ = torch.max(torch.softmax(pred_occ, dim=1), dim=1)
            patch_size = int(pred_occ.shape[-1] / patch)

        pred_occ = pred_occ.cpu().numpy()

        score = np.zeros((pred_occ.shape[0], patch, class_num, 3))
        for p in range(patch):

            pred_occ_patch = pred_occ[:, :, p * patch_size:(p + 1) * patch_size]
            gt_occ_patch = gt_occ[:, :, p * patch_size:(p + 1) * patch_size]

            for i in range(class_num):
                score[:, p, i, 0] += ((gt_occ_patch == i) * (pred_occ_patch == i)).sum()  # TP
                score[:, p, i, 1] += (gt_occ_patch == i).sum()  # AP
                score[:, p, i, 2] += (pred_occ_patch == i).sum()  # PP

        return score

    def evaluation_semantic_patch_ud(self,
                                     pred_occ,
                                     gt_occ,
                                     class_num,
                                     patch,):

        # pred_occ = pred_occ[:, :, :20]
        # gt_occ = gt_occ[:, :, :20]

        pred_occ = torch.sigmoid(pred_occ)
        pred_occ_ = torch.zeros_like(pred_occ).to(pred_occ.device)
        pred_occ_[pred_occ > 0.5] = 1
        pred_occ = pred_occ_

        patch_size = int(pred_occ.shape[-1] / patch)
        pred_occ = pred_occ.cpu().numpy()

        score = np.zeros((pred_occ.shape[0], patch, class_num, 3))
        for p in range(patch):
            pred_occ_patch = pred_occ[:, :, :, p * patch_size:(p + 1) * patch_size]
            gt_occ_patch = gt_occ[:, :, :, p * patch_size:(p + 1) * patch_size]

            for i in range(class_num):
                score[:, p, i, 0] += ((gt_occ_patch == i) * (pred_occ_patch == i)).sum()  # TP
                score[:, p, i, 1] += (gt_occ_patch == i).sum()  # AP
                score[:, p, i, 2] += (pred_occ_patch == i).sum()  # PP

        return score    # (B=8, patch=4, class_num=3, 3)

    def evaluation_reconstruction(self, pred_occs, data_batch, eval_metrics3d):
        results = []

        for i in range(len(pred_occs)):
            pred_occ = pred_occs[i]
            pred_occ = torch.sigmoid(pred_occ)
            # _, pred_occ = torch.max(torch.softmax(pred_occ, dim=0), dim=0)

            gt_occ = data_batch['data_samples'][i].gt_occ
            gt_occ = torch.from_numpy(gt_occ).to(pred_occ.device)
            img_meta = data_batch['data_samples'][i]

            vertices_pred = voxel_to_vertices(pred_occ, img_meta, eval_metrics3d, mask_camera=None, thresh=0.25)
            vertices_gt = voxel_to_vertices(gt_occ, img_meta, eval_metrics3d, mask_camera=None, thresh=0.25)

            metrics = eval_3d(vertices_pred.type(torch.double), vertices_gt.type(torch.double), threshold=0.05)

            results.append(metrics)

        return np.stack(results, axis=0)

def voxel_to_vertices(voxel, img_metas, eval_metrics3d, mask_camera=None, thresh=0.5):
    x = torch.linspace(0, voxel.shape[0] - 1, voxel.shape[0])
    y = torch.linspace(0, voxel.shape[1] - 1, voxel.shape[1])

    if eval_metrics3d:
        z = torch.linspace(0, voxel.shape[2] - 1, voxel.shape[2])
    else:
        z = torch.linspace(0, 0, 1)

    X, Y, Z = torch.meshgrid(x, y, z)
    vv = torch.stack([X, Y, Z], dim=-1).to(voxel.device)

    if mask_camera is None:
        if eval_metrics3d:
            vertices = vv[voxel > thresh]
        else:
            vertices = vv[voxel > thresh].squeeze(1)
    else:
        vertices = vv[mask_camera[0] * voxel > thresh]

    # point_cloud_range = [-2, 0, -0.05, 2, 4, 0.15]
    # occ_size = [80, 80, 4]

    vertices[:, 0] = (vertices[:, 0] + 0.5) * (img_metas.pc_range[3] - img_metas.pc_range[0]) / img_metas.occ_size[0] + \
                     img_metas.pc_range[0]
    vertices[:, 1] = (vertices[:, 1] + 0.5) * (img_metas.pc_range[4] - img_metas.pc_range[1]) / img_metas.occ_size[1] + \
                     img_metas.pc_range[1]
    vertices[:, 2] = (vertices[:, 2] + 0.5) * (img_metas.pc_range[5] - img_metas.pc_range[2]) / img_metas.occ_size[2] + \
                     img_metas.pc_range[2]

    return vertices
