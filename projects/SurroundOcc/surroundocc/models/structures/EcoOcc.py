import numpy as np
from typing import Dict, List, Optional, Union

import torch
import torch.nn as nn
import torch.nn.functional as F

from mmdet3d.structures.det3d_data_sample import (ForwardResults,
                                                  OptSampleList, SampleList)
from torch import Tensor
from projects.SurroundOcc.surroundocc.models.utils import GridMask

from mmengine.model import BaseModel
import cv2
from mmdet.registry import MODELS
from ..losses.dice import DiceLoss
from ..losses.focal import <PERSON>ocal<PERSON>oss
from ..losses.lovasz import LovaszLoss
from ..losses.loss_utils import geo_scal_loss, geo_scal_loss_with_logits, sem_scal_loss


@MODELS.register_module()
class EcoOccVision(BaseModel):
    def __init__(self,
                 img_backbone=None,
                 img_neck=None,
                 view_transformer=None,
                 bev_encoder=None,
                 bev_decoder=None,
                 use_grid_mask=True,
                 is_vis=False,
                 lambda_dice=0
                 ):
        super().__init__()
        if img_backbone is not None:
            self.img_backbone = MODELS.build(img_backbone)
        if img_neck is not None:
            self.img_neck = MODELS.build(img_neck)
        if view_transformer is not None:
            self.view_transformer = MODELS.build(view_transformer)
        if bev_encoder is not None:
            self.bev_encoder = MODELS.build(bev_encoder)
        if bev_decoder is not None:
            self.bev_decoder = MODELS.build(bev_decoder)

        self.lambda_dice = lambda_dice
        self.is_vis = is_vis
        self.use_grid_mask = use_grid_mask
        self.grid_mask = GridMask(
            True, True, rotate=1, offset=False, ratio=0.5, mode=1, prob=0.7)

        self.BCELoss_fn = nn.BCEWithLogitsLoss()
        self.FocalLoss_fn = FocalLoss(mode='binary')
        self.DiceLoss_fn = DiceLoss(mode='binary')
        self.LovaszLoss_fn = LovaszLoss(mode='binary')

    def forward(self,
                inputs: Union[dict, List[dict]],
                data_samples: OptSampleList = None,
                mode: str = 'tensor',
                **kwargs) -> ForwardResults:
        # for i, img in enumerate(inputs['img']):
        #     inputs['img'][i] = inputs['img'][i][:1]

        x = torch.cat(inputs['img'], dim=0)  # (B*N=8, C=3, H=384, W=512)
        if self.use_grid_mask and mode == "loss":
            x = self.grid_mask(x)

        x = self.img_backbone(x)  # outs[0] (B*N=8, C=112, H=24, W=32)
        x = self.img_neck(x)[-1]  # (B*N=8, C=128, H=24, W=32)
        x = self.view_transformer(x, meta=data_samples, compile_model=False)  # horizon (B=4, C=128, H=80, W=80)
        x = self.bev_encoder(x)  # (B=4, C=256, H=80, W=80)
        x = self.bev_decoder(x)  # (B=4, C=1, H=80, W=80, Z=4)

        return self._postprocess(x,
                                 data_samples,
                                 mode,
                                 **kwargs)

    def _postprocess(self,
                     outs,
                     data_samples,
                     mode,
                     ):
        # tensor 输出
        if mode == 'tensor':
            return outs

        # 计算loss
        elif mode == 'loss':
            return self.loss(outs, data_samples)

        # predict 模式
        elif mode == 'predict':
            if self.is_vis:
                self.generate_output_3d(outs, data_samples)
            return self.predict(outs, data_samples)

    def loss(self, batch_inputs: dict, batch_data_samples) -> Dict[Tensor, Tensor]:
        losses = dict()
        gt_occ = []
        for data_sample in batch_data_samples:
            gt_occ.append(data_sample.gt_occ)
        loss_inputs = [gt_occ, batch_inputs]
        loss = self._loss(*loss_inputs)

        losses.update(loss)
        return losses

    def _loss(self,
              gt_occ: List[Tensor],
              pred: Tensor) -> Dict[Tensor, Tensor]:
        # pred = pred[:, 0]
        gt_occ = np.stack(gt_occ, axis=0)
        gt_occ = torch.from_numpy(gt_occ).to(pred.device)

        loss_dict = {}
        loss_bce = self.BCELoss_fn(pred, gt_occ)
        loss_dice = self.lambda_dice * self.DiceLoss_fn(pred, gt_occ)
        # loss_lovasz = self.lambda_dice * self.LovaszLoss_fn(pred, gt_occ)
        # loss_geo = self.lambda_dice * sem_scal_loss(pred, gt_occ)

        loss_dict['loss_bce'] = loss_bce
        loss_dict['loss_dice'] = loss_dice
        # loss_dict['loss_geo'] = loss_geo
        # loss_dict['loss_lovasz'] = loss_lovasz

        return loss_dict

    def predict(self,
                batch_inputs: dict,
                batch_data_samples: SampleList) -> SampleList:
        pred_occ = batch_inputs
        if isinstance(pred_occ, list):
            pred_occ = pred_occ[-1]

        return pred_occ

    def generate_output_3d(self, pred_occ, data_samples, mask_camera=None):
        import open3d as o3d
        import os

        self.use_semantic = False

        gt_occ = []
        for data_sample in data_samples:
            gt_occ.append(data_sample.gt_occ)
        gt_occ = np.stack(gt_occ, axis=0)
        gt_occ = torch.from_numpy(gt_occ).to(pred_occ.device)

        color_map = np.array(
            [
                [0, 0, 0, 255],
                [255, 120, 50, 255],  # barrier              orangey
                [255, 192, 203, 255],  # bicycle              pink
            ]
        )

        if self.use_semantic:
            _, voxel = torch.max(torch.softmax(pred_occ, dim=1), dim=1)
        else:
            voxel = torch.sigmoid(pred_occ)

        #不进行顶点重建，直接的到sigmoid并取0.5阈值后的结果
        voxel = torch.sigmoid(pred_occ[0])
        voxel_ = torch.zeros_like(voxel)
        voxel_[voxel > 0.5] = 1
        voxel = voxel_.cpu().numpy()

        # scene = data_samples[0].filename[0].split('/')[6]   # indoor
        scene = data_samples[0].filename[0].split('/')[7]   # kjl

        save_path = 'visual_dirs/kjl_jiexiang/kjlStereoOcc_0509_new/'
        save_dir_pred = save_path + scene + '/pred'
        save_dir_gt = save_path + scene + '/gt'
        save_dir_rgb_left = save_path + scene + '/rgb_left'
        save_dir_rgb_right = save_path + scene + '/rgb_right'
        # save_dir_tof = save_path + scene + '/tof'
        # save_dir_proj = save_path + scene + '/proj'
        os.makedirs(save_dir_pred, exist_ok=True)
        os.makedirs(save_dir_gt, exist_ok=True)
        os.makedirs(save_dir_rgb_left, exist_ok=True)
        os.makedirs(save_dir_rgb_right, exist_ok=True)
        # os.makedirs(save_dir_tof, exist_ok=True)
        # os.makedirs(save_dir_proj, exist_ok=True)
        # scenes, token, camera = data_samples[0].filename[0].split('_')[1:4]
        # filename = scenes + '_' + camera + '_' + token + '_rgb.npy'
        # filename = 'scene_' + scene + '_' + data_samples[0].filename[0].split('/')[-1].split('.')[0] + '_occ.npy'   # indoor
        filename = 'scene_' + data_samples[0].filename[0].split('/')[-1].split('.')[0] + '_occ.npy'   # kjl
        rgb_left = cv2.imread(data_samples[0].filename[0])
        rgb_right = cv2.imread(data_samples[0].filename[1])
        # proj = cv2.imread(data_samples[0].filename[0].replace('oms_rgb', 'project_ouster_no_ground_post'))
        # tof = np.load(data_samples[0].filename[0].replace('oms_rgb', 'tof_lidar_1').replace('.jpg', '.npy'))

        np.save(os.path.join(save_dir_pred, filename), voxel)
        np.save(os.path.join(save_dir_gt, filename), gt_occ[0].cpu().numpy())

        rgb_filename = filename.replace('_occ.npy', '_rgb.jpg')
        # tof_filename = filename.replace('_occ.npy', '_tof.npy')
        # proj_filename = filename.replace('_occ.npy', '_proj.jpg')
        cv2.imwrite(os.path.join(save_dir_rgb_left, rgb_filename), rgb_left)
        cv2.imwrite(os.path.join(save_dir_rgb_right, rgb_filename.replace('camera2', 'camera4')), rgb_right)
        # # 不进行顶点重建，直接的到sigmoid并取0.5阈值后的结果
        # voxel = torch.sigmoid(pred_occ[0])
        # print(voxel)
        # voxel_ = torch.zeros_like(voxel)
        # voxel_[voxel > 0.5] = 1
        # voxel = voxel_.cpu().numpy()
        #
        # save_dir_pred = 'visual_dirs/zeddd'
        # # save_dir_gt = 'visual_dirs/horizon_stereo_occ/gt'
        # os.makedirs(save_dir_pred, exist_ok=True)
        # # os.makedirs(save_dir_gt, exist_ok=True)
        # # scenes, token, camera = data_samples[0].filename[0].split('_')[1:4]
        # # filename = scenes + '_' + camera + '_' + token + '_rgb.npy'
        # filename = data_samples[0].filename[0].split('_')[1].split('.')[0] + '_rgb.npy'
        #
        # np.save(os.path.join(save_dir_pred, filename), voxel)
        # # np.save(os.path.join(save_dir_gt, filename), gt_occ[0])

        # voxel shape: (1, 80, 80, 4)
        # for i in range(voxel.shape[0]):
        #     x = torch.linspace(0, voxel[i].shape[0] - 1, voxel[i].shape[0])
        #     y = torch.linspace(0, voxel[i].shape[1] - 1, voxel[i].shape[1])
        #     z = torch.linspace(0, voxel[i].shape[2] - 1, voxel[i].shape[2])
        #     X, Y, Z = torch.meshgrid(x, y, z)
        #     vv = torch.stack([X, Y, Z], dim=-1).to(voxel.device)
        #     if mask_camera is not None:
        #         vertices = vv[mask_camera[0] * voxel[i] >= 0.5]
        #     else:
        #         vertices = vv[voxel[i] >= 0.1]

        #     # pc_range = [-2, 0, -0.05, 2, 4, 0.15]
        #     # occ_size = [80, 80, 4]
        #     vertices[:, 0] = (vertices[:, 0] + 0.5) * (data_samples[i].pc_range[3] - data_samples[i].pc_range[0]) / \
        #                      data_samples[i].occ_size[0] + data_samples[i].pc_range[0]
        #     vertices[:, 1] = (vertices[:, 1] + 0.5) * (data_samples[i].pc_range[4] - data_samples[i].pc_range[1]) / \
        #                      data_samples[i].occ_size[1] + data_samples[i].pc_range[1]
        #     vertices[:, 2] = (vertices[:, 2] + 0.5) * (data_samples[i].pc_range[5] - data_samples[i].pc_range[2]) / \
        #                      data_samples[i].occ_size[2] + data_samples[i].pc_range[2]

        #     vertices = vertices.cpu().numpy()

        #     pcd = o3d.geometry.PointCloud()
        #     pcd.points = o3d.utility.Vector3dVector(vertices)
        #     if self.use_semantic:
        #         if mask_camera is not None:
        #             semantics = voxel[i][voxel[i] * mask_camera[0] >= 1].cpu().numpy()
        #         else:
        #             semantics = voxel[i][voxel[i] >= 1].cpu().numpy()

        #         color = color_map[semantics] / 255.0
        #         pcd.colors = o3d.utility.Vector3dVector(color[..., :3])
        #         vertices = np.concatenate([vertices, semantics[:, None]], axis=-1)

        #     # filename = data_samples[i].filename[0].split('_')[1].split('.')[0]
        #     # scene_idx = '001'
        #     filename = data_samples[i].occ_path.split('/')[-1].split('.')[0]
        #     scene_idx = data_samples[i].occ_path.split('scenes/')[-1].split('/')[0]
        #     save_dir = os.path.join('visual_dirs/kjl-cam23-bevpool', scene_idx)
        #     os.makedirs(save_dir, exist_ok=True)

        #     o3d.io.write_point_cloud(os.path.join(save_dir, filename + '_pred.ply'), pcd)
        #     np.save(os.path.join(save_dir, filename + '_pred.npy'), vertices)

        #     # mesh = o3d.io.read_point_cloud(os.path.join(save_dir, filename + '_pred.ply'))
        #     # o3d.visualization.draw_geometries([mesh])

        #     if len(gt_occ.shape) == 3:
        #         gt = torch.zeros([pred_occ.shape[0], pred_occ.shape[2], pred_occ.shape[3], pred_occ.shape[4]]).to(
        #             gt_occ.device)
        #         for i in range(gt.shape[0]):
        #             # coords = gt_occ[i][:, :3].type(torch.long)
        #             # gt[i, coords[:, 0], coords[:, 1], coords[:, 2]] = gt_occ[i][:, 3]

        #             gt_occ_i = gt_occ[i][gt_occ[i, :, 3] != 254]  # ! Support multi-batchsize
        #             coords = gt_occ_i[:, :3].type(torch.long)
        #             gt[i, coords[:, 0].long(), coords[:, 1].long(), coords[:, 2].long()] = gt_occ_i[:, 3]


        #     else:
        #         gt = gt_occ
        #     x = torch.linspace(0, gt[i].shape[0] - 1, gt[i].shape[0])
        #     y = torch.linspace(0, gt[i].shape[1] - 1, gt[i].shape[1])
        #     z = torch.linspace(0, gt[i].shape[2] - 1, gt[i].shape[2])
        #     X, Y, Z = torch.meshgrid(x, y, z)
        #     vv = torch.stack([X, Y, Z], dim=-1).to(voxel.device)
        #     if mask_camera is not None:
        #         vertices = vv[mask_camera[0] * gt[0] >= 0.5]
        #     else:
        #         # vv = torch.stack([X,  Z,Y], dim=-1).to(voxel.device)
        #         vertices = vv[gt[0] >= 0.5]

        #     vertices[:, 0] = (vertices[:, 0] + 0.5) * (data_samples[i].pc_range[3] - data_samples[i].pc_range[0]) / \
        #                      data_samples[i].occ_size[0] + data_samples[i].pc_range[0]
        #     vertices[:, 1] = (vertices[:, 1] + 0.5) * (data_samples[i].pc_range[4] - data_samples[i].pc_range[1]) / \
        #                      data_samples[i].occ_size[1] + data_samples[i].pc_range[1]
        #     vertices[:, 2] = (vertices[:, 2] + 0.5) * (data_samples[i].pc_range[5] - data_samples[i].pc_range[2]) / \
        #                      data_samples[i].occ_size[2] + data_samples[i].pc_range[2]

        #     vertices = vertices.cpu().numpy()

        #     pcd = o3d.geometry.PointCloud()
        #     pcd.points = o3d.utility.Vector3dVector(vertices)
        #     if self.use_semantic:
        #         if mask_camera is not None:
        #             semantics = gt[0][gt[0] * mask_camera[0] >= 1].cpu().numpy()
        #         else:
        #             semantics = gt[0][gt[0] >= 1].cpu().numpy()

        #         color = color_map[semantics] / 255.0
        #         pcd.colors = o3d.utility.Vector3dVector(color[..., :3])
        #         vertices = np.concatenate([vertices, semantics[:, None]], axis=-1)

        #     filename = data_samples[i].occ_path.split('/')[-1].split('.')[0]
        #     scene_idx = data_samples[i].occ_path.split('scenes/')[-1].split('/')[0]
        #     save_dir = os.path.join('visual_dirs/kjl-cam23-bevpool', scene_idx)
        #     os.makedirs(save_dir, exist_ok=True)

        #     o3d.io.write_point_cloud(os.path.join(save_dir, filename + '_gt.ply'), pcd)
        #     np.save(os.path.join(save_dir, filename + '_gt.npy'), vertices)

            # mesh = o3d.io.read_point_cloud(os.path.join(save_dir, filename + '_gt.ply'))
            # o3d.visualization.draw_geometries([mesh])


@MODELS.register_module()
class EcoOccVisionDepoly(EcoOccVision):
    def __init__(self,
                 img_backbone=None,
                 img_neck=None,
                 view_transformer=None,
                 bev_encoder=None,
                 bev_decoder=None,
                 use_grid_mask=True
                 ):
        super().__init__(img_backbone,
                         img_neck,
                         view_transformer,
                         bev_encoder,
                         bev_decoder,
                         use_grid_mask
                         )

    def forward(self,
                img1,
                img2,
                data_samples: OptSampleList = None,
                mode: str = 'tensor',
                **kwargs) -> ForwardResults:
        x = torch.cat((img1, img2), dim=0)

        x = self.img_backbone(x)  # 5个level输出
        for i in x:
            print(i.shape)
        x = self.img_neck(x)[-1]  # Bx6 64 30 50
        x = self.view_transformer(x, data_samples, compile_model=True)  #

        x = self.bev_encoder(x)
        x = self.bev_decoder(x)

        return self._postprocess(x,
                                 data_samples,
                                 mode,
                                 **kwargs)
