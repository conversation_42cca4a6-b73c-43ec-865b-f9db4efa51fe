import numpy as np
from typing import Dict, List, Optional, Union

import cv2
import os
import torch
import torch.nn as nn
import torch.nn.functional as F

from mmdet3d.structures.det3d_data_sample import (ForwardResults,
                                                  OptSampleList, SampleList)
from torch import Tensor
from projects.SurroundOcc.surroundocc.models.utils import GridMask

from mmengine.model import BaseModel
from mmdet.registry import MODELS
from ..losses.dice import DiceLoss
from ..losses.focal import FocalLoss
from ..losses.lovasz import LovaszLoss
from ..losses.loss_utils import geo_scal_loss, geo_scal_loss_with_logits, sem_scal_loss


@MODELS.register_module()
class EcoBEVVision(BaseModel):
    def __init__(self,
                 img_backbone=None,
                 img_neck=None,
                 view_transformer=None,
                 bev_encoder=None,
                 bev_decoder=None,
                 use_grid_mask=True,
                 is_vis=False,
                 lambda_dice=0
                 ):
        super().__init__()
        if img_backbone is not None:
            self.img_backbone = MODELS.build(img_backbone)
        if img_neck is not None:
            self.img_neck = MODELS.build(img_neck)
        if view_transformer is not None:
            self.view_transformer = MODELS.build(view_transformer)
        if bev_encoder is not None:
            self.bev_encoder = MODELS.build(bev_encoder)
        if bev_decoder is not None:
            self.bev_decoder = MODELS.build(bev_decoder)

        self.lambda_dice = lambda_dice
        self.is_vis = is_vis
        self.use_grid_mask = use_grid_mask
        self.grid_mask = GridMask(
            True, True, rotate=1, offset=False, ratio=0.5, mode=1, prob=0.7)

        self.BCELoss_fn = nn.BCEWithLogitsLoss()
        self.FocalLoss_fn = FocalLoss(mode='binary')
        self.CELoss_fn = nn.CrossEntropyLoss(weight=torch.Tensor([0.1, 0.8, 0.1]))
        self.DiceLoss_fn = DiceLoss(mode='multiclass', classes=[0, 1, 0])
        # self.DiceLoss_fn = DiceLoss(mode='binary')

    def forward(self,
                inputs: Union[dict, List[dict]],
                data_samples: OptSampleList = None,
                mode: str = 'tensor',
                **kwargs) -> ForwardResults:

        x = torch.cat(inputs['img'], dim=0)  # (B*N=8, C=3, H=384, W=512)
        if self.use_grid_mask and mode == 'loss':
            x = self.grid_mask(x)

        x = self.img_backbone(x)  # outs[0] (B*N=8, C=512, H=30, W=40)
        x = self.img_neck(x)[-1]  # (B*N=8, C=512, H=30, W=40)
        x = self.view_transformer(x, meta=data_samples, compile_model=False)  # horizon (B=4, C=64, H=80, W=80)

        x = self.bev_encoder(x)  # (B=4, C=256, H=80, W=80)
        x = self.bev_decoder(x)  # (B=4, C=1, H=80, W=80, Z=4)

        return self._postprocess(x,
                                 data_samples,
                                 mode,
                                 **kwargs)

    def _postprocess(self,
                     outs,
                     data_samples,
                     mode,
                     ):
        # tensor 输出
        if mode == 'tensor':
            return outs

        # 计算loss
        elif mode == 'loss':
            return self.loss(outs, data_samples)

        # predict 模式
        elif mode == 'predict':
            if self.is_vis:
                self.generate_output_2d(outs, data_samples)
            return self.predict(outs, data_samples)

    def loss(self, batch_inputs: dict, batch_data_samples) -> Dict[Tensor, Tensor]:
        losses = dict()
        gt_occ = []
        for data_sample in batch_data_samples:
            gt_occ.append(data_sample.gt_occ)
        loss_inputs = [gt_occ, batch_inputs]
        loss = self._loss(*loss_inputs)

        losses.update(loss)
        return losses

    def _loss(self,
              gt_occ: List[Tensor],
              pred: Tensor) -> Dict[Tensor, Tensor]:
        # pred = pred[..., 0]
        gt_occ = np.stack(gt_occ, axis=0)
        gt_occ = torch.from_numpy(gt_occ).to(pred.device)

        loss_dict = {}

        # loss_ce = self.BCELoss_fn(pred, gt_occ)
        # loss_dice = self.lambda_dice * self.DiceLoss_fn(pred, gt_occ)
        # loss_dict['loss_bce'] = loss_ce
        # loss_dict['loss_dice'] = loss_dice

        loss_ce = self.CELoss_fn(pred, gt_occ.long())
        loss_dice = self.lambda_dice * self.DiceLoss_fn(pred, gt_occ.long())
        loss_dict['loss_ce'] = loss_ce
        loss_dict['loss_dice'] = loss_dice

        return loss_dict

    def predict(self,
                batch_inputs: dict,
                batch_data_samples: SampleList) -> SampleList:
        pred_occ = batch_inputs
        if isinstance(pred_occ, list):
            pred_occ = pred_occ[-1]

        return pred_occ

    def generate_output_2d(self, pred_occ, data_samples, mask_camera=None):

        _, voxel = torch.max(torch.softmax(pred_occ, dim=1), dim=1)

        gt_occ = []
        for data_sample in data_samples:
            gt_occ.append(data_sample.gt_occ)
        gt_occ = np.stack(gt_occ, axis=0)
        gt_occ = torch.from_numpy(gt_occ).to(pred_occ.device)


        for i in range(voxel.shape[0]):
            # print('\n', data_samples[i].metainfo['filename'][0])
            # print('\n', img_metas[i]['filename'][1])
            # print('\n', 111, voxel.shape)
            voxel = voxel.squeeze()
            # print('\n', 222, voxel.shape)
            voxel[voxel == 0] = 128
            voxel[voxel == 1] = 255
            voxel[voxel == 2] = 0
            # voxel[voxel > 0.5] = 255
            # voxel[voxel <= 0.5] = 0
            voxel = voxel.detach().cpu().numpy().astype('int64')

            gt_occ[gt_occ == 0] = 128
            gt_occ[gt_occ == 1] = 255
            gt_occ[gt_occ == 2] = 0

            scene = data_samples[0].filename[0].split('/')[6]   # indoor
            # scene = data_samples[0].filename[0].split('/')[7]   # kjl

            save_path = 'visual_dirs/kjl_jiexiang/indoorStereoBEV_0717/'
            save_dir_pred = save_path + scene + '/pred'
            save_dir_gt = save_path + scene + '/gt'
            save_dir_rgb_left = save_path + scene + '/rgb_left'
            save_dir_rgb_right = save_path + scene + '/rgb_right'
            # save_dir_tof = save_path + scene + '/tof'
            # save_dir_proj = save_path + scene + '/proj'
            os.makedirs(save_dir_pred, exist_ok=True)
            os.makedirs(save_dir_gt, exist_ok=True)
            os.makedirs(save_dir_rgb_left, exist_ok=True)
            os.makedirs(save_dir_rgb_right, exist_ok=True)
            # os.makedirs(save_dir_tof, exist_ok=True)
            # os.makedirs(save_dir_proj, exist_ok=True)
            # scenes, token, camera = data_samples[0].filename[0].split('_')[1:4]
            # filename = scenes + '_' + camera + '_' + token + '_rgb.npy'
            filename = 'scene_' + scene + '_' + data_samples[0].filename[0].split('/')[-1].split('.')[0] + '_bev.png'   # indoor
            # filename = 'scene_' + data_samples[0].filename[0].split('/')[-1].split('.')[0] + '_bev.png'   # kjl
            rgb_left = cv2.imread(data_samples[0].filename[0])
            rgb_right = cv2.imread(data_samples[0].filename[1])
            # proj = cv2.imread(data_samples[0].filename[0].replace('oms_rgb', 'project_ouster_no_ground_post'))
            # tof = np.load(data_samples[0].filename[0].replace('oms_rgb', 'tof_lidar_1').replace('.jpg', '.npy'))

            voxel = np.flip(voxel.transpose(1, 0), 0)
            gt_occ = np.flip(gt_occ[0].cpu().numpy().transpose(1, 0), 0)
            cv2.imwrite(os.path.join(save_dir_pred, filename), voxel)
            cv2.imwrite(os.path.join(save_dir_gt, filename), gt_occ)

            rgb_filename = filename.replace('_bev.png', '_rgb.jpg')
            # tof_filename = filename.replace('_occ.npy', '_tof.npy')
            # proj_filename = filename.replace('_occ.npy', '_proj.jpg')
            cv2.imwrite(os.path.join(save_dir_rgb_left, rgb_filename), rgb_left)
            cv2.imwrite(os.path.join(save_dir_rgb_right, rgb_filename.replace('camera2', 'camera4')), rgb_right)


            # gt_occ1[gt_occ1 >= 1] = 255
            # cv2.imwrite(
            #     data_samples[i].img_metas['filename'][0] + '_bev_pred.png',
            #     voxel
            # )
            # cv2.imwrite(
            #     data_samples[i].img_metas['filename'][0] + '_bev_gt.png',
            #     gt_occ.squeeze().detach().cpu().numpy()
            # )

            # input("Press Enter to continue...")


@MODELS.register_module()
class EcoBEVVisionDepoly(EcoBEVVision):
    def __init__(self,
                 img_backbone=None,
                 img_neck=None,
                 view_transformer=None,
                 bev_encoder=None,
                 bev_decoder=None,
                 use_grid_mask=True
                 ):
        super().__init__(img_backbone,
                         img_neck,
                         view_transformer,
                         bev_encoder,
                         bev_decoder,
                         use_grid_mask
                         )

    def forward(self,
                img1,
                img2,
                data_samples: OptSampleList = None,
                mode: str = 'tensor',
                **kwargs) -> ForwardResults:
        x = torch.cat((img1, img2), dim=0)

        x = self.img_backbone(x)  # 5个level输出
        x = self.img_neck(x)[-1]  # Bx6 64 30 50
        x = self.view_transformer(x, data_samples, compile_model=True)

        x = self.bev_encoder(x)
        x = self.bev_decoder(x)

        return self._postprocess(x,
                                 data_samples,
                                 mode,
                                 **kwargs)
