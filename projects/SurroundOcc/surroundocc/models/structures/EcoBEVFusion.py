import numpy as np
from typing import Dict, List, Optional, Union
import cv2
import os

import torch
import torch.nn as nn
import torch.nn.functional as F

from mmdet3d.structures.det3d_data_sample import (ForwardResults,
                                                  OptSampleList, SampleList)
from torch import Tensor
from projects.SurroundOcc.surroundocc.models.utils import GridMask
from projects.SurroundOcc.surroundocc.models.ops import Voxelization

from mmengine.model import BaseModel
from mmdet.registry import MODELS
from ..losses.dice import DiceLoss
from ..losses.focal import FocalLoss
from ..losses.lovasz import LovaszLoss
from ..losses.loss_utils import geo_scal_loss, geo_scal_loss_with_logits, sem_scal_loss

@MODELS.register_module()
class EcoBEVFusion(BaseModel):
    def __init__(self,
                 img_backbone=None,
                 img_neck=None,
                 view_transformer=None,
                 bev_encoder=None,
                 bev_decoder=None,
                 use_grid_mask=True,
                 lidar_config=None,
                 reader=None,
                 scatter=None,
                 norm_range=None,
                 is_vis=False,
                 lambda_dice=0,
                 ):
        super().__init__()
        if img_backbone is not None:
            self.img_backbone = MODELS.build(img_backbone)
        if img_neck is not None:
            self.img_neck = MODELS.build(img_neck)
        if view_transformer is not None:
            self.view_transformer = MODELS.build(view_transformer)
        if bev_encoder is not None:
            self.bev_encoder = MODELS.build(bev_encoder)
        if bev_decoder is not None:
            self.bev_decoder = MODELS.build(bev_decoder)

        # lidar
        self.lidar_config = lidar_config
        voxelize_module = Voxelization(**self.lidar_config)
        self.lidarmodel = nn.ModuleDict(
            {
                "voxelize": voxelize_module,
                "reader": MODELS.build(reader),
                "scatter": MODELS.build(scatter)
            }
        )

        self.lambda_dice = lambda_dice
        self.is_vis = is_vis
        self.use_grid_mask = use_grid_mask
        self.grid_mask = GridMask(
            True, True, rotate=1, offset=False, ratio=0.5, mode=1, prob=0.7)

        self.fuser = ConvFuser(in_channels=[64, 64], out_channels=128)

        self.is_vis = is_vis
        self.BCELoss_fn = nn.BCEWithLogitsLoss()
        self.CELoss_fn = nn.CrossEntropyLoss(weight=torch.Tensor([0.1, 0.8, 0.1]))
        self.DiceLoss_fn = DiceLoss(mode='multiclass', classes=[0, 1, 0])
        self.FocalLoss_fn = FocalLoss(mode='multiclass', alpha=None, gamma=2)

        self.norm_range = norm_range
        self.norm_dims = [0, 1, 2]
        self.max_num = torch.arange(10, dtype=torch.int).view([-1, 1])

    def forward(self,
                inputs: Union[dict, List[dict]],
                data_samples: OptSampleList = None,
                mode: str = 'tensor',
                **kwargs) -> ForwardResults:

        # vision
        x = torch.cat(inputs['img'], dim=0)  # (B=4, C=3, H=384, W=512)

        if self.use_grid_mask and mode == "loss":
            x = self.grid_mask(x)

        x = self.img_backbone(x)  # EfficientNet: [-2](B=4, C=112, H=24, W=32), [-1](B=4, C=320, H=12, W=16)
        x = self.img_neck(x)[-1]  # FastSCNNNect: [-1](B=4, C=64, H=24, W=32)
        x = self.view_transformer(x, data_samples, compile_model=False)  # GridSample (B=4, C=64, H=80, W=80)

        # lidar
        points = inputs['points']  # B (N, 3)

        for i in range(len(points)):
            if points[i].shape == torch.Size([0, 3]):
                a = np.array([[0, 0, 0]]).astype(np.float32)
                a = torch.from_numpy(a).to(points[i].device)
                points[i] = a
        
        #         print(points[i].shape)
        #
        #
        # for i in points:
        #     if i.shape == torch.Size([0, 3]):
        #         print(i)

        feature = self.lidar_extract_features_hat(points, 'lidar', data_samples[0].filename)  # (B=4, C=64, H=80, W=80)
        x = self.fuser([x, feature])  # (B=4, C=128, H=80, W=80)

        x = self.bev_encoder(x)  # (B=4, C=256, W=80, H=80)
        x = self.bev_decoder(x)  # (B=4, C=3, W=80, H=80, Z=1)
        return self._postprocess(x,
                                 data_samples,
                                 mode,
                                 **kwargs)

    def _postprocess(self,
                     outs,
                     data_samples,
                     mode,
                     ):
        # tensor 输出
        if mode == 'tensor':
            return outs

        # 计算loss
        elif mode == 'loss':
            return self.loss(outs, data_samples)

        # predict 模式
        elif mode == 'predict':
            if self.is_vis:
                self.generate_output_2d(outs, data_samples)
            return self.predict(outs, data_samples)

    def loss(self, batch_inputs: dict, batch_data_samples) -> Dict[str, Tensor]:
        losses = dict()
        gt_occ = []
        for data_sample in batch_data_samples:
            gt_occ.append(data_sample.gt_occ)
        loss_inputs = [gt_occ, batch_inputs]
        loss = self._loss(*loss_inputs)

        losses.update(loss)
        return losses

    def _loss(self,
              gt_occ: List[Tensor],
              pred: Tensor) -> Dict[Tensor, Tensor]:
        # pred = pred[..., 0]
        gt_occ = np.stack(gt_occ, axis=0)
        gt_occ = torch.from_numpy(gt_occ).to(pred.device)

        loss_dict = {}

        gt_occ = gt_occ[:, :, :20]
        pred = pred[:, :, :, :20]

        loss_ce = self.CELoss_fn(pred, gt_occ.long())
        loss_dice = self.lambda_dice * self.DiceLoss_fn(pred, gt_occ.long())
        # loss_dict['loss_ce'] = loss_ce
        # loss_dict['loss_dice'] = loss_dice

        # loss_focal = self.FocalLoss_fn(pred, gt_occ.long())
        loss_dict['loss_ce'] = loss_ce
        loss_dict['loss_dice'] = loss_dice

        return loss_dict

    def predict(self,
                batch_inputs: dict,
                batch_data_samples: SampleList) -> SampleList:
        pred_occ = batch_inputs
        if isinstance(pred_occ, list):
            pred_occ = pred_occ[-1]

        return pred_occ

    def generate_output_2d(self, pred_occ, data_samples, mask_camera=None):

        _, voxel = torch.max(torch.softmax(pred_occ, dim=1), dim=1)

        gt_occ = []
        for data_sample in data_samples:
            gt_occ.append(data_sample.gt_occ)
        gt_occ = np.stack(gt_occ, axis=0)
        gt_occ = torch.from_numpy(gt_occ).to(pred_occ.device)

        for i in range(voxel.shape[0]):
            # print('\n', data_samples[i].filename[0][-35:-4])
            # print('\n', img_metas[i]['filename'][1])
            # print('\n', 111, voxel.shape)
            voxel = voxel.squeeze()
            # print('\n', 222, voxel.shape)
            voxel[voxel == 0] = 128
            voxel[voxel == 1] = 255
            voxel[voxel == 2] = 0
            # voxel[voxel > 0.5] = 255
            # voxel[voxel <= 0.5] = 0
            voxel = voxel.detach().cpu().numpy().astype('int64')

            gt_occ[gt_occ == 0] = 128
            gt_occ[gt_occ == 1] = 255
            gt_occ[gt_occ == 2] = 0

            # scene = data_samples[0].filename[0].split('/')[6]   # indoor
            scene = data_samples[0].filename[0].split('/')[7]   # kjl

            save_path = 'visual_dirs/matterport_dataset/0814/'
            save_dir_pred = save_path + scene + '/pred'
            save_dir_gt = save_path + scene + '/gt'
            save_dir_rgb = save_path + scene + '/rgb'
            save_dir_tof = save_path + scene + '/tof'
            # save_dir_proj = save_path + scene + '/proj'
            os.makedirs(save_dir_pred, exist_ok=True)
            os.makedirs(save_dir_gt, exist_ok=True)
            os.makedirs(save_dir_rgb, exist_ok=True)
            os.makedirs(save_dir_tof, exist_ok=True)
            # os.makedirs(save_dir_proj, exist_ok=True)
            # scenes, token, camera = data_samples[0].filename[0].split('_')[1:4]
            # filename = scenes + '_' + camera + '_' + token + '_rgb.npy'
            # filename = 'scene_' + scene + '_' + data_samples[0].filename[0].split('/')[-1].split('.')[0] + '_bev.png'   # indoor
            filename = 'scene_' + data_samples[0].filename[0].split('/')[-1].split('.')[0] + '_bev.png'   # kjl
            rgb = cv2.imread(data_samples[0].filename[0])
            # tof = np.load('/home/<USER>/yp/Datasets/kws_deliver_0509/scenes/3FO3NTO3MURV/points_tof_56/camera0/3FO3NTO3MURV_camera0_000001_pcd.npy')
            # proj = cv2.imread(data_samples[0].filename[0].replace('oms_rgb', 'project_ouster_no_ground_post'))
            # tof = np.load(data_samples[0].filename[0].replace('oms_rgb', 'tof_lidar_1').replace('.jpg', '.npy'))    # indoor
            tof = np.load(data_samples[0].filename[0].replace('oms_rgb', 'tof_lidar_1').replace('.jpg', '.npy'))    # kjl


            voxel = np.flip(voxel.transpose(1, 0), 0)
            gt_occ = np.flip(gt_occ[0].cpu().numpy().transpose(1, 0), 0)
            cv2.imwrite(os.path.join(save_dir_pred, filename), voxel)
            cv2.imwrite(os.path.join(save_dir_gt, filename), gt_occ)

            rgb_filename = filename.replace('_bev.png', '_rgb.jpg')
            tof_filename = filename.replace('_bev.png', '_pcd.npy')
            # proj_filename = filename.replace('_occ.npy', '_proj.jpg')
            cv2.imwrite(os.path.join(save_dir_rgb, rgb_filename), rgb)
            # cv2.imwrite(os.path.join(save_dir_proj, proj_filename), proj)
            np.save(os.path.join(save_dir_tof, tof_filename), tof)

            # gt_occ1[gt_occ1 >= 1] = 255
            # save_dir = 'visual_dirs/demo_fusion_bev/fusion_bev' + data_samples[i].filename[0][-35:-4]
            # os.makedirs(save_dir, exist_ok=True)

            # cv2.imwrite(
            #     save_dir + '_bev_pred.png',
            #     voxel
            # )
            # cv2.imwrite(
            #     save_dir + '_bev_gt.png',
            #     gt_occ.squeeze().detach().cpu().numpy()
            # )

            # input("Press Enter to continue...")

    def _voxel_feature_encoder(
            self,
            norm_range: Tensor,
            norm_dims: List[int],
            features: Tensor,
            num_points_in_voxel: Tensor,
    ) -> Tensor:
        # normolize features 归一化
        start = []
        norm = []
        for idx, dim in enumerate(norm_dims):
            norm.append(norm_range[idx + len(norm_range) // 2] - norm_range[idx])
            start = torch.tensor(norm_range[:len(norm_dims)]).to(features)
        norm = torch.tensor(norm).to(features)
        features = (features - start) / norm

        # The feature decorations were calculated without regard to whether
        # pillar was empty. Need to ensure that empty pillars remain set to
        # zeros.
        voxel_count = features.shape[1]
        mask = self._get_paddings_indicator(num_points_in_voxel, voxel_count, axis=0)
        features *= mask
        features = features.unsqueeze(0).permute(0, 3, 2, 1).contiguous()

        return features

    def lidar_extract_features_hat(self, x, sensor, filename) -> torch.Tensor:

        # 第一步，先用hat的数据组织方式&原始bevfusion的点云体素化方式进行点云体素化处理
        voxel_feature, coords, num_points_per_voxel, = self.voxelize_for_hat(x, sensor)

        # # 保存校准数据用
        # from PIL import Image
        #
        # N = coords.shape[0]
        # T = 200
        #
        # img = Image.open(filename[0])
        # # img.save('/home/<USER>/img/' + filename[0][-35:-8] + '.jpg')
        # # np.save('/home/<USER>/voxel_feature/voxel_feature_' + filename[0][-35:-8] + '.npy', voxel_feature.cpu().numpy())
        # # np.save('/home/<USER>/coords/coords_' + filename[0][-35:-8] + '.npy', coords.cpu().numpy())
        # # np.save('/home/<USER>/num_points/num_points_' + filename[0][-35:-8] + '.npy', num_points_per_voxel.cpu().numpy())
        #
        # voxel_feature_padded = torch.zeros(T, 10, 3)  # (N, 10, 3) -> (T=100, 10, 3)
        # coords_padded = torch.zeros(T, 4)  # (N, 4) -> (T=100, 4)
        # num_points_per_voxel_padded = torch.zeros(T)  # (N, ) -> (T, )
        #
        # if N <= T:
        #     voxel_feature_padded[:N, :, :] = voxel_feature
        #     coords_padded[:N, :] = coords
        #     num_points_per_voxel_padded[:N] = num_points_per_voxel
        # else:
        #     voxel_feature_padded = voxel_feature[:T, :, :]
        #     coords_padded = coords[:T, :]
        #     num_points_per_voxel_padded = num_points_per_voxel[:T]
        #
        # np.save('/home/<USER>/voxel_feature_padded/voxel_feature_padded_' + filename[0][-35:-8] + '.npy', voxel_feature_padded.cpu().numpy())
        # np.save('/home/<USER>/coords_padded/coords_padded_' + filename[0][-35:-8] + '.npy', coords_padded.cpu().numpy())
        # np.save('//home/<USER>/num_points_padded/num_points_padded_' + filename[0][-35:-8] + '.npy', num_points_per_voxel_padded.cpu().numpy())

        # 归一化和mask一下特征,比稀疏卷积多一个维度而已,信息量一样
        features = self._voxel_feature_encoder(
            norm_range=self.norm_range,
            norm_dims=self.norm_dims,
            features=voxel_feature.clone(),
            num_points_in_voxel=num_points_per_voxel,
        )
        
        data = dict(  # noqa C408
            features=features,
            coors=coords,
            num_points_in_voxel=None,
            batch_size=len(x),
            input_shape=[80, 80, 1],
        )
        
        input_features = self.lidarmodel["reader"](
            data["features"],
            data["coors"],
            data["num_points_in_voxel"],
            horizon_preprocess=True,
        )
        
        x = self.lidarmodel["scatter"](
            input_features,
            data["coors"],
            data["batch_size"],
            torch.tensor([80, 80, 1]),
        )

        return x

    def _get_paddings_indicator(self, actual_num: torch.Tensor, max_num: int, axis=0) -> torch.Tensor:
        actual_num = actual_num.reshape(-1, 1, 1).repeat(1, 10, 1)
        max_num = self.max_num.to(actual_num.device)
        paddings_indicator = torch.gt(actual_num, max_num)
        return paddings_indicator

    @torch.no_grad()
    def voxelize_for_hat(self, points_lst, sensor):

        if self.training:
            max_voxels = self.lidar_config['max_voxels'][0]
        else:
            max_voxels = self.lidar_config['max_voxels'][1]

        device = points_lst[0].device
        voxel_lst: List[torch.Tensor] = []
        coors_lst: List[torch.Tensor] = []
        num_points_per_voxel_lst: List[torch.Tensor] = []

        for points in points_lst:
            # 这里对batch 进行遍历
            voxels, coors, num_points_per_voxel = self.lidarmodel["voxelize"](points)
            # 这里获取的coors是xyz顺序的,地平线的是zyx顺序的
            # 这里应该先做一下顺序调换，后面就还是延续之前的操作不做修改
            coors[:, [0, 2]] = coors[:, [2, 0]]
            voxel_lst.append(voxels)
            coors_lst.append(coors)
            num_points_per_voxel_lst.append(num_points_per_voxel)

        voxel_feature = torch.cat(voxel_lst, dim=0)
        num_points_per_voxel = torch.cat(num_points_per_voxel_lst, dim=0)
        coors_batch: List[torch.Tensor] = []
        
        for i, coor in enumerate(coors_lst):
            coor_pad = F.pad(coor, (1, 0), mode="constant", value=float(i))
            coors_batch.append(coor_pad)
            
        coors_batch = torch.cat(coors_batch, dim=0).long()
        return voxel_feature, coors_batch, num_points_per_voxel

class ConvFuser(nn.Sequential):
    def __init__(self, in_channels: int, out_channels: int) -> None:
        self.in_channels = in_channels
        self.out_channels = out_channels
        super().__init__(
            nn.Conv2d(sum(in_channels), out_channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(True),
        )

    def forward(self, inputs: List[torch.Tensor]) -> torch.Tensor:
        return super().forward(torch.cat(inputs, dim=1))

@MODELS.register_module()
class EcoBEVFusionDepoly(EcoBEVFusion):
    def __init__(self,
                 img_backbone=None,
                 img_neck=None,
                 view_transformer=None,
                 bev_encoder=None,
                 bev_decoder=None,
                 use_grid_mask=True,
                 lidar_config=None,
                 reader=None,
                 scatter=None,
                 norm_range=None,
                 ):
        super().__init__(
            img_backbone,
            img_neck,
            view_transformer,
            bev_encoder,
            bev_decoder,
            use_grid_mask,
            lidar_config,
            reader,
            scatter,
            norm_range
        )

    def forward(self,
                img,
                pts_feature,
                pts_coord,
                pts_num,
                data_samples: OptSampleList = None,
                mode: str = 'tensor',
                **kwargs) -> ForwardResults:

        # vision
        x = self.img_backbone(img)  # EfficientNet: [-2](B=4, C=112, H=24, W=32), [-1](B=4, C=320, H=12, W=16)
        x = self.img_neck(x)[-1]  # FastSCNNNect: [-1](B=4, C=64, H=24, W=32)
        x = self.view_transformer(x, data_samples, compile_model=True)  # GridSample (B=4, C=64, Dy=80, Dx=80)

        # lidar
        feature = self.lidar_extract_features_hat(pts_feature, pts_coord, pts_num)  # (B=4, C=64, Dy=80, Dx=80)
        x = self.fuser([x, feature])  # (B=4, C=128, Dy=80, Dx=80)

        x = self.bev_encoder(x)  # (B=4, C=256, Dy=80, Dx=80)
        x = self.bev_decoder(x)  # (B=4, C=3, Dx=80, Dy=80, Z=1)

        return self._postprocess(x,
                                 data_samples,
                                 mode,
                                 **kwargs)

    def lidar_extract_features_hat(self, voxel_feature, coords, num_points_per_voxel) -> torch.Tensor:
        #    归一化和mask一下特征,比稀疏卷积多一个维度而已,信息量一样

        features = self._voxel_feature_encoder(
            norm_range=self.norm_range,
            norm_dims=self.norm_dims,
            features=voxel_feature.clone(),
            num_points_in_voxel=num_points_per_voxel,
        )
        data = dict(  # noqa C408
            features=features,
            coords=coords,
            num_points_in_voxel=None,
            batch_size=1,
            input_shape=[80, 80, 1],
        )
        input_features = self.lidarmodel["reader"](
            data["features"],
            data["coords"],
            data["num_points_in_voxel"],
            horizon_preprocess=True,
        )
        x = self.lidarmodel["scatter"](
            input_features,
            data["coords"],
            data["batch_size"],
            torch.tensor([80, 80, 1]),
        )
        return x
