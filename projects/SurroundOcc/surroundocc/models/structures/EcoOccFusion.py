import numpy as np
from typing import Dict, List, Optional, Union

import torch
import torch.nn as nn
import torch.nn.functional as F

from mmdet3d.structures.det3d_data_sample import (ForwardResults,
                                                  OptSampleList, SampleList)
from torch import Tensor
from projects.SurroundOcc.surroundocc.models.utils import GridMask
from projects.SurroundOcc.surroundocc.models.ops import Voxelization

from mmengine.model import BaseModel
from mmdet.registry import MODELS
from ..losses.dice import DiceLoss
from ..losses.focal import FocalLoss
from ..losses.lovasz import LovaszLoss
from ..losses.loss_utils import geo_scal_loss, geo_scal_loss_with_logits, sem_scal_loss


@MODELS.register_module()
class EcoOccFusion(BaseModel):
    def __init__(self,
                 img_backbone=None,
                 img_neck=None,
                 view_transformer=None,
                 bev_encoder=None,
                 bev_decoder=None,
                 use_grid_mask=True,
                 lidar_config=None,
                 reader=None,
                 scatter=None,
                 norm_range=None,
                 is_vis=False,
                 lambda_dice=0,
                 ):
        super().__init__()
        if img_backbone is not None:
            self.img_backbone = MODELS.build(img_backbone)
        if img_neck is not None:
            self.img_neck = MODELS.build(img_neck)
        if view_transformer is not None:
            self.view_transformer = MODELS.build(view_transformer)
        if bev_encoder is not None:
            self.bev_encoder = MODELS.build(bev_encoder)
        if bev_decoder is not None:
            self.bev_decoder = MODELS.build(bev_decoder)

        # lidar
        self.lidar_config = lidar_config
        voxelize_module = Voxelization(**self.lidar_config)
        self.lidarmodel = nn.ModuleDict(
            {
                "voxelize": voxelize_module,
                "reader": MODELS.build(reader),
                "scatter": MODELS.build(scatter)
            }
        )

        self.lambda_dice = lambda_dice
        self.is_vis = is_vis
        self.use_grid_mask = use_grid_mask
        self.grid_mask = GridMask(
            True, True, rotate=1, offset=False, ratio=0.5, mode=1, prob=0.7)

        self.fuser = ConvFuser(in_channels=[64, 64], out_channels=128)

        self.is_vis = is_vis
        self.BCELoss_fn = nn.BCEWithLogitsLoss()
        self.FocalLoss_fn = FocalLoss(mode='binary')
        self.DiceLoss_fn = DiceLoss(mode='binary')
        self.LovaszLoss_fn = LovaszLoss(mode='binary')

        self.norm_range = norm_range
        self.norm_dims = [0, 1, 2]
        self.max_num = torch.arange(10, dtype=torch.int).view([-1, 1])

    def forward(self,
                inputs: Union[dict, List[dict]],
                data_samples: OptSampleList = None,
                mode: str = 'tensor',
                **kwargs) -> ForwardResults:

        # vision
        x = torch.cat(inputs['img'], dim=0)  # (B=4, C=3, H=384, W=512)

        if self.use_grid_mask and mode == "loss":
            x = self.grid_mask(x)

        x = self.img_backbone(x)  # EfficientNet: [-2](B=4, C=112, H=24, W=32), [-1](B=4, C=320, H=12, W=16)
        x = self.img_neck(x)[-1]  # FastSCNNNect: [-1](B=4, C=64, H=24, W=32)
        x = self.view_transformer(x, data_samples, compile_model=False)  # GridSample (B=4, C=64, H=80, W=80)

        # # lidar
        points = inputs['points']  # B (N, 3)
        
        # print(points)
        # # points[:, 0]
        # for i in range(len(points)):
        #     if points[i].shape == torch.Size([0, 3]):
        #         a = np.array([[0, 0, 0]]).astype(np.float32)
        #         a = torch.from_numpy(a).to(points[i].device)
        #         points[i] = a
        # #         print(points[i].shape)
        # #
        # #
        # # for i in points:
        # #     if i.shape == torch.Size([0, 3]):
        # #         print(i)

        # feature = self.lidar_extract_features_hat(points, 'lidar', data_samples[0].filename)  # (B=4, C=64, H=80, W=80)
        
        
        empty_ = False

        #处理点数为0的情况，如果当前点数为0，那么就只使用camera进行处理

        for i in points:

            N,_ = i.size()

            if N==0:

                print("这个数据点云为空")

                empty_ = True

                break

        #

        if empty_:

            feature = x

        else:

            feature = self.lidar_extract_features_hat(points, 'lidar', data_samples[0].filename)  # (B=4, C=64, H=80, W=80)
                
                
        x = self.fuser([x, feature])  # (B=4, C=128, H=80, W=80)
        x = self.bev_encoder(x)  # (B=4, C=256, W=80, H=80)
        x = self.bev_decoder(x)  # (B=4, C=3, W=80, H=80, Z=1)
        # a = torch.rand(8, 80, 80, 4).to(x.device)
        return self._postprocess(x,
                                 data_samples,
                                 mode,
                                 **kwargs)

    def _postprocess(self,
                     outs,
                     data_samples,
                     mode,
                     ):
        # tensor 输出
        if mode == 'tensor':
            return outs

        # 计算loss
        elif mode == 'loss':
            return self.loss(outs, data_samples)

        # predict 模式
        elif mode == 'predict':
            if self.is_vis:
                self.generate_output_3d(outs, data_samples)
            return self.predict(outs, data_samples)

    def loss(self, batch_inputs: dict, batch_data_samples) -> Dict[str, Tensor]:
        losses = dict()
        gt_occ = []
        for data_sample in batch_data_samples:
            gt_occ.append(data_sample.gt_occ)
        loss_inputs = [gt_occ, batch_inputs]
        loss = self._loss(*loss_inputs)

        losses.update(loss)
        return losses

    def _loss(self,
              gt_occ: List[Tensor],
              pred: Tensor) -> Dict[Tensor, Tensor]:
        # pred = pred[:, 0]
        gt_occ = np.stack(gt_occ, axis=0)
        gt_occ = torch.from_numpy(gt_occ).to(pred.device)

        loss_dict = {}
        loss_bce = self.BCELoss_fn(pred, gt_occ)
        loss_dice = self.lambda_dice * self.DiceLoss_fn(pred, gt_occ)
        # loss_lovasz = self.lambda_dice * self.LovaszLoss_fn(pred, gt_occ)
        loss_dict['loss_bce'] = loss_bce
        loss_dict['loss_dice'] = loss_dice
        # loss_dict['loss_lovasz'] = loss_lovasz

        return loss_dict

    def predict(self,
                batch_inputs: dict,
                batch_data_samples: SampleList) -> SampleList:
        pred_occ = batch_inputs
        if isinstance(pred_occ, list):
            pred_occ = pred_occ[-1]

        return pred_occ

    def generate_output_3d(self, pred_occ, data_samples, mask_camera=None):
        import open3d as o3d
        import os
        import cv2

        self.use_semantic = False

        gt_occ = []
        for data_sample in data_samples:
            gt_occ.append(data_sample.gt_occ)
        gt_occ = np.stack(gt_occ, axis=0)
        gt_occ = torch.from_numpy(gt_occ).to(pred_occ.device)

        color_map = np.array(
            [
                [0, 0, 0, 255],
                [255, 120, 50, 255],  # barrier              orangey
                [255, 192, 203, 255],  # bicycle              pink
            ]
        )

        if self.use_semantic:
            _, voxel = torch.max(torch.softmax(pred_occ, dim=1), dim=1)
        else:
            voxel = torch.sigmoid(pred_occ)

            #不进行顶点重建，直接的到sigmoid并取0.5阈值后的结果
            voxel = torch.sigmoid(pred_occ[0])
            voxel_ = torch.zeros_like(voxel)
            voxel_[voxel > 0.5] = 1
            voxel = voxel_.cpu().numpy()

            # scene = data_samples[0].filename[0].split('/')[6]   # indoor
            scene = data_samples[0].filename[0].split('/')[8]   # kjl

            save_path = 'visual_dirs/matterport_dataset/0816/'
            save_dir_pred = save_path + scene + '/pred'
            save_dir_gt = save_path + scene + '/gt'
            save_dir_rgb = save_path + scene + '/rgb'
            # save_dir_tof = save_path + scene + '/tof'
            # save_dir_proj = save_path + scene + '/proj'
            os.makedirs(save_dir_pred, exist_ok=True)
            os.makedirs(save_dir_gt, exist_ok=True)
            os.makedirs(save_dir_rgb, exist_ok=True)
            # os.makedirs(save_dir_tof, exist_ok=True)
            # os.makedirs(save_dir_proj, exist_ok=True)
            # scenes, token, camera = data_samples[0].filename[0].split('_')[1:4]
            # filename = scenes + '_' + camera + '_' + token + '_rgb.npy'
            # filename = 'scene_' + scene + '_' + data_samples[0].filename[0].split('/')[-1].split('.')[0] + '_occ.npy'   # indoor
            # filename = 'scene_' + data_samples[0].filename[0].split('/')[-1].split('.')[0] + '_occ.npy'   # kjl
            filename = os.path.basename(data_samples[0].filename[0])
            filename = filename.replace(".jpg", "_occ.npy")
            # rgb = cv2.imread(data_samples[0].filename[0])
            # proj = cv2.imread(data_samples[0].filename[0].replace('oms_rgb', 'project_ouster_no_ground_post'))
            # tof = np.load(data_samples[0].filename[0].replace('oms_rgb', 'tof_lidar_1').replace('.jpg', '.npy'))

            np.save(os.path.join(save_dir_pred, filename), voxel)
            np.save(os.path.join(save_dir_gt, filename), gt_occ[0].cpu().numpy())

            # rgb_filename = filename.replace('_occ.npy', '_rgb.jpg')
            # tof_filename = filename.replace('_occ.npy', '_tof.npy')
            # proj_filename = filename.replace('_occ.npy', '_proj.jpg')
            # cv2.imwrite(os.path.join(save_dir_rgb, rgb_filename), rgb)
            # cv2.imwrite(os.path.join(save_dir_proj, proj_filename), proj)
            # np.save(os.path.join(save_dir_tof, tof_filename), tof)


        # voxel shape: (1, 80, 80, 4)
        # for i in range(voxel.shape[0]):
        #     x = torch.linspace(0, voxel[i].shape[0] - 1, voxel[i].shape[0])
        #     y = torch.linspace(0, voxel[i].shape[1] - 1, voxel[i].shape[1])
        #     z = torch.linspace(0, voxel[i].shape[2] - 1, voxel[i].shape[2])
        #     X, Y, Z = torch.meshgrid(x, y, z)
        #     vv = torch.stack([X, Y, Z], dim=-1).to(voxel.device)
        #     if mask_camera is not None:
        #         vertices = vv[mask_camera[0] * voxel[i] >= 0.5]
        #     else:
        #         vertices = vv[voxel[i] >= 0.5]
        #
        #     # pc_range = [-2, 0, -0.05, 2, 4, 0.15]
        #     # occ_size = [80, 80, 4]
        #     vertices[:, 0] = (vertices[:, 0] + 0.5) * (data_samples[i].pc_range[3] - data_samples[i].pc_range[0]) / \
        #                      data_samples[i].occ_size[0] + data_samples[i].pc_range[0]
        #     vertices[:, 1] = (vertices[:, 1] + 0.5) * (data_samples[i].pc_range[4] - data_samples[i].pc_range[1]) / \
        #                      data_samples[i].occ_size[1] + data_samples[i].pc_range[1]
        #     vertices[:, 2] = (vertices[:, 2] + 0.5) * (data_samples[i].pc_range[5] - data_samples[i].pc_range[2]) / \
        #                      data_samples[i].occ_size[2] + data_samples[i].pc_range[2]
        #
        #     vertices = vertices.cpu().numpy()
        #
        #     pcd = o3d.geometry.PointCloud()
        #     pcd.points = o3d.utility.Vector3dVector(vertices)
        #     if self.use_semantic:
        #         if mask_camera is not None:
        #             semantics = voxel[i][voxel[i] * mask_camera[0] >= 1].cpu().numpy()
        #         else:
        #             semantics = voxel[i][voxel[i] >= 1].cpu().numpy()
        #
        #         color = color_map[semantics] / 255.0
        #         pcd.colors = o3d.utility.Vector3dVector(color[..., :3])
        #         vertices = np.concatenate([vertices, semantics[:, None]], axis=-1)
        #
        #     filename = data_samples[i].occ_path.split('/')[-1].split('.')[0]
        #     scene_idx = data_samples[i].occ_path.split('scenes/')[-1].split('/')[0]
        #     save_dir = os.path.join('visual_dirs/0509_fusion_occ', scene_idx)
        #     os.makedirs(save_dir, exist_ok=True)
        #
        #     o3d.io.write_point_cloud(os.path.join(save_dir, filename + '_pred.ply'), pcd)
        #     np.save(os.path.join(save_dir, filename + '_pred.npy'), vertices)
        #
        #     # mesh = o3d.io.read_point_cloud(os.path.join(save_dir, filename + '_pred.ply'))
        #     # o3d.visualization.draw_geometries([mesh])
        #
        #     if len(gt_occ.shape) == 3:
        #         gt = torch.zeros([pred_occ.shape[0], pred_occ.shape[2], pred_occ.shape[3], pred_occ.shape[4]]).to(
        #             gt_occ.device)
        #         for i in range(gt.shape[0]):
        #             # coords = gt_occ[i][:, :3].type(torch.long)
        #             # gt[i, coords[:, 0], coords[:, 1], coords[:, 2]] = gt_occ[i][:, 3]
        #
        #             gt_occ_i = gt_occ[i][gt_occ[i, :, 3] != 254]  # ! Support multi-batchsize
        #             coords = gt_occ_i[:, :3].type(torch.long)
        #             gt[i, coords[:, 0].long(), coords[:, 1].long(), coords[:, 2].long()] = gt_occ_i[:, 3]
        #
        #
        #     else:
        #         gt = gt_occ
        #     x = torch.linspace(0, gt[i].shape[0] - 1, gt[i].shape[0])
        #     y = torch.linspace(0, gt[i].shape[1] - 1, gt[i].shape[1])
        #     z = torch.linspace(0, gt[i].shape[2] - 1, gt[i].shape[2])
        #     X, Y, Z = torch.meshgrid(x, y, z)
        #     vv = torch.stack([X, Y, Z], dim=-1).to(voxel.device)
        #     if mask_camera is not None:
        #         vertices = vv[mask_camera[0] * gt[0] >= 0.5]
        #     else:
        #         # vv = torch.stack([X,  Z,Y], dim=-1).to(voxel.device)
        #         vertices = vv[gt[0] >= 0.5]
        #
        #     vertices[:, 0] = (vertices[:, 0] + 0.5) * (data_samples[i].pc_range[3] - data_samples[i].pc_range[0]) / \
        #                      data_samples[i].occ_size[0] + data_samples[i].pc_range[0]
        #     vertices[:, 1] = (vertices[:, 1] + 0.5) * (data_samples[i].pc_range[4] - data_samples[i].pc_range[1]) / \
        #                      data_samples[i].occ_size[1] + data_samples[i].pc_range[1]
        #     vertices[:, 2] = (vertices[:, 2] + 0.5) * (data_samples[i].pc_range[5] - data_samples[i].pc_range[2]) / \
        #                      data_samples[i].occ_size[2] + data_samples[i].pc_range[2]
        #
        #     vertices = vertices.cpu().numpy()
        #
        #     pcd = o3d.geometry.PointCloud()
        #     pcd.points = o3d.utility.Vector3dVector(vertices)
        #     if self.use_semantic:
        #         if mask_camera is not None:
        #             semantics = gt[0][gt[0] * mask_camera[0] >= 1].cpu().numpy()
        #         else:
        #             semantics = gt[0][gt[0] >= 1].cpu().numpy()
        #
        #         color = color_map[semantics] / 255.0
        #         pcd.colors = o3d.utility.Vector3dVector(color[..., :3])
        #         vertices = np.concatenate([vertices, semantics[:, None]], axis=-1)
        #
        #     filename = data_samples[i].occ_path.split('/')[-1].split('.')[0]
        #     scene_idx = data_samples[i].occ_path.split('scenes/')[-1].split('/')[0]
        #     save_dir = os.path.join('visual_dirs/0509_fusion_occ', scene_idx)
        #     os.makedirs(save_dir, exist_ok=True)
        #
        #     o3d.io.write_point_cloud(os.path.join(save_dir, filename + '_gt.ply'), pcd)
        #     np.save(os.path.join(save_dir, filename + '_gt.npy'), vertices)

            # mesh = o3d.io.read_point_cloud(os.path.join(save_dir, filename + '_gt.ply'))
            # o3d.visualization.draw_geometries([mesh])

    def _voxel_feature_encoder(
            self,
            norm_range: Tensor,
            norm_dims: List[int],
            features: Tensor,
            num_points_in_voxel: Tensor,
    ) -> Tensor:
        # normolize features 归一化
        # for idx, dim in enumerate(norm_dims):
        #     start = norm_range[idx]
        #     norm = norm_range[idx + len(norm_range) // 2] - norm_range[idx]
        #     features[:, :, dim] = features[:, :, dim] - start
        #     features[:, :, dim] = features[:, :, dim] / norm

        start = []
        norm = []
        for idx, dim in enumerate(norm_dims):
            norm.append(norm_range[idx + len(norm_range) // 2] - norm_range[idx])
            start = torch.tensor(norm_range[:len(norm_dims)]).to(features)
        norm = torch.tensor(norm).to(features)
        features = (features - start) / norm

        # The feature decorations were calculated without regard to whether
        # pillar was empty. Need to ensure that empty pillars remain set to
        # zeros.
        voxel_count = features.shape[1]
        mask = self._get_paddings_indicator(num_points_in_voxel, voxel_count, axis=0)
        features *= mask
        features = features.unsqueeze(0).permute(0, 3, 2, 1).contiguous()

        return features

    def lidar_extract_features_hat(self, x, sensor, data_samples) -> torch.Tensor:

        voxel_feature, coords, num_points_per_voxel, = self.voxelize_for_hat(x,
                                                                            sensor)  # 第一步，先用hat的数据组织方式&原始bevfusion的点云体素化方式进行点云体素化处理
        # save
        # vox_200 = np.zeros((200,10,3)).astype(np.float32)
        # num_point_200 = np.zeros((200)).astype(np.float32)
        # coord_200 = np.zeros((200,4)).astype(np.float32)

        # N = voxel_feature.shape[0]

        # if N >= 200:
        #     vox_200[:] = voxel_feature[:200].cpu().numpy()
        #     num_point_200[:] = num_points_per_voxel[:200].cpu().numpy()
        #     coord_200[:] = coords[:200].cpu().numpy()
        # else:
        #     vox_200[:N] = voxel_feature.cpu().numpy()
        #     num_point_200[:N] = num_points_per_voxel.cpu().numpy()
        #     coord_200[:N] = coords.cpu().numpy()
        # import os
        # np.save(
        #     os.path.join('data/voxel_feature_2443_infer',data_samples[0].filename[0].split('/')[-1].replace('_rgb.jpg','.npy')),
        #     vox_200
        # )
        # np.save(
        #     os.path.join('data/num_points_2443_infer/',data_samples[0].filename[0].split('/')[-1].replace('_rgb.jpg','.npy')),
        #     num_point_200
        # )
        # np.save(
        #     os.path.join('data/coords_2443_infer/',data_samples[0].filename[0].split('/')[-1].replace('_rgb.jpg','.npy')),
        #     coord_200
        # )

        # 归一化和mask一下特征,比稀疏卷积多一个维度而已,信息量一样
        features = self._voxel_feature_encoder(
            norm_range=self.norm_range,
            norm_dims=self.norm_dims,
            features=voxel_feature.clone(),
            num_points_in_voxel=num_points_per_voxel,
        )
        data = dict(  # noqa C408
            features=features,
            coors=coords,
            num_points_in_voxel=None,
            batch_size=len(x),
            input_shape=[80, 80, 1],
        )
        input_features = self.lidarmodel["reader"](
            data["features"],
            data["coors"],
            data["num_points_in_voxel"],
            horizon_preprocess=True,
        )
        x = self.lidarmodel["scatter"](
            input_features,
            data["coors"],
            data["batch_size"],
            torch.tensor([80, 80, 1]),
        )

        return x

    def _get_paddings_indicator(self, actual_num: torch.Tensor, max_num: int, axis=0) -> torch.Tensor:
        actual_num = actual_num.reshape(-1, 1, 1).repeat(1, 10, 1)

        max_num = self.max_num.to(actual_num.device)
        paddings_indicator = torch.gt(actual_num, max_num)
        return paddings_indicator

    @torch.no_grad()
    def voxelize_for_hat(self, points_lst, sensor):

        if self.training:
            max_voxels = self.lidar_config['max_voxels'][0]
        else:
            max_voxels = self.lidar_config['max_voxels'][1]

        device = points_lst[0].device
        voxel_lst: List[torch.Tensor] = []
        coors_lst: List[torch.Tensor] = []
        num_points_per_voxel_lst: List[torch.Tensor] = []

        for points in points_lst:
            # 这里对batch 进行遍历
            voxels, coors, num_points_per_voxel = self.lidarmodel["voxelize"](points)
            # 这里获取的coors是xyz顺序的,地平线的是zyx顺序的
            # 这里应该先做一下顺序调换，后面就还是延续之前的操作不做修改
            coors[:, [0, 2]] = coors[:, [2, 0]]
            voxel_lst.append(voxels)
            coors_lst.append(coors)
            num_points_per_voxel_lst.append(num_points_per_voxel)

        voxel_feature = torch.cat(voxel_lst, dim=0)
        num_points_per_voxel = torch.cat(num_points_per_voxel_lst, dim=0)
        coors_batch: List[torch.Tensor] = []
        for i, coor in enumerate(coors_lst):
            coor_pad = F.pad(coor, (1, 0), mode="constant", value=float(i))
            coors_batch.append(coor_pad)
        coors_batch = torch.cat(coors_batch, dim=0).long()
        return voxel_feature, coors_batch, num_points_per_voxel

class ConvFuser(nn.Sequential):
    def __init__(self, in_channels: int, out_channels: int) -> None:
        self.in_channels = in_channels
        self.out_channels = out_channels
        super().__init__(
            nn.Conv2d(sum(in_channels), out_channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(True),
        )

    def forward(self, inputs: List[torch.Tensor]) -> torch.Tensor:
        return super().forward(torch.cat(inputs, dim=1))

@MODELS.register_module()
class EcoOccFusionDepoly(EcoOccFusion):
    def __init__(self,
                 img_backbone=None,
                 img_neck=None,
                 view_transformer=None,
                 bev_encoder=None,
                 bev_decoder=None,
                 use_grid_mask=True,
                 lidar_config=None,
                 reader=None,
                 scatter=None,
                 norm_range=None,
                 ):
        super().__init__(
            img_backbone,
            img_neck,
            view_transformer,
            bev_encoder,
            bev_decoder,
            use_grid_mask,
            lidar_config,
            reader,
            scatter,
            norm_range
        )

        print(norm_range)

    def forward(self,
                img,
                pts_feature,
                pts_coord,
                pts_num,
                data_samples: OptSampleList = None,
                mode: str = 'tensor',
                **kwargs) -> ForwardResults:
        # vision
        x = self.img_backbone(img)  # EfficientNet: [-2](B=4, C=112, H=24, W=32), [-1](B=4, C=320, H=12, W=16)
        x = self.img_neck(x)[-1]  # FastSCNNNect: [-1](B=4, C=64, H=24, W=32)
        x = self.view_transformer(x, data_samples, compile_model=True)  # GridSample (B=4, C=64, Dy=80, Dx=80)

        # lidar
        feature = self.lidar_extract_features_hat(pts_feature, pts_coord, pts_num)  # (B=4, C=64, Dy=80, Dx=80)
        x = self.fuser([x, feature])  # (B=4, C=128, Dy=80, Dx=80)

        x = self.bev_encoder(x)  # (B=4, C=256, Dy=80, Dx=80)
        x = self.bev_decoder(x)  # (B=4, C=3, Dx=80, Dy=80, Z=4)

        return self._postprocess(x,
                                 data_samples,
                                 mode,
                                 **kwargs)

    def lidar_extract_features_hat(self, voxel_feature, coords, num_points_per_voxel) -> torch.Tensor:
        #    归一化和mask一下特征,比稀疏卷积多一个维度而已,信息量一样
        features = self._voxel_feature_encoder(
            norm_range=self.norm_range,
            norm_dims=self.norm_dims,
            features=voxel_feature.clone(),
            num_points_in_voxel=num_points_per_voxel,
        )
        data = dict(  # noqa C408
            features=features,
            coors=coords,
            num_points_in_voxel=None,
            batch_size=1,
            # input_shape=[80, 80, 1],
        )
        input_features = self.lidarmodel["reader"](
            data["features"],
            data["coors"],
            data["num_points_in_voxel"],
            horizon_preprocess=True,
        )
        x = self.lidarmodel["scatter"](
            input_features,
            data["coors"],
            data["batch_size"],
            torch.tensor([80, 80, 1]),
        )
        return x
