# Copyright (c) Horizon Robotics. All rights reserved.

# from turtle import forward
from typing import Union, Tuple

import torch
import torch.nn as nn
import numpy as np
import logging
from collections import OrderedDict
import torch.nn.functional as F
import time
import matplotlib.pyplot as plt
# from thop import profile
# from .loss.mbce import MbceLoss
# from .loss.supervised_conf_loss import SupervisedConfLoss
# from ..builder import BACKBONES
from mmdet.registry import MODELS


class ConvBlock(nn.Module):
    """Layer to perform a convolution followed by ELU
    """

    def __init__(self, in_channels, out_channels):
        super(ConvBlock, self).__init__()

        self.conv = Conv3x3(in_channels, out_channels)
        # self.nonlin = nn.ELU(inplace=True)
        self.nonlin = nn.ReLU(inplace=True)  # X3支持 效果不变

    def forward(self, x):
        out = self.conv(x)
        out = self.nonlin(out)
        return out


class Conv3x3(nn.Module):
    """Layer to pad and convolve input
    """

    # def __init__(self, in_channels, out_channels, use_refl=True):
    def __init__(self, in_channels, out_channels, use_refl=False):  # X3支持
        super(Conv3x3, self).__init__()

        if use_refl:
            self.pad = nn.ReflectionPad2d(1)
        else:
            self.pad = nn.ZeroPad2d(1)
        self.conv = nn.Conv2d(int(in_channels), int(out_channels), 3)

    def forward(self, x):
        out = self.pad(x)
        out = self.conv(out)
        return out


class Conv3x3_mask(nn.Module):
    """Layer to pad and convolve input
    """

    # def __init__(self, in_channels, out_channels, use_refl=True):
    def __init__(self, in_channels, out_channels, use_refl=False):  # X3支持
        super(Conv3x3_mask, self).__init__()

        if use_refl:
            self.pad = nn.ReflectionPad2d(1)
        else:
            self.pad = nn.ZeroPad2d(1)
        self.conv = nn.Conv2d(int(in_channels), int(out_channels), 3)

    def forward(self, x):
        out = self.pad(x)
        out = self.conv(out)
        return out


def upsample(x):
    """Upsample input tensor by a factor of 2
    """
    return F.interpolate(x, scale_factor=2, mode="nearest")


class DepthDecoder(nn.Module):
    def __init__(self, num_ch_enc, scales=range(1), num_output_channels=2, use_skips=True, maxdisp=256,
                 multi_scale=False):
        super(DepthDecoder, self).__init__()

        self.maxdisp = maxdisp
        self.multi_scale = multi_scale
        self.num_output_channels = num_output_channels
        self.use_skips = use_skips

        if self.multi_scale == True:
            self.scales = range(3)
        else:
            self.scales = scales

        self.num_ch_enc = num_ch_enc
        # self.num_ch_enc = torch.from_numpy(np.array([16 ,24 ,40 , 112 ,320])).cuda()#B1 B0

        # self.num_ch_dec = torch.from_numpy( np.array([16 ,16 ,24 , 40 ,112]))#B1 B0
        # self.num_ch_dec = np.array([16, 17, 26 , 44 ,123])#B2
        # self.num_ch_dec = np.array([16, 19, 28, 48, 134])#B3
        # self.num_ch_dec = np.array([16, 22, 33, 56, 156])#B4
        # self.num_ch_dec = np.array([16, 32, 64, 128, 256])#B4 BIGGER decoder
        if self.num_ch_enc[4] == 320:
            self.num_ch_dec = np.array([16, 16, 24, 40, 112])
        elif self.num_ch_enc[4] == 352:
            self.num_ch_dec = np.array([16, 17, 26, 44, 123])
        elif self.num_ch_enc[4] == 384:
            self.num_ch_dec = np.array([16, 19, 28, 48, 134])
        else:
            self.num_ch_dec = np.array([16, 22, 33, 56, 156])  # B4

        # self.convs_upconv_0_4 = ConvBlock(320, 112)
        # self.convs_upconv_0_3 = ConvBlock(224, 40)
        # self.convs_upconv_0_2= ConvBlock(80, 24)
        # self.convs_upconv_0_1= ConvBlock(48, 16)
        # self.convs_upconv_0_0= ConvBlock(32, 16)

        # self.convs_upconv_1_4 = ConvBlock(224, 224)
        # self.convs_upconv_1_3 = ConvBlock(80, 80)
        # self.convs_upconv_1_2= ConvBlock(48, 48)
        # self.convs_upconv_1_1= ConvBlock(32, 32)
        # self.convs_upconv_1_0= ConvBlock(16, 16)

        self.convs_upconv_0_4 = ConvBlock(self.num_ch_enc[4], self.num_ch_dec[4])
        self.convs_upconv_0_3 = ConvBlock(2 * self.num_ch_enc[3], self.num_ch_dec[3])
        self.convs_upconv_0_2 = ConvBlock(2 * self.num_ch_enc[2], self.num_ch_dec[2])
        self.convs_upconv_0_1 = ConvBlock(2 * self.num_ch_enc[1], self.num_ch_dec[1])
        self.convs_upconv_0_0 = ConvBlock(2 * self.num_ch_enc[0], self.num_ch_dec[0])

        self.convs_upconv_1_4 = ConvBlock(2 * self.num_ch_dec[4], 2 * self.num_ch_dec[4])
        self.convs_upconv_1_3 = ConvBlock(2 * self.num_ch_dec[3], 2 * self.num_ch_dec[3])
        self.convs_upconv_1_2 = ConvBlock(2 * self.num_ch_dec[2], 2 * self.num_ch_dec[2])
        self.convs_upconv_1_1 = ConvBlock(2 * self.num_ch_dec[1], 2 * self.num_ch_dec[1])
        self.convs_upconv_1_0 = ConvBlock(self.num_ch_dec[0], self.num_ch_dec[0])

        self.convs_dispconv2 = Conv3x3(2 * self.num_ch_dec[2], 2)
        self.convs_dispconv1 = Conv3x3(2 * self.num_ch_dec[1], 2)
        self.convs_dispconv0 = Conv3x3(self.num_ch_dec[0], 2)

        # b0
        self.convs_conf_mask_conv21 = Conv3x3(48, 2)
        self.convs_conf_mask_conv11 = Conv3x3(32, 2)
        self.convs_conf_mask_conv01 = Conv3x3(16, 2)

        # b4
        self.convs_conf_mask_conv21 = Conv3x3(66, 2)
        self.convs_conf_mask_conv11 = Conv3x3(44, 2)
        self.convs_conf_mask_conv01 = Conv3x3(16, 2)

        # self.relu=nn.ReLU6()
        # for p in self.parameters():
        #         p.requires_grad=False

    def forward(self, input_features):
        self.outputs = []
        self.outputs_masks = []
        x = input_features[-1]

        # 4
        x = self.convs_upconv_0_4(x)  # 256 6 20
        x = [F.interpolate(x, scale_factor=2, mode="nearest")]
        x += [input_features[3]]
        x = torch.cat(x, 1)
        x = self.convs_upconv_1_4(x)
        # 3
        x = self.convs_upconv_0_3(x)  # 256 6 20
        x = [F.interpolate(x, scale_factor=2, mode="nearest")]
        x += [input_features[2]]
        x = torch.cat(x, 1)
        x = self.convs_upconv_1_3(x)
        # 2
        x = self.convs_upconv_0_2(x)  # 256 6 20
        x = [F.interpolate(x, scale_factor=2, mode="nearest")]
        x += [input_features[1]]
        x = torch.cat(x, 1)
        x = self.convs_upconv_1_2(x)
        feature_disparety = self.convs_dispconv2(x) * self.maxdisp
        confidence_mask = self.convs_conf_mask_conv21(x)
        feature_disparety = F.interpolate(feature_disparety, scale_factor=2 ** 2, mode='nearest')
        confidence_mask = F.interpolate(confidence_mask, scale_factor=2 ** 2, mode='nearest')
        self.outputs.append(feature_disparety)  # bilinear
        self.outputs_masks.append(confidence_mask)
        # 1
        x = self.convs_upconv_0_1(x)  # 256 6 20
        x = [F.interpolate(x, scale_factor=2, mode="nearest")]
        x += [input_features[0]]
        x = torch.cat(x, 1)
        x = self.convs_upconv_1_1(x)
        feature_disparety = self.convs_dispconv1(x) * self.maxdisp
        confidence_mask = self.convs_conf_mask_conv11(x)
        feature_disparety = F.interpolate(feature_disparety, scale_factor=2 ** 1, mode='nearest')
        confidence_mask = F.interpolate(confidence_mask, scale_factor=2 ** 1, mode='nearest')
        self.outputs.append(feature_disparety)  # bilinear
        self.outputs_masks.append(confidence_mask)
        # 0
        x = self.convs_upconv_0_0(x)  # 256 6 20
        x = [F.interpolate(x, scale_factor=2, mode="nearest")]
        x = torch.cat(x, 1)
        x = self.convs_upconv_1_0(x)
        feature_disparety = self.convs_dispconv0(x) * self.maxdisp
        confidence_mask = self.convs_conf_mask_conv01(x)
        self.outputs.append(feature_disparety)  # bilinear
        self.outputs_masks.append(confidence_mask)

        # self.outputs = self.outputs[::-1]   #0最大

        return self.outputs, self.outputs_masks


class ConvModule2d(nn.Module):
    """
    A conv block that bundles conv/norm/activation layers.

    Args:
        in_channels (int): Same as nn.Conv2d.
        out_channels (int): Same as nn.Conv2d.
        kernel_size (int | tuple[int]): Same as nn.Conv2d.
        stride (int | tuple[int]): Same as nn.Conv2d.
        padding (int | tuple[int]): Same as nn.Conv2d.
        dilation (int | tuple[int]): Same as nn.Conv2d.
        groups (int): Same as nn.Conv2d.
        bias (bool): Same as nn.Conv2d.
        padding_mode (str): Same as nn.Conv2d.
        norm_type (nn.Module): Type of normalization layer.
        norm_cfg (dict): Dict for normalization layer.
        act_type (nn.Module): Type of activation layer.
        act_cfg (dict): Dict for activation layer.
    """

    def __init__(
            self,
            in_channels: int,
            out_channels: int,
            kernel_size: Union[int, Tuple[int, int]],
            stride: Union[int, Tuple[int, int]] = 1,
            padding: Union[int, Tuple[int, int]] = 0,
            dilation: Union[int, Tuple[int, int]] = 1,
            groups: int = 1,
            bias: bool = True,
            padding_mode: str = "zeros",
            norm_type: Union[None, nn.Module] = nn.BatchNorm2d,
            norm_cfg: dict = dict(momentum=0.1),
            act_type: Union[None, nn.Module] = nn.ReLU,
            act_cfg: dict = dict(inplace=True),
    ):
        super(ConvModule2d, self).__init__()
        self.conv = nn.Conv2d(
            in_channels,
            out_channels,
            kernel_size,
            stride,
            padding,
            dilation,
            groups,
            bias,
            padding_mode,
        )
        self.norm = (
            None if norm_type is None else norm_type(out_channels, **norm_cfg)
        )
        self.act = None if act_type is None else act_type(**act_cfg)
        # conv_list = [conv, norm, act]
        # self.conv_list = [layer for layer in conv_list if layer is not None]

    def forward(self, x):
        x = self.conv(x)
        if self.norm:
            x = self.norm(x)
        if self.act:
            x = self.act(x)
        return x


class MBBlock(nn.Module):
    """
    A basic block for Efficientnet.

    Args:
        in_channels (int): Input channels.
        out_channels (int): Output channels.
        kernel_size (int): kernel for depthwise conv.
        pad (int): pad for depthwise conv.
        stride (int): Stride for first conv.
        expand (float): Expand for MB block.
        norm_type (nn.Module): Type of normalization layer.
        norm_cfg (dict): Dict for normalization layer.
        act_type (nn.Module): Type of activation layer.
        act_cfg (dict): Dict for activation layer.
        bias (bool): Whether to use bias in module.
    """

    def __init__(
            self,
            in_channel: int,
            out_channel: int,
            kernel_size: int = 3,
            pad: int = 1,
            stride: int = 2,
            expand: float = 2.0,
            norm_type: Union[None, nn.Module] = nn.BatchNorm2d,
            norm_cfg: dict = dict(momentum=0.1),
            act_type: Union[None, nn.Module] = nn.ReLU,
            act_cfg: dict = dict(inplace=True),
            bias: bool = True,
    ):
        super(MBBlock, self).__init__()
        self.expand = expand
        expand_channel = int(in_channel * expand)
        if self.expand > 1:
            self.expand_conv = ConvModule2d(
                in_channel,
                expand_channel,
                1,
                padding=0,
                stride=1,
                norm_type=norm_type,
                norm_cfg=norm_cfg,
                act_type=act_type,
                act_cfg=act_cfg,
                bias=bias,
            )
        self.depth = ConvModule2d(
            expand_channel,
            expand_channel,
            kernel_size=kernel_size,
            padding=pad,
            stride=stride,
            groups=expand_channel,
            norm_type=norm_type,
            norm_cfg=norm_cfg,
            act_type=act_type,
            act_cfg=act_cfg,
            bias=bias,
        )

        self.project = ConvModule2d(
            expand_channel,
            out_channel,
            1,
            padding=0,
            stride=1,
            norm_type=norm_type,
            norm_cfg=norm_cfg,
            act_type=None,
            bias=bias,
        )

    def forward(self, x):
        out = x
        if self.expand > 1:
            out = self.expand_conv(x)
        out = self.depth(out)
        out = self.project(out)
        if out.shape == x.shape:
            out = out + x
        return out


class StemBlock(nn.Module):
    """
    A stem block for resnet.

    Args:
        out_channel (int): Output channels.
        kernel_size (int): kernel for depthwise conv.
        pad (int): pad for depthwise conv.
        norm_type (nn.Module): Type of normalization layer.
        norm_cfg (dict): Dict for normalization layer.
        act_type (nn.Module): Type of activation layer.
        act_cfg (dict): Dict for activation layer.
        bias (bool): Whether to use bias in module.
    """

    def __init__(
            self,
            out_channel: int,
            kernel_size: int,
            pad: int,
            norm_type: Union[None, nn.Module] = nn.BatchNorm2d,
            norm_cfg: dict = dict(momentum=0.1),
            act_type: Union[None, nn.Module] = nn.ReLU,
            act_cfg: dict = dict(inplace=True),
            bias: bool = True,
    ):
        super(StemBlock, self).__init__()
        self.stem = self.getStem(
            out_channel=out_channel,
            kernel_size=kernel_size,
            pad=pad,
            norm_type=norm_type,
            norm_cfg=norm_cfg,
            act_type=act_type,
            act_cfg=act_cfg,
            bias=bias,
        )

    def getStem(
            self,
            out_channel,
            kernel_size,
            pad,
            norm_type,
            norm_cfg,
            act_type,
            act_cfg,
            bias,
    ):
        return ConvModule2d(
            3,
            out_channel,
            kernel_size,
            padding=pad,
            stride=2,
            norm_type=norm_type,
            norm_cfg=norm_cfg,
            act_type=act_type,
            act_cfg=act_cfg,
            bias=bias,
        )

    def forward(self, x):
        return self.stem(x)


class StageBlock(nn.Module):
    """
    Stage block for EfficientNet.
    Args:
        in_channel (int): Input channels.
        out_channel (int): Output channels.
        kernel_size (int): kernel for depthwise conv.
        pad (int): pad for depthwise conv.
        stride (int): Stride for first conv.
        expand (float): Expand for MB block.
        repeat (int):  Repeat number of Stage.
        norm_type (nn.Module): Type of normalization layer.
        norm_cfg (dict): Dict for normalization layer.
        act_type (nn.Module): Type of activation layer.
        act_cfg (dict): Dict for activation layer.
        bias (bool): Whether to use bias in module.
    """

    def __init__(
            self,
            in_channel: int,
            out_channel: int,
            kernel_size: int,
            stride: int,
            pad: int,
            expand: float,
            repeat: int,
            norm_type: Union[None, nn.Module] = nn.BatchNorm2d,
            norm_cfg: dict = dict(momentum=0.1),
            act_type: Union[None, nn.Module] = nn.ReLU,
            act_cfg: dict = dict(inplace=True),
            bias: bool = True,
    ):
        super(StageBlock, self).__init__()
        self.conv = self.getBlock(
            in_channel=in_channel,
            out_channel=out_channel,
            kernel_size=kernel_size,
            pad=pad,
            stride=stride,
            expand=expand,
            norm_type=norm_type,
            norm_cfg=norm_cfg,
            act_type=act_type,
            act_cfg=act_cfg,
            bias=bias,
        )
        self.repeat = repeat
        if repeat > 1:
            self.blocks = nn.Sequential(
                *(
                    self.getBlock(
                        out_channel,
                        out_channel,
                        kernel_size=kernel_size,
                        pad=pad,
                        stride=1,
                        expand=expand,
                        norm_type=norm_type,
                        norm_cfg=norm_cfg,
                        act_type=act_type,
                        act_cfg=act_cfg,
                        bias=bias,
                    )
                    for _ in range(repeat - 1)
                )
            )

    def forward(self, x):
        x = self.conv(x)
        if self.repeat > 1:
            x = self.blocks(x)
        return x

    def getBlock(
            self,
            in_channel,
            out_channel,
            kernel_size,
            pad,
            stride,
            expand,
            norm_type,
            norm_cfg,
            act_type,
            act_cfg,
            bias,
    ):
        return MBBlock(
            in_channel=in_channel,
            out_channel=out_channel,
            kernel_size=kernel_size,
            pad=pad,
            stride=stride,
            expand=expand,
            norm_type=norm_type,
            norm_cfg=norm_cfg,
            act_type=act_type,
            act_cfg=act_cfg,
            bias=bias,
        )


class EfficientNet(nn.Module):
    """
    EfficientNet base structure.

    Args:
        width_coefficient (float): Width coefficient.
        depth_coefficient (float): Depth coefficient.
        norm_type (nn.Module): Type of normalization layer.
        norm_cfg (dict): Dict for normalization layer.
        act_type (nn.Module): Type of activation layer.
        act_cfg (dict): Dict for activation layer.
        bias (bool): Whether to use bias in module.
    """

    def __init__(
            self,
            width_coefficient: float = 1.0,
            depth_coefficient: float = 1.0,
            norm_type: Union[None, nn.Module] = nn.BatchNorm2d,
            norm_cfg: dict = dict(momentum=0.1),
            act_type: Union[None, nn.Module] = nn.ReLU,
            act_cfg: dict = dict(inplace=True),
            bias: bool = True,
    ):
        super(EfficientNet, self).__init__()
        self.channels = [
            int(x * width_coefficient)
            for x in [32, 16, 24, 40, 80, 112, 192, 320]
        ]
        self.repeats = [
            int(x * depth_coefficient) for x in [1, 2, 2, 3, 3, 4, 1]
        ]
        self.expands = [1, 6, 6, 6, 6, 6, 6]
        self.strides = [1, 2, 2, 2, 1, 2, 1]
        self.num_ch_enc = np.array(self.channels)[np.where(np.array(self.strides) == 2)]
        self.num_ch_enc = np.append(self.num_ch_enc, self.channels[-1])
        self.kernels = [3, 3, 5, 3, 5, 5, 3]
        self.paddings = [1, 1, 2, 1, 2, 2, 1]
        self.stem = StemBlock(
            out_channel=self.channels[0],
            kernel_size=3,
            pad=1,
            norm_type=norm_type,
            norm_cfg=norm_cfg,
            act_type=act_type,
            act_cfg=act_cfg,
            bias=bias,
        )
        self.stage_blocks = nn.ModuleList(
            (
                StageBlock(
                    in_channel=self.channels[i],
                    out_channel=self.channels[i + 1],
                    kernel_size=self.kernels[i],
                    pad=self.paddings[i],
                    stride=self.strides[i],
                    repeat=self.repeats[i],
                    expand=self.expands[i],
                    norm_type=norm_type,
                    norm_cfg=norm_cfg,
                    act_type=act_type,
                    act_cfg=act_cfg,
                    bias=bias,
                )
                for i in range(len(self.repeats))
            )
        )

        # self.num_ch_enc = np.array([16 ,24 ,40 , 112 ,320])#B1 B0
        # self.num_ch_enc = np.array([17 ,26, 44 , 123, 352])#B2
        # self.num_ch_enc = np.array([19, 28, 48, 134, 384])#B3
        # self.num_ch_enc = np.array([22, 33, 56, 156, 448])#B4

        self.decoder = DepthDecoder(self.num_ch_enc, multi_scale=True)

    def forward(self, inputs):

        x = self.stem(inputs)
        outputs = list()
        for i, stage in enumerate(self.stage_blocks):
            if self.strides[i] == 2:
                outputs.append(x)
            x = stage(x)
        # 16 24 40  112 320 B0
        # 16 24 40  112 320 B1
        # 17 26 44  123 352 B2
        # 19 28 48  134 384 B3
        # 22 33 56  156 448 B4
        outputs.append(x)

        return outputs[2:]

    def init_weights(self, pretrained=None, ):
        if isinstance(pretrained, str):
            pretrained_dict = torch.load(pretrained, map_location={'cuda:0': 'cpu'})
            # logger.info('=> loading pretrained model {}'.format(pretrained))
            model_dict = self.state_dict()
            pretrained_dict = {k.replace('last_layer', 'aux_head').replace('model.', ''): v for k, v in
                               pretrained_dict.items()}
            print(set(model_dict) - set(pretrained_dict))
            print(set(pretrained_dict) - set(model_dict))
            pretrained_dict = {k: v for k, v in pretrained_dict.items()
                               if k in model_dict.keys()}
            pretrained_dict['stage0.rbr_dense.conv.weight'] = torch.cat(
                [pretrained_dict['stage0.rbr_dense.conv.weight']] * 2, dim=1) / 2
            pretrained_dict['stage0.rbr_1x1.conv.weight'] = torch.cat(
                [pretrained_dict['stage0.rbr_1x1.conv.weight']] * 2, dim=1) / 2
            # for k, _ in pretrained_dict.items():
            # logger.info(
            #     '=> loading {} pretrained model {}'.format(k, pretrained))
            model_dict.update(pretrained_dict)
            self.load_state_dict(model_dict, strict=False)
        else:
            logging.info('=> init weights from normal distribution')
            for name, m in self.named_modules():
                if any(part in name for part in {'cls', 'aux', 'ocr'}):
                    # print('skipped', name)
                    continue
                if isinstance(m, nn.Conv2d):
                    nn.init.normal_(m.weight, std=0.001)
                elif isinstance(m, nn.BatchNorm2d):
                    nn.init.constant_(m.weight, 1)
                    nn.init.constant_(m.bias, 0)


@MODELS.register_module()
class EfficientNetB0(EfficientNet):
    """
    EfficientNet b0 structure.

    Args:
        norm_type (nn.Module): Type of normalization layer.
        norm_cfg (dict): Dict for normalization layer.
        act_type (nn.Module): Type of activation layer.
        act_cfg (dict): Dict for activation layer.
        bias (bool): Whether to use bias in module.
    """

    def __init__(
            self,
            norm_type: Union[None, nn.Module] = nn.BatchNorm2d,
            norm_cfg: dict = dict(momentum=0.1),
            act_type: Union[None, nn.Module] = nn.ReLU,
            act_cfg: dict = dict(inplace=True),
            bias: bool = True,
    ):
        super(EfficientNetB0, self).__init__(
            width_coefficient=1.0,
            depth_coefficient=1.0,
            norm_type=norm_type,
            norm_cfg=norm_cfg,
            act_type=act_type,
            act_cfg=act_cfg,
            bias=bias,
        )


@MODELS.register_module()
class EfficientNetB1(EfficientNet):
    """
    EfficientNet b1 structure.

    Args:
        norm_type (nn.Module): Type of normalization layer.
        norm_cfg (dict): Dict for normalization layer.
        act_type (nn.Module): Type of activation layer.
        act_cfg (dict): Dict for activation layer.
        bias (bool): Whether to use bias in module.
    """

    def __init__(
            self,
            norm_type: Union[None, nn.Module] = nn.BatchNorm2d,
            norm_cfg: dict = dict(momentum=0.1),
            act_type: Union[None, nn.Module] = nn.ReLU,
            act_cfg: dict = dict(inplace=True),
            bias: bool = True,
    ):
        super(EfficientNetB1, self).__init__(
            width_coefficient=1.0,
            depth_coefficient=1.1,
            norm_type=norm_type,
            norm_cfg=norm_cfg,
            act_type=act_type,
            act_cfg=act_cfg,
            bias=bias,
        )


@MODELS.register_module()
class EfficientNetB2(EfficientNet):
    """
    EfficientNet b2 structure.

    Args:
        norm_type (nn.Module): Type of normalization layer.
        norm_cfg (dict): Dict for normalization layer.
        act_type (nn.Module): Type of activation layer.
        act_cfg (dict): Dict for activation layer.
        bias (bool): Whether to use bias in module.
    """

    def __init__(
            self,
            norm_type: Union[None, nn.Module] = nn.BatchNorm2d,
            norm_cfg: dict = dict(momentum=0.1),
            act_type: Union[None, nn.Module] = nn.ReLU,
            act_cfg: dict = dict(inplace=True),
            bias: bool = True,
    ):
        super(EfficientNetB2, self).__init__(
            width_coefficient=1.1,
            depth_coefficient=1.2,
            norm_type=norm_type,
            norm_cfg=norm_cfg,
            act_type=act_type,
            act_cfg=act_cfg,
            bias=bias,
        )


@MODELS.register_module()
class EfficientNetB3(EfficientNet):
    """
    EfficientNet b2 structure.

    Args:
        norm_type (nn.Module): Type of normalization layer.
        norm_cfg (dict): Dict for normalization layer.
        act_type (nn.Module): Type of activation layer.
        act_cfg (dict): Dict for activation layer.
        bias (bool): Whether to use bias in module.
    """

    def __init__(
            self,
            norm_type: Union[None, nn.Module] = nn.BatchNorm2d,
            norm_cfg: dict = dict(momentum=0.1),
            act_type: Union[None, nn.Module] = nn.ReLU,
            act_cfg: dict = dict(inplace=True),
            bias: bool = True,
    ):
        super(EfficientNetB3, self).__init__(
            width_coefficient=1.2,
            depth_coefficient=1.4,
            norm_type=norm_type,
            norm_cfg=norm_cfg,
            act_type=act_type,
            act_cfg=act_cfg,
            bias=bias,
        )


@MODELS.register_module()
class EfficientNetB4(EfficientNet):
    """
    EfficientNet b2 structure.

    Args:
        norm_type (nn.Module): Type of normalization layer.
        norm_cfg (dict): Dict for normalization layer.
        act_type (nn.Module): Type of activation layer.
        act_cfg (dict): Dict for activation layer.
        bias (bool): Whether to use bias in module.
    """

    def __init__(
            self,
            norm_type: Union[None, nn.Module] = nn.BatchNorm2d,
            norm_cfg: dict = dict(momentum=0.1),
            act_type: Union[None, nn.Module] = nn.ReLU,
            act_cfg: dict = dict(inplace=True),
            bias: bool = True,
    ):
        super(EfficientNetB4, self).__init__(
            width_coefficient=1.4,
            depth_coefficient=1.8,
            norm_type=norm_type,
            norm_cfg=norm_cfg,
            act_type=act_type,
            act_cfg=act_cfg,
            bias=bias,
        )


params_dict = {
    # Coefficients:   width,depth,res,dropout
    'efficientnet-b0': (1.0, 1.0, 224, 0.2),
    'efficientnet-b1': (1.0, 1.1, 240, 0.2),
    'efficientnet-b2': (1.1, 1.2, 260, 0.3),
    'efficientnet-b3': (1.2, 1.4, 300, 0.3),
    'efficientnet-b4': (1.4, 1.8, 380, 0.4),
    'efficientnet-b5': (1.6, 2.2, 456, 0.4),
    'efficientnet-b6': (1.8, 2.6, 528, 0.5),
    'efficientnet-b7': (2.0, 3.1, 600, 0.5),
    'efficientnet-b8': (2.2, 3.6, 672, 0.5),
    'efficientnet-l2': (4.3, 5.3, 800, 0.5),
}


def convnext_tiny0(pretrained=False, num_input_images=2, **kwargs):
    model = EfficientNetB0().cuda()

    return model


if __name__ == "__main__":
    torch.backends.cudnn.benchmark = True

    model = EfficientNetB1().cuda()
    model.eval()

    B = 1

    tgt_img = torch.randn(B, 3, 384, 512).cuda()

    flops, params = profile(model, inputs=(tgt_img, tgt_img))

    since = time.time()
    for i in range(2000):
        tgt_depth = model(tgt_img, tgt_img)

    time_elapsed = time.time() - since

    print('Training complete in {:.0f}m {:.0f}s'.format(time_elapsed // 60, time_elapsed % 60))
