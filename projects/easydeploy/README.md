# MMYOLO Model Easy-Deployment

## Introduction

This project is developed for easily converting your MMYOLO models to other inference backends without the need of MMDeploy, which reduces the cost of both time and effort on getting familiar with MMDeploy.

Currently we support converting to `ONNX` and `TensorRT` formats, other inference backends such `ncnn` will be added to this project as well.

## Supported Backends

- [Model Convert](docs/model_convert.md)
