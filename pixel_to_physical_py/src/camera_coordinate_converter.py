import numpy as np
import yaml
import cv2
from typing import Tuple, List, Optional
from dataclasses import dataclass

@dataclass
class CameraCoordinate:
    """相机坐标系中的点"""
    x: float  # 相机坐标系x轴 (水平方向，右为正)
    y: float  # 相机坐标系y轴 (垂直方向，下为正)
    z: float  # 相机坐标系z轴 (深度方向，前为正)
    pixel_x: int  # 原始像素坐标x
    pixel_y: int  # 原始像素坐标y
    confidence: float = 1.0  # 置信度

@dataclass
class OutlierInfo:
    """异常点信息"""
    is_outlier: bool = False
    original_distance: float = 0.0
    corrected_distance: float = 0.0
    correction_method: str = ""

class CameraCoordinateConverter:
    """
    相机坐标转换器
    将像素坐标和距离值转换为相机坐标系(x,y,z)，以及相机坐标反投影为像素坐标
    """
    
    def __init__(self, camera_config_path: str):
        self.camera_config_path = camera_config_path
        self.camera_matrix = None
        self.distortion_coeffs = None
        self.image_size = None
        self._load_camera_parameters()
    
    def _load_camera_parameters(self):
        try:
            with open(self.camera_config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            camera_params = config['camera_parameters']
            
            fx = camera_params['camera_matrix']['fx']
            fy = camera_params['camera_matrix']['fy']
            cx = camera_params['camera_matrix']['cx']
            cy = camera_params['camera_matrix']['cy']
            
            self.camera_matrix = np.array([
                [fx, 0, cx],
                [0, fy, cy],
                [0, 0, 1]
            ], dtype=np.float32)
            
            distortion = camera_params['distortion_coefficients']
            self.distortion_coeffs = np.array([
                distortion['radial']['k1'],
                distortion['radial']['k2'],
                distortion['tangential']['p1'],
                distortion['tangential']['p2'],
                distortion['radial']['k3']
            ], dtype=np.float32)
            
            self.image_size = (
                config['image_properties']['width'],
                config['image_properties']['height']
            )
            
            print(f"✅ 相机参数加载成功:")
            print(f"   fx={fx:.2f}, fy={fy:.2f}, cx={cx:.2f}, cy={cy:.2f}")
            print(f"   图像尺寸: {self.image_size}")
            
        except Exception as e:
            raise RuntimeError(f"加载相机参数失败: {e}")
    
    def pixel_to_camera_coordinate(self, pixel_x: int, pixel_y: int, distance: float, 
                                   confidence: float = 1.0) -> CameraCoordinate:
        fx = self.camera_matrix[0, 0]
        fy = self.camera_matrix[1, 1]
        cx = self.camera_matrix[0, 2]
        cy = self.camera_matrix[1, 2]
        
        z = distance
        x = (pixel_x - cx) * z / fx
        y = (cy - pixel_y ) * z / fy
        
        return CameraCoordinate(
            x=x, y=y, z=z,
            pixel_x=pixel_x, pixel_y=pixel_y,
            confidence=confidence
        )

    def camera_to_pixel(self, camera_coord: CameraCoordinate) -> Tuple[float, float]:
        """
        将相机坐标 (x, y, z) 转换为像素坐标 (u, v)
        Args:
            camera_coord: CameraCoordinate 对象
        Returns:
            Tuple[float, float]: 图像像素坐标 (u, v)
        """
        fx = self.camera_matrix[0, 0]
        fy = self.camera_matrix[1, 1]
        cx = self.camera_matrix[0, 2]
        cy = self.camera_matrix[1, 2]

        x, y, z = camera_coord.x, camera_coord.y, camera_coord.z
        if z == 0:
            raise ValueError("Z值不能为0，无法投影到像素平面")
        
        u = (x * fx / z) + cx
        v = cy - (y * fy / z) 
        return (u, v), (camera_coord.pixel_x, camera_coord.pixel_y), z
    
    def project_point_to_ground_and_pixel(self, cam_point, camera_height=60.0):
        """
        将相机坐标系中的点投影到地面（Y=0），再投影回像素坐标系。
        
        Args:
            cam_point: (x, y, z) 相机坐标系点
            camera_matrix: 3x3 相机内参矩阵
            camera_height: 相机在世界坐标系中的高度（默认10.0）
            
        Returns:
            (u, v): 投影后的像素坐标
        """
        ground_z = -camera_height
    
        # 步骤1：计算像素对应的射线方向（归一化）
        u, v = cam_point[0], cam_point[1]
        fx, fy = self.camera_matrix[0,0], self.camera_matrix[1,1]
        cx, cy = self.camera_matrix[0,2], self.camera_matrix[1,2]
        
        ray_dir = np.array([(u - cx)/fx, (v - cy)/fy, 1.0])
        ray_dir /= np.linalg.norm(ray_dir)  # 归一化

        # 步骤2：计算射线与地面平面的交点
        t = ground_z / ray_dir[2]  # 交点参数
        ground_point_cam = t * ray_dir  # 3D交点（相机坐标系）

        # 步骤3：将地面交点反投影到像素坐标
        new_u = fx * ground_point_cam[0] + cx
        new_v = fy * ground_point_cam[1] + cy

        return (new_u, new_v)


    def convert_detection_points(self, detection_points: List[Tuple], 
                                 distances: List[float]) -> List[CameraCoordinate]:
        if len(detection_points) != len(distances):
            raise ValueError("检测点数量与距离数量不匹配")
        
        camera_coordinates = []
        for (pixel_x, pixel_y, conf), distance in zip(detection_points, distances):
            camera_coord = self.pixel_to_camera_coordinate(
                int(pixel_x), int(pixel_y), distance, conf
            )
            camera_coordinates.append(camera_coord)
        
        return camera_coordinates
