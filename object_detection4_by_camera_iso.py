# Copyright (c) OpenMMLab. All rights reserved.
import os
import sys
import random
import time
from pathlib import Path
from argparse import ArgumentParser

import cv2
import mmcv
import numpy as np
import torch
from PIL import Image
from typing import List, Tuple

# Custom module imports
sys.path.append(str(Path(__file__).resolve().parents[3]))
sys.path.append('/home/<USER>/panpan/mm_detection/test_carm')

from projects.easydeploy.model import ORTWrapper  # noqa: E402
from utils import (  # noqa: E402
    bbox_postprocess, 
    preprocess, 
    visualize_detections,
    reg_max2bbox,
    resize_and_pad
)
from decode import (  # noqa: E402
    predict_by_feat,
    _bbox_post_process,
    get_single_pred,
    
)
from easydict import EasyDict
from pixel_to_physical_py.src.detection_processor import DetectionProcessor, DetectionResult, BBox
from pixel_to_physical_py.src.camera_coordinate_converter import CameraCoordinateConverter

# Configuration and Constants
COLORS = [[random.randint(0, 255) for _ in range(3)] for _ in range(1000)]
CLASS_NAMES = ['bin', 'cloth', 'rug', 'shoe', 'wire', 'rail', 'wheel']
def transform_point(point, scale_factor, pad_param):
    """
    将模型输出的点坐标转换到原始图像坐标系
    Args:
        point: 元组 (x, y, conf)
        scale_factor: 缩放因子（原图尺寸 / 模型输入尺寸）
        pad_param: 填充参数 [top, bottom, left, right]
    Returns:
        Tuple: 转换后的坐标 (x_actual, y_actual, conf)
    """
    x, y, conf = point
    top, bottom, left, right = pad_param
    
    # 去除填充并缩放
    x_actual = (x - left) / scale_factor
    y_actual = (y - top)  / scale_factor
    
    return (x_actual, y_actual, conf)
def parse_args():
    parser = ArgumentParser()
    parser.add_argument('--camera-id', type=int, default=0, help='Camera device ID (default: 0)')
    parser.add_argument('--frame-size', nargs=2, type=int, default=[1280, 720],
                        help='Frame processing size [width, height] (default: 1280 720)')
    parser.add_argument('--config', default="yolov8s_old7cls_640.py",
                        help='Main config file')
    parser.add_argument('--checkpoint', '-c', default="yolov8v6_only.onnx",
                        help='Main checkpoint file')
    parser.add_argument('--line_config', default="line_in_five_head.py",
                        help='Line detection config file')
    parser.add_argument('--line_checkpoint', '-l', default="yolov8v13_only.onnx",
                        help='Line detection checkpoint file')
    parser.add_argument('--out-dir', default='camera_output',
                        help='Path to output file (unused in camera mode)')
    parser.add_argument('--device', default='cuda:0',
                        help='Device used for inference')
    parser.add_argument('--fps-display', action='store_true',
                        help='Show FPS on output')
    parser.add_argument('--outlier-mode', choices=['filter', 'correct'], default='correct',
                        help='异常点处理模式: filter=删除异常点, correct=修正异常点距离 (default: correct)')
    parser.add_argument('--camera-outlier-mode', choices=['filter', 'correct'], default='correct',
                        help='相机坐标异常点处理模式: filter=删除异常点, correct=修正异常点 (default: correct)')
    parser.add_argument('--window-size', type=int, default=20,
                        help='滑动窗口大小 (default: 10)')
    parser.add_argument('--threshold-ratio', type=float, default=1,
                        help='阈值比例 (default: 1.3)')
    parser.add_argument('--prominence', type=float, default=10,
                        help='峰值突出度 (default: 10)')
    parser.add_argument('--neighbor-window', type=int, default=2,
                        help='异常点校正时使用的邻居窗口大小 (default: 2)')
    return parser.parse_args()

def load_model(checkpoint_path, device):
    if checkpoint_path.endswith('.onnx'):
        model = ORTWrapper(checkpoint_path, device)
    model.to(device)
    return model

def build_preprocessing_pipeline():
    return {
        'pipeline': resize_and_pad,
        'preprocessor': preprocess(),
    }

def preprocess_image(rgb_img, pipeline, preprocessor, device):
    data_dict = dict(img=rgb_img, img_id=0)
    processed = pipeline(rgb_img)
    data, scale,pad_param= processed
    scale = 1.0/scale
    data = preprocessor(data).to(device)
    samples ={"scale":scale,"pad_param":pad_param}

    return data, samples

def process_detection_results(feats, device, test_cfg, original_shape, scale_factor, pad_param):
    bbox_preds = []
    cls_scores = []
    
    for feat in feats:
        bbox_pred, cls_score = torch.split(feat, [64, 7], dim=1)
        bbox_preds.append(bbox_pred)
        cls_scores.append(cls_score)
    
    if bbox_preds[0].shape[1] == 64:
        proj = torch.arange(16, dtype=torch.float).to(device)
        bbox_preds = [reg_max2bbox(bbox, proj) for bbox in bbox_preds]
    # print(original_shape)
    batch_img_metas = [{
        'ori_shape': original_shape,
        'pad_shape': (640, 640, 3),
        'scale_factor': scale_factor,
        "pad_param": pad_param
    }]
    
    return predict_by_feat(
        cls_scores, 
        bbox_preds, 
        objectnesses=None,
        batch_img_metas=batch_img_metas,
        cfg=test_cfg,
        post_processing=_bbox_post_process
    )

def _preprocess_image(img: np.ndarray, calibration_mapx_path, calibration_mapy_path) -> np.ndarray:
        """预处理图像（去畸变）"""
        
        calibration_mapx = np.fromfile(calibration_mapx_path, 
                    dtype=np.float32).reshape(img.shape[0], img.shape[1])
        calibration_mapy = np.fromfile(calibration_mapy_path, 
                    dtype=np.float32).reshape(img.shape[0], img.shape[1])
        processed_img = cv2.remap(img, calibration_mapx, calibration_mapy, cv2.INTER_LINEAR)
        print("使用标定映射表处理图像")
        
            
        return processed_img

def _initialize_physical_processor() -> None:
        """初始化物理距离处理器"""
        # with open(self.config.size_ranges_config, 'r') as f:
        #     size_config = yaml.safe_load(f)

        physical_processor = DetectionProcessor()
        # physical_processor.set_size_ranges_config(size_config)
        return physical_processor

def _initialize_camera_converter() -> CameraCoordinateConverter:
        """初始化相机坐标转换器"""
        camera_config_path = "pixel_to_physical_py/config/calib_intrix_new.yaml"
        camera_converter = CameraCoordinateConverter(camera_config_path)
        return camera_converter

def _convert_points_to_detection_results(points_data: List[Tuple], point_size: int = 10) -> List[DetectionResult]:
        """
        将点检测结果转换为DetectionResult列表

        Args:
            points_data: 点检测结果列表，每个元素为 (x_actual, y_actual, conf)
            point_size: 点周围的边界框大小（像素）

        Returns:
            转换后的检测结果列表
        """
        detection_results = []

        if not points_data or len(points_data) == 0:
            return detection_results

        for i, (x_actual, y_actual, conf) in enumerate(points_data):
            # 为点创建一个小的边界框用于测距计算
            bbox_obj = BBox(
                x1=int(x_actual),
                y1=int(y_actual ),
                x2=int(x_actual ),
                y2=int(y_actual )
            )

            # 创建DetectionResult对象
            det_result = DetectionResult(
                bbox=bbox_obj,
                label=f"point_{i}",  # 给点一个标签
                confidence=float(conf)
            )

            detection_results.append(det_result)

        return detection_results

import numpy as np
from scipy.signal import find_peaks

def detect_more_bulges(data, window_sizes=[5, 10, 15], threshold_ratios=[1.2, 1.3, 1.4]):
    all_bulges = []
    
    # 多尺度检测
    for ws in window_sizes:
        for tr in threshold_ratios:
            bulges = []
            for i in range(len(data)):
                left = max(0, i - ws)
                right = min(len(data), i + ws)
                window = data[left:right]
                local_mean = np.mean(window)
                local_std = np.std(window)
                
                # 动态阈值：均值 + n倍标准差
                threshold = local_mean + tr * local_std
                
                if data[i] > threshold:
                    bulges.append((i, data[i]))
            all_bulges.extend(bulges)
    
    # 去重并排序
    unique_bulges = list(set(all_bulges))
    unique_bulges.sort()
    return unique_bulges


def detect_bulges_sliding_window(data, window_size=5, threshold_ratio=1.3):
    """
    滑动窗口局部阈值检测隆起部分

    参数:
        data (list): 距离数据
        window_size (int): 滑动窗口大小
        threshold_ratio (float): 阈值比例

    返回:
        bulges (list): 隆起点的索引列表
    """
    bulges = []
    for i in range(len(data)):
        left = max(0, i - window_size)
        right = min(len(data), i + window_size)
        window = data[left:right]
        local_mean = np.mean(window)
        if data[i] > local_mean * threshold_ratio:
            bulges.append(i)
    return bulges

def detect_bulges_peaks(data, prominence=10):
    """
    峰值检测隆起部分

    参数:
        data (list): 距离数据
        prominence (float): 峰值突出度

    返回:
        peaks (list): 峰值点的索引列表
    """
    peaks, _ = find_peaks(data, prominence=prominence)
    return peaks.tolist()

def find_outlier_indices(distance_list, window_size=10, threshold_ratio=1.3, prominence=10):
    """
    基于滑动窗口和峰值检测方法找出异常点的索引

    参数:
        distance_list (list of str or float): 原始距离序列
        window_size (int): 滑动窗口大小（默认 10）
        threshold_ratio (float): 阈值比例（默认 1.3）
        prominence (float): 峰值突出度（默认 10）

    返回:
        outlier_indices (list of int): 异常值的索引位置
    """
    data = [float(x) for x in distance_list]

    # # window_sizes = [15, 35, 45]  # 不同窗口大小
    # # threshold_ratios = [1, 1.2, 1.3]  # 更低的阈值

    # # bulges_window = detect_more_bulges(data, window_sizes, threshold_ratios)
    # # 方法1：滑动窗口局部阈值检测
    # bulges_window = detect_bulges_sliding_window(data, window_size, threshold_ratio)

    # # # 方法2：峰值检测
    # # bulges_peaks = detect_bulges_peaks(data, prominence)

    # # 合并结果（取并集）
    # # all_bulges = list(set(bulges_window + bulges_peaks))


    # all_bulges = list(set(bulges_window))

    # all_bulges.sort()  # 按索引排序

    # return all_bulges
    # return bulges_window


    # bulges = detect_bulges_lof(data, n_neighbors=40)
    bulges = detect_bulges_isolation_forest(data)
    return bulges



def correct_outliers_by_neighbors(distance_list, outlier_indices, window=2):
    """
    用周围非异常值的平均值校正给定的异常点

    参数:
        distance_list (list of str or float): 原始距离序列
        outlier_indices (list of int): 异常值的索引位置
        window (int): 向前向后各取多少个非异常值（默认 2）

    返回:
        corrected (list of float): 校正后的距离序列
    """
    data = np.array([float(x) for x in distance_list])
    corrected = data.copy()

    # 全部异常点用于跳过异常邻居
    outlier_set = set(outlier_indices)

    for idx in outlier_indices:
        neighbors = []

        # 向前查找
        i = idx - 1
        while i >= 0 and len(neighbors) < window:
            if i not in outlier_set:
                neighbors.append(data[i])
            i -= 1

        # 向后查找
        i = idx + 1
        while i < len(data) and len(neighbors) < 2 * window:
            if i not in outlier_set:
                neighbors.append(data[i])
            i += 1

        # 平均值替代
        if neighbors:
            corrected[idx] = np.mean(neighbors)

    return corrected.tolist()


from sklearn.neighbors import LocalOutlierFactor

def detect_bulges_lof(distances, n_neighbors=40):
    X = np.array(distances).reshape(-1, 1)
    lof = LocalOutlierFactor(n_neighbors=n_neighbors, novelty=False, contamination="auto")
    outliers = lof.fit_predict(X)
    bulge_indices = np.where(outliers == -1)[0]  # -1表示异常
    return [(i, distances[i]) for i in bulge_indices]

from sklearn.ensemble import IsolationForest

def detect_bulges_isolation_forest(distances):
    X = np.array(distances).reshape(-1, 1)
    clf = IsolationForest(contamination="auto", random_state=60)
    outliers = clf.fit_predict(X)
    bulge_indices = np.where(outliers == -1)[0]
    return [(i, distances[i]) for i in bulge_indices]


def process_wire_distance_outliers_bulge(valid_results: List[Tuple], outlier_mode: str = 'correct',
                                        window_size: int = 10, threshold_ratio: float = 1.3,
                                        prominence: float = 10, neighbor_window: int = 2) -> List[Tuple]:
    """
    使用滑动窗口和峰值检测方法处理wire检测点的距离异常值（隆起部分）

    Args:
        valid_results: 包含(DetectionResult, point_coords)的列表
        outlier_mode: 处理模式 'filter'=删除异常点, 'correct'=修正异常点距离
        window_size: 滑动窗口大小（默认 10）
        threshold_ratio: 阈值比例（默认 1.3）
        prominence: 峰值突出度（默认 10）
        neighbor_window: 校正时使用的邻居窗口大小（默认 2）

    Returns:
        处理后的结果列表
    """
    if len(valid_results) < 3:
        print("检测点数量不足，跳过异常值处理")
        return valid_results

    # 按照x坐标从左到右排序
    sorted_results = sorted(valid_results, key=lambda x: x[1][0])  # x[1][0] 是 point_coords 的 x 坐标

    distances = [result[0].physical_distance for result in sorted_results]
    print(f"原始距离序列: {[f'{d:.1f}' for d in distances]}")

    # 使用滑动窗口和峰值检测方法检测异常点（隆起部分）
    outlier_indices = find_outlier_indices(distances, window_size, threshold_ratio, prominence)

    if not outlier_indices:
        print("未检测到距离异常点（隆起部分）")
        return sorted_results

    # print(f"滑动窗口+峰值检测到异常点索引: {outlier_indices}")
    # print(f"异常点距离值: {[f'{distances[i]:.1f}' for i in outlier_indices]}")
    # 只提取索引部分
    outlier_indices = [idx for (idx, val) in outlier_indices]
    print(f"异常点距离值: {[f'{distances[i]:.1f}' for i in outlier_indices]}")

    if outlier_mode == 'filter':
        # 模式1: 删除异常点
        filtered_results = []
        for i, result in enumerate(sorted_results):
            if i not in outlier_indices:
                filtered_results.append(result)
            else:
                print(f"删除异常点 {i}: 距离={result[0].physical_distance:.1f}cm")

        print(f"过滤后保留 {len(filtered_results)}/{len(sorted_results)} 个点")
        return filtered_results

    elif outlier_mode == 'correct':
        # 模式2: 修正异常点距离
        corrected_distances = correct_outliers_by_neighbors(distances, outlier_indices, neighbor_window)

        print(f"\n=== 隆起检测距离修正详情 ===")
        correction_count = 0

        for i, (result, corrected_distance) in enumerate(zip(sorted_results, corrected_distances)):
            det_result, point_coords = result

            if i in outlier_indices:
                original_distance = det_result.physical_distance
                det_result.physical_distance = corrected_distance
                det_result.left_distance = corrected_distance
                det_result.right_distance = corrected_distance
                det_result.des = f"[距离已修正] 原值:{original_distance:.1f}cm -> 修正值:{corrected_distance:.1f}cm (隆起检测方法)"

                print(f"修正点 {i}: {original_distance:.1f}cm -> {corrected_distance:.1f}cm")
                correction_count += 1

        print(f"共修正了 {correction_count} 个异常点")
        return sorted_results

    return sorted_results




def main():
    args = parse_args()
    # Create camera capture
    cap = cv2.VideoCapture(args.camera_id)
    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
    desired_fps = 30
    cap.set(cv2.CAP_PROP_FPS, desired_fps)
# cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
# cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 960)
    # Set camera resolution
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.frame_size[0])
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.frame_size[1])
    
    # Create output window
    cv2.namedWindow("监控视角 - 检测结果", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("监控视角 - 检测结果", 1280, 720)

    calibration_mapx = "pixel_to_physical_py/config/mapx"
    calibration_mapy = "pixel_to_physical_py/config/mapy"
    physical_processor = _initialize_physical_processor()
    camera_converter = _initialize_camera_converter()

    # Load models
    print("正在加载模型...")
    print(os.path.exists(args.checkpoint))
    model = load_model(args.checkpoint, args.device)
    line_model = load_model(args.line_checkpoint, args.device)
    
    # Build preprocessing pipelines
    print("正在构建预处理流程...")
    main_pp = build_preprocessing_pipeline()
    line_pp = build_preprocessing_pipeline()
    
    # Get configuration
    # test_cfg = main_pp['cfg'].model_test_cfg
    test_cfg = EasyDict(
        max_per_img=300,
        multi_label=True,
        nms=dict(iou_threshold=0.7, type='nms'),
        nms_pre=30000,
        score_thr=0.001)
    
    # FPS calculation
    frame_count = 0
    start_time = time.time()
    
    print("开始实时检测，按ESC键退出...")
    while cap.isOpened():
        # Capture frame-by-frame
        ret, frame = cap.read()
        if not ret:
            print("无法获取帧，退出...")
            # continue
            break
        # Convert BGR to RGB
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        original_shape = rgb_frame.shape
        
        rgb_frame = _preprocess_image(rgb_frame, calibration_mapx, calibration_mapy)
        # 在去畸变图像上绘制范围框
        x_min, x_max = (0, 1280)
        y_min, y_max = (420, 700)
        
        # 绘制矩形框 (红色，线宽2)
        cv2.rectangle(rgb_frame, (x_min, y_min), (x_max, y_max), (128, 255, 0), 2)

        # Main detection processing
        main_data, main_samples = preprocess_image(
            rgb_frame, 
            main_pp['pipeline'], 
            main_pp['preprocessor'], 
            args.device
        )
        main_result = model(main_data)
        main_results = process_detection_results(
            main_result,
            args.device,
            test_cfg,
            original_shape,
            main_samples.get('scale', 1),
            main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32))
        )
        
        # Line detection processing
        scale_factor = main_samples.get('scale', 1)
        pad_param = main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32))
        # Line detection processing
        line_data, line_samples = preprocess_image(
            rgb_frame, 
            main_pp['pipeline'], 
            main_pp['preprocessor'], 
            args.device
        )
        line_result = line_model(line_data)
        line_preds, line_scores = torch.split(line_result[0], [2, 1], dim=1)
        _, line_points, _ = get_single_pred(line_scores, line_preds, (640, 640))
        
        # Visualization - convert back to BGR for display
        # vis_img = visualize_detections(rgb_frame, main_results[0][0], class_names=CLASS_NAMES)
        


        # 处理线检测点并转换坐标
        transformed_points = []
        for point in line_points:
            x, y, conf = point
            if conf > 0.5:
                # 坐标转换
                x_actual, y_actual, conf = transform_point(point, scale_factor, pad_param)
                transformed_points.append((x_actual, y_actual, conf))
                # 在原始图像上绘制圆
                cv2.circle(rgb_frame, (int(x_actual), int(y_actual)), 5, (0, 0, 255), -1)

        # 处理物理距离计算
        detection_results = []
        distance_table_path = "pixel_to_physical_py/config/distance_table"  # 添加距离表路径

        if physical_processor is not None and len(transformed_points) > 0:
            try:
                # 将点转换为DetectionResult格式
                detection_results = _convert_points_to_detection_results(transformed_points)
                print(f"\n检测到 {len(detection_results)} 个目标点")

                # 计算所有点的物理距离
                valid_results = []
                for i, det_result in enumerate(detection_results):
                    print(f"\n--- 处理目标点 {i+1}: {det_result.label} ---")
                    # 使用physical_processor处理检测结果
                    physical_processor.process_detection_result(det_result, distance_table_path)

                    # 只保留成功计算出距离的点
                    if det_result.physical_distance > 0:
                        valid_results.append((det_result, transformed_points[i]))

                # === 相机坐标转换 ===
                print(f"\n=== 开始相机坐标转换 ===")
                camera_coordinates = []
                detection_points = []
                distances = []

                for det_result, point_coords in valid_results:
                    detection_points.append(point_coords)
                    distances.append(det_result.physical_distance)

                if len(detection_points) > 0:
                    # 转换到相机坐标系
                    camera_coordinates = camera_converter.convert_detection_points(detection_points, distances)

                    print(f"转换了 {len(camera_coordinates)} 个点到相机坐标系:")
                    for i, coord in enumerate(camera_coordinates):
                        print(f"  点{i+1}: 像素({coord.pixel_x}, {coord.pixel_y}) -> "
                              f"相机坐标({coord.x:.3f}, {coord.y:.3f}, {coord.z:.3f}cm)")

                    # 在相机坐标空间中检测异常点
                    print(f"\n=== 在相机坐标空间中检测异常点 ===")
                    print(f"处理模式: {args.camera_outlier_mode} ({'删除异常点' if args.camera_outlier_mode == 'filter' else '修正异常点'})")
                    outlier_infos = camera_converter.detect_outliers_in_camera_space(
                        camera_coordinates, method='isolation_forest'
                    )

                    outlier_count = sum(1 for info in outlier_infos if info.is_outlier)
                    print(f"检测到 {outlier_count} 个异常点")

                    if outlier_count > 0:
                        if args.camera_outlier_mode == 'filter':
                            # 模式1: 删除相机坐标异常点
                            print(f"=== 删除相机坐标异常点模式 ===")
                            filtered_results = []
                            for i, (outlier_info, (det_result, point_coords)) in enumerate(zip(outlier_infos, valid_results)):
                                if not outlier_info.is_outlier:
                                    filtered_results.append((det_result, point_coords))
                                else:
                                    print(f"  删除异常点{i+1}: 像素({camera_coordinates[i].pixel_x}, {camera_coordinates[i].pixel_y}), "
                                          f"距离={outlier_info.original_distance:.1f}cm, "
                                          f"相机坐标=({camera_coordinates[i].x:.3f}, {camera_coordinates[i].y:.3f}, {camera_coordinates[i].z:.3f})")

                            processed_results = filtered_results
                            print(f"过滤后保留 {len(processed_results)} 个点")

                        elif args.camera_outlier_mode == 'correct':
                            # 模式2: 修正相机坐标异常点
                            print(f"=== 修正相机坐标异常点模式 ===")
                            corrected_coordinates = camera_converter.correct_outliers(
                                camera_coordinates, outlier_infos, correction_method='neighbor_average'
                            )

                            # 更新检测结果中的距离值
                            for i, (coord, outlier_info) in enumerate(zip(corrected_coordinates, outlier_infos)):
                                if outlier_info.is_outlier:
                                    det_result, point_coords = valid_results[i]
                                    original_distance = det_result.physical_distance
                                    corrected_distance = coord.z   # 距离值

                                    det_result.physical_distance = corrected_distance
                                    det_result.left_distance = corrected_distance
                                    det_result.right_distance = corrected_distance
                                    det_result.des = f"[相机坐标异常修正] 原值:{original_distance:.1f}cm -> 修正值:{corrected_distance:.1f}cm"

                                    print(f"  修正点{i+1}: {original_distance:.1f}cm -> {corrected_distance:.1f}cm")

                            # 更新相机坐标列表
                            camera_coordinates = corrected_coordinates
                            processed_results = valid_results

                # 处理wire变形导致的距离异常 - 使用隆起检测方法
                # if len(valid_results) > 0:
                #     print(f"\n=== 开始处理距离异常值 (模式: {args.outlier_mode}, 方法: 隆起检测) ===")
                #     print(f"参数: 窗口大小={args.window_size}, 阈值比例={args.threshold_ratio}, " +
                #           f"峰值突出度={args.prominence}, 邻居窗口={args.neighbor_window}")
                #     processed_results = process_wire_distance_outliers_bulge(
                #         valid_results,
                #         outlier_mode=args.outlier_mode,
                #         window_size=args.window_size,
                #         threshold_ratio=args.threshold_ratio,
                #         prominence=args.prominence,
                #         neighbor_window=args.neighbor_window
                #     )
                #     print(f"处理完成: {len(processed_results)}/{len(valid_results)} 个点保留")
                # else:
                #     processed_results = valid_results

                # 显示处理后的结果
                print(f"\n=== 可视化 {len(processed_results)} 个处理后的点 ===")
                for i, (det_result, point_coords) in enumerate(processed_results):
                    print(f"目标点 {det_result.label}:")
                    print(f"  物理距离: {det_result.physical_distance:.2f} cm")

                    # 显示相机坐标信息（如果有的话）
                    if i < len(camera_coordinates):
                        coord = camera_coordinates[i]
                        print(f"  相机坐标: X={coord.x:.3f}m, Y={coord.y:.3f}m, Z={coord.z:.3f}m")
                        print(f"  像素坐标: ({coord.pixel_x}, {coord.pixel_y})")

                    # 检查是否为修正过的点
                    is_corrected = "[距离已修正]" in det_result.des or "[相机坐标异常修正]" in det_result.des
                    if is_corrected:
                        print(f"  ✓ 此点已被修正: {det_result.des}")

                    # 在圆圈内显示距离值
                    point_x, point_y = int(point_coords[0]), int(point_coords[1])
                    distance_value = int(det_result.physical_distance)  # 取整数，更简洁
                    distance_text = f"{distance_value}"

                    # 根据距离和是否修正选择颜色
                    if is_corrected:
                        # 修正过的点用特殊颜色标识
                        circle_color = (255, 0, 255)  # 紫色 - 修正过的点
                        border_color = (255, 255, 0)  # 黄色边框
                        print(f"  绘制修正点: 位置({point_x}, {point_y}), 距离={distance_value}cm, 颜色=紫色")
                    else:
                        # 正常点按距离选择颜色
                        if det_result.physical_distance < 30:
                            circle_color = (0, 255, 0)  # 绿色 - 近距离
                        elif det_result.physical_distance < 60:
                            circle_color = (255, 255, 0)  # 黄色 - 中距离
                        else:
                            circle_color = (255, 0, 0)  # 红色 - 远距离
                        border_color = (255, 255, 255)  # 白色边框
                        print(f"  绘制正常点: 位置({point_x}, {point_y}), 距离={distance_value}cm")

                    # 圆圈大小和字体设置
                    circle_radius = 8
                    font_scale = 0.3

                    # 绘制填充的圆圈作为背景
                    cv2.circle(rgb_frame, (point_x, point_y), circle_radius, circle_color, -1)

                    # 绘制圆圈边框（修正过的点用不同颜色边框）
                    cv2.circle(rgb_frame, (point_x, point_y), circle_radius, border_color, 2)

                    # 计算文字位置（居中）
                    text_size = cv2.getTextSize(distance_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)[0]
                    text_x = point_x - text_size[0] // 2
                    text_y = point_y + text_size[1] // 2

                    # 在圆圈内绘制距离数字（黑色）
                    cv2.putText(rgb_frame, distance_text, (text_x, text_y),
                               cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), 1)
            except Exception as e:
                print(f"物理距离计算出错: {e}")
                import traceback
                traceback.print_exc()
        

        # Convert back to BGR for OpenCV display
        display_img = cv2.cvtColor(rgb_frame, cv2.COLOR_RGB2BGR)
        cv2.imwrite("output/"+str(frame_count)+".png",display_img)
        # Calculate and display FPS
        frame_count += 1
        if args.fps_display and frame_count % 10 == 0:
            fps = frame_count / (time.time() - start_time)
            cv2.putText(display_img, f"FPS: {fps:.2f}", (20, 50), 
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        # Display the resulting frame
        cv2.imshow("监控视角 - 检测结果", display_img)
        # print(scale_factor,pad_param)

        # Exit on ESC key
        key = cv2.waitKey(1)
        if key == 27:  # ESC key
            print("ESC键按下，退出程序...")
            break
    
    # Cleanup
    cap.release()
    cv2.destroyAllWindows()
    print("程序已结束")

if __name__ == '__main__':
    main()