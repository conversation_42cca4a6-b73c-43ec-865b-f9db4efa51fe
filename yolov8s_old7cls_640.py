_backend_args = None
_multiscale_resize_transforms = [
    dict(
        _scope_='mmyolo',
        transforms=[
            dict(scale=(
                640,
                640,
            ), type='YOLOv5KeepRatioResize'),
            dict(
                allow_scale_up=False,
                pad_val=dict(img=114),
                scale=(
                    640,
                    640,
                ),
                type='LetterResize'),
        ],
        type='Compose'),
    dict(
        _scope_='mmyolo',
        transforms=[
            dict(scale=(
                320,
                320,
            ), type='YOLOv5KeepRatioResize'),
            dict(
                allow_scale_up=False,
                pad_val=dict(img=114),
                scale=(
                    320,
                    320,
                ),
                type='LetterResize'),
        ],
        type='Compose'),
    dict(
        _scope_='mmyolo',
        transforms=[
            dict(scale=(
                960,
                960,
            ), type='YOLOv5KeepRatioResize'),
            dict(
                allow_scale_up=False,
                pad_val=dict(img=114),
                scale=(
                    960,
                    960,
                ),
                type='LetterResize'),
        ],
        type='Compose'),
]
affine_scale = 0.5
albu_train_transforms = [
    dict(_scope_='mmyolo', p=0.01, type='Blur'),
    dict(_scope_='mmyolo', p=0.01, type='MedianBlur'),
    dict(_scope_='mmyolo', p=0.01, type='ToGray'),
    dict(_scope_='mmyolo', p=0.01, type='CLAHE'),
]
backend_args = None
base_lr = 0.01
batch_shapes_cfg = None
close_mosaic_epochs = 10
custom_hooks = [
    dict(
        _scope_='mmyolo',
        ema_type='ExpMomentumEMA',
        momentum=0.0001,
        priority=49,
        strict_load=False,
        type='EMAHook',
        update_buffers=True),
    dict(
        _scope_='mmyolo',
        switch_epoch=290,
        switch_pipeline=[
            dict(
                _scope_='mmyolo', backend_args=None, type='LoadImageFromFile'),
            dict(_scope_='mmyolo', type='LoadAnnotations', with_bbox=True),
            dict(
                _scope_='mmyolo',
                img_scale=(
                    640,
                    384,
                ),
                scale=(
                    640,
                    640,
                ),
                type='YOLOv5KeepRatioResize'),
            dict(
                _scope_='mmyolo',
                allow_scale_up=True,
                img_scale=(
                    640,
                    384,
                ),
                pad_val=dict(img=114.0),
                scale=(
                    640,
                    640,
                ),
                type='LetterResize'),
            dict(
                _scope_='mmyolo',
                border_val=(
                    114,
                    114,
                    114,
                ),
                max_aspect_ratio=100,
                max_rotate_degree=0.0,
                max_shear_degree=0.0,
                scaling_ratio_range=(
                    0.5,
                    1.5,
                ),
                type='YOLOv5RandomAffine'),
            dict(
                _scope_='mmyolo',
                bbox_params=dict(
                    format='pascal_voc',
                    label_fields=[
                        'gt_bboxes_labels',
                        'gt_ignore_flags',
                    ],
                    type='BboxParams'),
                keymap=dict(gt_bboxes='bboxes', img='image'),
                transforms=[
                    dict(p=0.01, type='Blur'),
                    dict(p=0.01, type='MedianBlur'),
                    dict(p=0.01, type='ToGray'),
                    dict(p=0.01, type='CLAHE'),
                ],
                type='mmdet.Albu'),
            dict(_scope_='mmyolo', type='YOLOv5HSVRandomAug'),
            dict(_scope_='mmyolo', prob=0.5, type='mmdet.RandomFlip'),
            dict(
                _scope_='mmyolo',
                meta_keys=(
                    'img_id',
                    'img_path',
                    'ori_shape',
                    'img_shape',
                    'flip',
                    'flip_direction',
                ),
                type='mmdet.PackDetInputs'),
        ],
        type='mmdet.PipelineSwitchHook'),
    dict(type='EcoAi.SaveBestHook'),
]
custom_imports = dict(
    allow_failed_imports=False,
    imports=[
        'EcoAi.models._econn_models',
        'EcoAi.datasets.det_dataset.econn_datasets.eco_coco',
        'EcoAi.visualization.detection_visualizer',
        'EcoAi.evaluation.metrics',
        'EcoAi.engine.hooks.savebest_hook',
        'EcoAi.transforms.det_transforms',
        'EcoAi.transforms.color_transforms',
    ])
data_root = ''
dataset_type = 'EcoAi.SZgroundOld7Dataset'
deepen_factor = 0.33
default_hooks = dict(
    checkpoint=dict(
        _scope_='mmyolo',
        interval=10,
        max_keep_ckpts=2,
        save_best='auto',
        type='CheckpointHook'),
    logger=dict(_scope_='mmyolo', interval=50, type='LoggerHook'),
    param_scheduler=dict(
        _scope_='mmyolo',
        lr_factor=0.01,
        max_epochs=300,
        scheduler_type='linear',
        type='YOLOv5ParamSchedulerHook'),
    sampler_seed=dict(_scope_='mmyolo', type='DistSamplerSeedHook'),
    timer=dict(_scope_='mmyolo', type='IterTimerHook'),
    visualization=dict(
        _scope_='mmyolo',
        draw=True,
        test_out_dir='output',
        type='mmdet.DetVisualizationHook'))
default_scope = 'mmyolo'
env_cfg = dict(
    cudnn_benchmark=True,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))
img_scale = (
    640,
    384,
)
img_scales = [
    (
        640,
        640,
    ),
    (
        320,
        320,
    ),
    (
        960,
        960,
    ),
]
last_stage_out_channels = 1024
last_transform = [
    dict(
        _scope_='mmyolo',
        bbox_params=dict(
            format='pascal_voc',
            label_fields=[
                'gt_bboxes_labels',
                'gt_ignore_flags',
            ],
            type='BboxParams'),
        keymap=dict(gt_bboxes='bboxes', img='image'),
        transforms=[
            dict(p=0.01, type='Blur'),
            dict(p=0.01, type='MedianBlur'),
            dict(p=0.01, type='ToGray'),
            dict(p=0.01, type='CLAHE'),
        ],
        type='mmdet.Albu'),
    dict(_scope_='mmyolo', type='YOLOv5HSVRandomAug'),
    dict(_scope_='mmyolo', prob=0.5, type='mmdet.RandomFlip'),
    dict(
        _scope_='mmyolo',
        meta_keys=(
            'img_id',
            'img_path',
            'ori_shape',
            'img_shape',
            'flip',
            'flip_direction',
        ),
        type='mmdet.PackDetInputs'),
]
launcher = 'none'
load_from = 'work_dirs/exps/test_econn/实验6/best_coco_bin_precision_epoch_223.pth'
log_level = 'INFO'
log_processor = dict(
    _scope_='mmyolo', by_epoch=True, type='LogProcessor', window_size=50)
loss_bbox_weight = 7.5
loss_cls_weight = 0.5
loss_dfl_weight = 0.375
lr_factor = 0.01
max_aspect_ratio = 100
max_epochs = 300
max_keep_ckpts = 2
model = dict(
    _scope_='mmyolo',
    backbone=dict(
        act_cfg=dict(inplace=True, negative_slope=0.1, type='LeakyReLU'),
        arch='P5',
        deepen_factor=0.33,
        last_stage_out_channels=1024,
        norm_cfg=dict(eps=0.001, momentum=0.03, type='BN'),
        type='YOLOv8CSPDarknet',
        widen_factor=0.5),
    bbox_head=dict(
        bbox_coder=dict(type='DistancePointBBoxCoder'),
        head_module=dict(
            act_cfg=dict(inplace=True, type='SiLU'),
            featmap_strides=[
                8,
                16,
                32,
            ],
            in_channels=[
                256,
                512,
                1024,
            ],
            norm_cfg=dict(eps=0.001, momentum=0.03, type='BN'),
            num_classes=7,
            reg_max=16,
            type='YOLOv8HeadModule',
            widen_factor=0.5),
        loss_bbox=dict(
            bbox_format='xyxy',
            iou_mode='ciou',
            loss_weight=7.5,
            reduction='sum',
            return_iou=False,
            type='IoULoss'),
        loss_cls=dict(
            loss_weight=0.5,
            reduction='none',
            type='mmdet.CrossEntropyLoss',
            use_sigmoid=True),
        loss_dfl=dict(
            loss_weight=0.375,
            reduction='mean',
            type='mmdet.DistributionFocalLoss'),
        prior_generator=dict(
            offset=0.5, strides=[
                8,
                16,
                32,
            ], type='mmdet.MlvlPointGenerator'),
        type='YOLOv8Head'),
    data_preprocessor=dict(
        bgr_to_rgb=True,
        mean=[
            0.0,
            0.0,
            0.0,
        ],
        std=[
            255.0,
            255.0,
            255.0,
        ],
        type='YOLOv5DetDataPreprocessor'),
    neck=dict(
        act_cfg=dict(inplace=True, negative_slope=0.1, type='LeakyReLU'),
        deepen_factor=0.33,
        in_channels=[
            256,
            512,
            1024,
        ],
        norm_cfg=dict(eps=0.001, momentum=0.03, type='BN'),
        num_csp_blocks=3,
        out_channels=[
            256,
            512,
            1024,
        ],
        type='YOLOv8PAFPN',
        widen_factor=0.5),
    test_cfg=dict(
        max_per_img=300,
        multi_label=True,
        nms=dict(iou_threshold=0.7, type='nms'),
        nms_pre=30000,
        score_thr=0.001),
    train_cfg=dict(
        assigner=dict(
            alpha=0.5,
            beta=6.0,
            eps=1e-09,
            num_classes=7,
            topk=10,
            type='BatchTaskAlignedAssigner',
            use_ciou=True)),
    type='YOLODetector')
model_test_cfg = dict(
    max_per_img=300,
    multi_label=True,
    nms=dict(_scope_='mmyolo', iou_threshold=0.7, type='nms'),
    nms_pre=30000,
    score_thr=0.001)
norm_cfg = dict(_scope_='mmyolo', eps=0.001, momentum=0.03, type='BN')
num_classes = 7
num_det_layers = 3
num_workers = 18
optim_wrapper = dict(
    _scope_='mmyolo',
    clip_grad=dict(max_norm=10.0),
    constructor='YOLOv5OptimizerConstructor',
    optimizer=dict(
        batch_size_per_gpu=16,
        lr=0.01,
        momentum=0.937,
        nesterov=True,
        type='SGD',
        weight_decay=0.0005),
    type='OptimWrapper')
param_scheduler = None
persistent_workers = True
pre_transform = [
    dict(_scope_='mmyolo', backend_args=None, type='LoadImageFromFile'),
    dict(_scope_='mmyolo', type='LoadAnnotations', with_bbox=True),
]
resume = True
save_epoch_intervals = 10
strides = [
    8,
    16,
    32,
]
tal_alpha = 0.5
tal_beta = 6.0
tal_topk = 10
test_cfg = dict(_scope_='mmyolo', type='TestLoop')
test_dataloader = dict(
    batch_size=48,
    dataset=dict(
        _scope_='mmyolo',
        ann_file='/李燃青/4data/扫地机/ground_old8cls_of/val.json',
        batch_shapes_cfg=None,
        data_prefix=dict(
            img='val2017/',
            sub_data_root='/李燃青/4data/扫地机/ground_old8cls_of/ground_val_0526'),
        data_root='',
        pipeline=[
            dict(backend_args=None, type='LoadImageFromFile'),
            dict(type='LoadAnnotations', with_bbox=True),
            dict(
                allow_scale_up=False,
                pad_val=dict(img=114),
                scale=(
                    640,
                    640,
                ),
                type='mmyolo.LetterResize'),
            dict(
                meta_keys=(
                    'img_id',
                    'img_path',
                    'ori_shape',
                    'img_shape',
                    'scale_factor',
                    'pad_param',
                ),
                type='mmdet.PackDetInputs'),
        ],
        test_mode=True,
        type='EcoAi.SZgroundOld7Dataset'),
    drop_last=False,
    num_workers=18,
    persistent_workers=True,
    pin_memory=True,
    sampler=dict(_scope_='mmyolo', shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    _scope_='mmyolo',
    ann_file='/李燃青/4data/扫地机/ground_old8cls_of/val.json',
    classwise=True,
    format_only=False,
    metric=[
        'bbox',
    ],
    outfile_prefix=
    '/李燃青/1code/eco_code/ecoaitoolkits/work_dirs/exps/test_econn/实验6/result',
    proposal_nums=(
        100,
        1,
        10,
    ),
    type='EcoAi.CustomCocoMetric')
train_ann_file = 'annotations/instances_train2017.json'
train_batch_size_per_gpu = 48
train_cfg = dict(
    _scope_='mmyolo',
    dynamic_intervals=[
        (
            490,
            1,
        ),
    ],
    max_epochs=500,
    type='EpochBasedTrainLoop',
    val_interval=1)
train_data_prefix = 'train2017/'
train_dataloader = dict(
    batch_size=48,
    collate_fn=dict(type='yolov5_collate'),
    dataset=dict(
        _scope_='mmyolo',
        ann_file='/李燃青/4data/扫地机/ground_old8cls_of/train.json',
        data_prefix=dict(
            img='train2017/',
            sub_data_root='/李燃青/4data/扫地机/ground_old8cls_of/ground_train_0526'
        ),
        data_root='',
        filter_cfg=None,
        pipeline=[
            dict(backend_args=None, type='LoadImageFromFile'),
            dict(type='LoadAnnotations', with_bbox=True),
            dict(keep_ratio=True, scale=(
                640,
                640,
            ), type='Resize'),
            dict(
                img_scale=(
                    640,
                    640,
                ),
                pad_val=114.0,
                pre_transform=[
                    dict(backend_args=None, type='LoadImageFromFile'),
                    dict(type='LoadAnnotations', with_bbox=True),
                ],
                type='Mosaic'),
            dict(
                border=(
                    -320,
                    -320,
                ),
                border_val=(
                    114,
                    114,
                    114,
                ),
                max_aspect_ratio=100,
                max_rotate_degree=0.0,
                max_shear_degree=0.0,
                scaling_ratio_range=(
                    0.5,
                    1.5,
                ),
                type='YOLOv5RandomAffine'),
            dict(type='YOLOv5HSVRandomAug'),
            dict(prob=0.5, type='mmdet.RandomFlip'),
            dict(
                meta_keys=(
                    'img_id',
                    'img_path',
                    'ori_shape',
                    'img_shape',
                    'flip',
                    'flip_direction',
                    'scale_factor',
                ),
                type='mmdet.PackDetInputs'),
        ],
        type='EcoAi.SZgroundOld7Dataset'),
    num_workers=18,
    persistent_workers=True,
    pin_memory=True,
    sampler=dict(_scope_='mmyolo', shuffle=True, type='DefaultSampler'))
train_num_workers = 8
train_pipeline = [
    dict(backend_args=None, type='LoadImageFromFile'),
    dict(type='LoadAnnotations', with_bbox=True),
    dict(keep_ratio=True, scale=(
        640,
        640,
    ), type='Resize'),
    dict(
        img_scale=(
            640,
            640,
        ),
        pad_val=114.0,
        pre_transform=[
            dict(backend_args=None, type='LoadImageFromFile'),
            dict(type='LoadAnnotations', with_bbox=True),
        ],
        type='Mosaic'),
    dict(
        border=(
            -320,
            -320,
        ),
        border_val=(
            114,
            114,
            114,
        ),
        max_aspect_ratio=100,
        max_rotate_degree=0.0,
        max_shear_degree=0.0,
        scaling_ratio_range=(
            0.5,
            1.5,
        ),
        type='YOLOv5RandomAffine'),
    dict(type='YOLOv5HSVRandomAug'),
    dict(prob=0.5, type='mmdet.RandomFlip'),
    dict(
        meta_keys=(
            'img_id',
            'img_path',
            'ori_shape',
            'img_shape',
            'flip',
            'flip_direction',
            'scale_factor',
        ),
        type='mmdet.PackDetInputs'),
]
train_pipeline_stage2 = [
    dict(_scope_='mmyolo', backend_args=None, type='LoadImageFromFile'),
    dict(_scope_='mmyolo', type='LoadAnnotations', with_bbox=True),
    dict(
        _scope_='mmyolo',
        img_scale=(
            640,
            384,
        ),
        scale=(
            640,
            640,
        ),
        type='YOLOv5KeepRatioResize'),
    dict(
        _scope_='mmyolo',
        allow_scale_up=True,
        img_scale=(
            640,
            384,
        ),
        pad_val=dict(img=114.0),
        scale=(
            640,
            640,
        ),
        type='LetterResize'),
    dict(
        _scope_='mmyolo',
        border_val=(
            114,
            114,
            114,
        ),
        max_aspect_ratio=100,
        max_rotate_degree=0.0,
        max_shear_degree=0.0,
        scaling_ratio_range=(
            0.5,
            1.5,
        ),
        type='YOLOv5RandomAffine'),
    dict(
        _scope_='mmyolo',
        bbox_params=dict(
            format='pascal_voc',
            label_fields=[
                'gt_bboxes_labels',
                'gt_ignore_flags',
            ],
            type='BboxParams'),
        keymap=dict(gt_bboxes='bboxes', img='image'),
        transforms=[
            dict(p=0.01, type='Blur'),
            dict(p=0.01, type='MedianBlur'),
            dict(p=0.01, type='ToGray'),
            dict(p=0.01, type='CLAHE'),
        ],
        type='mmdet.Albu'),
    dict(_scope_='mmyolo', type='YOLOv5HSVRandomAug'),
    dict(_scope_='mmyolo', prob=0.5, type='mmdet.RandomFlip'),
    dict(
        _scope_='mmyolo',
        meta_keys=(
            'img_id',
            'img_path',
            'ori_shape',
            'img_shape',
            'flip',
            'flip_direction',
        ),
        type='mmdet.PackDetInputs'),
]
tta_model = None
tta_pipeline = None
val_ann_file = 'annotations/instances_val2017.json'
val_batch_size_per_gpu = 1
val_cfg = dict(_scope_='mmyolo', type='ValLoop')
val_data_prefix = 'val2017/'
val_dataloader = dict(
    batch_size=48,
    dataset=dict(
        _scope_='mmyolo',
        ann_file='/李燃青/4data/扫地机/ground_old8cls_of/val.json',
        batch_shapes_cfg=None,
        data_prefix=dict(
            img='val2017/',
            sub_data_root='/李燃青/4data/扫地机/ground_old8cls_of/ground_val_0526'),
        data_root='',
        pipeline=[
            dict(backend_args=None, type='LoadImageFromFile'),
            dict(type='LoadAnnotations', with_bbox=True),
            dict(
                allow_scale_up=False,
                pad_val=dict(img=114),
                scale=(
                    640,
                    384,
                ),
                type='mmyolo.LetterResize'),
            dict(
                meta_keys=(
                    'img_id',
                    'img_path',
                    'ori_shape',
                    'img_shape',
                    'scale_factor',
                    'pad_param',
                ),
                type='mmdet.PackDetInputs'),
        ],
        test_mode=True,
        type='EcoAi.SZgroundOld7Dataset'),
    drop_last=False,
    num_workers=18,
    persistent_workers=True,
    pin_memory=True,
    sampler=dict(_scope_='mmyolo', shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    _scope_='mmyolo',
    ann_file='/李燃青/4data/扫地机/ground_old8cls_of/val.json',
    classwise=True,
    format_only=False,
    metric=[
        'bbox',
    ],
    outfile_prefix=
    '/李燃青/1code/eco_code/ecoaitoolkits/work_dirs/exps/test_econn/实验6/result',
    proposal_nums=(
        100,
        1,
        10,
    ),
    type='EcoAi.CustomCocoMetric')
val_interval_stage2 = 1
val_num_workers = 2
val_pipeline = [
    dict(backend_args=None, type='LoadImageFromFile'),
    dict(type='LoadAnnotations', with_bbox=True),
    dict(keep_ratio=True, scale=(
        640,
        640,
    ), type='Resize'),
    dict(pad_val=dict(img=114.0), size=(
        640,
        640,
    ), type='Pad'),
    dict(
        meta_keys=(
            'img_id',
            'img_path',
            'ori_shape',
            'img_shape',
            'scale_factor',
        ),
        type='mmdet.PackDetInputs'),
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    _scope_='mmyolo',
    name='visualizer',
    type='mmdet.DetCustomVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
weight_decay = 0.0005
widen_factor = 0.5
work_dir = '/李燃青/1code/eco_code/ecoaitoolkits/work_dirs/exps/test_econn/实验6'
