# import numpy as np

# def correct_outliers(distance_list, multiplier=1.5, window=8):
#     """
#     识别并校正距离序列中的异常值（基于IQR + 周边均值校正）

#     参数:
#         distance_list (list of str): 原始距离字符串列表
#         multiplier (float): IQR乘数，控制异常检测灵敏度
#         window (int): 用于校正的前后窗口大小（非异常点）

#     返回:
#         corrected (list of float): 校正后的距离列表
#         outlier_indices (list of int): 被识别为异常的索引位置
#     """
#     data = np.array([float(x) for x in distance_list])
#     corrected = data.copy()

#     # 计算 IQR 异常阈值
#     Q1 = np.percentile(data, 25)
#     Q3 = np.percentile(data, 75)
#     IQR = Q3 - Q1
#     lower_bound = Q1 - multiplier * IQR
#     upper_bound = Q3 + multiplier * IQR

#     # 找出异常值索引
#     outlier_indices = np.where((data < lower_bound) | (data > upper_bound))[0]

#     # 逐个校正异常点
#     for idx in outlier_indices:
#         # 向前、向后查找非异常值
#         neighbors = []

#         # 前向查找
#         i = idx - 1
#         while i >= 0 and len(neighbors) < window:
#             if lower_bound <= data[i] <= upper_bound:
#                 neighbors.append(data[i])
#             i -= 1

#         # 后向查找
#         i = idx + 1
#         while i < len(data) and len(neighbors) < 2 * window:
#             if lower_bound <= data[i] <= upper_bound:
#                 neighbors.append(data[i])
#             i += 1

#         # 计算邻居均值校正
#         if neighbors:
#             corrected[idx] = np.mean(neighbors)

#     return corrected.tolist(), outlier_indices.tolist()


# raw_data = ['22.6', '22.6', '24.1', '21.5', '24.4', '20.9', '25.0', '20.3', '25.6', '20.0',
#             '26.5', '19.4', '27.1', '19.1', '27.9', '18.8', '28.5', '18.5', '29.4', '18.2',
#             '29.7', '30.6', '17.9', '17.1', '31.2', '18.2', '17.1', '16.2', '32.1', '18.2',
#             '17.1', '16.2', '32.4', '18.2', '17.1', '16.2', '32.6', '18.5', '17.1', '33.5',
#             '16.2', '19.4', '34.4', '19.4', '18.5', '17.1', '16.2', '35.0', '18.2', '17.1',
#             '16.2', '35.6', '16.8', '16.2', '36.5', '16.8', '37.9', '16.2', '39.4', '16.8',
#             '16.2', '40.9', '15.9', '15.9', '42.4', '15.9', '15.9', '43.8', '45.9', '47.9',
#             '51.2', '55.6', '57.9', '67.1', '72.4']

# corrected_values, outlier_idxs = correct_outliers(raw_data)

# print("异常点位置:", outlier_idxs)
# print("校正后结果（末尾10项）:", corrected_values[-10:])




import numpy as np
from scipy.signal import find_peaks
import matplotlib.pyplot as plt

# 原始距离序列
original_sequence = ['43.8', '39.7', '46.8', '38.2', '49.1', '51.2', '37.6', '53.5', '36.8', '55.9', '36.8', '56.8', '35.9', '57.9', '59.7', '35.9', '61.5', '36.8', '63.5', '36.5', '63.5', '36.5', '63.5', '37.4', '65.3', '37.1', '65.3', '37.9', '67.1', '37.9', '72.4', '67.1', '37.9', '72.4', '72.4', '67.1', '38.5', '72.4', '67.1', '38.5', '67.1', '38.5', '65.3', '38.5', '74.1', '63.5', '37.9', '54.7', '63.5', '51.5', '37.9', '47.1', '49.4', '63.5', '37.1', '45.9', '65.3', '45.3', '37.4', '37.6', '51.5', '40.3', '68.8', '72.4', '41.2', '55.3', '70.6', '72.4', '52.6', '36.2', '54.7', '72.4', '34.4', '61.5', '70.6', '63.5', '33.5', '28.2', '31.2', '25.6', '33.2', '28.2', '25.6', '31.2', '32.9', '31.2', '27.6', '25.6']

# 转换为浮点数列表
distances = [float(x) for x in original_sequence]

# 方法1：滑动窗口局部阈值检测
def detect_bulges_sliding_window(data, window_size=5, threshold_ratio=1.3):
    bulges = []
    for i in range(len(data)):
        left = max(0, i - window_size)
        right = min(len(data), i + window_size)
        window = data[left:right]
        local_mean = np.mean(window)
        if data[i] > local_mean * threshold_ratio:
            bulges.append((i, data[i]))  # 返回(索引, 距离值)
    return bulges

# 方法2：峰值检测
def detect_bulges_peaks(data, prominence=10):
    peaks, _ = find_peaks(data, prominence=prominence)
    return [(i, data[i]) for i in peaks]

# 检测隆起部分
bulges_window = detect_bulges_sliding_window(distances, window_size=10, threshold_ratio=1.3)
bulges_peaks = detect_bulges_peaks(distances, prominence=10)

# 合并结果（取并集）
all_bulges = list(set(bulges_window + bulges_peaks))
all_bulges.sort()  # 按索引排序

# 可视化
plt.figure(figsize=(15, 6))
plt.plot(distances, 'b-', label='list', linewidth=1)

# 标记滑动窗口检测结果
plt.scatter(
    [x[0] for x in bulges_window],
    [x[1] for x in bulges_window],
    color='red',
    s=50,
    label='window'
)

# 标记峰值检测结果
plt.scatter(
    [x[0] for x in bulges_peaks],
    [x[1] for x in bulges_peaks],
    color='green',
    marker='x',
    s=100,
    linewidths=2,
    label='top'
)

plt.title('Wire隆起部分检测', fontsize=14)
plt.xlabel('数据点索引', fontsize=12)
plt.ylabel('距离值', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True, linestyle='--', alpha=0.7)
plt.tight_layout()

# 打印检测结果
print("检测到的隆起点（索引, 距离值）：")
for idx, val in all_bulges:
    print(f"索引 {idx}: {val}")

plt.show()