# # Copyright (c) OpenMMLab. All rights reserved.
# import os
# import sys
# import random
# import time
# from pathlib import Path
# from argparse import ArgumentParser

# import cv2
# import mmcv
# import numpy as np
# import torch
# from PIL import Image
# from typing import List, Tuple

# # Custom module imports
# sys.path.append(str(Path(__file__).resolve().parents[3]))
# sys.path.append('/home/<USER>/panpan/mm_detection/test_carm')

# from projects.easydeploy.model import ORTWrapper  # noqa: E402
# from utils import (  # noqa: E402
#     bbox_postprocess, 
#     preprocess, 
#     visualize_detections,
#     reg_max2bbox,
#     resize_and_pad
# )
# from decode import (  # noqa: E402
#     predict_by_feat,
#     _bbox_post_process,
#     get_single_pred,
    
# )
# from easydict import EasyDict
# from pixel_to_physical_py.src.detection_processor import DetectionProcessor, DetectionResult, BBox
# from pixel_to_physical_py.src.camera_coordinate_converter import CameraCoordinateConverter

# # Configuration and Constants
# COLORS = [[random.randint(0, 255) for _ in range(3)] for _ in range(1000)]
# CLASS_NAMES = ['bin', 'cloth', 'rug', 'shoe', 'wire', 'rail', 'wheel']
# def transform_point(point, scale_factor, pad_param):
#     """
#     将模型输出的点坐标转换到原始图像坐标系
#     Args:
#         point: 元组 (x, y, conf)
#         scale_factor: 缩放因子（原图尺寸 / 模型输入尺寸）
#         pad_param: 填充参数 [top, bottom, left, right]
#     Returns:
#         Tuple: 转换后的坐标 (x_actual, y_actual, conf)
#     """
#     x, y, conf = point
#     top, bottom, left, right = pad_param
    
#     # 去除填充并缩放
#     x_actual = (x - left) / scale_factor
#     y_actual = (y - top)  / scale_factor
    
#     return (x_actual, y_actual, conf)
# def parse_args():
#     parser = ArgumentParser()
#     parser.add_argument('--camera-id', type=int, default=0, help='Camera device ID (default: 0)')
#     parser.add_argument('--frame-size', nargs=2, type=int, default=[1280, 720],
#                         help='Frame processing size [width, height] (default: 1280 720)')
#     parser.add_argument('--config', default="yolov8s_old7cls_640.py",
#                         help='Main config file')
#     parser.add_argument('--checkpoint', '-c', default="yolov8v6_only.onnx",
#                         help='Main checkpoint file')
#     parser.add_argument('--line_config', default="line_in_five_head.py",
#                         help='Line detection config file')
#     parser.add_argument('--line_checkpoint', '-l', default="yolov8v13_only.onnx",
#                         help='Line detection checkpoint file')
#     parser.add_argument('--out-dir', default='camera_output',
#                         help='Path to output file (unused in camera mode)')
#     parser.add_argument('--device', default='cuda:0',
#                         help='Device used for inference')
#     parser.add_argument('--fps-display', action='store_true',
#                         help='Show FPS on output')
#     parser.add_argument('--outlier-mode', choices=['filter', 'correct'], default='correct',
#                         help='异常点处理模式: filter=删除异常点, correct=修正异常点距离 (default: correct)')
#     parser.add_argument('--camera-outlier-mode', choices=['filter', 'correct'], default='correct',
#                         help='相机坐标异常点处理模式: filter=删除异常点, correct=修正异常点 (default: correct)')
#     parser.add_argument('--window-size', type=int, default=20,
#                         help='滑动窗口大小 (default: 10)')
#     parser.add_argument('--threshold-ratio', type=float, default=1,
#                         help='阈值比例 (default: 1.3)')
#     parser.add_argument('--prominence', type=float, default=10,
#                         help='峰值突出度 (default: 10)')
#     parser.add_argument('--neighbor-window', type=int, default=2,
#                         help='异常点校正时使用的邻居窗口大小 (default: 2)')
#     return parser.parse_args()

# def load_model(checkpoint_path, device):
#     if checkpoint_path.endswith('.onnx'):
#         model = ORTWrapper(checkpoint_path, device)
#     model.to(device)
#     return model

# def build_preprocessing_pipeline():
#     return {
#         'pipeline': resize_and_pad,
#         'preprocessor': preprocess(),
#     }

# def preprocess_image(rgb_img, pipeline, preprocessor, device):
#     data_dict = dict(img=rgb_img, img_id=0)
#     processed = pipeline(rgb_img)
#     data, scale,pad_param= processed
#     scale = 1.0/scale
#     data = preprocessor(data).to(device)
#     samples ={"scale":scale,"pad_param":pad_param}

#     return data, samples

# def process_detection_results(feats, device, test_cfg, original_shape, scale_factor, pad_param):
#     bbox_preds = []
#     cls_scores = []
    
#     for feat in feats:
#         bbox_pred, cls_score = torch.split(feat, [64, 7], dim=1)
#         bbox_preds.append(bbox_pred)
#         cls_scores.append(cls_score)
    
#     if bbox_preds[0].shape[1] == 64:
#         proj = torch.arange(16, dtype=torch.float).to(device)
#         bbox_preds = [reg_max2bbox(bbox, proj) for bbox in bbox_preds]
#     # print(original_shape)
#     batch_img_metas = [{
#         'ori_shape': original_shape,
#         'pad_shape': (640, 640, 3),
#         'scale_factor': scale_factor,
#         "pad_param": pad_param
#     }]
    
#     return predict_by_feat(
#         cls_scores, 
#         bbox_preds, 
#         objectnesses=None,
#         batch_img_metas=batch_img_metas,
#         cfg=test_cfg,
#         post_processing=_bbox_post_process
#     )

# def _preprocess_image(img: np.ndarray, calibration_mapx_path, calibration_mapy_path) -> np.ndarray:
#         """预处理图像（去畸变）"""
        
#         calibration_mapx = np.fromfile(calibration_mapx_path, 
#                     dtype=np.float32).reshape(img.shape[0], img.shape[1])
#         calibration_mapy = np.fromfile(calibration_mapy_path, 
#                     dtype=np.float32).reshape(img.shape[0], img.shape[1])
#         processed_img = cv2.remap(img, calibration_mapx, calibration_mapy, cv2.INTER_LINEAR)
#         print("使用标定映射表处理图像")
        
            
#         return processed_img

# def _initialize_physical_processor() -> None:
#         """初始化物理距离处理器"""
#         # with open(self.config.size_ranges_config, 'r') as f:
#         #     size_config = yaml.safe_load(f)

#         physical_processor = DetectionProcessor()
#         # physical_processor.set_size_ranges_config(size_config)
#         return physical_processor

# def _initialize_camera_converter() -> CameraCoordinateConverter:
#         """初始化相机坐标转换器"""
#         camera_config_path = "pixel_to_physical_py/config/calib_intrix_new.yaml"
#         camera_converter = CameraCoordinateConverter(camera_config_path)
#         return camera_converter

# def _convert_points_to_detection_results(points_data: List[Tuple], point_size: int = 10) -> List[DetectionResult]:
#         """
#         将点检测结果转换为DetectionResult列表

#         Args:
#             points_data: 点检测结果列表，每个元素为 (x_actual, y_actual, conf)
#             point_size: 点周围的边界框大小（像素）

#         Returns:
#             转换后的检测结果列表
#         """
#         detection_results = []

#         if not points_data or len(points_data) == 0:
#             return detection_results

#         for i, (x_actual, y_actual, conf) in enumerate(points_data):
#             # 为点创建一个小的边界框用于测距计算
#             bbox_obj = BBox(
#                 x1=int(x_actual),
#                 y1=int(y_actual ),
#                 x2=int(x_actual ),
#                 y2=int(y_actual )
#             )

#             # 创建DetectionResult对象
#             det_result = DetectionResult(
#                 bbox=bbox_obj,
#                 label=f"point_{i}",  # 给点一个标签
#                 confidence=float(conf)
#             )

#             detection_results.append(det_result)

#         return detection_results


# def main():
#     args = parse_args()
#     # Create camera capture
#     cap = cv2.VideoCapture(args.camera_id)
#     cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
#     desired_fps = 30
#     cap.set(cv2.CAP_PROP_FPS, desired_fps)
# # cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
# # cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 960)
#     # Set camera resolution
#     cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.frame_size[0])
#     cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.frame_size[1])
    
#     # Create output window
#     cv2.namedWindow("监控视角 - 检测结果", cv2.WINDOW_NORMAL)
#     cv2.resizeWindow("监控视角 - 检测结果", 1280, 720)

#     calibration_mapx = "pixel_to_physical_py/config/mapx"
#     calibration_mapy = "pixel_to_physical_py/config/mapy"
#     physical_processor = _initialize_physical_processor()
#     camera_converter = _initialize_camera_converter()

#     # Load models
#     print("正在加载模型...")
#     print(os.path.exists(args.checkpoint))
#     model = load_model(args.checkpoint, args.device)
#     line_model = load_model(args.line_checkpoint, args.device)
    
#     # Build preprocessing pipelines
#     print("正在构建预处理流程...")
#     main_pp = build_preprocessing_pipeline()
#     line_pp = build_preprocessing_pipeline()
    
#     # Get configuration
#     # test_cfg = main_pp['cfg'].model_test_cfg
#     test_cfg = EasyDict(
#         max_per_img=300,
#         multi_label=True,
#         nms=dict(iou_threshold=0.7, type='nms'),
#         nms_pre=30000,
#         score_thr=0.001)
    
#     # FPS calculation
#     frame_count = 0
#     start_time = time.time()
    
#     print("开始实时检测，按ESC键退出...")
#     while cap.isOpened():
#         # Capture frame-by-frame
#         ret, frame = cap.read()
#         if not ret:
#             print("无法获取帧，退出...")
#             # continue
#             break
#         # Convert BGR to RGB
#         rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
#         original_shape = rgb_frame.shape
        
#         rgb_frame = _preprocess_image(rgb_frame, calibration_mapx, calibration_mapy)
#         # 在去畸变图像上绘制范围框
#         x_min, x_max = (0, 1280)
#         y_min, y_max = (420, 700)
        
#         # 绘制矩形框 (红色，线宽2)
#         cv2.rectangle(rgb_frame, (x_min, y_min), (x_max, y_max), (128, 255, 0), 2)

#         # Main detection processing
#         main_data, main_samples = preprocess_image(
#             rgb_frame, 
#             main_pp['pipeline'], 
#             main_pp['preprocessor'], 
#             args.device
#         )
#         main_result = model(main_data)
#         main_results = process_detection_results(
#             main_result,
#             args.device,
#             test_cfg,
#             original_shape,
#             main_samples.get('scale', 1),
#             main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32))
#         )
        
#         # Line detection processing
#         scale_factor = main_samples.get('scale', 1)
#         pad_param = main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32))
#         # Line detection processing
#         line_data, line_samples = preprocess_image(
#             rgb_frame, 
#             main_pp['pipeline'], 
#             main_pp['preprocessor'], 
#             args.device
#         )
#         line_result = line_model(line_data)
#         line_preds, line_scores = torch.split(line_result[0], [2, 1], dim=1)
#         _, line_points, _ = get_single_pred(line_scores, line_preds, (640, 640))
        
#         # Visualization - convert back to BGR for display
#         # vis_img = visualize_detections(rgb_frame, main_results[0][0], class_names=CLASS_NAMES)
        


#         # 处理线检测点并转换坐标
#         transformed_points = []
#         for point in line_points:
#             x, y, conf = point
#             if conf > 0.5:
#                 # 坐标转换
#                 x_actual, y_actual, conf = transform_point(point, scale_factor, pad_param)
#                 transformed_points.append((x_actual, y_actual, conf))
#                 # 在原始图像上绘制圆
#                 cv2.circle(rgb_frame, (int(x_actual), int(y_actual)), 5, (0, 0, 255), -1)

#         # 处理物理距离计算
#         detection_results = []
#         distance_table_path = "pixel_to_physical_py/config/distance_table"  # 添加距离表路径

#         if physical_processor is not None and len(transformed_points) > 0:
#             try:
#                 # 将点转换为DetectionResult格式
#                 detection_results = _convert_points_to_detection_results(transformed_points)
#                 print(f"\n检测到 {len(detection_results)} 个目标点")

#                 # 计算所有点的物理距离
#                 valid_results = []
#                 for i, det_result in enumerate(detection_results):
#                     print(f"\n--- 处理目标点 {i+1}: {det_result.label} ---")
#                     # 使用physical_processor处理检测结果
#                     physical_processor.process_detection_result(det_result, distance_table_path)

#                     # 只保留成功计算出距离的点
#                     if det_result.physical_distance > 0:
#                         valid_results.append((det_result, transformed_points[i]))

                
#                 # 显示处理后的结果
#                 print(f"\n=== 可视化 {len(valid_results)} 个处理后的点 ===")
#                 for i, (det_result, point_coords) in enumerate(valid_results):
#                     print(f"目标点 {det_result.label}:")
#                     print(f"  物理距离: {det_result.physical_distance:.2f} cm")

                    
#                     # 在圆圈内显示距离值
#                     point_x, point_y = int(point_coords[0]), int(point_coords[1])
#                     distance_value = int(det_result.physical_distance)  # 取整数，更简洁
#                     distance_text = f"{distance_value}"

                    
#                     # 正常点按距离选择颜色
#                     if det_result.physical_distance < 30:
#                         circle_color = (0, 255, 0)  # 绿色 - 近距离
#                     elif det_result.physical_distance < 60:
#                         circle_color = (255, 255, 0)  # 黄色 - 中距离
#                     else:
#                         circle_color = (255, 0, 0)  # 红色 - 远距离
#                     border_color = (255, 255, 255)  # 白色边框
#                     print(f"  绘制正常点: 位置({point_x}, {point_y}), 距离={distance_value}cm")

#                     # 圆圈大小和字体设置
#                     circle_radius = 8
#                     font_scale = 0.3

#                     # 绘制填充的圆圈作为背景
#                     cv2.circle(rgb_frame, (point_x, point_y), circle_radius, circle_color, -1)

#                     # 绘制圆圈边框（修正过的点用不同颜色边框）
#                     cv2.circle(rgb_frame, (point_x, point_y), circle_radius, border_color, 2)

#                     # 计算文字位置（居中）
#                     text_size = cv2.getTextSize(distance_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)[0]
#                     text_x = point_x - text_size[0] // 2
#                     text_y = point_y + text_size[1] // 2

#                     # 在圆圈内绘制距离数字（黑色）
#                     cv2.putText(rgb_frame, distance_text, (text_x, text_y),
#                                cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), 1)
#             except Exception as e:
#                 print(f"物理距离计算出错: {e}")
#                 import traceback
#                 traceback.print_exc()
        

#         # Convert back to BGR for OpenCV display
#         display_img = cv2.cvtColor(rgb_frame, cv2.COLOR_RGB2BGR)
#         cv2.imwrite("output/"+str(frame_count)+".png",display_img)
#         # Calculate and display FPS
#         frame_count += 1
#         if args.fps_display and frame_count % 10 == 0:
#             fps = frame_count / (time.time() - start_time)
#             cv2.putText(display_img, f"FPS: {fps:.2f}", (20, 50), 
#                         cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
#         # Display the resulting frame
#         cv2.imshow("监控视角 - 检测结果", display_img)
#         # print(scale_factor,pad_param)

#         # Exit on ESC key
#         key = cv2.waitKey(1)
#         if key == 27:  # ESC key
#             print("ESC键按下，退出程序...")
#             break
    
#     # Cleanup
#     cap.release()
#     cv2.destroyAllWindows()
#     print("程序已结束")

# if __name__ == '__main__':
#     main()



# Copyright (c) OpenMMLab. All rights reserved.
import os
import sys
import random
import time
from pathlib import Path
from argparse import ArgumentParser

import cv2
import mmcv
import numpy as np
import torch
from PIL import Image
from typing import List, Tuple

# Custom module imports
sys.path.append(str(Path(__file__).resolve().parents[3]))
sys.path.append('/home/<USER>/panpan/mm_detection/test_carm')

from projects.easydeploy.model import ORTWrapper  # noqa: E402
from utils import (  # noqa: E402
    bbox_postprocess, 
    preprocess, 
    visualize_detections,
    reg_max2bbox,
    resize_and_pad
)
from decode import (  # noqa: E402
    predict_by_feat,
    _bbox_post_process,
    get_single_pred,
    
)
from easydict import EasyDict
from pixel_to_physical_py.src.detection_processor import DetectionProcessor, DetectionResult, BBox
from pixel_to_physical_py.src.camera_coordinate_converter import CameraCoordinateConverter, CameraCoordinate

# Configuration and Constants
COLORS = [[random.randint(0, 255) for _ in range(3)] for _ in range(1000)]
CLASS_NAMES = ['bin', 'cloth', 'rug', 'shoe', 'wire', 'rail', 'wheel']
def transform_point(point, scale_factor, pad_param):
    x, y, conf = point
    top, bottom, left, right = pad_param
    x_actual = (x - left) / scale_factor
    y_actual = (y - top)  / scale_factor
    return (x_actual, y_actual, conf)

def parse_args():
    parser = ArgumentParser()
    parser.add_argument('--camera-id', type=int, default=0)
    parser.add_argument('--frame-size', nargs=2, type=int, default=[1280, 720])
    parser.add_argument('--config', default="yolov8s_old7cls_640.py")
    parser.add_argument('--checkpoint', '-c', default="yolov8v6_only.onnx")
    parser.add_argument('--line_config', default="line_in_five_head.py")
    parser.add_argument('--line_checkpoint', '-l', default="yolov8v13_only.onnx")
    parser.add_argument('--out-dir', default='camera_output')
    parser.add_argument('--device', default='cuda:0')
    parser.add_argument('--fps-display', action='store_true')
    parser.add_argument('--outlier-mode', choices=['filter', 'correct'], default='correct')
    parser.add_argument('--camera-outlier-mode', choices=['filter', 'correct'], default='correct')
    parser.add_argument('--window-size', type=int, default=20)
    parser.add_argument('--threshold-ratio', type=float, default=1)
    parser.add_argument('--prominence', type=float, default=10)
    parser.add_argument('--neighbor-window', type=int, default=2)
    return parser.parse_args()

def load_model(checkpoint_path, device):
    if checkpoint_path.endswith('.onnx'):
        model = ORTWrapper(checkpoint_path, device)
    model.to(device)
    return model

def build_preprocessing_pipeline():
    return {
        'pipeline': resize_and_pad,
        'preprocessor': preprocess(),
    }

def preprocess_image(rgb_img, pipeline, preprocessor, device):
    processed = pipeline(rgb_img)
    data, scale, pad_param = processed
    scale = 1.0/scale
    data = preprocessor(data).to(device)
    samples = {"scale": scale, "pad_param": pad_param}
    return data, samples

def process_detection_results(feats, device, test_cfg, original_shape, scale_factor, pad_param):
    bbox_preds, cls_scores = [], []
    for feat in feats:
        bbox_pred, cls_score = torch.split(feat, [64, 7], dim=1)
        bbox_preds.append(bbox_pred)
        cls_scores.append(cls_score)
    if bbox_preds[0].shape[1] == 64:
        proj = torch.arange(16, dtype=torch.float).to(device)
        bbox_preds = [reg_max2bbox(bbox, proj) for bbox in bbox_preds]
    batch_img_metas = [{
        'ori_shape': original_shape,
        'pad_shape': (640, 640, 3),
        'scale_factor': scale_factor,
        'pad_param': pad_param
    }]
    return predict_by_feat(cls_scores, bbox_preds, objectnesses=None,
                           batch_img_metas=batch_img_metas, cfg=test_cfg,
                           post_processing=_bbox_post_process)

def _preprocess_image(img: np.ndarray, calibration_mapx_path, calibration_mapy_path) -> np.ndarray:
    calibration_mapx = np.fromfile(calibration_mapx_path, dtype=np.float32).reshape(img.shape[0], img.shape[1])
    calibration_mapy = np.fromfile(calibration_mapy_path, dtype=np.float32).reshape(img.shape[0], img.shape[1])
    processed_img = cv2.remap(img, calibration_mapx, calibration_mapy, cv2.INTER_LINEAR)
    print("使用标定映射表处理图像")
    return processed_img

def _initialize_physical_processor() -> DetectionProcessor:
    return DetectionProcessor()

def _initialize_camera_converter() -> CameraCoordinateConverter:
    camera_config_path = "pixel_to_physical_py/config/calib_intrix_new.yaml"
    return CameraCoordinateConverter(camera_config_path)

def _convert_points_to_detection_results(points_data: List[Tuple], point_size: int = 10) -> List[DetectionResult]:
    detection_results = []
    for i, (x_actual, y_actual, conf) in enumerate(points_data):
        bbox_obj = BBox(x1=int(x_actual), y1=int(y_actual), x2=int(x_actual), y2=int(y_actual))
        det_result = DetectionResult(bbox=bbox_obj, label=f"point_{i}", confidence=float(conf))
        detection_results.append(det_result)
    return detection_results


def project_to_ground(cam_coord: CameraCoordinate, camera_height_cm: float = 60.0) -> CameraCoordinate:
    """
    将相机坐标系中的点投影到地面（假设地面为 y = camera_height_cm）

    Args:
        cam_coord: 相机坐标系下的点
        camera_height_cm: 相机安装离地面高度（单位 cm）

    Returns:
        CameraCoordinate: 投影到地面的点（y = camera_height_cm）
    """
    x, y, z = cam_coord.x, cam_coord.y, cam_coord.z
    if y <= 0.001:
        raise ValueError(f"相机坐标 y 值太小（{y:.3f}），无法进行地面投影")

    scale = camera_height_cm / y
    ground_x = x * scale
    ground_y = camera_height_cm
    ground_z = z * scale

    return CameraCoordinate(
        x=ground_x,
        y=ground_y,
        z=ground_z,
        pixel_x=cam_coord.pixel_x,
        pixel_y=cam_coord.pixel_y,
        confidence=cam_coord.confidence
    )




def main():
    args = parse_args()
    cap = cv2.VideoCapture(args.camera_id)
    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
    cap.set(cv2.CAP_PROP_FPS, 30)
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.frame_size[0])
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.frame_size[1])
    cv2.namedWindow("监控视角 - 检测结果", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("监控视角 - 检测结果", 1280, 720)

    calibration_mapx = "pixel_to_physical_py/config/mapx"
    calibration_mapy = "pixel_to_physical_py/config/mapy"
    physical_processor = _initialize_physical_processor()
    camera_converter = _initialize_camera_converter()

    model = load_model(args.checkpoint, args.device)
    line_model = load_model(args.line_checkpoint, args.device)
    main_pp = build_preprocessing_pipeline()
    test_cfg = EasyDict(
        max_per_img=300,
        multi_label=True,
        nms=dict(iou_threshold=0.7, type='nms'),
        nms_pre=30000,
        score_thr=0.001)

    frame_count, start_time = 0, time.time()
    print("开始实时检测，按ESC键退出...")

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        original_shape = rgb_frame.shape
        rgb_frame = _preprocess_image(rgb_frame, calibration_mapx, calibration_mapy)
        
        # 在去畸变图像上绘制范围框
        x_min, x_max = (0, 1280)
        y_min, y_max = (420, 700)
        # 绘制矩形框 (红色，线宽2)
        cv2.rectangle(rgb_frame, (x_min, y_min), (x_max, y_max), (128, 255, 0), 2)

        main_data, main_samples = preprocess_image(rgb_frame, main_pp['pipeline'], main_pp['preprocessor'], args.device)
        main_result = model(main_data)
        main_results = process_detection_results(main_result, args.device, test_cfg, original_shape,
                                                 main_samples.get('scale', 1), main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32)))

        line_data, _ = preprocess_image(rgb_frame, main_pp['pipeline'], main_pp['preprocessor'], args.device)
        line_result = line_model(line_data)
        line_preds, line_scores = torch.split(line_result[0], [2, 1], dim=1)
        _, line_points, _ = get_single_pred(line_scores, line_preds, (640, 640))

        scale_factor = main_samples.get('scale', 1)
        pad_param = main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32))
        transformed_points = [transform_point(p, scale_factor, pad_param) for p in line_points if p[2] > 0.5]

        # 先绘制原始检测点（红色小圆点）
        for pt in transformed_points:
            cv2.circle(rgb_frame, (int(pt[0]), int(pt[1])), 3, (0, 0, 255), -1)

        distance_table_path = "pixel_to_physical_py/config/distance_table"
        detection_results = _convert_points_to_detection_results(transformed_points)
        valid_results = []

        for i, det_result in enumerate(detection_results):
            physical_processor.process_detection_result(det_result, distance_table_path)
            if det_result.physical_distance > 0:
                valid_results.append((det_result, transformed_points[i]))

        # 可视化距离值
        print(f"\n=== 可视化 {len(valid_results)} 个检测点的距离值 ===")
        for i, (det_result, point_coords) in enumerate(valid_results):
            point_x, point_y = int(point_coords[0]), int(point_coords[1])
            distance_value = det_result.physical_distance

            # # 根据距离选择颜色
            # if distance_value < 30:
            #     circle_color = (0, 255, 0)  # 绿色 - 近距离
            # elif distance_value < 60:
            #     circle_color = (255, 255, 0)  # 黄色 - 中距离
            # else:
            #     circle_color = (255, 0, 0)  # 红色 - 远距离

            # border_color = (255, 255, 255)  # 白色边框

            # # 圆圈和字体设置
            # circle_radius = 12
            # font_scale = 0.4
            # font_thickness = 1

            # # 格式化距离文本
            # distance_text = f"{distance_value:.1f}"

            # # 绘制圆圈（填充）
            # cv2.circle(rgb_frame, (point_x, point_y), circle_radius, circle_color, -1)
            # # 绘制圆圈边框
            # cv2.circle(rgb_frame, (point_x, point_y), circle_radius, border_color, 2)

            # 计算文字位置（居中）
            # text_size = cv2.getTextSize(distance_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, font_thickness)[0]
            # text_x = point_x - text_size[0] // 2
            # text_y = point_y + text_size[1] // 2

            # # 在圆圈内绘制距离数字（黑色）
            # cv2.putText(rgb_frame, distance_text, (text_x, text_y),
            #            cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), font_thickness)

            # print(f"  点 {i+1}: 位置({point_x}, {point_y}), 距离={distance_value:.1f}cm")

            # 相机坐标转换和地面投影（保持原有功能）
            x1, x2 = det_result.bbox.x1, det_result.bbox.x2
            y2 = det_result.bbox.y2
            depth = det_result.physical_distance
            cam_left = camera_converter.pixel_to_camera_coordinate(x1, y2, depth)
            # cam_right = camera_converter.pixel_to_camera_coordinate(x2, y2, depth)
            # 设置为地面上的投影点（y=0）
            ground_left = CameraCoordinate(x=cam_left.x, y=cam_left.y, z=cam_left.z,
                                         pixel_x=cam_left.pixel_x, pixel_y=cam_left.pixel_y,
                                         confidence=cam_left.confidence)
            # ground_right = CameraCoordinate(x=cam_right.x, y=cam_right.y, z=cam_right.z,
            #                               pixel_x=cam_right.pixel_x, pixel_y=cam_right.pixel_y,
            #                               confidence=cam_right.confidence)

            # 2. 投影到地面（z=0）
            # ground_left1 = project_to_ground(ground_left, camera_height_cm=5.0)
            # ground_right = project_to_ground(ground_right, camera_height_cm=60.0)

            # 转回图像坐标
            # proj_left, org_left, org_left_z = camera_converter.project_point_to_ground_and_pixel(ground_left, camera_height=60)
            proj_left, org_left, org_left_z = camera_converter.camera_to_pixel(ground_left)
            # proj_right, org_right, org_right_z = camera_converter.camera_to_pixel(ground_right)

            # 对转回的图像坐标重新计算距离
            # 创建新的检测结果用于重新计算距离
            proj_left_point = (proj_left[0], proj_left[1], 1.0)  # 添加置信度
            # proj_right_point = (proj_right[0], proj_right[1], 1.0)

            # 转换为DetectionResult格式并重新计算距离
            proj_left_result = _convert_points_to_detection_results([proj_left_point])[0]
            # proj_right_result = _convert_points_to_detection_results([proj_right_point])[0]

            # 重新计算投影点的距离
            physical_processor.process_detection_result(proj_left_result, distance_table_path)
            # physical_processor.process_detection_result(proj_right_result, distance_table_path)

            # 绘制投影点并显示重新计算的距离
            if proj_left_result.physical_distance > 0:
                cv2.circle(rgb_frame, (int(proj_left[0]), int(proj_left[1])), 4, (0, 255, 255), -1)
                # 在投影点旁边显示重新计算的距离
                proj_distance_text = f"{proj_left_result.physical_distance:.1f}"
                cv2.putText(rgb_frame, proj_distance_text,
                           (int(proj_left[0]) + 8, int(proj_left[1]) - 8),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 255, 255), 1)
                print(f"    原始左点: 位置({org_left[0]:.1f}, {org_left[1]:.1f}), 原始距离={ground_left.z:.1f}cm")
                print(f"    左投影点: 位置({proj_left[0]:.1f}, {proj_left[1]:.1f}), 重新计算距离={proj_left_result.physical_distance:.1f}cm")

            # if proj_right_result.physical_distance > 0:
            #     cv2.circle(rgb_frame, (int(proj_right[0]), int(proj_right[1])), 4, (0, 255, 255), -1)
            #     # 在投影点旁边显示重新计算的距离
            #     proj_distance_text = f"{proj_right_result.physical_distance:.1f}"
            #     cv2.putText(rgb_frame, proj_distance_text,
            #                (int(proj_right[0]) + 8, int(proj_right[1]) - 8),
            #                cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 255, 255), 1)
            #     print(f"    右投影点: 位置({proj_right[0]:.1f}, {proj_right[1]:.1f}), 重新计算距离={proj_right_result.physical_distance:.1f}cm")

        display_img = cv2.cvtColor(rgb_frame, cv2.COLOR_RGB2BGR)
        cv2.imwrite(f"output/{frame_count}.png", display_img)
        frame_count += 1
        if args.fps_display and frame_count % 10 == 0:
            fps = frame_count / (time.time() - start_time)
            cv2.putText(display_img, f"FPS: {fps:.2f}", (20, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        cv2.imshow("监控视角 - 检测结果", display_img)
        if cv2.waitKey(1) == 27:
            break

    cap.release()
    cv2.destroyAllWindows()
    print("程序已结束")

if __name__ == '__main__':
    main()

