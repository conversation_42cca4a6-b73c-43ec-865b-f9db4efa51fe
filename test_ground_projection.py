#!/usr/bin/env python3
"""
测试地面投影功能的脚本
演示如何将wire线上变形隆起的点投影到地面位置来获得更准确的距离值
"""

import numpy as np
import cv2
from pixel_to_physical_py.src.camera_coordinate_converter import CameraCoordinateConverter, CameraCoordinate
from pixel_to_physical_py.src.detection_processor import DetectionProcessor, DetectionResult, BBox

def project_to_ground(cam_coord: CameraCoordinate, camera_height_cm: float = 60.0) -> CameraCoordinate:
    """
    将相机坐标系中的点投影到地面（假设地面为 y = camera_height_cm）
    """
    x, y, z = cam_coord.x, cam_coord.y, cam_coord.z
    if y <= 0.001:
        raise ValueError(f"相机坐标 y 值太小（{y:.3f}），无法进行地面投影")

    scale = camera_height_cm / y
    ground_x = x * scale
    ground_y = camera_height_cm
    ground_z = z * scale

    return CameraCoordinate(
        x=ground_x,
        y=ground_y,
        z=ground_z,
        pixel_x=cam_coord.pixel_x,
        pixel_y=cam_coord.pixel_y,
        confidence=cam_coord.confidence
    )

def test_ground_projection():
    """测试地面投影功能"""
    print("=== 测试wire线地面投影功能 ===\n")
    
    # 初始化相机坐标转换器和距离处理器
    camera_config_path = "pixel_to_physical_py/config/calib_intrix_new.yaml"
    distance_table_path = "pixel_to_physical_py/config/distance_table"
    
    try:
        camera_converter = CameraCoordinateConverter(camera_config_path)
        physical_processor = DetectionProcessor()
        
        # 模拟一些wire线上的检测点（包括一些隆起的点）
        test_points = [
            (640, 500, 0.9),   # 中心点，可能贴地
            (620, 480, 0.8),   # 左侧点，可能隆起（y值较小）
            (660, 485, 0.85),  # 右侧点，可能隆起
            (600, 520, 0.7),   # 更左侧点，可能贴地
            (680, 515, 0.75),  # 更右侧点，可能贴地
        ]
        
        camera_height = 60.0  # 相机离地面高度60cm
        
        print(f"相机高度设置: {camera_height} cm")
        print(f"测试点数量: {len(test_points)}\n")
        
        for i, (pixel_x, pixel_y, conf) in enumerate(test_points):
            print(f"--- 测试点 {i+1}: 像素坐标({pixel_x}, {pixel_y}) ---")
            
            try:
                # 1. 计算原始距离
                bbox = BBox(x1=pixel_x, y1=pixel_y, x2=pixel_x, y2=pixel_y)
                det_result = DetectionResult(bbox=bbox, label=f"wire_point_{i}", confidence=conf)
                physical_processor.process_detection_result(det_result, distance_table_path)
                original_distance = det_result.physical_distance
                
                if original_distance <= 0:
                    print(f"  ❌ 无法获取原始距离")
                    continue
                
                print(f"  📏 原始距离: {original_distance:.1f} cm")
                
                # 2. 转换为相机坐标
                cam_coord = camera_converter.pixel_to_camera_coordinate(
                    pixel_x, pixel_y, original_distance, conf
                )
                print(f"  📍 相机坐标: ({cam_coord.x:.1f}, {cam_coord.y:.1f}, {cam_coord.z:.1f})")
                
                # 3. 投影到地面
                ground_coord = project_to_ground(cam_coord, camera_height)
                print(f"  🌍 地面坐标: ({ground_coord.x:.1f}, {ground_coord.y:.1f}, {ground_coord.z:.1f})")
                
                # 4. 投影回像素坐标
                projected_pixel, _, _ = camera_converter.camera_to_pixel(ground_coord)
                proj_x, proj_y = int(projected_pixel[0]), int(projected_pixel[1])
                print(f"  📐 投影像素: ({proj_x}, {proj_y})")
                
                # 5. 重新计算投影点的距离
                proj_bbox = BBox(x1=proj_x, y1=proj_y, x2=proj_x, y2=proj_y)
                proj_det_result = DetectionResult(bbox=proj_bbox, label=f"projected_wire_point_{i}", confidence=conf)
                physical_processor.process_detection_result(proj_det_result, distance_table_path)
                projected_distance = proj_det_result.physical_distance
                
                print(f"  📏 投影距离: {projected_distance:.1f} cm")
                
                # 6. 分析结果
                distance_diff = abs(projected_distance - original_distance)
                height_diff = abs(cam_coord.y - camera_height)
                
                print(f"  📊 距离差异: {distance_diff:.1f} cm")
                print(f"  📏 高度差异: {height_diff:.1f} cm")
                
                if height_diff > 5.0:  # 如果高度差异超过5cm，认为是隆起点
                    print(f"  ⚠️  检测到隆起点（高度偏差 {height_diff:.1f}cm）")
                    if distance_diff > 2.0:  # 距离差异超过2cm
                        print(f"  ✅ 地面投影修正有效（距离修正 {distance_diff:.1f}cm）")
                    else:
                        print(f"  ℹ️  地面投影修正较小")
                else:
                    print(f"  ✅ 点接近地面，投影修正不明显")
                
            except Exception as e:
                print(f"  ❌ 处理失败: {e}")
            
            print()
        
        print("=== 测试完成 ===")
        print("\n使用说明:")
        print("1. 运行主程序时添加 --use-ground-projection 参数启用地面投影")
        print("2. 使用 --camera-height 参数设置相机离地面高度（默认60cm）")
        print("3. 投影修正后的点会用青色边框标识")
        print("\n示例命令:")
        print("python object_detection4_by_camera.py --use-ground-projection --camera-height 60.0")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        print("请确保相机配置文件和距离表文件存在")

if __name__ == "__main__":
    test_ground_projection()
