#!/usr/bin/env python3
"""
地面投影原理演示脚本
展示如何将wire线上隆起的点投影到地面来修正距离测量误差
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def demo_ground_projection_principle():
    """演示地面投影的几何原理"""
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 相机参数（简化）
    camera_height = 60.0  # 相机离地面高度 (cm)
    focal_length = 500.0  # 焦距 (像素)
    
    # 模拟wire线上的点
    # 假设wire线在地面时的真实位置
    ground_points_z = np.array([30, 40, 50, 60, 70])  # 地面距离 (cm)
    ground_points_x = np.array([-10, -5, 0, 5, 10])   # 横向位置 (cm)
    ground_points_y = np.array([camera_height] * 5)   # 地面高度 = 相机高度
    
    # 模拟wire线变形隆起后的位置
    deformed_heights = np.array([5, 8, 12, 7, 4])     # 隆起高度 (cm)
    deformed_points_y = ground_points_y - deformed_heights  # 隆起后的y坐标
    deformed_points_x = ground_points_x  # x坐标不变
    deformed_points_z = ground_points_z  # z坐标不变（实际物理位置）
    
    print("=== 地面投影原理演示 ===\n")
    
    # 创建3D图形
    fig = plt.figure(figsize=(15, 10))
    
    # 3D视图
    ax1 = fig.add_subplot(221, projection='3d')
    
    # 绘制相机位置
    ax1.scatter([0], [0], [0], color='red', s=100, label='相机位置')
    
    # 绘制地面
    ground_x = np.linspace(-20, 20, 10)
    ground_z = np.linspace(20, 80, 10)
    Ground_X, Ground_Z = np.meshgrid(ground_x, ground_z)
    Ground_Y = np.full_like(Ground_X, camera_height)
    ax1.plot_surface(Ground_X, Ground_Y, Ground_Z, alpha=0.3, color='brown', label='地面')
    
    # 绘制原始地面点
    ax1.scatter(ground_points_x, ground_points_y, ground_points_z, 
               color='green', s=50, label='地面真实位置')
    
    # 绘制变形隆起点
    ax1.scatter(deformed_points_x, deformed_points_y, deformed_points_z, 
               color='orange', s=50, label='隆起后位置')
    
    # 绘制投影线（从相机到隆起点）
    for i in range(len(ground_points_z)):
        ax1.plot([0, deformed_points_x[i]], [0, deformed_points_y[i]], [0, deformed_points_z[i]], 
                'r--', alpha=0.6)
    
    # 绘制投影线延伸到地面
    for i in range(len(ground_points_z)):
        # 计算投影比例
        scale = camera_height / deformed_points_y[i]
        projected_x = deformed_points_x[i] * scale
        projected_z = deformed_points_z[i] * scale
        
        ax1.plot([deformed_points_x[i], projected_x], 
                [deformed_points_y[i], camera_height], 
                [deformed_points_z[i], projected_z], 
                'b--', alpha=0.8)
        
        # 绘制投影点
        ax1.scatter([projected_x], [camera_height], [projected_z], 
                   color='blue', s=50)
    
    ax1.set_xlabel('X (cm)')
    ax1.set_ylabel('Y (cm)')
    ax1.set_zlabel('Z (cm)')
    ax1.set_title('3D视图：地面投影原理')
    ax1.legend()
    
    # 侧视图 (X-Y平面)
    ax2 = fig.add_subplot(222)
    ax2.scatter([0], [0], color='red', s=100, label='相机')
    ax2.axhline(y=camera_height, color='brown', linestyle='-', alpha=0.5, label='地面')
    ax2.scatter(deformed_points_z, deformed_points_y, color='orange', s=50, label='隆起点')
    ax2.scatter(ground_points_z, ground_points_y, color='green', s=50, label='地面真实点')
    
    # 绘制投影线
    for i in range(len(ground_points_z)):
        scale = camera_height / deformed_points_y[i]
        projected_z = deformed_points_z[i] * scale
        
        # 从相机到隆起点
        ax2.plot([0, deformed_points_z[i]], [0, deformed_points_y[i]], 'r--', alpha=0.6)
        # 延伸到地面
        ax2.plot([deformed_points_z[i], projected_z], 
                [deformed_points_y[i], camera_height], 'b--', alpha=0.8)
        # 投影点
        ax2.scatter([projected_z], [camera_height], color='blue', s=50)
    
    ax2.set_xlabel('Z距离 (cm)')
    ax2.set_ylabel('Y高度 (cm)')
    ax2.set_title('侧视图：投影原理')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.invert_yaxis()  # Y轴向下为正
    
    # 距离对比图
    ax3 = fig.add_subplot(223)
    
    # 计算各种距离
    original_distances = ground_points_z  # 真实地面距离
    measured_distances = deformed_points_z  # 直接测量隆起点的距离
    
    # 计算投影修正后的距离
    projected_distances = []
    for i in range(len(ground_points_z)):
        scale = camera_height / deformed_points_y[i]
        projected_z = deformed_points_z[i] * scale
        projected_distances.append(projected_z)
    
    projected_distances = np.array(projected_distances)
    
    x_pos = np.arange(len(ground_points_z))
    width = 0.25
    
    ax3.bar(x_pos - width, original_distances, width, label='真实地面距离', color='green', alpha=0.7)
    ax3.bar(x_pos, measured_distances, width, label='直接测量距离', color='orange', alpha=0.7)
    ax3.bar(x_pos + width, projected_distances, width, label='投影修正距离', color='blue', alpha=0.7)
    
    ax3.set_xlabel('测试点编号')
    ax3.set_ylabel('距离 (cm)')
    ax3.set_title('距离测量对比')
    ax3.set_xticks(x_pos)
    ax3.set_xticklabels([f'点{i+1}' for i in range(len(ground_points_z))])
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 误差分析图
    ax4 = fig.add_subplot(224)
    
    direct_error = np.abs(measured_distances - original_distances)
    projected_error = np.abs(projected_distances - original_distances)
    
    ax4.bar(x_pos - width/2, direct_error, width, label='直接测量误差', color='orange', alpha=0.7)
    ax4.bar(x_pos + width/2, projected_error, width, label='投影修正误差', color='blue', alpha=0.7)
    
    ax4.set_xlabel('测试点编号')
    ax4.set_ylabel('距离误差 (cm)')
    ax4.set_title('测量误差对比')
    ax4.set_xticks(x_pos)
    ax4.set_xticklabels([f'点{i+1}' for i in range(len(ground_points_z))])
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 打印数值结果
    print("数值分析结果:")
    print("-" * 60)
    print(f"{'点编号':<6} {'真实距离':<8} {'直接测量':<8} {'投影修正':<8} {'直接误差':<8} {'投影误差':<8}")
    print("-" * 60)
    
    for i in range(len(ground_points_z)):
        print(f"{i+1:<6} {original_distances[i]:<8.1f} {measured_distances[i]:<8.1f} "
              f"{projected_distances[i]:<8.1f} {direct_error[i]:<8.1f} {projected_error[i]:<8.1f}")
    
    print("-" * 60)
    print(f"平均误差: 直接测量 {np.mean(direct_error):.1f}cm, 投影修正 {np.mean(projected_error):.1f}cm")
    print(f"误差改善: {np.mean(direct_error) - np.mean(projected_error):.1f}cm")
    
    print("\n原理说明:")
    print("1. 当wire线隆起时，直接测量会得到错误的距离值")
    print("2. 通过相机内参将像素坐标转换为3D相机坐标")
    print("3. 将3D点投影到地面平面（Y = 相机高度）")
    print("4. 重新计算投影点的距离，获得修正后的距离值")
    print("5. 投影修正能显著减少由于wire线变形造成的距离测量误差")

if __name__ == "__main__":
    demo_ground_projection_principle()
